id=="string""function""number""function")i(a);else{let u=s({...a},n);!1&&s.id&&u===null&&te.log(``${e.protocol}:`:"",a=e.port?`:${e.port}`:"";return`${t}//${e.host}${a}${e.path?`/${e.path}`:""}/api/`}function Hy(e){return`${Gy(e)}${e.projectId}/envelope/`}function Ky(e,t){return D0({sentry_key:e.public<PERSON>ey,sentry_version:qy,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${a.name}`))}),t}
 a.filename||null}return null}function Si(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?r3(t):null}catch{return!1&&te.error(`Cannot extract url for event ${sa(e)}`),null}}function Ks(e,t){let a=Ws(e,t),n={type:t&&t.name,value:l3(t)};return a.length&&(n.stacktrace={frames:a}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function o3(e,t,a,n){let r={except
[0].type==="SentryError"}catch{}return!1}function r3(e=[]){for(let t=e.length-1;t>=0;t--){let a=e[t];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}function Si(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?r3(t):null}catch{return!1&&te.error(`Cannot extract url for event ${sa(e)}`),null}}function Ks(e,t){let a=Ws(e,t),n={type:t&
{super(a);this.message=a;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var hy=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function fy(e){return e==="http"||e==="https"}function wn(e,t=!1){let{host:a,path:n,pass:r,port:i,projectId:o,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&r?`:${r}`:""}@${a}${i?`:${i}`:""}/${n&&`${n}/`}${o}`
{},Object.keys(t).map(a=>{let[n,r]=a.split(":");return{reason:n,category:r,quantity:t[a]}})}};function Vy(e){let t="`beforeSend` method has to return `null` or a valid event.";if(xn(e))return e.then(a=>{if(!(It(a)||a===null))throw new _e(t);return a},a=>{throw new _e(`beforeSend rejected with ${a}``Sentry responded with status code ${d.statusCode} to sent event.``${a}`,`${t}: ${a}`]}catch{return!1&&
eateElement("script");n.src=t,n.id="imt-browser-bridge-inject",n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}}function Gv(e){_i.handleMessages(({id:t,type:a})=>{if(a==="getConfig"){let n=e.rule.subtitleRule;_i.sendMessages({id:t,data:n})}})}Nv();var Fn=null;async function Hv(e,t){let a=Object.keys(t);if(Fn){let n={url:e,config:Fn.config,state:{...Fn.state,..
u.map((p,x)=>`${x+1}. ${p}`).join("<br/>")}``Exceeded max retry count (${a})``${a.split("base64_""",r="";return a&&a.length===3&&(n=a[1],r=a[2]),{mimeType:n,base64:r}}var f0=Object.prototype.toString;function fi(e){switch(f0.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ot(e,Error)}}function sr(e,t){return f0.call(e)===`[object ${t}]``[${d[0]}="${d
event ${sa(e)}`),null}}function Ks(e,t){let a=Ws(e,t),n={type:t&&t.name,value:l3(t)};return a.length&&(n.stacktrace={frames:a}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function o3(e,t,a,n){let r={exception:{values:[{type:lr(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:`Non-Error ${n?"promise rejection":"exception"} captured with keys: ${C0(t)}``${s}: ${o.message}
n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=
ssage",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventL
ordDroppedEvent("event_processor",t.type||"error"),new _e("An event processor returned null, will not send event.");if(a.data&&a.data.__sentry__===!0||o||!r)return s;let c=r(s,a);return Vy(c)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new _e("`beforeSend` returned `null`, will not send event.""Error while sending event:",a)}):!1&&te.error("Transport disabled"
n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}return Fn}function Kv(){return pe()?aa():Wv({method:"getConfig",data:{userAgent:globalThis.navigator.userAgent}})}async function Wv(e){return await iu().sendMessage("background:main",e)}})();


r(`script[src='${t}']`))return;let n=document.createElement("script");n.src=t,n.id="imt-browser-bridge-inject",n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}}function Gv(e){_i.handleMessages(({id:t,type:a})=>{if(a==="getConfig"){let n=e.rule.subtitleRule;_i.sendMessages({id:t,data:n})}})}Nv();var Fn=null;async function Hv(e,t){let a=Object.keys(t);if(Fn){let n={url:e
ion.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")||(e=`${globalThis.location.protocol}//${r}``\\x1B[${r.join(";")}m`,close:`\\x1B[${e}m`,regexp:new RegExp(`\\\\x1b\\\\[${e}m`,"g")}}function w(r,e){return ie?`${e.open}${r.replace(e.regexp,e.open)}${e.close}``https://config.${p}/`,rt=`https://app.${p}/`,u=S()||N()?`https://${p}/`:`https://test.${p}/`,G=`https://dash.${p}/`,ot=S()||N()?`http
=s({...a},n);!1&&s.id&&u===null&&te.log(``${e.protocol}:`:"",a=e.port?`:${e.port}`:"";return`${t}//${e.host}${a}${e.path?`/${e.path}`:""}/api/`}function Hy(e){return`${Gy(e)}${e.projectId}/envelope/`}function Ky(e,t){return D0({sentry_key:e.publicKey,sentry_version:qy,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${a.name}`))}),t}var mm="Not capturing exception because it''s not includ
ent:",a)}):!1&&te.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(a=>{let[n,r]=a.split(":");return{reason:n,category:r,quantity:t[a]}})}};function Vy(e){let t="`beforeSend` method has to return `null` or a valid event.";if(xn(e))return e.then(a=>{if(!(It(a)||a===null))throw new _e(t);return a},a=>{throw new _e(`beforeSend rejected with ${a}``Sentr
er("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`$
e{let n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}return Fn}function Kv(){return pe()?aa():Wv({method:"getConfig",data:{userAgent:globalThis.navigator.userAgent}})}async function Wv(e){return await iu().sendMessage("background:main",e)}})();


(()=>{var Mp=Object.defineProperty;var Iu=(e,t)=>{for(var a in t)Mp(e,a,{get:t[a],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s
prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var hy=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function fy(e){return e==="http"||e==="https"}function wn(e,t=!1){let{host:a,path:n,pass:r,port:i,projectId:o,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&r?`:${r}`:""}@${a}${i?`:${i}`:""}/${n&&`${n}/`}${o}`}function by(e){let t=hy.exec(e);if(!t)throw n
for(var a in t)Mp(e,a,{get:t[a],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{
foreSend` returned `null`, will not send event.""Error while sending event:",a)}):!1&&te.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(a=>{let[n,r]=a.split(":");return{reason:n,category:r,quantity:t[a]}})}};function Vy(e){let t="`beforeSend` method has to return `null` or a valid event.";if(xn(e))return e.then(a=>{if(!(It(a)||a===null))
e){return e==="http"||e==="https"}function wn(e,t=!1){let{host:a,path:n,pass:r,port:i,projectId:o,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&r?`:${r}`:""}@${a}${i?`:${i}`:""}/${n&&`${n}/`}${o}`}function by(e){let t=hy.exec(e);if(!t)throw new _e(`Invalid Sentry Dsn: ${e}``Invalid Sentry Dsn: ${String(i)} missing`)}),!a.match(/^\d+$/))throw new _e(`Invalid Sentry Dsn: Invalid projectId ${a}`);if(!fy(n))t
[u]}`);let l=["type","name","title","alt"];for(u=0;u<l.length;u++)o=l[u],s=a.getAttribute(o),s&&n.push(`[${o}="${s}"]`);return n.join("")}function A0(){try{return location.href}catch{return""}}var _e=class extends Error{constructor(a){super(a);this.message=a;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var hy=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]
>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","cont
(()=>{var Mp=Object.defineProperty;var Iu=(e,t)=>{for(var a in t)Mp(e,a,{get:t[a],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(
erfaceLanguage);if(this.status!==-999)return;let n=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${a("mangaQuotaError.solvedTitle")}<br/></br>
        ${r.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${n("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((p,x)=>`${x+1}. ${p}`).join("<br/>")}``Exceeded max retry count (${a})``${a.split("base64_""",r=""
n globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;retur
 [dir="ltr"] > span``
``
``))return;let n=document.createElement("script");n.src=t,n.setAttribute("async","true"),n.id="imt-subtitles-inject",document.head?.insertBefore(n,document.head?.firstChild)}}function qv(e){if(!(!Xt()&&!Zt()))if(pe()){vu();return}else{let t=ie.runtime.getURL("browser-bridge/inject.js");if(document.querySelector(`script[src='${t}']`))return;let n=document.createElement("script");n.src
sent event.``${a}`,`${t}: ${a}`]}catch{return!1&&te.error(`Cannot extract message for event ${sa(e)}`),[]}return[]}function n3(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function r3(e=[]){for(let t=e.length-1;t>=0;t--){let a=e[t];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}function Si(e){try{let t;try{t=e.exception.values[0]
Listener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.app
){let a=Object.keys(t);if(Fn){let n={url:e,config:Fn.config,state:{...Fn.state,...t}};Fn=await mu(n)}else{let n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}return Fn}function Kv(){return pe()?aa():Wv({method:"getConfig",data:{userAgent:globalThis.navigator.userAgent}})}async function Wv(e){return await iu().sendMessage("background:main",e)}})();


d?.insertBefore(n,document.head?.firstChild)}}function Gv(e){_i.handleMessages(({id:t,type:a})=>{if(a==="getConfig"){let n=e.rule.subtitleRule;_i.sendMessages({id:t,data:n})}})}Nv();var Fn=null;async function Hv(e,t){let a=Object.keys(t);if(Fn){let n={url:e,config:Fn.config,state:{...Fn.state,...t}};Fn=await mu(n)}else{let n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}return
.name}/${t.version}``Integration installed: ${a.name}`))}),t}var mm="Not capturing exception because it''s not included in the random sample (sampling rate = ${i})`))):this._prepareEvent(t,a,n).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new _e("An event processor returned null, will not send event.");if(a.data&&a.data.__sentry__===!0||o||!r)return s;let c=r
{mimeType:n,base64:r}}var f0=Object.prototype.toString;function fi(e){switch(f0.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ot(e,Error)}}function sr(e,t){return f0.call(e)===`[object ${t}]``[${d[0]}="${d[1]}"]`)});else if(a.id&&n.push(`#${a.id}`),r=a.className,r&&ra(r))for(i=r.split(/\s+/),u=0;u<i.length;u++)n.push(`.${i[u]}`);let l=["type","name
,r&&ra(r))for(i=r.split(/\s+/),u=0;u<i.length;u++)n.push(`.${i[u]}`);let l=["type","name","title","alt"];for(u=0;u<l.length;u++)o=l[u],s=a.getAttribute(o),s&&n.push(`[${o}="${s}"]`);return n.join("")}function A0(){try{return location.href}catch{return""}}var _e=class extends Error{constructor(a){super(a);this.message=a;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prot
`beforeSend rejected with ${a}``Sentry responded with status code ${d.statusCode} to sent event.``${a}`,`${t}: ${a}`]}catch{return!1&&te.error(`Cannot extract message for event ${sa(e)}`),[]}return[]}function n3(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function r3(e=[]){for(let t=e.length-1;t>=0;t--){let a=e[t];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")ret
ode":`${o.code}`}),i}return fi(t)?Gs(e,t):It(t)||lr(t)?(i=o3(e,t,a,r),va(i,{synthetic:!0}),i):(i=Hs(e,t,a,n),gr(i,`${t}``ui.${a.name}`,message:n},{event:a.event,name:a.name,global:a.global})}return t}function d3(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:N0(e.level),message:ws(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${ws(e.args.slice(
erviceName}] ``
        ${a("mangaQuotaError.solvedTitle")}<br/></br>
        ${r.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${n("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((p,x)=>`${x+1}. ${p}`).join("<br/>")}``Exceeded max retry count (${a})``${a.split("base64_""",r="";return a&&a.length===3&&(n=a[1],r=a[2]),{mimeType:n,base64:r}}var f0=Object.prototype.toString;function fi(e
rn{reason:n,category:r,quantity:t[a]}})}};function Vy(e){let t="`beforeSend` method has to return `null` or a valid event.";if(xn(e))return e.then(a=>{if(!(It(a)||a===null))throw new _e(t);return a},a=>{throw new _e(`beforeSend rejected with ${a}``Sentry responded with status code ${d.statusCode} to sent event.``${a}`,`${t}: ${a}`]}catch{return!1&&te.error(`Cannot extract message for event ${sa(e)}`
in t)Mp(e,a,{get:t[a],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.da
nt.head?.insertBefore(n,document.head?.firstChild)}}function Gv(e){_i.handleMessages(({id:t,type:a})=>{if(a==="getConfig"){let n=e.rule.subtitleRule;_i.sendMessages({id:t,data:n})}})}Nv();var Fn=null;async function Hv(e,t){let a=Object.keys(t);if(Fn){let n={url:e,config:Fn.config,state:{...Fn.state,...t}};Fn=await mu(n)}else{let n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}ret
ent-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`${globalThis.location.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")