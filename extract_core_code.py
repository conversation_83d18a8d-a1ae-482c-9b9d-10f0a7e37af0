#!/usr/bin/env python3
"""
Chrome Extension Code Extractor
提取Chrome扩展JS文件中的核心业务代码，去除重复的依赖和公共代码
"""

import re
import os
import json
from typing import Dict, List, Tuple, Set

class ChromeExtensionCodeExtractor:
    def __init__(self):
        # 定义需要移除的重复内容模式
        self.common_patterns = [
            # CSS 内容
            r'IMMERSIVE_TRANSLATE_[A-Z_]+_CSS:`[^`]*`',
            r'IMMERSIVE_TRANSLATE_[A-Z_]+:`[^`]*`',

            # 大型内联资源
            r'IMMERSIVE_TRANSLATE_[A-Z_]+_INJECT:`[^`]*`',

            # 语言包内容 (大段的翻译文本)
            r'"[^"]*[\u4e00-\u9fff][^"]*":\s*"[^"]*"',  # 中文翻译
            r'"[a-zA-Z.]+"\s*:\s*"[^"]*[\u4e00-\u9fff][^"]*"',  # 包含中文的翻译

            # 大型配置对象
            r'translationServices\s*:\s*\{[^}]*\}',

            # 许可证信息
            r'/\*![\s\S]*?\*/',

            # 大型常量定义
            r'userscript_domains\s*:\s*\'[^\']*\'',

            # 大型语言映射对象
            r'Gl\s*=\s*\{[^}]*\}',
            r'Hl\s*=\s*\{[^}]*\}',
            r'Kl\s*=\s*\{[^}]*\}',

            # 大型URL常量
            r'[A-Z_]+_URL\s*:\s*"[^"]*"',

            # 大型域名列表
            r'\["google\.com"[^\]]*\]',

            # 大型内联脚本
            r'IMMERSIVE_TRANSLATE_VIDEO_SUBTITLE_INJECT\s*:\s*\'[^\']*\'',
        ]
        
        # 定义文件特定的业务代码标识
        self.file_specific_patterns = {
            'background.js': [
                r'chrome\.runtime\.',
                r'chrome\.tabs\.',
                r'chrome\.storage\.',
                r'chrome\.webRequest\.',
                r'background',
                r'service_worker',
                r'onInstalled',
                r'onMessage',
            ],
            'content_script.js': [
                r'document\.',
                r'window\.',
                r'DOM',
                r'content',
                r'inject',
                r'translate',
                r'mutation',
                r'observer',
            ],
            'content_start.js': [
                r'document\.',
                r'DOMContentLoaded',
                r'readyState',
                r'early',
                r'start',
            ]
        }
    
    def identify_code_blocks(self, content: str) -> List[Tuple[str, int, int]]:
        """识别代码中的不同块"""
        blocks = []
        
        # 查找大型内联资源块
        css_pattern = r'(IMMERSIVE_TRANSLATE_[A-Z_]+_CSS:`)(.*?)(`[,}])'
        for match in re.finditer(css_pattern, content, re.DOTALL):
            blocks.append(('css', match.start(), match.end()))
        
        # 查找其他大型内联资源
        inject_pattern = r'(IMMERSIVE_TRANSLATE_[A-Z_]+_INJECT:`)(.*?)(`[,}])'
        for match in re.finditer(inject_pattern, content, re.DOTALL):
            blocks.append(('inject', match.start(), match.end()))
        
        # 查找语言包
        lang_pattern = r'(\{[^}]*"[^"]*[\u4e00-\u9fff][^"]*"[^}]*\})'
        for match in re.finditer(lang_pattern, content, re.DOTALL):
            if len(match.group(1)) > 1000:  # 只处理大型语言包
                blocks.append(('lang', match.start(), match.end()))
        
        return blocks
    
    def extract_business_logic(self, content: str, filename: str) -> str:
        """提取业务逻辑代码"""
        # 首先移除大型内联资源
        blocks = self.identify_code_blocks(content)

        # 按位置倒序排列，从后往前删除，避免位置偏移
        blocks.sort(key=lambda x: x[1], reverse=True)

        for block_type, start, end in blocks:
            if block_type in ['css', 'inject']:
                # 保留变量名，但清空内容
                before = content[:start]
                after = content[end:]
                # 找到变量名
                var_match = re.search(r'(IMMERSIVE_TRANSLATE_[A-Z_]+):`', content[start:end])
                if var_match:
                    var_name = var_match.group(1)
                    replacement = f'{var_name}:""'  # 保留变量但清空内容
                    content = before + replacement + after
            elif block_type == 'lang' and len(content[start:end]) > 5000:
                # 大型语言包替换为空对象
                content = content[:start] + '{}' + content[end:]

        # 移除大型语言映射对象 (这些通常很大)
        content = re.sub(r'var\s+[A-Z][a-z]*\s*=\s*\{[^}]*"[\u4e00-\u9fff][^}]*\}[,;]?', 'var REMOVED_LANG_MAP={};', content, flags=re.DOTALL)

        # 移除大型常量对象
        content = re.sub(r'var\s+[a-z]+\s*=\s*\{[^}]{1000,}\}[,;]?', 'var REMOVED_CONSTANTS={};', content, flags=re.DOTALL)

        # 移除大型数组定义
        content = re.sub(r'var\s+[A-Z][a-z]*\s*=\s*\[[^\]]{1000,}\][,;]?', 'var REMOVED_ARRAY=[];', content, flags=re.DOTALL)

        # 移除其他重复内容
        for pattern in self.common_patterns:
            content = re.sub(pattern, '', content, flags=re.DOTALL)

        # 移除大型字符串常量 (超过500字符的字符串)
        content = re.sub(r'"[^"]{500,}"', '""', content)
        content = re.sub(r"'[^']{500,}'", "''", content)
        content = re.sub(r'`[^`]{500,}`', '``', content)

        # 清理多余的空行和空格
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r'{\s*,', '{', content)
        content = re.sub(r',\s*}', '}', content)

        return content
    
    def analyze_file_structure(self, content: str) -> Dict:
        """分析文件结构"""
        analysis = {
            'total_size': len(content),
            'has_css': bool(re.search(r'IMMERSIVE_TRANSLATE_.*_CSS:', content)),
            'has_lang_pack': bool(re.search(r'"[^"]*[\u4e00-\u9fff][^"]*":', content)),
            'has_inject_code': bool(re.search(r'IMMERSIVE_TRANSLATE_.*_INJECT:', content)),
            'function_count': len(re.findall(r'function\s+\w+', content)),
            'class_count': len(re.findall(r'class\s+\w+', content)),
        }
        
        # 估算各部分大小
        css_matches = re.findall(r'IMMERSIVE_TRANSLATE_[A-Z_]+_CSS:`[^`]*`', content, re.DOTALL)
        analysis['css_size'] = sum(len(match) for match in css_matches)
        
        inject_matches = re.findall(r'IMMERSIVE_TRANSLATE_[A-Z_]+_INJECT:`[^`]*`', content, re.DOTALL)
        analysis['inject_size'] = sum(len(match) for match in inject_matches)
        
        return analysis
    
    def process_file(self, filepath: str, output_dir: str = "extracted") -> Dict:
        """处理单个文件"""
        filename = os.path.basename(filepath)
        
        with open(filepath, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 分析原始文件
        original_analysis = self.analyze_file_structure(original_content)
        
        # 提取业务代码
        extracted_content = self.extract_business_logic(original_content, filename)
        
        # 分析提取后的文件
        extracted_analysis = self.analyze_file_structure(extracted_content)
        
        # 保存提取的代码
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"extracted_{filename}")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(extracted_content)
        
        # 计算压缩比例
        compression_ratio = (1 - len(extracted_content) / len(original_content)) * 100
        
        result = {
            'filename': filename,
            'original_size': len(original_content),
            'extracted_size': len(extracted_content),
            'compression_ratio': compression_ratio,
            'original_analysis': original_analysis,
            'extracted_analysis': extracted_analysis,
            'output_path': output_path
        }
        
        return result

    def extract_file_specific_logic(self, content: str, filename: str) -> str:
        """根据文件类型提取特定的业务逻辑"""

        if filename == 'background.js':
            # 保留 Chrome API 调用和事件监听器
            patterns_to_keep = [
                r'chrome\.[a-zA-Z]+\.[a-zA-Z]+',
                r'onInstalled',
                r'onMessage',
                r'onStartup',
                r'onCommand',
                r'contextMenus',
                r'tabs\.',
                r'storage\.',
                r'runtime\.',
            ]

        elif filename == 'content_script.js':
            # 保留 DOM 操作和页面交互
            patterns_to_keep = [
                r'document\.',
                r'window\.',
                r'addEventListener',
                r'querySelector',
                r'createElement',
                r'appendChild',
                r'innerHTML',
                r'textContent',
                r'MutationObserver',
                r'translate',
            ]

        elif filename == 'popup.js' or filename == 'options.js':
            # 保留 UI 相关代码
            patterns_to_keep = [
                r'document\.',
                r'getElementById',
                r'addEventListener',
                r'click',
                r'change',
                r'input',
                r'form',
                r'button',
                r'select',
            ]

        else:
            # 默认保留基本的函数和类定义
            patterns_to_keep = [
                r'function\s+\w+',
                r'class\s+\w+',
                r'async\s+function',
                r'=>',
            ]

        # 提取匹配的代码段
        extracted_parts = []
        for pattern in patterns_to_keep:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                # 提取匹配行及其上下文
                start = max(0, match.start() - 200)
                end = min(len(content), match.end() + 200)
                extracted_parts.append(content[start:end])

        # 合并提取的部分，去重
        if extracted_parts:
            return '\n'.join(set(extracted_parts))
        else:
            # 如果没有匹配到特定模式，返回原始提取结果
            return content

def main():
    extractor = ChromeExtensionCodeExtractor()

    # 要处理的文件列表
    js_files = [
        'background.js',
        'content_script.js',
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]

    results = []

    print("Chrome Extension Code Extractor v2.0")
    print("=" * 50)

    for filename in js_files:
        if os.path.exists(filename):
            print(f"\n处理文件: {filename}")
            try:
                result = extractor.process_file(filename)
                results.append(result)

                print(f"  原始大小: {result['original_size']:,} bytes ({result['original_size']/1024:.1f} KB)")
                print(f"  提取后大小: {result['extracted_size']:,} bytes ({result['extracted_size']/1024:.1f} KB)")
                print(f"  压缩比例: {result['compression_ratio']:.1f}%")
                print(f"  输出文件: {result['output_path']}")

                # 创建进一步精简的版本
                with open(result['output_path'], 'r', encoding='utf-8') as f:
                    extracted_content = f.read()

                specific_content = extractor.extract_file_specific_logic(extracted_content, filename)

                # 保存特定业务逻辑版本
                specific_path = result['output_path'].replace('extracted_', 'business_logic_')
                with open(specific_path, 'w', encoding='utf-8') as f:
                    f.write(specific_content)

                specific_size = len(specific_content)
                specific_ratio = (1 - specific_size / result['original_size']) * 100

                print(f"  业务逻辑大小: {specific_size:,} bytes ({specific_size/1024:.1f} KB)")
                print(f"  业务逻辑压缩比: {specific_ratio:.1f}%")
                print(f"  业务逻辑文件: {specific_path}")

            except Exception as e:
                print(f"  错误: {e}")
        else:
            print(f"\n文件不存在: {filename}")

    # 保存分析报告
    with open('extraction_report.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"\n处理完成! 分析报告已保存到: extraction_report.json")
    print(f"提取的文件保存在: extracted/ 目录")
    print(f"业务逻辑文件保存在: business_logic_* 文件")

if __name__ == "__main__":
    main()
