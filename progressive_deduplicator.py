#!/usr/bin/env python3
"""
渐进式去重工具
基于用户的两个关键洞察：
1. 两个文件间的重复也应该移除（库依赖重复）
2. 重复块之前的代码很可能也是重复打包的代码
"""

import os
import json
from typing import List, Dict, Set, Tuple
from collections import defaultdict

class ProgressiveDeduplicator:
    def __init__(self):
        self.files_content = {}
        self.strategies = []
        self.results_history = []
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_duplicate_blocks(self, min_size: int = 2000, min_files: int = 2) -> Dict:
        """查找重复块（支持2个或更多文件间的重复）"""
        print(f"\n策略1: 查找在至少{min_files}个文件中重复的大块代码 (>={min_size}字符)")
        
        file_contents = list(self.files_content.values())
        file_names = [os.path.basename(path) for path in self.files_content.keys()]
        
        # 存储重复块信息
        duplicate_blocks = []
        
        # 使用第一个文件作为基准
        base_content = file_contents[0]
        base_name = file_names[0]
        
        step = 500  # 步长
        
        for start in range(0, len(base_content) - min_size + 1, step):
            if start % 50000 == 0:
                print(f"  扫描进度: {start/len(base_content)*100:.1f}%")
            
            for size in [10000, 5000, 3000, min_size]:
                if start + size > len(base_content):
                    continue
                
                block = base_content[start:start + size]
                
                # 跳过主要是空白的块
                if len(block.strip()) < size * 0.3:
                    continue
                
                # 检查在多少个其他文件中存在
                found_in_files = [base_name]
                for i, other_content in enumerate(file_contents[1:], 1):
                    if block in other_content:
                        found_in_files.append(file_names[i])
                
                # 如果在足够多的文件中找到
                if len(found_in_files) >= min_files:
                    # 检查是否已经被更大的块包含
                    is_contained = any(
                        block in existing['block'] and len(block) <= len(existing['block'])
                        for existing in duplicate_blocks
                    )
                    
                    if not is_contained:
                        # 移除被当前块包含的较小块
                        duplicate_blocks = [
                            existing for existing in duplicate_blocks
                            if existing['block'] not in block
                        ]
                        
                        duplicate_blocks.append({
                            'block': block,
                            'size': size,
                            'files': found_in_files,
                            'start_pos': start
                        })
                        
                        print(f"  找到重复块: {len(found_in_files)}个文件, {size}字符")
                        break  # 找到最大的块后跳出
        
        # 按大小排序
        duplicate_blocks.sort(key=lambda x: x['size'], reverse=True)
        
        print(f"  总共找到 {len(duplicate_blocks)} 个重复块")
        return {'duplicate_blocks': duplicate_blocks}
    
    def find_prefix_dependencies(self, duplicate_blocks: List[Dict]) -> Dict:
        """策略2: 查找重复块之前的依赖代码"""
        print(f"\n策略2: 查找重复块之前的可能依赖代码")
        
        prefix_blocks = []
        
        for dup_block in duplicate_blocks[:5]:  # 只处理前5个最大的重复块
            block = dup_block['block']
            files_with_block = dup_block['files']
            
            print(f"  分析重复块 (大小: {dup_block['size']}, 文件: {len(files_with_block)}个)")
            
            # 在每个包含此重复块的文件中查找其位置
            block_positions = {}
            for file_path, content in self.files_content.items():
                filename = os.path.basename(file_path)
                if filename in files_with_block:
                    pos = content.find(block)
                    if pos != -1:
                        block_positions[filename] = pos
            
            if len(block_positions) >= 2:
                # 找到最小的位置作为参考
                min_pos = min(block_positions.values())
                
                # 如果重复块不在文件开头，检查之前的内容
                if min_pos > 1000:  # 至少有1000字符的前缀
                    # 检查是否有公共的前缀内容
                    prefix_candidates = []
                    
                    for prefix_size in [min_pos, min_pos//2, min_pos//4, 2000, 1000]:
                        if prefix_size < 500:
                            continue
                        
                        # 获取第一个文件的前缀
                        first_file = None
                        first_content = None
                        for file_path, content in self.files_content.items():
                            filename = os.path.basename(file_path)
                            if filename in files_with_block:
                                first_file = filename
                                first_content = content
                                break
                        
                        if first_content:
                            pos = first_content.find(block)
                            if pos >= prefix_size:
                                prefix = first_content[pos-prefix_size:pos]
                                
                                # 检查这个前缀在其他文件中是否也存在
                                prefix_files = [first_file]
                                for file_path, content in self.files_content.items():
                                    filename = os.path.basename(file_path)
                                    if filename != first_file and filename in files_with_block:
                                        if prefix in content:
                                            prefix_files.append(filename)
                                
                                if len(prefix_files) >= 2:
                                    prefix_candidates.append({
                                        'prefix': prefix,
                                        'size': prefix_size,
                                        'files': prefix_files,
                                        'related_block_size': dup_block['size']
                                    })
                                    print(f"    找到前缀依赖: {len(prefix_files)}个文件, {prefix_size}字符")
                                    break
                    
                    prefix_blocks.extend(prefix_candidates)
        
        print(f"  总共找到 {len(prefix_blocks)} 个前缀依赖块")
        return {'prefix_blocks': prefix_blocks}
    
    def apply_removals(self, duplicate_blocks: List[Dict], prefix_blocks: List[Dict], 
                      output_dir: str = "progressive_dedup") -> Dict:
        """应用移除策略"""
        print(f"\n应用移除策略...")
        
        os.makedirs(output_dir, exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            removal_log = []
            
            # 策略1: 移除重复块
            for i, dup_block in enumerate(duplicate_blocks):
                if filename in dup_block['files']:
                    block = dup_block['block']
                    if block in cleaned_content:
                        old_size = len(cleaned_content)
                        cleaned_content = cleaned_content.replace(block, f'/* REMOVED_DUPLICATE_BLOCK_{i} */')
                        removed = old_size - len(cleaned_content)
                        total_removed += removed
                        removal_log.append(f"重复块{i}: {removed:,}字符")
            
            # 策略2: 移除前缀依赖块
            for i, prefix_block in enumerate(prefix_blocks):
                if filename in prefix_block['files']:
                    prefix = prefix_block['prefix']
                    if prefix in cleaned_content:
                        old_size = len(cleaned_content)
                        cleaned_content = cleaned_content.replace(prefix, f'/* REMOVED_PREFIX_DEPENDENCY_{i} */')
                        removed = old_size - len(cleaned_content)
                        total_removed += removed
                        removal_log.append(f"前缀依赖{i}: {removed:,}字符")
            
            # 保存文件
            output_path = os.path.join(output_dir, f"prog_{filename}")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计结果
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'removal_log': removal_log,
                'output_path': output_path
            }
            
            print(f"  原始: {original_size:,} 字符 ({original_size/1024:.1f} KB)")
            print(f"  清理后: {cleaned_size:,} 字符 ({cleaned_size/1024:.1f} KB)")
            print(f"  压缩比例: {compression_ratio:.1f}%")
            for log in removal_log:
                print(f"    - {log}")
        
        return results
    
    def run_progressive_deduplication(self, output_dir: str = "progressive_dedup") -> Dict:
        """运行渐进式去重"""
        print("=" * 60)
        print("渐进式去重开始")
        print("=" * 60)
        
        # 策略1: 查找重复块（包括2个文件间的重复）
        strategy1_result = self.find_duplicate_blocks(min_size=2000, min_files=2)
        duplicate_blocks = strategy1_result['duplicate_blocks']
        
        # 策略2: 查找前缀依赖
        strategy2_result = self.find_prefix_dependencies(duplicate_blocks)
        prefix_blocks = strategy2_result['prefix_blocks']
        
        # 应用移除
        results = self.apply_removals(duplicate_blocks, prefix_blocks, output_dir)
        
        # 保存详细结果
        detailed_results = {
            'strategy1': strategy1_result,
            'strategy2': strategy2_result,
            'file_results': results
        }
        
        with open(f'{output_dir}_detailed_report.json', 'w', encoding='utf-8') as f:
            # 移除不能序列化的大块内容
            serializable_results = detailed_results.copy()
            for block in serializable_results['strategy1']['duplicate_blocks']:
                block['block'] = f"[BLOCK_{len(block['block'])}_CHARS]"
            for block in serializable_results['strategy2']['prefix_blocks']:
                block['prefix'] = f"[PREFIX_{len(block['prefix'])}_CHARS]"
            
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        return results

def main():
    deduplicator = ProgressiveDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("渐进式去重工具")
    print("基于两个关键洞察：")
    print("1. 两个文件间的重复也应该移除（库依赖重复）")
    print("2. 重复块之前的代码很可能也是重复打包的代码")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("需要至少2个文件进行比较")
        return
    
    # 运行渐进式去重
    results = deduplicator.run_progressive_deduplication()
    
    # 打印总结
    print("\n" + "=" * 60)
    print("渐进式去重完成！总结:")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"总原始大小: {total_original:,} 字符 ({total_original/1024:.1f} KB)")
    print(f"总清理后大小: {total_cleaned:,} 字符 ({total_cleaned/1024:.1f} KB)")
    print(f"总体压缩比例: {overall_compression:.1f}%")
    print(f"\n清理后的文件保存在: progressive_dedup/ 目录")
    print(f"详细报告保存在: progressive_dedup_detailed_report.json")
    
    # 显示每个文件的详细移除信息
    print(f"\n各文件详细移除信息:")
    for filename, result in results.items():
        print(f"\n{filename}:")
        for log in result['removal_log']:
            print(f"  - {log}")

if __name__ == "__main__":
    main()
