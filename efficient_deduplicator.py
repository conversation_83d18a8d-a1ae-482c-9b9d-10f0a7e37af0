#!/usr/bin/env python3
"""
高效渐进式去重工具
优化算法，减少运行时间
基于用户的两个关键洞察
"""

import os
import json
from typing import List, Dict, Set, Tuple

class EfficientDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"✓ 加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def quick_find_duplicates(self, min_size: int = 3000) -> List[Dict]:
        """快速查找重复块 - 优化算法"""
        print(f"\n🔍 快速查找重复块 (>={min_size}字符)...")
        
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        duplicates = []
        
        # 只检查几个关键大小，减少计算量
        sizes_to_check = [10000, 5000, 3000]
        
        for size in sizes_to_check:
            print(f"  检查大小: {size}")
            found_count = 0
            
            # 使用更大的步长，减少检查次数
            step = max(1000, size // 5)
            
            base_path, base_content = files[0]
            base_name = file_names[0]
            
            for start in range(0, len(base_content) - size + 1, step):
                block = base_content[start:start + size]
                
                # 快速过滤：跳过主要是空白或重复字符的块
                if len(set(block[:100])) < 10:  # 前100字符中不同字符太少
                    continue
                
                if len(block.strip()) < size * 0.4:  # 空白字符太多
                    continue
                
                # 检查在其他文件中的存在
                found_in = [base_name]
                for i, (_, other_content) in enumerate(files[1:], 1):
                    if block in other_content:
                        found_in.append(file_names[i])
                
                # 如果在至少2个文件中找到
                if len(found_in) >= 2:
                    duplicates.append({
                        'block': block,
                        'size': size,
                        'files': found_in,
                        'start_pos': start
                    })
                    found_count += 1
                    print(f"    ✓ 找到: {len(found_in)}个文件, 位置{start}")
                    
                    # 限制每个大小级别的查找数量
                    if found_count >= 3:
                        break
        
        # 去重：移除被更大块包含的小块
        filtered_duplicates = []
        for dup in sorted(duplicates, key=lambda x: x['size'], reverse=True):
            is_contained = any(
                dup['block'] in existing['block'] and dup['size'] < existing['size']
                for existing in filtered_duplicates
            )
            if not is_contained:
                filtered_duplicates.append(dup)
        
        print(f"  📊 总共找到 {len(filtered_duplicates)} 个有效重复块")
        return filtered_duplicates
    
    def find_prefix_patterns(self, duplicates: List[Dict]) -> List[Dict]:
        """查找前缀模式 - 简化版本"""
        print(f"\n🔍 查找前缀依赖模式...")
        
        prefix_blocks = []
        
        for i, dup in enumerate(duplicates[:3]):  # 只处理前3个最大的
            print(f"  分析重复块 {i+1}: {dup['size']}字符, {len(dup['files'])}个文件")
            
            # 在第一个文件中找到这个块的位置
            first_file_path = None
            for path, content in self.files_content.items():
                if os.path.basename(path) in dup['files']:
                    first_file_path = path
                    break
            
            if first_file_path:
                content = self.files_content[first_file_path]
                block_pos = content.find(dup['block'])
                
                if block_pos > 2000:  # 如果前面有足够的内容
                    # 检查几个固定大小的前缀
                    for prefix_size in [2000, 1000]:
                        if block_pos >= prefix_size:
                            prefix = content[block_pos - prefix_size:block_pos]
                            
                            # 检查这个前缀在其他相关文件中是否存在
                            prefix_files = []
                            for path, file_content in self.files_content.items():
                                filename = os.path.basename(path)
                                if filename in dup['files'] and prefix in file_content:
                                    prefix_files.append(filename)
                            
                            if len(prefix_files) >= 2:
                                prefix_blocks.append({
                                    'prefix': prefix,
                                    'size': prefix_size,
                                    'files': prefix_files,
                                    'related_block': i
                                })
                                print(f"    ✓ 找到前缀: {prefix_size}字符, {len(prefix_files)}个文件")
                                break
        
        print(f"  📊 总共找到 {len(prefix_blocks)} 个前缀依赖")
        return prefix_blocks
    
    def apply_removals_safe(self, duplicates: List[Dict], prefixes: List[Dict]) -> Dict:
        """安全地应用移除"""
        print(f"\n🛠️  应用移除策略...")
        
        os.makedirs("efficient_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n  处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            operations = []
            
            # 移除重复块
            for i, dup in enumerate(duplicates):
                if filename in dup['files'] and dup['block'] in cleaned_content:
                    old_size = len(cleaned_content)
                    # 安全替换：保留结构
                    replacement = f'/* REMOVED_DUPLICATE_BLOCK_{i} ({dup["size"]} chars) */'
                    cleaned_content = cleaned_content.replace(dup['block'], replacement, 1)  # 只替换第一个
                    removed = old_size - len(cleaned_content)
                    total_removed += removed
                    operations.append(f"重复块{i}: -{removed:,}字符")
            
            # 移除前缀依赖
            for i, prefix in enumerate(prefixes):
                if filename in prefix['files'] and prefix['prefix'] in cleaned_content:
                    old_size = len(cleaned_content)
                    replacement = f'/* REMOVED_PREFIX_DEP_{i} ({prefix["size"]} chars) */'
                    cleaned_content = cleaned_content.replace(prefix['prefix'], replacement, 1)
                    removed = old_size - len(cleaned_content)
                    total_removed += removed
                    operations.append(f"前缀依赖{i}: -{removed:,}字符")
            
            # 保存文件
            output_path = f"efficient_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行高效去重"""
        print("🚀 高效渐进式去重工具")
        print("=" * 50)
        
        # 步骤1: 快速查找重复
        duplicates = self.quick_find_duplicates()
        
        # 步骤2: 查找前缀模式
        prefixes = self.find_prefix_patterns(duplicates)
        
        # 步骤3: 安全移除
        results = self.apply_removals_safe(duplicates, prefixes)
        
        return results, duplicates, prefixes

def main():
    deduplicator = EfficientDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 高效渐进式去重工具")
    print("💡 基于两个关键洞察：")
    print("   1. 两个文件间的重复也应该移除")
    print("   2. 重复块之前的代码很可能也是依赖")
    print("=" * 50)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行去重
    results, duplicates, prefixes = deduplicator.run()
    
    # 保存简化报告
    summary = {
        'duplicates_found': len(duplicates),
        'prefixes_found': len(prefixes),
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('efficient_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("✅ 去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 总体效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: efficient_dedup/clean_*.js")
    print(f"   详细报告: efficient_dedup_summary.json")
    
    print(f"\n🎯 下一步建议:")
    if overall_compression > 20:
        print(f"   ✅ 压缩效果良好，可以用于逆向分析")
    elif overall_compression > 10:
        print(f"   ⚠️  压缩效果一般，可以考虑添加更多策略")
    else:
        print(f"   ❌ 压缩效果有限，可能需要调整参数或策略")

if __name__ == "__main__":
    main()
