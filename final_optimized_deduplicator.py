#!/usr/bin/env python3
"""
最终优化的去重工具
基于前面的成功经验，进一步优化策略
"""

import os
import json
from typing import List, Dict, Set, Tuple

class FinalOptimizedDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"✓ 加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_all_large_strings(self) -> List[Dict]:
        """查找所有大型字符串字面量"""
        print(f"\n🔍 查找所有大型字符串字面量...")
        
        all_strings = []
        
        for file_path, content in self.files_content.items():
            filename = os.path.basename(file_path)
            
            # 查找所有大型字符串（模板字符串、双引号、单引号）
            i = 0
            while i < len(content):
                char = content[i]
                
                if char in ['`', '"', "'"]:
                    quote = char
                    start = i + 1
                    i += 1
                    
                    # 查找字符串结束
                    while i < len(content):
                        if content[i] == quote and (i == 0 or content[i-1] != '\\'):
                            string_content = content[start:i]
                            if len(string_content) >= 1000:  # 只处理1KB以上的字符串
                                all_strings.append({
                                    'content': string_content,
                                    'size': len(string_content),
                                    'file': filename,
                                    'quote': quote,
                                    'full_string': content[start-1:i+1]  # 包含引号
                                })
                            break
                        i += 1
                else:
                    i += 1
        
        print(f"  📊 找到 {len(all_strings)} 个大型字符串")
        return all_strings
    
    def find_common_strings(self, all_strings: List[Dict]) -> List[Dict]:
        """查找公共字符串"""
        print(f"\n🔍 查找公共字符串...")
        
        # 按内容分组
        content_groups = {}
        for string_info in all_strings:
            content = string_info['content']
            if content not in content_groups:
                content_groups[content] = []
            content_groups[content].append(string_info)
        
        # 找出在多个文件中出现的字符串
        common_strings = []
        for content, string_list in content_groups.items():
            files = set(s['file'] for s in string_list)
            if len(files) >= 2:  # 在至少2个文件中出现
                common_strings.append({
                    'content': content,
                    'size': len(content),
                    'files': list(files),
                    'count': len(string_list),
                    'examples': string_list[:3]  # 保留几个例子
                })
        
        # 按大小排序
        common_strings.sort(key=lambda x: x['size'], reverse=True)
        
        print(f"  📊 找到 {len(common_strings)} 个公共字符串")
        for i, cs in enumerate(common_strings[:10]):  # 显示前10个
            print(f"    {i+1}. {cs['size']:,}字符, {len(cs['files'])}个文件")
        
        return common_strings
    
    def categorize_strings(self, common_strings: List[Dict]) -> Dict[str, List[Dict]]:
        """分类字符串"""
        print(f"\n🏷️  分类字符串...")
        
        categories = {
            'css': [],
            'html': [],
            'javascript': [],
            'config': [],
            'other': []
        }
        
        for cs in common_strings:
            content = cs['content'][:500].lower()  # 检查前500字符
            
            # CSS指标
            css_indicators = [
                'background-color', 'font-family', 'position:', 'display:', 
                'color:', 'margin:', 'padding:', 'border:', '@media', 'rgba(',
                'px;', 'rem;', '--', '.immersive-translate'
            ]
            
            # HTML指标
            html_indicators = ['<div', '<span', '<button', '<input', '<form', 'class=', 'id=']
            
            # JavaScript指标
            js_indicators = ['function', 'var ', 'let ', 'const ', 'return', 'if(', 'for(']
            
            # 配置指标
            config_indicators = ['"zh-cn"', '"en"', 'https://', 'api.', '.com', 'config']
            
            css_score = sum(1 for indicator in css_indicators if indicator in content)
            html_score = sum(1 for indicator in html_indicators if indicator in content)
            js_score = sum(1 for indicator in js_indicators if indicator in content)
            config_score = sum(1 for indicator in config_indicators if indicator in content)
            
            if css_score >= 3:
                categories['css'].append(cs)
            elif html_score >= 2:
                categories['html'].append(cs)
            elif js_score >= 3:
                categories['javascript'].append(cs)
            elif config_score >= 2:
                categories['config'].append(cs)
            else:
                categories['other'].append(cs)
        
        for category, strings in categories.items():
            if strings:
                total_size = sum(s['size'] for s in strings)
                print(f"  {category}: {len(strings)} 个字符串, 总大小: {total_size:,} 字符")
        
        return categories
    
    def apply_smart_removal(self, categorized_strings: Dict[str, List[Dict]]) -> Dict:
        """智能移除策略"""
        print(f"\n🛠️  应用智能移除策略...")
        
        os.makedirs("final_dedup", exist_ok=True)
        results = {}
        
        # 移除策略：优先级从高到低
        removal_priority = ['css', 'html', 'javascript', 'config', 'other']
        removal_limits = {'css': 20, 'html': 10, 'javascript': 5, 'config': 15, 'other': 5}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n  处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            operations = []
            
            # 按优先级移除
            for category in removal_priority:
                strings = categorized_strings[category]
                if not strings:
                    continue
                
                print(f"    处理{category}字符串...")
                category_removed = 0
                processed_count = 0
                
                # 按大小排序，优先移除大的
                sorted_strings = sorted(strings, key=lambda x: x['size'], reverse=True)
                
                for cs in sorted_strings:
                    if processed_count >= removal_limits[category]:
                        break
                    
                    if filename in cs['files']:
                        # 查找这个字符串的完整形式
                        content = cs['content']
                        
                        # 尝试不同的引号形式
                        patterns = [f'`{content}`', f'"{content}"', f"'{content}'"]
                        
                        for pattern in patterns:
                            if pattern in cleaned_content:
                                old_size = len(cleaned_content)
                                # 替换为空字符串，保持引号
                                if pattern.startswith('`'):
                                    replacement = '``'
                                elif pattern.startswith('"'):
                                    replacement = '""'
                                else:
                                    replacement = "''"
                                
                                cleaned_content = cleaned_content.replace(pattern, replacement, 1)
                                removed = old_size - len(cleaned_content)
                                if removed > 0:
                                    category_removed += removed
                                    processed_count += 1
                                break
                
                if category_removed > 0:
                    total_removed += category_removed
                    operations.append(f"{category}: -{category_removed:,}字符 ({processed_count}个)")
            
            # 保存文件
            output_path = f"final_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行最终优化去重"""
        print("🚀 最终优化去重工具")
        print("=" * 50)
        
        # 步骤1: 查找所有大型字符串
        all_strings = self.find_all_large_strings()
        
        # 步骤2: 查找公共字符串
        common_strings = self.find_common_strings(all_strings)
        
        # 步骤3: 分类字符串
        categorized_strings = self.categorize_strings(common_strings)
        
        # 步骤4: 智能移除
        results = self.apply_smart_removal(categorized_strings)
        
        return results, categorized_strings

def main():
    deduplicator = FinalOptimizedDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 最终优化去重工具")
    print("💡 基于前面成功经验的最终优化")
    print("=" * 50)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行去重
    results, categorized_strings = deduplicator.run()
    
    # 保存报告
    summary = {
        'categories': {k: len(v) for k, v in categorized_strings.items()},
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('final_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("✅ 最终优化去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 最终效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: final_dedup/clean_*.js")
    print(f"   详细报告: final_dedup_summary.json")
    
    print(f"\n🎯 建议:")
    if overall_compression >= 20:
        print(f"   ✅ 压缩效果优秀！可以用于逆向分析")
    elif overall_compression >= 10:
        print(f"   ✅ 压缩效果良好，适合进一步分析")
    elif overall_compression >= 5:
        print(f"   ⚠️  压缩效果一般，但有一定帮助")
    else:
        print(f"   ❌ 压缩效果有限，可能需要其他方法")

if __name__ == "__main__":
    main()
