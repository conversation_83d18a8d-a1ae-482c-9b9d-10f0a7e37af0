(()=>{var $1=Object.defineProperty;var qr=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported'',IMMERSIVE_TRANSLATE_IMAGE_INJECT:""IMMERSIVE_TRANSLATE_IMAGE_TOOLS_CSS:""IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS:""IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS:"",,,,,};var tT=Object.create,Uu=Object.defineProperty,nT=Object.getOwnPropertyDescriptor,I0=Object.getOwnPropertyNames,rT=Object.getPrototypeOf,aT=Object.prototype.hasOwnProperty,iT=(e,t)=>function(){return t||(0,e[I0(e)[0]])((t={exports:{}}).exports,t),t.exports},oT=(e,t)=>{for(var n in t)Uu(e,n,{get:t[n],enumerable:!0})},zu=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of I0(t))!aT.call(e,i)&&i!==n&&Uu(e,i,{get:()=>t[i],enumerable:!(r=nT(t,i))||r.enumerable});return e},sT=(e,t,n)=>(zu(e,t,"default"),n&&zu(n,t,"default")),_0=(e,t,n)=>(n=e!=null?tT(rT(e)):{},zu(t||!e||!e.__esModule?Uu(n,"default",{value:e,enumerable:!0}):n,e)),P0=iT({"../esmd/npm/webextension-polyfill@0.10.0/node_modules/.pnpm/webextension-polyfill@0.10.0/node_modules/webextension-polyfill/dist/browser-polyfill.js"(e,t){(function(n,r){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],r);else if(typeof e<"u")r(t);else{var i={exports:{}};r(i),n.browser=i.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:e,function(n){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let r="The message port closed before a response was received.""api-metadata.json has not been included in browser-polyfill");class s extends WeakMap{constructor(D,I=void 0){super(I),this.createItem=D}get(D){return this.has(D)||this.set(D,this.createItem(D)),super.get(D)}}let u=P=>P&&typeof P=="object"&&typeof P.then=="function",a=(P,D)=>(...I)=>{o.runtime.lastError?P.reject(new Error(o.runtime.lastError.message)):D.singleCallbackArg||I.length<=1&&D.singleCallbackArg!==!1?P.resolve(I[0]):P.resolve(I)},d=P=>P==1?"argument":"arguments""function")if(typeof D[K]=="function")$=p(P,P[K],D[K]);else if(m(I,K)){let oe=c(K,I[K]);$=p(P,P[K],oe)}else $=$.bind(P);else if(typeof $=="object"&&$!==null&&(m(D,K)||m(I,K)))$=g($,D[K],I[K]);else if(m(I,"*"))$=g($,D[K],I["*""function"?P:function(I){let E=g(I,{},{getContent:{minArgs:0,maxArgs:0}});P(E)}),b=new s(P=>typeof P!="function"?P:function(I,E,z){let q=!1,j,K=new Promise(Pe=>{j=function(Z){q=!0,Pe(Z)}}),re;try{re=P(I,E,j)}catch(Pe){re=Promise.reject(Pe)}let $=re!==!0&&u(re);if(re!==!0&&!$&&!q)return!1;let oe=Pe=>{Pe.then(Z=>{z(Z)},Z=>{let le;Z&&(Z instanceof Error||typeof Z.message=="string")?le=Z.message:le="An unexpected error occurred""sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:y.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},B={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return l.privacy={network:{"*":B},services:{"*":B},websites:{"*":B}},g(o,S,l)};n.exports=i(chrome)}else n.exports=globalThis.browser})}}),M0={};oT(M0,{default:()=>ts});var uT=_0(P0());sT(M0,_0(P0()));var{default:C0,...lT}=uT,ts=C0!==void 0?C0:lT;globalThis.immersiveTranslateBrowserAPI=ts;var{Deno:F0}=globalThis,cT=typeof F0?.noColor=="boolean"?F0.noColor:!0,dT=!cT;function ns(e,t){return{open:`\x1B[${e.join(";")}m`,close:`\x1B[${t}m`,regexp:new RegExp(`\\x1b\\[${t}m`,"g")}}function rs(e,t){return dT?`${t.open}${e.replace(t.regexp,t.open)}${t.close}``https://config.${wt}/`,__=`https://app.${wt}/`,W=$n()||is()?`https://${wt}/`:`https://test.${wt}/`,us=`https://dash.${wt}/`,Vu=$n()||is()?`https://api2.${wt}/`:`https://test-api2.${wt}/`,P_=$n()||is()?`https://ai.${wt}/`:`https://test-ai.${wt}/`,M_=`https://assets.${mT}.cn/`,z0=W+"accounts/login?from=plugin",Wu=W+"profile/",Tt=W+"auth/pricing/",er=W+"pricing/";gn()&&(Tt=W+"accounts/safari-iap/",er=W+"accounts/safari-iap/");function U0(e){e&&(W=`https://test.${wt}/`,Vu=`https://test-api2.${wt}/`,z0=W+"accounts/login?from=plugin",Wu=W+"profile/",Tt=W+"auth/pricing/",er=W+"pricing/",gn()&&(Tt=W+"accounts/safari-iap/",er=W+"accounts/safari-iap/"))}var F_=$n()?`https://onboarding.${wt}/`:`https://test-onboarding.${wt}/`,q0=`https://github.com/${Ke}/${Ke}/`,B_=`https://s.${wt}/``${te}Share`,nP=`${te}ShowFloatBallGuide`,rP=`${te}ShowPopupModalGuide`,aP=te+"DocumentMessageTempEnableSubtitleChanged",iP=te+"DocumentMessageUpdateQuickButtonAiSubtitle",V0=`${te}ToggleMouseHoverTranslateDirectly`,oP=`${te}ReqDraft`,sP=`${te}ResDraft`,fT=`${te}Container`,bT=`${te}SpecifiedContainer`,Ku="buildinConfig",wi="localConfig";var W0="translateMangaMenuId";var yT=`${te}PageTranslatedStatus`,vT=`${te}MangaTranslatedStatus`,uP=`${te}PageUrlChanged`,lP=`${te}ReceiveCommand`,cP=te+"LastUseMouseHoverTime",dP=te+"LastUseInputTime",hn=te+"LastUseManualTranslatePageTime",xT=`${te}PopupReceiveMessage`,pP=te+"DocumentMessageEventTogglePopup""@","#"];var _T=`${Ke}-target-wrapper`,FP=`${Ke}-pdf-target-container`,BP=`${Ke}-target-inner`,RP=`${Ke}-source-wrapper`,OP=`${Ke}-target-translation-block-wrapper`,jP=`${Ke}-root-translation-theme`,LP=`${te}RootTranslationTheme`,NP=`${Ke}-target-translation-vertical-block-wrapper`,zP=`${Ke}-target-translation-pdf-block-wrapper`,UP=`${Ke}-target-translation-pre-whitespace`,qP=`${Ke}-target-translation-inline-wrapper``${Ti}download-subtitle/`,vM=`${Ti}pdf-pro/`,xM=`${Ti}text/`;var wM=W+"docs/usage/";var BT="G-BHWL0KMJB8",RT="7pr-olTJR6GKAjIW48UD0Q",R0="G-MKMD9LWFTR",O0="sitc4WmvShWYwfU0dANM3Q",j0="G-V5H2F5MJFJ",L0="UBjpGOLISEaY5LVXNj3WvQ""LdgzvqcdlDvNLdxrJVtZqxMTKaIgExlL",X0="0VmM83i2D1ICuYBf",AM=50*1e4,SM=`[${N0}-ctx-divider]`,OT=`${N0}_context_preview`;var Ju="fullLocalUserConfig";var $0="https://<EMAIL>/4506813369548800",kM=`${te}_selection_update_params`,DM=`data-${Ke}-subtitle-type`,CM=`data-${Ke}-ai-subtitle-url`,IM=`data-${Ke}-has-subtitle``dims[${n}] must be an integer, got: ${r}`);if(r<0)throw new RangeError(`dims[${n}] must be a non-negative integer, got: ${r}`);t*=r}return t},ef=(e,t)=>{switch(e.location){case"cpu":return new tn(e.type,e.data,t);case"cpu-pinned":return new tn({location:"cpu-pinned",data:e.data,type:e.type,dims:t});case"texture":return new tn({location:"texture",texture:e.texture,type:e.type,dims:t});case"gpu-buffer":return new tn({location:"gpu-buffer",gpuBuffer:e.gpuBuffer,type:e.type,dims:t});case"ml-tensor":return new tn({location:"ml-tensor",mlTensor:e.mlTensor,type:e.type,dims:t});default:throw new Error(`tensorReshape: tensor location ${e.location} is not supported`)}}}),tn,Ic=O(()=>{"use strict";ZT(),XT(),$T(),eE(),tn=class{constructor(e,t,n){Xh();let r,i;if(typeof e=="object"&&"location"in e)switch(this.dataLocation=e.location,r=e.type,i=e.dims,e.location){case"cpu-pinned":{let l=Xr.get(r);if(!l)throw new TypeError(`unsupported type "${r}" to create tensor from pinned buffer`);if(!(e.data instanceof l))throw new TypeError(`buffer should be of type ${l.name}`);this.cpuData=e.data;break}case"texture":{if(r!=="float32")throw new TypeError(`unsupported type "${r}" to create tensor from texture`);this.gpuTextureData=e.texture,this.downloader=e.download,this.disposer=e.dispose;break}case"gpu-buffer":{if(r!=="float32"&&r!=="float16"&&r!=="int32"&&r!=="int64"&&r!=="uint32"&&r!=="uint8"&&r!=="bool"&&r!=="uint4"&&r!=="int4")throw new TypeError(`unsupported type "${r}" to create tensor from gpu buffer`);this.gpuBufferData=e.gpuBuffer,this.downloader=e.download,this.disposer=e.dispose;break}case"ml-tensor":{if(r!=="float32"&&r!=="float16"&&r!=="int32"&&r!=="int64"&&r!=="uint32"&&r!=="uint64"&&r!=="int8"&&r!=="uint8"&&r!=="bool")throw new TypeError(`unsupported type "${r}" to create tensor from MLTensor`);this.mlTensorData=e.mlTensor,this.downloader=e.download,this.disposer=e.dispose;break}default:throw new Error(`Tensor constructor: unsupported location '${this.dataLocation}'`)}else{let l,s;if(typeof e=="string")if(r=e,s=n,e==="string"){if(!Array.isArray(t))throw new TypeError("A string tensor''s data must be type of uint8");else throw new TypeError(`A ${r} tensor''s dims must be a number array");i=s,this.cpuData=l,this.dataLocation="cpu"}let o=$h(i);if(this.cpuData&&o!==this.cpuData.length&&!((r==="uint4"||r==="int4""The data is not on CPU. Use `getData()` to download GPU data to CPU, or use `texture` or `gpuBuffer``cannot get data from location: ${this.dataLocation}``FUNC_${e}::${n[i].trim().split(" ")[1]}`;t&&(o+=`::${t}``'fetches' contains invalid output name: ${a}.``input '${a}' is missing in 'feeds'.``'byteOffset' is out of range [0, ${d.byteLength}).`);if(p=t.byteLength-c,typeof r=="number"){if(p=r,!Number.isSafeInteger(p))throw new RangeError("'byteLength' must be an integer.");if(p<=0||c+p>d.byteLength)throw new RangeError(`'byteLength' is out of range (0, ${d.byteLength-c}].``'fetches' contains invalid output name: ${a}.``Invalid shape: ${r} is not an integer`);if(r<0||r>2147483647)throw new TypeError(`Invalid shape: length ${r} is not allowed``cache should be type ${a.name}``GPU for rank ${e} is not yet supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = imod(dim, 2);
      return modCoord == 0 ? frag.r : frag.g;
    }

    float getChannel(vec4 frag, vec2 innerDims) {
      vec2 modCoord = mod(innerDims, 2.);
      return modCoord.x == 0. ?
        (modCoord.y == 0. ? frag.r : frag.g) :
        (modCoord.y == 0. ? frag.b : frag.a);
    }
  `}var Ra=O(()=>{"use strict";ar()});function OE(e,t,n){if(e===0)return"false";if(e===1)return`rc > ${t[0]}`;let r="";for(let i=e-2;i<e;i++)r+=`${n[i]} >= ${t[i-e+2]}`,i<e-1&&(r+="||");return r}function jE(e,t){let n=e.length;if(n===0)return"getA(), 0, 0, 0";if(n===1)return`getA(rc),
            rc + 1 >= ${e[0]} ? 0. : getA(rc + 1),
            0, 0`;let r="r, c",i="r, cp1",o="rp1, c",l="rp1, cp1",s="";if(n>2)for(let u=0;u<n-2;++u)s=s+`${t[u]},`;return`getA(${s}${r}),
          rEdge ? 0. : getA(${s}${o}),
          cEdge ? 0. : getA(${s}${i}),
          rEdge || cEdge ? 0. : getA(${s}${l})`}function LE(e,t,n,r){return e===0||e===1?"":`
    int r = ${t[e-2]};
    int c = ${t[e-1]};
    int rp1 = ${t[e-2]} + 1;
    int cp1 = ${t[e-1]} + 1;
    bool rEdge = rp1 >= ${r};
    bool cEdge = cp1 >= ${n};
    `}var pl,xm,xf,NE=O(()=>{"use strict";ct(),Oe(),ar(),Ra(),pl={name:"pack",inputNames:["A"],inputTypes:[1]},xm=(e,t)=>{let n=Re(e.session.backend.glContext.version),r=t.dims,i=r.length,o=t.dims.length,l=Ar(o),s=Fc("rc""b","r","c"],r="index";return`
    ivec3 inputCoordsFromReshapedOutCoords(int index) {
      ${t.map((i,o)=>{let l=`int ${n[o]} = ${r} / ${i}`,s=o===t.length-1?`int ${n[o+1]} = ${r} - ${n[o]} * ${i}`:`index -= ${n[o]} * ${i}`;return`${l}; ${s};`}).join("")}
      return ivec3(b, r, c);
    }
  `}function qE(e){let t=Ae.computeStrides(e);return`
  int getFlattenedIndex(ivec3 coords) {
    // reverse y, z order
    return coords.x * ${t[0]} + coords.z * ${t[1]} + coords.y;
  }
`}var Tm,Em,wf,GE=O(()=>{"use strict";He(),ct(),Oe(),Ra(),Tm=e=>({name:"Reshape (packed)",inputTypes:[2],inputNames:["A"],cacheHint:`${e}`}),Em=(e,t,n,r)=>{let i=t.dims,o=r,l="";for(let a=0;a<4;a++){let d="";switch(a){case 0:d="outputCoords = rc;";break;case 1:d="outputCoords = ivec3(rc.x, rc.y+1, rc.z);";break;case 2:d="outputCoords = ivec3(rc.x, rc.y, rc.z+1);";break;case 3:d="outputCoords = ivec3(rc.x, rc.y+1, rc.z+1);";break;default:throw new Error}l+=`
        ${d}
        ${a>0?"if(outputCoords.y < rows && outputCoords.z < cols){":""}
          int flattenedIndex = getFlattenedIndex(outputCoords);

          ivec3 inputRC = inputCoordsFromReshapedOutCoords(flattenedIndex);
          vec2 innerDims = vec2(float(inputRC.y),float(inputRC.z));

          result[${a}] = getChannel(getA(inputRC.x, inputRC.y, inputRC.z), innerDims);

        ${a>0?"}":""}
      `}let s=Re(e.session.backend.glContext.version),u=`
      ${UE(i)}
      ${qE(o)}
      ${eo()}

      void main() {
        ivec3 rc = getOutputCoords();

        vec4 result = vec4(0.0);

        ivec3 outputCoords;
        int rows = ${o[2]};
        int cols = ${o[1]};

        ${l}
        ${s.output} = result;
      }
    ``vec2(${i.join(",")})`,a=Re(e.session.backend.glContext.version),d=`
    ${l}
    void main() {
      ${o} rc = getOutputCoords();

       // Sample the texture with the coords to get the rgba channel value.
       vec4 packedInput = getA(${s});

       ${a.output} = vec4(getChannel(packedInput, ${u}), 0, 0, 0);
     }
   `;return{...ml,hasMain:!0,output:{dims:t.dims,type:t.type,textureType:0},shaderSource:d}},Tf=(e,t)=>({...ml,get:()=>Am(e,t)})}),Ef,ac,Af,Cs=O(()=>{"use strict";Dn(),Ef=class{constructor(e,t=1){if(t===1)this.internalFormat=e.R32F,this.format=e.RED,this.textureType=e.FLOAT,this.channelSize=t;else if(t===4)this.internalFormat=e.RGBA32F,this.format=e.RGBA,this.textureType=e.FLOAT,this.channelSize=t;else throw new Error(`Invalid number of channels: ${t}``Invalid number of channels: ${t}``${i.unpackedShape.join(",")};${i.width}x${i.height}`).join("_"),r=e.name;return e.cacheHint&&(r+="["+e.cacheHint+"]"),r+=":"+n,r},kf=class{constructor(e){this.session=e,this.packedTextureDataCache=new Map,this.unpackedTextureDataCache=new Map}calculateTextureWidthAndHeight(e,t){return Sf(this.session.layoutStrategy,e,t)}executeProgram(e,t){if(t.length<e.inputNames.length)throw new Error(`Input size mustn''t topologically sort routines needed for shader.");t.add(e.name);let i=e.dependencies;if(i&&i.length>0)for(let o=0;o<i.length;++o)this.dfsTraverse(i[o],t,n,r);r.push(e),n.add(e.name),t.delete(e.name)}}});function JE(){let e="add_";return{body:`
  float ${e}(float a, float b) {
    return a + b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 + v2;
  }
  `,name:e,type:0}}function ZE(){let e="div_";return{body:`
  float ${e}(float a, float b) {
    return a / b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 / v2;
  }
  `,name:e,type:0}}function XE(){let e="mul_";return{body:`
  float ${e}(float a, float b) {
    return a * b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 * v2;
  }
  `,name:e,type:0}}function $E(){let e="sub_";return{body:`
  float ${e}(float a, float b) {
    return a - b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 - v2;
  }
  `,name:e,type:0}}function eA(){let e="equal_";return{body:`
  float ${e}(float a, float b) {
    return float(a == b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4(equal(v1, v2));
  }
  `,name:e,type:0}}function tA(){let e="greater_";return{body:`
  float ${e}(float a, float b) {
    return float(a > b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4( v1.r > v2.r ,
      v1.g > v2.g,
      v1.b > v2.b,
      v1.a > v2.a );
  }
  `,name:e,type:0}}function nA(){let e="less_";return{body:`
  float ${e}(float a, float b) {
    return float(a < b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4( v1.r < v2.r ,
                v1.g < v2.g,
                v1.b < v2.b,
                v1.a < v2.a );
  }
  `,name:e,type:0}}function rA(){let e="and_";return{body:`
  float ${e}(float a, float b) {
    return float( bool(a) && bool(b) );
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r && b2.r ,
                b1.g && b2.g,
                b1.b && b2.b,
                b1.a && b2.a );
  }
  `,name:e,type:0}}function aA(){let e="or_";return{body:`
  float ${e}(float a, float b) {
    return float( bool(a) || bool(b) );
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r || b2.r ,
                b1.g || b2.g,
                b1.b || b2.b,
                b1.a || b2.a );
  }
  `,name:e,type:0}}function iA(){let e="xor_";return{body:`
  float ${e}(float a, float b) {
    return float( bool(a) ^^ bool(b) );
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r ^^ b2.r ,
                b1.g ^^ b2.g,
                b1.b ^^ b2.b,
                b1.a ^^ b2.a );
  }
  ``X${r}`),inputTypes:Array(e).fill(0),cacheHint:t}),Bm=(e,t,n,r)=>{let i=n[0].dims.slice();if(r>=i.length||r<-1*i.length)throw new Error("axis specified for concat doesn''t use matmul on the given tensors");let l=Ar(o.length),s=Fa(),{activationFunction:u,applyActivation:a}=to(n),d=t.length>2,c=d?"value += getBiasForMatmul();":"",p=d?`${Ab(l,s,t[2].dims,o,!1)}`:"""",l=n.length,s=r.length,u=s-l;s<2&&l>0?o="coords":o=n.map((p,m)=>`coords.${t[m+u]}`).join(", ");let a=Vn.getBroadcastDims(n,r).map(p=>`coords.${t[p+u]} = 0;`).join(`
`),d=Ae.size(n)===1,c="vec4(outputValue.xx, outputValue.yy)";return d&&(c="vec4(outputValue.x)"),i?`
vec4 getBiasForMatmul() {
  ${e} coords = getOutputCoords();
  ${a}
  vec4 outputValue = getBias(${o});
  return ${c};
}`:`
float getBiasForMatmul() {
  ${e} coords = getOutputCoords();
  ${a}
  return getBias(coords.x);
}``coords.${t[y+c]}`),i[u-1]="i*2",i.join(", "),o=s.map((x,y)=>`coords.${t[y+p]}`),o[a-2]="i*2",o.join(", """;for(let r=0;r<t-2;r++)n+=`rc.${e[r]}, `;return n+=`rc.${e[t-2]}, i*2`,n}function OA(e,t){let n="";for(let r=0;r<t-2;r++)n+=`rc.${e[r]}, `;return n+=`i*2, rc.${e[t-1]}``int c[${t[2].dims.length}];`:"",p=t.length===3?"bcastIndices_C(indices, c);":"",m=t.length===3?"value += beta * _C(c);":"",g=`
      float process(int indices[${d}]) {
          int a[${d}];
          int b[${d}];
          ${c}

          copyVec(indices, a);
          copyVec(indices, b);
          ${p}

          float value = 0.0;
          for (int k=0; k<${u}; ++k) {
              a[${d-1}] = k;
              b[${d-2}] = k;
              ${a}
          }

          value = value * alpha;
          ${m}
          return value;
      }``
      ${_g(r.bias.length)}
      float process(int indices[${o}]) {
        return _X(indices) * scale + getBias(bias, indices[1]);
      }`;return{...t,output:{dims:i,type:n[0].type,textureType:0},variables:[{name:"bias",type:"float",arrayLength:r.bias.length,data:r.bias},{name:"scale",type:"float",data:r.scale}],shaderSource:l}},Ig=(e,t,n)=>{let r={...Dg,cacheHint:n.cacheKey};return{...r,get:()=>Cg(e,r,t,n)}},_g=e=>{let t=[`float getBias(float bias[${e}], int channel) {`];for(let n=0;n<e;++n)n===0?t.push(`	if (channel == ${n}) { return bias[${n}]; }`):n===e-1?t.push(`	else { return bias[${n}]; }`):t.push(`	else if (channel == ${n}) { return bias[${n}]; }`);return t.push("	}"),t.join(`
``${n}``);let s=l==="tf_crop_and_resize",u=s,a=r==="nearest"&&t>=11?e.attributes.getString("nearest_mode","round_prefer_floor"):"";if(["round_prefer_floor","round_prefer_ceil","floor","ceil",""].indexOf(a)===-1)throw new Error(`nearest_mode '${a}''Linear' mode and 'Cubic' mode only support 2-D inputs ('Bilinear', 'Bicubic''${n.mode}''${o}''webgl2'. Error: ${o}`)}if((!t||t==="webgl")&&(r=e.getContext("webgl",i)||e.getContext("experimental-webgl",i),r))try{return new Tc(r,1)}catch(o){gt.warning("GlContextFactory",``)}}compareTensorDims(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(e[r]!==t[r]&&(!n||e[r]!==0))return!1;return!0}createOutput(e){let t=this._model.graph.getOutputNames();if(e.length!==t.length)throw new Error("expected number of outputs do not match number of generated outputs");let n=new Map;for(let r=0;r<t.length;++r)n.set(t[r],e[r]);return n}initializeOps(e){let t=e.getNodes();this._ops=new Array(t.length);for(let n=0;n<t.length;n++)this._ops[n]=this.sessionHandler.resolve(t[n],this._model.opsets,e)}}}),ty,LS=O(()=>{"use strict""use strict";jS(),LS(),Dh=class{async init(){}async createInferenceSessionHandler(e,t){let n=new ey(t);return typeof e=="string"?await n.loadModel(e):await n.loadModel(e),new ty(n)}},ry=new Dh}),zc=O(()=>{"use strict"}),ay={};Xi(ay,{default:()=>iy});var Rl,Ol,iy,zS=O(()=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})});break}case"copy-from":{let{buffer:r}=n,i=Ps(r);postMessage({type:t,out:i});break}case"create":{let{model:r,options:i}=n;Qc(r,i).then(o=>{postMessage({type:t,out:o})},o=>{postMessage({type:t,err:o})});break}case"release":Yc(n),postMessage({type:t});break;case"run":{let{sessionId:r,inputIndices:i,inputs:o,outputIndices:l,options:s}=n;Jc(r,i,o,l,new Array(l.length).fill(null),s).then(u=>{u.some(a=>a[3]!=="cpu")?postMessage({type:t,err:"Proxy does not support non-cpu tensor location."}):postMessage({type:t,out:u},Xc([...o,...u]))},u=>{postMessage({type:t,err:u})});break}case"end-profiling":Zc(n),postMessage({type:t});break;default:}}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.buffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign({},e),a=new Promise((h,w)=>{l=h,s=w}),d=typeof window=="object",c=typeof importScripts=="function",p=c&&self.name=="em-pthread";u.mountExternalData=(h,w)=>{h.startsWith("./")&&(h=h.substring(2)),(u.Ua||(u.Ua=new Map)).set(h,w)},u.unmountExternalData=()=>{delete u.Ua};var m,g,f=globalThis.SharedArrayBuffer??new WebAssembly.Memory({initial:0,maximum:0,shared:!0}).buffer.constructor,v=Object.assign({},u),b="./this.program",x=(h,w)=>{throw w},y="";(d||c)&&(c?y=self.location.href:typeof document<"u"&&document.currentScript&&(y=document.currentScript.src),jl&&(y=jl),y=y.startsWith("blob:")?"":y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1),c&&(g=h=>{var w=new XMLHttpRequest;return w.open("GET",h,!1),w.responseType="arraybuffer",w.send(null),new Uint8Array(w.response)}),m=(h,w,A)=>{var C=new XMLHttpRequest;C.open("GET",h,!0),C.responseType="arraybuffer",C.onload=()=>{C.status==200||C.status==0&&C.response?w(C.response):A()},C.onerror=A,C.send(null)});var S,B=void 0,P=void 0,D=B,I=P;if(Object.assign(u,v),v=null,p){let h=function(w){try{var A=w.data,C=A.cmd;if(C==="load"){let M=[];self.onmessage=N=>M.push(N),self.startWorker=()=>{postMessage({cmd:"loaded"});for(let N of M)h(N);self.onmessage=h};for(let N of A.handlers)u[N]&&!u[N].proxy||(u[N]=(...pe)=>{postMessage({Za:"callHandler",kb:N,args:pe})},N=="print"&&(D=u[N]),N=="printErr"&&(I=u[N]));j=A.wasmMemory,F(),z(A.wasmModule)}else if(C==="run"){Ou(A.pthread_ptr,0,0,1,0,0),lt(A.pthread_ptr),Sn(),Bt(),q||(q=!0);try{qe(A.start_routine,A.arg)}catch(M){if(M!="unwind")throw M}}else C==="cancel"?Ea()&&$o(-1):A.target!=="setimmediate"&&(C==="checkMailbox"?q&&xt():C&&(I(`worker: received unknown command ${C}``Program terminated with exit(${h})``Internal error! Worker sent a message "${N}" to target pthread ${M.targetThread}, but that thread no longer exists!`)}else N==="checkMailbox"?xt():N==="spawnThread"?ve(M):N==="cleanupThread"?St(dt[M.thread]):N==="killThread"?(M=M.thread,N=dt[M],delete dt[M],Xe(N),ju(M),Qt.splice(Qt.indexOf(N),1),N.Ra=0):N==="cancelThread"?dt[M.thread].postMessage({cmd:"cancel"}):N==="loaded"?(h.loaded=!0,w(h)):N==="alert"?alert(`Thread ${M.threadId}: ${M.text}`):M.target==="setimmediate"?h.postMessage(M):N==="callHandler"?u[M.handler](...M.args):N&&I(`worker sent an unknown command ${N}`)},h.onerror=M=>{throw I(`worker sent an error! ${M.filename}:${M.lineno}: ${M.message}``
`);return h[0]=="Error"&&h.shift(),u0(h),Ta.$a=Zo(),Ta.bb=h,Ta.$a}function Q1(h,w,A){if(h>>>=0,w>>>=0,Ta.$a==h)var C=Ta.bb;else(C=Error().stack.toString().split(`
``
``wasm streaming compile failed: ${pe}``)})},kt=e=>{let t=Lt(),n=t.stackSave();try{let r=t.stackAlloc(8);t._OrtGetLastError(r,r+4);let i=t.HEAP32[r/4],o=t.HEAPU32[r/4+1],l=o?t.UTF8ToString(o):"";throw new Error(`${e} ERROR_CODE: ${i}, ERROR_MESSAGE: ${l}`)}finally{t.stackRestore(n)}}}),cy,qS=O(()=>{"use strict";La(),qc(),cy=e=>{let t=Lt(),n=0,r=[],i=e||{};try{if(e?.logSeverityLevel===void 0)i.logSeverityLevel=2;else if(typeof e.logSeverityLevel!="number"||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);if(e?.logVerbosityLevel===void 0)i.logVerbosityLevel=0;else if(typeof e.logVerbosityLevel!="number"||!Number.isInteger(e.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);e?.terminate===void 0&&(i.terminate=!1);let o=0;return e?.tag!==void 0&&(o=_t(e.tag,r)),n=t._OrtCreateRunOptions(i.logSeverityLevel,i.logVerbosityLevel,!!i.terminate,o),n===0&&kt("Can't create run options."),e?.extra!==void 0&&_s(e.extra,"",new WeakSet,(l,s)=>{let u=_t(l,r),a=_t(s,r);t._OrtAddRunConfigEntry(n,u,a)!==0&&kt(`Can''t set a session config entry: 'deviceType' - ${l}.`)}}break;case"webgpu":if(i="JS",typeof r!="string"){let l=r;if(l?.preferredLayout){if(l.preferredLayout!=="NCHW"&&l.preferredLayout!=="NHWC")throw new Error(`preferredLayout must be either 'NCHW' or 'NHWC': ${l.preferredLayout}`);let s=_t("preferredLayout",n),u=_t(l.preferredLayout,n);Lt()._OrtAddSessionConfigEntry(e,s,u)!==0&&kt(`Can't set a session config entry: 'preferredLayout' - ${l.preferredLayout}.`)}}break;case"wasm":case"cpu":continue;default:throw new Error(`not supported execution provider: ${i}`)}let o=_t(i,n);Lt()._OrtAppendExecutionProvider(e,o)!==0&&kt(`Can''t create session options."),i.executionProviders&&jh(n,i.executionProviders,r),i.enableGraphCapture!==void 0){if(typeof i.enableGraphCapture!="boolean")throw new Error(`enableGraphCapture must be a boolean value: ${i.enableGraphCapture}`);let c=_t("enableGraphCapture",r),p=_t(i.enableGraphCapture.toString(),r);t._OrtAddSessionConfigEntry(n,c,p)!==0&&kt(`Can't set a session config entry: 'enableGraphCapture' - ${i.enableGraphCapture}.`)}if(i.freeDimensionOverrides)for(let[c,p]of Object.entries(i.freeDimensionOverrides)){if(typeof c!="string")throw new Error(`free dimension override name must be a string: ${c}`);if(typeof p!="number"||!Number.isInteger(p)||p<0)throw new Error(`free dimension override value must be a non-negative integer: ${p}`);let m=_t(c,r);t._OrtAddFreeDimensionOverride(n,m,p)!==0&&kt(`Can't set a free dimension override: ${c} - ${p}.`)}return i.extra!==void 0&&_s(i.extra,"",new WeakSet,(c,p)=>{let m=_t(c,r),g=_t(p,r);t._OrtAddSessionConfigEntry(n,m,g)!==0&&kt(``External buffer must be provided for input/output index ${i} when enableGraphCapture is true.``Unsupported data type: ${me}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let l=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?l=`${n("error.googleLimitIp")}<br/><br/>${l}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?l=`${n("error.openAIFreeLimit")}<br/><br/>
          ${l}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?l=`${n("error.openAIExceededQuota")}<br/><br/>
          ${l}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?l=`${n("error.gemini.429")}<br/><br/> ${l}`:l=`${n("error.429")}<br/><br/> ${l}`:this.status===403?this.data?.translationService=="claude"?l=`${n("error.claude.403")}<br/><br/>${l}`:l=`${n("error.403")}<br/><br/>${l}`:this.status===400?l=`${n("error.400")}<br/><br/> ${l}`:this.status===502?l=`${n("error.502")}<br/><br/> ${l}`:this.status===404?l.includes("User subscription not found")&&(l=`${n("error.subscriptionExpired")}<br/><br/> ${l}`,r="setting",i="configError",o=n("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(l=`${n("error.azure.401")}<br/><br/> ${l}`),{type:i,title:o,errMsg:l,action:r}}handleFetchError(t){let n=Wt.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let r=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${n("mangaQuotaError.solvedTitle")}<br/></br>
        ${i.map((l,s)=>`${s+1}. ${l}`).join("<br/>")}
        ``${r("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((m,g)=>`${g+1}. ${m}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",i="";return n&&n.length===3&&(r=n[1],i=n[2]),{mimeType:r,base64:i}}var z3=Object.prototype.toString;function hu(e){switch(z3.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Qn(e,Error)}}function _o(e,t){return z3.call(e)===`[object ${t}]``[${c[0]}="${c[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),i=n.className,i&&ur(i))for(o=i.split(/\s+/),u=0;u<o.length;u++)r.push(`.${o[u]}`);let d=["type","name","title","alt"];for(u=0;u<d.length;u++)l=d[u],s=n.getAttribute(l),s&&r.push(`[${l}="${s}"]`);return r.join("")}function W3(){try{return location.href}catch{return""}}var ht=class extends Error{constructor(n){super(n);this.message=n;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var ZC=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function XC(e){return e==="http"||e==="https"}function ai(e,t=!1){let{host:n,path:r,pass:i,port:o,projectId:l,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${l}`}function $C(e){let t=ZC.exec(e);if(!t)throw new ht(`Invalid Sentry Dsn: ${e}``Invalid Sentry Dsn: ${String(o)} missing`)}),!n.match(/^\d+$/))throw new ht(`Invalid Sentry Dsn: Invalid projectId ${n}`);if(!XC(r))throw new ht(`Invalid Sentry Dsn: Invalid protocol ${r}`);if(t&&isNaN(parseInt(t,10)))throw new ht(`Invalid Sentry Dsn: Invalid port ${t}``${e.substr(0,t)}...``${encodeURIComponent(t)}=${encodeURIComponent(e[t])}``
``Error while triggering instrumentation handler.
Type: ${e}
Name: ${Ln(n)}
Error:``${r.type}: ${r.value}``**non-serializable** (${r})``[Function: ${Ln(t)}]`:typeof t=="symbol"?`[${String(t)}]``
${JSON.stringify(s)}
``${e}`,10);if(!isNaN(n))return n*1e3;let r=Date.parse(`${e}``${t.did}``${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string""function""number""function")o(n);else{let u=s({...n},r);!1&&s.id&&u===null&&fe.log(``${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function k9(e){return`${S9(e)}${e.projectId}/envelope/`}function D9(e,t){return Z3({sentry_key:e.publicKey,sentry_version:A9,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${n.name}`))}),t}var Ow="Not capturing exception because it''s not included in the random sample (sampling rate = ${o})`))):this._prepareEvent(t,n,r).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new ht("An event processor returned null, will not send event.");if(n.data&&n.data.__sentry__===!0||l||!i)return s;let a=i(s,n);return _9(a)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new ht("`beforeSend` returned `null`, will not send event.""Error while sending event:",n)}):!1&&fe.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(n=>{let[r,i]=n.split(":");return{reason:r,category:i,quantity:t[n]}})}};function _9(e){let t="`beforeSend` method has to return `null` or a valid event.";if(ri(e))return e.then(n=>{if(!(On(n)||n===null))throw new ht(t);return n},n=>{throw new ht(`beforeSend rejected with ${n}``Sentry responded with status code ${c.statusCode} to sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&fe.error(`Cannot extract message for event ${dr(e)}`),[]}return[]}function N9(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function z9(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Eu(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?z9(t):null}catch{return!1&&fe.error(`Cannot extract url for event ${dr(e)}`),null}}function Lp(e,t){let n=Np(e,t),r={type:t&&t.name,value:V9(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function q9(e,t,n,r){let i={exception:{values:[{type:Mo(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:`Non-Error ${r?"promise rejection":"exception"} captured with keys: ${X3(t)}``${s}: ${l.message}`:s;o=jp(e,u,n,r),jo(o,u)}return"code"in l&&(o.tags={...o.tags,"DOMException.code":`${l.code}`}),o}return hu(t)?Op(e,t):On(t)||Mo(t)?(o=q9(e,t,n,i),Br(o,{synthetic:!0}),o):(o=jp(e,t,n,r),jo(o,`${t}``ui.${n.name}`,message:r},{event:n.event,name:n.name,global:n.global})}return t}function K9(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:uw(e.level),message:gp(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${gp(e.args.slice(1)," ")||"console.assert"}``sentry.${t.type==="transaction"?"transaction":"event"}``safari-extension:${t}`:`safari-web-extension:${t}``Non-Error promise rejection captured with value: ${String(e)}``Global Handler attached: ${e}``${t}@${e}``Request timeout after ${s}ms``fail response: ${t} `,d);let c="";d&&(c=d.slice(0,500));let p=c,g=new URL(t).hostname.endsWith(`.${wt}``${K.year}${K.week}``[domain]` tag:** Apply strictly with absolute priority, ** overriding any other translation logic for the term**. (Use original Source Term if Source==Translation, else use listed Translation).\n    *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2D\u6587\uFF0C{{html_only}}\u4EC5\u8F93\u51FA\u7FFB\u8BD1\u3002\u5982\u679C\u67D0\u4E9B\u5185\u5BB9\u65E0\u9700\u7FFB\u8BD1\uFF08\u5982\u4E13\u6709\u540D\u8BCD\u3001\u4EE3\u7801\u7B49\uFF09\uFF0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u4E1C\u5317\u4EBA\u7684\u53E3\u543B\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u751F\u6D3B,\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA",multipleSystemPrompt:""},{id:"wyw2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u7CBE\u901A\u53E4\u6587\u7684\u5B66\u8005\uFF0C\u53EA\u8FD4\u56DE\u767D\u8BDD\u6587",prompt:`\u8BF7\u5C06\u6587\u8A00\u6587\u7528\u767D\u8BDD\u6587\u89E3\u91CA:

{{text}}`,multiplePrompt:`\u5C06\u4E0B\u9762 YAML \u683C\u5F0F\u4E2D\u6240\u6709\u7684 {{imt_source_field}} \u5B57\u6BB5\u4E2D\u7684\u6587\u8A00\u6587\u7FFB\u8BD1\u4E3A\u767D\u8BDD\u6587\uFF0C\u5E76\u5C06\u7FFB\u8BD1\u7ED3\u679C\u5199\u5728 {{imt_trans_field}} \u5B57\u6BB5\u4E2D

{{normal_result_yaml_example}}

\u5F00\u59CB\u7FFB\u8BD1:

{{yaml}}`,multipleSystemPrompt:""},{id:"auto2wyw",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u6587\u8A00\u6587\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u6587\u8A00\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are you"}
    }
  ],
  "contextual_analysis": "Analysis of the word''t ","n''m sorry, but I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``${l_}${e}.onnx`,[`Loading ${e}`,({text:r,progress:i})=>{}]),n&&await C1(e,n)),n}var cn={};chrome.runtime.onMessage.addListener((e,t,n)=>{if(e.target==="offscreen")switch(e.data.type){case"trigger":return d_(e.data),!1;case"state":return c_(n,e.data),!0;default:throw new Error("Unrecognized message:",e.type)}});function c_(e,t){let{urlHash:n}=t;cn[n]||(cn[n]={state:"extension_uploading"}),e({state:cn[n].state,errorMsg:cn[n].errorMsg,result:cn[n].result})}async function d_(e){let{mimeType:t,imgBuffer:n,urlHash:r}=e;(!cn[r]||cn[r].state=="error")&&(cn[r]={...cn[r],state:"extension_uploading",errorMsg:""});let i=Object.values(n),l=new Uint8Array(i).buffer;try{let s=await M1(t,l,u=>{cn[r].state=u,Ce.debug("imgState",u)});cn[r].result=s}catch(s){Ce.error(s),cn[r].state="error",cn[r].errorMsg=s.message}}})();

