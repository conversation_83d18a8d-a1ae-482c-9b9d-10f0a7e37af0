#!/usr/bin/env python3
"""
跨文件去重工具
通过比较多个文件，识别并移除在所有文件中都出现的相同代码块
特别针对CSS和语言文件等重复内容
"""

import re
import os
import json
from typing import Dict, List, Set, Tuple
from difflib import SequenceMatcher

class CrossFileDeduplicator:
    def __init__(self):
        self.files_content = {}
        self.common_blocks = []
        self.min_block_size = 100  # 最小代码块大小
        self.similarity_threshold = 0.95  # 相似度阈值
        
    def load_files(self, file_paths: List[str]) -> Dict[str, str]:
        """加载所有文件内容"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.files_content[file_path] = f.read()
                print(f"已加载: {file_path} ({len(self.files_content[file_path]):,} 字符)")
            else:
                print(f"文件不存在: {file_path}")
        return self.files_content
    
    def find_exact_common_substrings(self, min_length: int = 200) -> List[str]:
        """查找所有文件中完全相同的子字符串"""
        if len(self.files_content) < 2:
            return []

        file_contents = list(self.files_content.values())
        first_file = file_contents[0]
        common_substrings = []

        print(f"正在查找长度 >= {min_length} 的完全相同子字符串...")

        # 使用更高效的方法：先找到所有可能的起始位置
        for length in [5000, 2000, 1000, 500, min_length]:
            print(f"  检查长度 {length} 的子字符串...")
            found_any = False

            for i in range(0, len(first_file) - length + 1, 100):  # 跳跃式搜索
                if i % 10000 == 0:
                    print(f"    进度: {i/len(first_file)*100:.1f}%")

                substring = first_file[i:i+length]

                # 跳过主要是空白字符的子字符串
                if len(substring.strip()) < length * 0.5:
                    continue

                # 检查是否在所有其他文件中都存在
                if all(substring in content for content in file_contents[1:]):
                    # 检查是否已经包含在更长的子字符串中
                    if not any(substring in existing for existing in common_substrings):
                        # 移除被当前子字符串包含的较短子字符串
                        common_substrings = [s for s in common_substrings if s not in substring]
                        common_substrings.append(substring)
                        print(f"    发现公共子字符串 (长度: {length}): {substring[:100].replace(chr(10), ' ')[:100]}...")
                        found_any = True

            if found_any:
                print(f"  在长度 {length} 找到 {len([s for s in common_substrings if len(s) >= length])} 个子字符串")

        return sorted(common_substrings, key=len, reverse=True)
    
    def find_pattern_based_common_blocks(self) -> List[Dict]:
        """基于模式查找公共代码块"""
        patterns = [
            # CSS 相关模式
            {
                'name': 'CSS_CONSTANTS',
                'pattern': r'IMMERSIVE_TRANSLATE_[A-Z_]+_CSS\s*:\s*["`\'][^`"\']*["`\']',
                'description': 'CSS常量定义'
            },
            {
                'name': 'LARGE_CSS_BLOCKS', 
                'pattern': r'["`\'][^`"\']{1000,}["`\']',
                'description': '大型CSS/样式块'
            },
            # 语言包模式
            {
                'name': 'LANGUAGE_MAPS',
                'pattern': r'var\s+[A-Z][a-z]*\s*=\s*\{[^}]*[\u4e00-\u9fff][^}]*\}',
                'description': '语言映射对象'
            },
            {
                'name': 'TRANSLATION_STRINGS',
                'pattern': r'"[^"]*[\u4e00-\u9fff][^"]*"\s*:\s*"[^"]*"',
                'description': '翻译字符串'
            },
            # URL和配置模式
            {
                'name': 'URL_CONSTANTS',
                'pattern': r'[A-Z_]+_URL\s*:\s*"[^"]*"',
                'description': 'URL常量'
            },
            {
                'name': 'DOMAIN_LISTS',
                'pattern': r'\["[^"]*\.com"[^\]]*\]',
                'description': '域名列表'
            },
            # 大型对象模式
            {
                'name': 'LARGE_OBJECTS',
                'pattern': r'\{[^{}]{2000,}\}',
                'description': '大型对象定义'
            },
            # 函数和类模式
            {
                'name': 'UTILITY_FUNCTIONS',
                'pattern': r'function\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\([^)]*\)\s*\{[^{}]*\}',
                'description': '工具函数'
            }
        ]
        
        common_patterns = []
        
        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            name = pattern_info['name']
            description = pattern_info['description']
            
            print(f"\n检查模式: {name} - {description}")
            
            # 在每个文件中查找匹配
            file_matches = {}
            for file_path, content in self.files_content.items():
                matches = re.findall(pattern, content, re.DOTALL)
                file_matches[file_path] = set(matches)
                print(f"  {os.path.basename(file_path)}: {len(matches)} 个匹配")
            
            # 找出在所有文件中都出现的匹配
            if file_matches:
                common_matches = set.intersection(*file_matches.values())
                if common_matches:
                    total_size = sum(len(match) for match in common_matches)
                    common_patterns.append({
                        'name': name,
                        'description': description,
                        'pattern': pattern,
                        'matches': list(common_matches),
                        'count': len(common_matches),
                        'total_size': total_size
                    })
                    print(f"  ✓ 公共匹配: {len(common_matches)} 个 (总大小: {total_size:,} 字符)")
                else:
                    print(f"  ✗ 无公共匹配")
        
        return common_patterns
    
    def remove_common_content(self, content: str, common_patterns: List[Dict]) -> str:
        """从内容中移除公共代码块"""
        cleaned_content = content
        removed_info = []
        
        for pattern_info in common_patterns:
            pattern = pattern_info['pattern']
            matches = pattern_info['matches']
            name = pattern_info['name']
            
            original_size = len(cleaned_content)
            
            # 移除所有匹配的内容
            for match in matches:
                # 转义特殊字符用于正则替换
                escaped_match = re.escape(match)
                cleaned_content = re.sub(escaped_match, f'/* REMOVED_{name} */', cleaned_content)
            
            removed_size = original_size - len(cleaned_content)
            if removed_size > 0:
                removed_info.append({
                    'pattern': name,
                    'removed_size': removed_size,
                    'matches_count': len(matches)
                })
        
        return cleaned_content, removed_info
    
    def process_files(self, output_dir: str = "deduplicated_v2") -> Dict:
        """处理所有文件，移除公共内容"""
        os.makedirs(output_dir, exist_ok=True)

        # 查找基于模式的公共块
        print("=" * 60)
        print("查找基于模式的公共代码块...")
        common_patterns = self.find_pattern_based_common_blocks()

        # 查找精确的公共子字符串
        print("\n" + "=" * 60)
        print("查找精确的公共子字符串...")
        common_substrings = self.find_exact_common_substrings()

        results = {
            'common_patterns': common_patterns,
            'common_substrings': len(common_substrings),
            'files': {}
        }

        print("\n" + "=" * 60)
        print("开始去重处理...")

        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n处理文件: {filename}")

            # 移除公共内容
            cleaned_content, removed_info = self.remove_common_content(original_content, common_patterns)

            # 移除精确匹配的公共子字符串
            substring_removed = 0
            for i, substring in enumerate(common_substrings):
                if substring in cleaned_content:
                    old_size = len(cleaned_content)
                    cleaned_content = cleaned_content.replace(substring, f'/* REMOVED_COMMON_BLOCK_{i} */')
                    substring_removed += old_size - len(cleaned_content)

            if substring_removed > 0:
                removed_info.append({
                    'pattern': 'EXACT_SUBSTRINGS',
                    'removed_size': substring_removed,
                    'matches_count': len(common_substrings)
                })

            # 额外清理
            cleaned_content = self.additional_cleanup(cleaned_content)

            # 保存清理后的文件
            output_path = os.path.join(output_dir, f"dedup_{filename}")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)

            # 计算压缩比例
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100

            file_result = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'output_path': output_path,
                'removed_info': removed_info
            }

            results['files'][filename] = file_result

            print(f"  原始大小: {original_size:,} 字符 ({original_size/1024:.1f} KB)")
            print(f"  清理后大小: {cleaned_size:,} 字符 ({cleaned_size/1024:.1f} KB)")
            print(f"  压缩比例: {compression_ratio:.1f}%")
            print(f"  输出文件: {output_path}")

            for info in removed_info:
                print(f"    移除 {info['pattern']}: {info['removed_size']:,} 字符 ({info['matches_count']} 个匹配)")

        return results
    
    def additional_cleanup(self, content: str) -> str:
        """额外的清理步骤"""
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 移除连续的注释
        content = re.sub(r'(/\*\s*REMOVED_[^*]*\*/\s*){2,}', '/* MULTIPLE_REMOVED_BLOCKS */', content)
        
        # 清理多余的逗号和分号
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r';\s*;', ';', content)
        
        return content

def main():
    deduplicator = CrossFileDeduplicator()
    
    # 要处理的文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("跨文件去重工具 v1.0")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("需要至少2个文件进行比较")
        return
    
    # 处理文件
    results = deduplicator.process_files()
    
    # 保存结果报告
    with open('deduplication_report.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("去重完成！总结:")
    
    total_original = sum(info['original_size'] for info in results['files'].values())
    total_cleaned = sum(info['cleaned_size'] for info in results['files'].values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"总原始大小: {total_original:,} 字符 ({total_original/1024:.1f} KB)")
    print(f"总清理后大小: {total_cleaned:,} 字符 ({total_cleaned/1024:.1f} KB)")
    print(f"总体压缩比例: {overall_compression:.1f}%")
    
    print(f"\n发现的公共模式:")
    for pattern in results['common_patterns']:
        print(f"  {pattern['name']}: {pattern['count']} 个匹配, {pattern['total_size']:,} 字符")
    
    print(f"\n清理后的文件保存在: deduplicated/ 目录")
    print(f"详细报告保存在: deduplication_report.json")

if __name__ == "__main__":
    main()
