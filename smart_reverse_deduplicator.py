#!/usr/bin/env python3
"""
智能反向去重工具
修正版本：更合理的反向查找策略
"""

import os
import json
from typing import List, Dict, Set, Tuple

class SmartReverseDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"✓ 加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_unique_business_code_from_end(self, min_unique_length: int = 1000) -> Dict[str, int]:
        """从文件末尾开始查找独特的业务代码"""
        print(f"\n🔍 从文件末尾查找独特业务代码...")
        
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        business_start_positions = {}
        
        for i, (file_path, content) in enumerate(files):
            filename = file_names[i]
            print(f"\n  分析: {filename}")
            
            file_length = len(content)
            unique_start_pos = file_length  # 默认没有独特代码
            
            # 从文件末尾开始，查找独特的代码段
            chunk_size = 2000  # 每次检查2KB
            step = 500  # 步长
            
            for end_pos in range(file_length, chunk_size, -step):
                start_pos = max(0, end_pos - chunk_size)
                chunk = content[start_pos:end_pos]
                
                # 跳过主要是空白的块
                if len(chunk.strip()) < chunk_size * 0.3:
                    continue
                
                # 检查这个块是否在其他文件中存在
                is_unique = True
                for j, (other_path, other_content) in enumerate(files):
                    if i == j:
                        continue
                    
                    # 如果这个块在其他文件中存在，说明不是独特的业务代码
                    if chunk in other_content:
                        is_unique = False
                        break
                
                if is_unique:
                    # 找到独特代码，继续向前扩展查找独特区域的开始
                    unique_start_pos = start_pos
                    print(f"    ✓ 在位置 {start_pos:,}-{end_pos:,} 找到独特代码")
                    
                    # 向前扩展，找到独特区域的真正开始
                    for expand_start in range(start_pos, -1, -1000):
                        expand_chunk = content[expand_start:end_pos]
                        
                        still_unique = True
                        for other_path, other_content in files:
                            if other_path != file_path and expand_chunk in other_content:
                                still_unique = False
                                break
                        
                        if still_unique:
                            unique_start_pos = expand_start
                            print(f"    📍 扩展独特区域到位置 {expand_start:,}")
                        else:
                            break
                else:
                    # 如果找到了独特代码的开始位置，就停止搜索
                    if unique_start_pos < file_length:
                        break
            
            business_start_positions[filename] = unique_start_pos
            
            if unique_start_pos < file_length:
                dependency_size = unique_start_pos
                business_size = file_length - unique_start_pos
                print(f"    📊 依赖代码: {dependency_size:,} 字符 ({dependency_size/file_length*100:.1f}%)")
                print(f"    📊 独特业务代码: {business_size:,} 字符 ({business_size/file_length*100:.1f}%)")
            else:
                print(f"    ⚠️  未找到独特代码，可能整个文件都是共享的")
                # 保守策略：保留文件的后半部分
                business_start_positions[filename] = file_length // 2
                print(f"    🛡️  保守策略：保留后半部分 ({file_length//2:,} 字符)")
        
        return business_start_positions
    
    def find_common_large_blocks(self, min_size: int = 1000) -> List[Dict]:
        """查找公共的大块代码"""
        print(f"\n🔍 查找公共大块代码 (>={min_size}字符)...")
        
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        common_blocks = []
        
        # 使用第一个文件作为基准
        base_path, base_content = files[0]
        base_name = file_names[0]
        
        step = 1000  # 较大的步长
        
        for size in [10000, 5000, 2000, min_size]:
            print(f"  检查大小: {size}")
            found_count = 0
            
            for start in range(0, len(base_content) - size + 1, step):
                block = base_content[start:start + size]
                
                # 跳过主要是空白的块
                if len(block.strip()) < size * 0.4:
                    continue
                
                # 检查在其他文件中的存在
                found_in = [base_name]
                for i, (_, other_content) in enumerate(files[1:], 1):
                    if block in other_content:
                        found_in.append(file_names[i])
                
                # 如果在至少一半的文件中找到
                if len(found_in) >= len(files) // 2:
                    common_blocks.append({
                        'block': block,
                        'size': size,
                        'files': found_in,
                        'start_pos': start
                    })
                    found_count += 1
                    print(f"    ✓ 找到公共块: {len(found_in)}个文件, {size}字符")
                    
                    if found_count >= 5:  # 限制数量
                        break
        
        # 去重处理
        filtered_blocks = []
        for block in sorted(common_blocks, key=lambda x: x['size'], reverse=True):
            is_contained = any(
                block['block'] in existing['block'] and block['size'] < existing['size']
                for existing in filtered_blocks
            )
            if not is_contained:
                filtered_blocks.append(block)
        
        print(f"  📊 找到 {len(filtered_blocks)} 个有效公共块")
        return filtered_blocks
    
    def apply_smart_removal(self, business_positions: Dict[str, int], 
                          common_blocks: List[Dict]) -> Dict:
        """应用智能移除策略"""
        print(f"\n🛠️  应用智能移除策略...")
        
        os.makedirs("smart_reverse_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n  处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            operations = []
            
            # 策略1: 移除前置依赖代码（但保留一定比例）
            business_start_pos = business_positions.get(filename, 0)
            if business_start_pos > 0:
                # 保守移除：只移除前面80%的依赖代码
                safe_remove_pos = int(business_start_pos * 0.8)
                if safe_remove_pos > 1000:  # 至少移除1KB才有意义
                    cleaned_content = cleaned_content[safe_remove_pos:]
                    total_removed += safe_remove_pos
                    operations.append(f"前置依赖: -{safe_remove_pos:,}字符")
            
            # 策略2: 移除公共块
            for i, block in enumerate(common_blocks[:10]):  # 最多处理10个
                if filename in block['files'] and block['block'] in cleaned_content:
                    old_size = len(cleaned_content)
                    replacement = f'/* REMOVED_COMMON_BLOCK_{i} */'
                    cleaned_content = cleaned_content.replace(block['block'], replacement, 1)
                    removed = old_size - len(cleaned_content)
                    total_removed += removed
                    operations.append(f"公共块{i}: -{removed:,}字符")
            
            # 策略3: 移除明显的大型字符串常量
            string_removed = 0
            import re
            
            # 移除大型CSS字符串
            css_pattern = r'["`\']([^"`\']{2000,})["`\']'
            css_matches = re.findall(css_pattern, cleaned_content, re.DOTALL)
            for match in css_matches[:5]:
                if any(indicator in match[:200] for indicator in [
                    'background-color', 'font-family', '.immersive-translate', '--'
                ]):
                    old_size = len(cleaned_content)
                    # 查找完整的字符串并替换
                    for quote in ['`', '"', "'"]:
                        full_string = f'{quote}{match}{quote}'
                        if full_string in cleaned_content:
                            cleaned_content = cleaned_content.replace(full_string, f'{quote}{quote}', 1)
                            break
                    removed = old_size - len(cleaned_content)
                    string_removed += removed
            
            if string_removed > 0:
                total_removed += string_removed
                operations.append(f"大型字符串: -{string_removed:,}字符")
            
            # 保存文件
            output_path = f"smart_reverse_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'business_start_pos': business_positions.get(filename, 0),
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行智能反向去重"""
        print("🚀 智能反向去重工具")
        print("💡 策略: 保守的反向查找，保留独特业务代码")
        print("=" * 60)
        
        # 步骤1: 查找独特业务代码
        business_positions = self.find_unique_business_code_from_end()
        
        # 步骤2: 查找公共大块
        common_blocks = self.find_common_large_blocks()
        
        # 步骤3: 应用智能移除
        results = self.apply_smart_removal(business_positions, common_blocks)
        
        return results, business_positions, common_blocks

def main():
    deduplicator = SmartReverseDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 智能反向去重工具")
    print("🧠 保守策略：保留独特业务代码")
    print("🔄 从文件末尾反向查找")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行智能反向去重
    results, business_positions, common_blocks = deduplicator.run()
    
    # 保存报告
    summary = {
        'business_start_positions': business_positions,
        'common_blocks_count': len(common_blocks),
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'business_start_pos': v['business_start_pos'],
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('smart_reverse_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("✅ 智能反向去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 智能反向去重效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    print(f"   找到公共块: {len(common_blocks)} 个")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: smart_reverse_dedup/clean_*.js")
    print(f"   详细报告: smart_reverse_dedup_summary.json")
    
    print(f"\n🎯 智能策略效果:")
    if overall_compression >= 30:
        print(f"   🎉 智能策略非常成功！")
    elif overall_compression >= 15:
        print(f"   ✅ 智能策略效果良好")
    elif overall_compression >= 5:
        print(f"   ⚠️  智能策略有一定效果")
    else:
        print(f"   ❌ 智能策略效果有限")
    
    print(f"\n💡 建议:")
    print(f"   - 检查生成的文件是否保持了核心功能")
    print(f"   - 如果压缩过度，可以调整保守系数(当前80%)")
    print(f"   - 可以基于结果进一步优化策略")

if __name__ == "__main__":
    main()
