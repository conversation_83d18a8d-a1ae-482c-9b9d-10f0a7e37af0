#!/usr/bin/env python3
"""
激进去重工具
基于用户的激进策略：
只要两个文件间有连续100字符重复，就认定前面所有代码都是重复的依赖
"""

import os
import json
from typing import List, Dict, Set, Tuple

class AggressiveDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"✓ 加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_first_business_code_position(self, min_match_length: int = 100) -> Dict[str, int]:
        """找到每个文件中业务代码开始的位置"""
        print(f"\n🔍 查找业务代码开始位置 (基于{min_match_length}字符匹配)...")
        
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        business_start_positions = {}
        
        for i, (file_path, content) in enumerate(files):
            filename = file_names[i]
            print(f"\n  分析: {filename}")
            
            earliest_business_pos = len(content)  # 默认整个文件都是业务代码
            found_matches = []
            
            # 与其他所有文件比较
            for j, (other_path, other_content) in enumerate(files):
                if i == j:
                    continue
                
                other_filename = file_names[j]
                
                # 在当前文件中查找与其他文件的匹配
                for pos in range(0, len(content) - min_match_length + 1, 50):  # 步长50
                    block = content[pos:pos + min_match_length]
                    
                    # 跳过主要是空白或重复字符的块
                    if len(set(block.strip())) < 10:
                        continue
                    
                    if block in other_content:
                        found_matches.append({
                            'pos': pos,
                            'other_file': other_filename,
                            'block_preview': block[:50].replace('\n', ' ')
                        })
                        
                        # 更新最早的业务代码位置
                        if pos < earliest_business_pos:
                            earliest_business_pos = pos
                        
                        print(f"    ✓ 在位置 {pos:,} 找到与 {other_filename} 的匹配")
                        break  # 找到第一个匹配就够了
            
            # 如果找到匹配，业务代码从最早匹配位置开始
            if found_matches:
                business_start_positions[filename] = earliest_business_pos
                print(f"    📍 业务代码开始位置: {earliest_business_pos:,}")
                print(f"    🗑️  可删除依赖代码: {earliest_business_pos:,} 字符 ({earliest_business_pos/len(content)*100:.1f}%)")
            else:
                business_start_positions[filename] = 0
                print(f"    ⚠️  未找到重复块，保留整个文件")
        
        return business_start_positions
    
    def find_additional_duplicates_in_business_code(self, business_positions: Dict[str, int]) -> Dict[str, List[Dict]]:
        """在业务代码部分查找额外的重复块"""
        print(f"\n🔍 在业务代码部分查找额外重复...")
        
        additional_duplicates = {}
        
        for filename, start_pos in business_positions.items():
            if start_pos == 0:
                continue
            
            file_path = None
            for path in self.files_content.keys():
                if os.path.basename(path) == filename:
                    file_path = path
                    break
            
            if not file_path:
                continue
            
            content = self.files_content[file_path]
            business_code = content[start_pos:]
            
            print(f"\n  分析 {filename} 的业务代码部分...")
            duplicates = []
            
            # 查找业务代码中的重复块
            for size in [5000, 2000, 1000, 500]:
                found_count = 0
                
                for pos in range(0, len(business_code) - size + 1, 200):
                    block = business_code[pos:pos + size]
                    
                    if len(block.strip()) < size * 0.3:
                        continue
                    
                    # 检查在其他文件中是否存在
                    found_in = []
                    for other_path, other_content in self.files_content.items():
                        other_filename = os.path.basename(other_path)
                        if other_filename != filename and block in other_content:
                            found_in.append(other_filename)
                    
                    if found_in:
                        duplicates.append({
                            'block': block,
                            'size': size,
                            'pos_in_business': pos,
                            'found_in': found_in
                        })
                        found_count += 1
                        print(f"    ✓ 找到业务代码重复块: {size}字符, 在{len(found_in)}个其他文件中")
                        
                        if found_count >= 3:
                            break
            
            additional_duplicates[filename] = duplicates
        
        return additional_duplicates
    
    def apply_aggressive_removal(self, business_positions: Dict[str, int], 
                               additional_duplicates: Dict[str, List[Dict]]) -> Dict:
        """应用激进移除策略"""
        print(f"\n🛠️  应用激进移除策略...")
        
        os.makedirs("aggressive_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n  处理: {filename}")
            
            start_pos = business_positions.get(filename, 0)
            
            if start_pos > 0:
                # 激进策略：删除开头的依赖代码
                business_code = original_content[start_pos:]
                
                # 保持JS结构完整性：确保以合适的方式开始
                if business_code.strip().startswith('}') or business_code.strip().startswith(')'):
                    # 如果业务代码以}或)开始，保留一些上下文
                    context_size = min(200, start_pos)
                    cleaned_content = original_content[start_pos - context_size:]
                    removed_size = start_pos - context_size
                else:
                    cleaned_content = business_code
                    removed_size = start_pos
                
                operations = [f"前缀依赖: -{removed_size:,}字符"]
                total_removed = removed_size
                
                # 移除业务代码中的额外重复块
                if filename in additional_duplicates:
                    for i, dup in enumerate(additional_duplicates[filename][:5]):  # 最多5个
                        if dup['block'] in cleaned_content:
                            old_size = len(cleaned_content)
                            replacement = f'/* REMOVED_BUSINESS_DUPLICATE_{i} */'
                            cleaned_content = cleaned_content.replace(dup['block'], replacement, 1)
                            removed = old_size - len(cleaned_content)
                            total_removed += removed
                            operations.append(f"业务重复{i}: -{removed:,}字符")
                
            else:
                # 没有找到重复块，只移除业务代码中的重复
                cleaned_content = original_content
                total_removed = 0
                operations = []
                
                if filename in additional_duplicates:
                    for i, dup in enumerate(additional_duplicates[filename][:5]):
                        if dup['block'] in cleaned_content:
                            old_size = len(cleaned_content)
                            replacement = f'/* REMOVED_DUPLICATE_{i} */'
                            cleaned_content = cleaned_content.replace(dup['block'], replacement, 1)
                            removed = old_size - len(cleaned_content)
                            total_removed += removed
                            operations.append(f"重复块{i}: -{removed:,}字符")
            
            # 额外清理：移除明显的大型字符串常量
            large_strings_removed = 0
            for quote in ['`', '"', "'"]:
                # 查找并移除超大字符串
                import re
                pattern = f'{quote}([^{quote}]{{2000,}}){quote}'
                matches = re.findall(pattern, cleaned_content, re.DOTALL)
                for match in matches[:3]:  # 最多移除3个
                    if any(indicator in match[:200] for indicator in [
                        'background-color', 'font-family', '.immersive-translate',
                        '简体中文', 'English', 'function', 'var '
                    ]):
                        old_size = len(cleaned_content)
                        cleaned_content = cleaned_content.replace(f'{quote}{match}{quote}', f'{quote}{quote}')
                        removed = old_size - len(cleaned_content)
                        large_strings_removed += removed
            
            if large_strings_removed > 0:
                total_removed += large_strings_removed
                operations.append(f"大型字符串: -{large_strings_removed:,}字符")
            
            # 保存文件
            output_path = f"aggressive_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'business_start_pos': start_pos,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行激进去重"""
        print("🚀 激进去重工具")
        print("💡 策略: 找到100字符重复块，删除之前的所有依赖代码")
        print("=" * 60)
        
        # 步骤1: 找到业务代码开始位置
        business_positions = self.find_first_business_code_position(100)
        
        # 步骤2: 在业务代码中查找额外重复
        additional_duplicates = self.find_additional_duplicates_in_business_code(business_positions)
        
        # 步骤3: 应用激进移除
        results = self.apply_aggressive_removal(business_positions, additional_duplicates)
        
        return results, business_positions

def main():
    deduplicator = AggressiveDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 激进去重工具")
    print("💥 基于100字符重复块的激进策略")
    print("⚠️  注意: 这是激进策略，可能会移除较多代码")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行激进去重
    results, business_positions = deduplicator.run()
    
    # 保存报告
    summary = {
        'business_start_positions': business_positions,
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'business_start_pos': v['business_start_pos'],
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('aggressive_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("✅ 激进去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 激进去重效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: aggressive_dedup/clean_*.js")
    print(f"   详细报告: aggressive_dedup_summary.json")
    
    print(f"\n🎯 激进策略效果:")
    if overall_compression >= 50:
        print(f"   🎉 激进策略非常成功！大幅减少文件大小")
    elif overall_compression >= 30:
        print(f"   ✅ 激进策略效果良好，显著减少重复")
    elif overall_compression >= 15:
        print(f"   ⚠️  激进策略有一定效果")
    else:
        print(f"   ❌ 激进策略效果有限，可能需要调整参数")
    
    print(f"\n⚠️  注意事项:")
    print(f"   - 请检查生成的文件是否保持了JS语法完整性")
    print(f"   - 如果压缩过度，可以调整min_match_length参数")
    print(f"   - 建议先测试一个文件确认效果")

if __name__ == "__main__":
    main()
