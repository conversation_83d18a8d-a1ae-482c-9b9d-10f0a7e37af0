e",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;`
{j}-new-user-guide`,FM=``;var W0=`${j}-new-user-guide-root``#${W0}`);a&&r.target!==a&&$b(W0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function $b(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Io(e.config.shortcuts.toggleTranslatePage)})``meta[name='${jM}'][content='true''
gn:left;margin-top:-20px;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=`${j}-btn ${j}-cancel-btn``${c}px Arial`,m=this.breakTextIntoLines(t,l-this.H_PADDING);c<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${m}">
TML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-cancel-btn`,d.innerText=s("close"),d.onclick=()=>{i(d,!0)},a.appendChild(d),ha(e,"freeImageError")}function LD(e,t,n,r,a,i){let o=e.rule.imageRule,s=ve.bind(null,e.config
ing":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.cons
rtInfo.emailPlaceholder"),$e.get(dt,null).then(N=>{if(!N)return;let B=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(N.email);N.email&&!B&&(x.value=N.email)});let f=document.createElement("label");f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;col
obalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globa
e.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-cancel-btn`,d.innerText=s("close"),d.onclick=()=>{i(d,!0)},a.appendChild(d),ha(e,"freeImage
omicHash=${n}&domain=${ID(location.hostname)}``<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-canc
"");let p=!0,g=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}N0.call(Ee,Ee.ctx)}async function _y(e,t){let n={};e
[e].length){let t=[...el[e]];el[e]=[],t.forEach(n=>n())}}async function oR(){try{if(X())return;let e=$.runtime.getURL("locales.json"),n=await(await fetch(e)).json();Object.assign(To,n)}catch{}}var sR=document.getElementById("mount");Uw();sR&&(async()=>{let e=await Ot();await oR(),e.debug&&I.setLevel("debug"),_r(C(Qa,{lang:e.interfaceLanguage,children:C($9,{})}),sR)})();})();


if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.setupMouseHoverListener=Jh);let o={...Sn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.documentElement)!==u.state.translationMode&&Na(n.document.documentElement,u.stat
textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div"
p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(N){N.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",
can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=
==""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style
g("url change newRule",r);try{Vo(!1);let a=n.config.generalRule;r&&(n.rule=Rs(a,r),n.rule=await Wp(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await Hm("updateRuleByUrlChange",t.mutationChangeDelay||50),kn("Translated");let i=LL(n);return n.filterRule=Sn(n,!0),IL(i),!0}catch(a){return I.debug(a),!1}}return Rn,!1}var V9=[],HX=new AbortController,{signal:qX}=HX,hd=0,fd=0,$s
itle"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackgrou
=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`htt
Text:s("browser.PdfProFile"),tooltipStyle:{left:"unset",right:-10,transform:"unset",fontSize:13},onClick:()=>{e(Jv,`${r}_btn_pdfpro``${e.type}_trial_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:n=>{e.onOpenUrl(fn,`${e.type}_trial_pro_service``min-select ${t?"":"min-select-no-arrow"} ${r||""}`,value:ef,style:{maxWidth:`${i}
t-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function VQ(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fi
0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanatio
&YX(n,t);function s(){o=!o,o?(I.debug("mouse hover translate on"),n.state.isTranslateDirectlyOnHover=!0,Jh(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,Jh(n,t))}t.document.addEventListener(sc,s),Zi.push(()=>{t.document.removeEventListener(sc,s)});let l=er(m=>{if(!(Math.abs(m.clientX-hd)+Math.abs(m.clientY-fd)<=3)&&(hd=m.clientX,fd=m.clientY,$s&&!Ko&&Q9(n,!1,t),o||$s&&!K
he same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${n("er
+l("browser.translateLocalPdfFile"),value:"translateLocalPdfFile",onClick:()=>{e.onOpenUrl(d.PDF_VIEWER_URL,`${e.type}_more_pdf`)}},{label:"\u2747\uFE0F "+l("browser.PdfProFile"),value:"pdfProFile",onClick:()=>{e.onOpenUrl(d.PDF_PRO_URL,`${e.type}_more_pdfpro``${j}-no-select text-sm text-gray-c2`,onClick:a=>{a.preventDefault(),YC(`${le}docs/CHANGELOG/#${r.replace(/\./ig,"")}``popup-container ${e.classN
\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are 
uide`,FM=``;var W0=`${j}-new-user-guide-root``#${W0}`);a&&r.target!==a&&$b(W0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function $b(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Io(e.config.shortcuts.toggleTranslatePage)})``meta[name='${jM}'][content='true''*+/=?^_`{|}
e,(s,l,u)=>{n+=1;let c=u.id,d=Ll(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=vt.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${Gd}='${r}''"+Da(t.document.activeElement)+"''[contenteditable="true"], [contenteditable=""]''/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r?\n/g,"<BR>"),htmlDecode:(e,t)=>e.replace(/<BR\s*\/?>/gi,t||`\r
`).re
f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style"
t x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      font-size: 14px;
      width: 303px;
      height: 45px;
      box-sizing: border-box;
      margin: 0 1px;
      outline: none;`),x.placeholder=s("reportIn
)}),$t.setScope("mouseHover"),Zi.push(()=>{$t.deleteScope("mouseHover")})}}function GX(){try{Zi.forEach(e=>e())}catch{}Zi=[],$t.setScope("all")}function NL(e){$s=!1}function gd(e,t,n=window){return n.addEventListener(e,t,{signal:qX})}function Xh(e,t,n,r){if(r=r||zL(e.rule,n),!r){I.debug("can not find selection part!");return}if(WX(e,r,t))return;if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.set
color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(N){N.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackgrou
ection part!");return}if(WX(e,r,t))return;if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.setupMouseHoverListener=Jh);let o={...Sn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.documentElement)!==u.state.translation
[C("div",{children:[C("img",{src:Yb,class:"service-icon"}),e("autoEnableSubtitle")]}),C(jg,{})]})})}var V0=`${j}-new-user-guide``${V0}-container`,style:d,children:[C("div",{class:`${V0}-close-icon`,onClick:e,children:C(AM,{})}),C("img",{class:`${V0}-bg ${n}`,src:vQ}),C("div",{class:`${V0}-content ${n}`,children:[C(c,{}),C("div",{class:`${V0}-message text-red-500`,children:[i[r],Re().any&&r==="float-bal
;o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function VQ(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerH
ent.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function VQ(e,t,n,r,a,i,o,s){r.innerHTML="",a.
587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation 
ule",r);try{Vo(!1);let a=n.config.generalRule;r&&(n.rule=Rs(a,r),n.rule=await Wp(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await Hm("updateRuleByUrlChange",t.mutationChangeDelay||50),kn("Translated");let i=LL(n);return n.filterRule=Sn(n,!0),IL(i),!0}catch(a){return I.debug(a),!1}}return Rn,!1}var V9=[],HX=new AbortController,{signal:qX}=HX,hd=0,fd=0,$s=!1,Ko,_L=0,Zi=[];
function VX(e,t,n,r){let a=oC(e,t,n,r);if(a==null)return;if(a instanceof HTMLElement)return a;let i=()=>{let l=r.document.elementFromPoint(e,t);if(!l)return;let u=W1(l,e,t);return u===l?l.nodeName==="BUTTON"?l:void 0:jL(u,n)},o=()=>{try{a.setStartBefore(a.startContainer),a.setEndAfter(a.startContainer)}catch(u){I.debug("get mouse over word fail",u)}let l=a.getBoundingClientRect();if(!(l.left>e||l.right<
`background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${Nn.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="non
l_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:n=>{e.onOpenUrl(fn,`${e.type}_trial_pro_service``min-select ${t?"":"min-select-no-arrow"} ${r||""}`,value:ef,style:{maxWidth:`${i}px``${e.type}_more``\u{1F4AA} ${l(p?"disableNavTranslate":"enableNavTranslate")}``${e.type}_more_babeldoc`)}},{label:"\u{1F4C1} "+l("browser.transla
lick:()=>{e.onOpenUrl(d.PDF_VIEWER_URL,`${e.type}_more_pdf`)}},{label:"\u2747\uFE0F "+l("browser.PdfProFile"),value:"pdfProFile",onClick:()=>{e.onOpenUrl(d.PDF_PRO_URL,`${e.type}_more_pdfpro``${j}-no-select text-sm text-gray-c2`,onClick:a=>{a.preventDefault(),YC(`${le}docs/CHANGELOG/#${r.replace(/\./ig,"")}``popup-container ${e.className||""}``text-sm text-gray-9 mt-4 ml-1 ${u?"":"display-none"}`,childr
.rule,n),!r){I.debug("can not find selection part!");return}if(WX(e,r,t))return;if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.setupMouseHoverListener=Jh);let o={...Sn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.
tart=parseInt(d[1]),m.end=m.start+2e3,m.duration=m.end-m.start,m.content=d[2].replace(/^<\/SYNC[^>]*>/gi,"");let p=!0,g=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typ
ge/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${u}activities/components/image-pro`;var vr=50*1e4,Er=`[${Y}-ctx-divider]`,Cr=`${Y}_context_preview`;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``"+e+"` is not a valid argument for `n-gram``Translate the text to {{to}}, please do not expla
;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=`${j}-btn ${j}-cancel-btn``${c}px Arial`,m=this.breakTextIntoLines(t,l-this.H_PADDING);c<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${m}">${u.message}</span>`)
=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-cancel-btn`,d.innerText=s("close"),d.onclick=()=>{i(d,!0)},a.appendChild(d),ha(e,"freeImageError")}function LD(e,t,n,r,a,i){let o=e.rule.imageRule,s=ve.bind(null,e.config.interfaceLanguage),l=document.createElement("div");l.innerText=s("freeIma
,1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-cancel-btn`,d.innerText=s("close"),d.onclick=()=>{i(d,!0)},a.appendChild(d),ha(e,"freeImageError")}function LD(e,t,n,r,a,i){let o=e.rule.imageRule,s=
{i}px``${e.type}_more``\u{1F4AA} ${l(p?"disableNavTranslate":"enableNavTranslate")}``${e.type}_more_babeldoc`)}},{label:"\u{1F4C1} "+l("browser.translateLocalPdfFile"),value:"translateLocalPdfFile",onClick:()=>{e.onOpenUrl(d.PDF_VIEWER_URL,`${e.type}_more_pdf`)}},{label:"\u2747\uFE0F "+l("browser.PdfProFile"),value:"pdfProFile",onClick:()=>{e.onOpenUrl(d.PDF_PRO_URL,`${e.type}_more_pdfpro``${j}-no-sele
 it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. I
ver translate on"),n.state.isTranslateDirectlyOnHover=!0,Jh(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,Jh(n,t))}t.document.addEventListener(sc,s),Zi.push(()=>{t.document.removeEventListener(sc,s)});let l=er(m=>{if(!(Math.abs(m.clientX-hd)+Math.abs(m.clientY-fd)<=3)&&(hd=m.clientX,fd=m.clientY,$s&&!Ko&&Q9(n,!1,t),o||$s&&!Ko)){let p=zL(n.rule,t);if(p){if(K9(p))return;Xh
return;case"\\":ne(),Ke="identifierNameStartEscape";return;case"}":return It("punctuator",ne());case'"':case"'":xc=ne()==='"''${DF(e)}' at ${ro}:${oa}`)}function os(){return y1(`JSON5: invalid end of input at ${ro}:${oa}`)}function V5(){return oa-=5,y1(`JSON5: invalid identifier character at ${ro}:${oa}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on 
ooltipStyle:{fontSize:13},onClick:()=>{e(Xv,`${r}_btn_text`)}},{title:"PDF Pro",icon:C(S7,{}),tooltipText:s("browser.PdfProFile"),tooltipStyle:{left:"unset",right:-10,transform:"unset",fontSize:13},onClick:()=>{e(Jv,`${r}_btn_pdfpro``${e.type}_trial_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:n=>{e.onOpenUrl(fn,`${e.type}
rance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.t
lass:`${lQ}-img`,src:Ng("images/new_float_ball_intro.png")})}var Og=`${j}-new-user-guide`;function Qb(){let{t:e}=re();return C("div",{class:`${Og}-select-service-guide`,children:C("div",{class:`${Og}-select-service-guide-card`,children:[C("div",{class:`${Og}-max-model`,children:e("translationServicesGroup.pro")}),C("div",{class:`${Og}-model-example``${j}-new-user-guide`;function Jb(){let{t:e}=re();retur
ys(this.apiServiceConfig).length)throw new Z("serivce id not found config");let{text:n,from:r,to:a}=t;if(!this.langMap.has(a))throw new Z(`Unsupported language: ${a}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${r==="auto"?"":D2.get(r)+"-"}${D2.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.t
Color Emoji"'.split(","),serif:'ui-serif,Georgia,Cambria,"Times New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{v
t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}N0.call(Ee,Ee.ctx)}async function _y(e,t){let n={};e&&e.detail?.trigger&&(n.trigger=e.detail.trigger),Ae({key:"share
o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}tex
D(location.hostname)}``<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-cancel-btn`,d.innerText=s
:g})}})},onFrame:()=>{},onIgnoreElement:()=>{}})}function zL(e,t){return VX(hd,fd,e,t)}function VX(e,t,n,r){let a=oC(e,t,n,r);if(a==null)return;if(a instanceof HTMLElement)return a;let i=()=>{let l=r.document.elementFromPoint(e,t);if(!l)return;let u=W1(l,e,t);return u===l?l.nodeName==="BUTTON"?l:void 0:jL(u,n)},o=()=>{try{a.setStartBefore(a.startContainer),a.setEndAfter(a.startContainer)}catch(u){I.debug("
ror-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Qs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Vt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,d=Ll(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=vt.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${Gd}='${r}''"+Da(t.document.activeElement)+"''[contenteditabl
 translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FF
().any&&r==="float-ball"?`
${a("floatBall.longPress")}`:""]})]}),C(Wb,{position:n})]})}var vQ="";var br=`${j}-new-user-guide`,FM=``;var W0=`${j}-new-user-guide-root``#${W0}`);a&&r.target!==a&&$b(W0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function $b(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.curr
00px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h)
n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a
h(e=>e())}catch{}Zi=[],$t.setScope("all")}function NL(e){$s=!1}function gd(e,t,n=window){return n.addEventListener(e,t,{signal:qX})}function Xh(e,t,n,r){if(r=r||zL(e.rule,n),!r){I.debug("can not find selection part!");return}if(WX(e,r,t))return;if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.setupMouseHoverListener=Jh);let o={...Sn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSel
function s(){o=!o,o?(I.debug("mouse hover translate on"),n.state.isTranslateDirectlyOnHover=!0,Jh(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,Jh(n,t))}t.document.addEventListener(sc,s),Zi.push(()=>{t.document.removeEventListener(sc,s)});let l=er(m=>{if(!(Math.abs(m.clientX-hd)+Math.abs(m.clientY-fd)<=3)&&(hd=m.clientX,fd=m.clientY,$s&&!Ko&&Q9(n,!1,t),o||$s&&!Ko)){let p=zL(n.r
pro``${e.type}_trial_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:n=>{e.onOpenUrl(fn,`${e.type}_trial_pro_service``min-select ${t?"":"min-select-no-arrow"} ${r||""}`,value:ef,style:{maxWidth:`${i}px``${e.type}_more``\u{1F4AA} ${l(p?"disableNavTranslate":"enableNavTranslate")}``${e.type}_more_babeldoc`)}},{label:"\u{1F4C1} "
is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO not
n}if(WX(e,r,t))return;if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.setupMouseHoverListener=Jh);let o={...Sn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.documentElement)!==u.state.translationMode&&Na(n.document.
 of input at ${ro}:${oa}`)}function V5(){return oa-=5,y1(`JSON5: invalid identifier character at ${ro}:${oa}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can als
{p}/`:`https://test-onboarding.${p}/`,Z=`https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTrans
ength)return r.forEach(a=>{QX(e,a)}),!0}function KX(e){let t=[e];if(e.nodeName=="FONT"&&e.className.includes(j)){let n=e.closest(`.${j}-target-wrapper`);n&&(t=[n.parentElement])}else{let r=[...e.querySelectorAll(`.${j}-target-wrapper``fingers.${t.generalRule[r[n]]}``${r}_btn_document`)}},{title:s("widget.text"),icon:C(f7,{}),tooltipText:s("widget.textTooltip",{shortcut:Re().any?"":`(${i.shortcuts.toggle
.border};
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportI
udeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.documentElement)!==u.state.translationMode&&Na(n.document.documentElement,u.state.translationMode),qs(l,c,!0,"hover").then(m=>{if(i.autoIncreaseParagraphId=l.autoIncreaseParagraphId,i.paragraphEntities=l.paragraphEntities,i.paragraphQueue=l.paragraphQueue,m&
 ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(N){N.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-ra
label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(N){N.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("
h){let t=[...el[e]];el[e]=[],t.forEach(n=>n())}}async function oR(){try{if(X())return;let e=$.runtime.getURL("locales.json"),n=await(await fetch(e)).json();Object.assign(To,n)}catch{}}var sR=document.getElementById("mount");Uw();sR&&(async()=>{let e=await Ot();await oR(),e.debug&&I.setLevel("debug"),_r(C(Qa,{lang:e.interfaceLanguage,children:C($9,{})}),sR)})();})();


cument.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${
er("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("mess
https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatu
`${j}-new-user-guide`;function Kb(){return C("img",{class:`${lQ}-img`,src:Ng("images/new_float_ball_intro.png")})}var Og=`${j}-new-user-guide`;function Qb(){let{t:e}=re();return C("div",{class:`${Og}-select-service-guide`,children:C("div",{class:`${Og}-select-service-guide-card`,children:[C("div",{class:`${Og}-max-model`,children:e("translationServicesGroup.pro")}),C("div",{class:`${Og}-model-example``$
e(/^<\/SYNC[^>]*>/gi,"");let p=!0,g=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}N0.call(Ee,Ee.ctx)}async functi
udeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.documentElement)!==u.state.translationMode&&Na(n.document.documentElement,u.state.translationMode),qs(l,c,!0,"hover").then(m=>{if(i.autoIncreaseParagraphId=l.autoIncreaseParagraphId,i.paragraphEntitie
mage'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let
ext),Qs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function LI(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Qs),a=[],i=[],o=Fc(e,"");for(let s of r){let l=Qs[s];if(!l.ok){let c=docume
tn ${j}-cancel-btn`,d.innerText=s("close"),d.onclick=()=>{i(d,!0)},a.appendChild(d),ha(e,"freeImageError")}function LD(e,t,n,r,a,i){let o=e.rule.imageRule,s=ve.bind(null,e.config.interfaceLanguage),l=document.createElement("div");l.innerText=s("freeImage.title"),l.setAttribute("style","text-align:left;margin-top:-20px;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang
747\uFE0F "+l("browser.PdfProFile"),value:"pdfProFile",onClick:()=>{e.onOpenUrl(d.PDF_PRO_URL,`${e.type}_more_pdfpro``${j}-no-select text-sm text-gray-c2`,onClick:a=>{a.preventDefault(),YC(`${le}docs/CHANGELOG/#${r.replace(/\./ig,"")}``popup-container ${e.className||""}``text-sm text-gray-9 mt-4 ml-1 ${u?"":"display-none"}`,children:u}),C("div",{class:`text-sm text-gray-9 mt-4 ml-1 ${i?"":"display-none"
path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=documen
ign: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awg
<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${m}">${u.message}</span>`),Qs[m]={ok:!1,sentence:d},RI(l,e,t,n,u)):c&&(g.innerHTML=vt.sanitize(c.text),Qs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function LI(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(J
path="url(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=docu
-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(N){N.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackground};
      color: ${
,":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){retur
{let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;Rn;let r=n.config.rules?.find(a=>Po(e.currentUrl,a));if(n.rule.id!=r?.id){I.debug("new rule.id",r?.id,"old id",n.rule.id),I.debug("url change newRule",r);try{Vo(!1);let a=n.config.generalRule;r&&(n.rule=Rs(a,r),n.rule=await Wp(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await Hm("updateRuleByUrlChange",t.mut
eralRule[r[n]]}``${r}_btn_document`)}},{title:s("widget.text"),icon:C(f7,{}),tooltipText:s("widget.textTooltip",{shortcut:Re().any?"":`(${i.shortcuts.toggleSidePanel})`}),tooltipStyle:{fontSize:13},onClick:()=>{e(Xv,`${r}_btn_text`)}},{title:"PDF Pro",icon:C(S7,{}),tooltipText:s("browser.PdfProFile"),tooltipStyle:{left:"unset",right:-10,transform:"unset",fontSize:13},onClick:()=>{e(Jv,`${r}_btn_pdfpro`
WER_URL,`${e.type}_more_pdf`)}},{label:"\u2747\uFE0F "+l("browser.PdfProFile"),value:"pdfProFile",onClick:()=>{e.onOpenUrl(d.PDF_PRO_URL,`${e.type}_more_pdfpro``${j}-no-select text-sm text-gray-c2`,onClick:a=>{a.preventDefault(),YC(`${le}docs/CHANGELOG/#${r.replace(/\./ig,"")}``popup-container ${e.className||""}``text-sm text-gray-9 mt-4 ml-1 ${u?"":"display-none"}`,children:u}),C("div",{class:`text-sm
ons or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \
ePage)})``meta[name='${jM}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(N){return l.test(N.trim())}let c=()=>p.value.trim()===""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("styl
").replace(/\*/g,"%2A")}async signedRequest({secretId:t,secretKey:n,action:r,payload:a,service:i,version:o}){let s=new Date().toISOString(),l=Math.random().toString(36).slice(2),u={Action:r,Version:o,Format:"JSON",AccessKeyId:t,SignatureNonce:l,Timestamp:s,SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0"},d=(h=>{let f=Object.keys(h).sort().map(y=>`${this.encode(y)}=${this.encode(h[y])}`).join("&");r
bute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function VQ(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/
or:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,
tcuts.toggleSidePanel})`}),tooltipStyle:{fontSize:13},onClick:()=>{e(Xv,`${r}_btn_text`)}},{title:"PDF Pro",icon:C(S7,{}),tooltipText:s("browser.PdfProFile"),tooltipStyle:{left:"unset",right:-10,transform:"unset",fontSize:13},onClick:()=>{e(Jv,`${r}_btn_pdfpro``${e.type}_trial_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:
mes New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-web
}=re();return C("div",{class:`${PM}-video-subtitle-guide`,children:C("div",{class:`${PM}-video-subtitle-guide-card`,children:C("div",{children:[C("img",{src:Zb,class:"service-icon"}),e("subtitle.quickButton.requestAiSubtitle")]})})})}var LM=`${j}-new-user-guide`;function Xb(){let{t:e}=re();return C("div",{class:`${LM}-video-subtitle-guide`,children:C("div",{class:`${LM}-video-subtitle-guide-card`,childr
 Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u51
son"}}),ys("reportActive","1")}catch(e){I.error(e)}}function BL(e,t){let n=e.rule?.subtitleRule,r=n?.autoEnableSubtitle,a=n?.enableTriggerTranslate||n?.liveSubtitleRule?.enableTriggerTranslate;!r&&a&&document.dispatchEvent(new CustomEvent(cl,{detail:{tempEnableSubtitle:t}}))}async function UX(e){let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;Rn;let r=n.config.rules?.find(a=>Po(e.currentUrl
ngPress")}`:""]})]}),C(Wb,{position:n})]})}var vQ="";var br=`${j}-new-user-guide`,FM=``;var W0=`${j}-new-user-guide-root``#${W0}`);a&&r.target!==a&&$b(W0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function $b(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Io(e.config.shortc
(()=>{var pR=Object.defineProperty;var c6=(e,t)=>{for(var n in t)pR(e,n,{get:t[n],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.ad
ument.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Qs),a=[],i=[],o=Fc(e,"");for(let s of r){let l=Qs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Qs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Vt({sentences:i},e,(s,l,u)=>{n+=
}`)}function V5(){return oa-=5,y1(`JSON5: invalid identifier character at ${ro}:${oa}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick
ansform:"unset",fontSize:13},onClick:()=>{e(Jv,`${r}_btn_pdfpro``${e.type}_trial_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:n=>{e.onOpenUrl(fn,`${e.type}_trial_pro_service``min-select ${t?"":"min-select-no-arrow"} ${r||""}`,value:ef,style:{maxWidth:`${i}px``${e.type}_more``\u{1F4AA} ${l(p?"disableNavTranslate":"enableNavT
t p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAtt
Event(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Qs),a=[],i=[],o=Fc(e,"");for(let s of r){let l=Qs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Qs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Vt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.i
}`:""]})]}),C(Wb,{position:n})]})}var vQ="";var br=`${j}-new-user-guide`,FM=``;var W0=`${j}-new-user-guide-root``#${W0}`);a&&r.target!==a&&$b(W0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function $b(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Io(e.config.shortcuts.toggleTransl
?.enableTriggerTranslate||n?.liveSubtitleRule?.enableTriggerTranslate;!r&&a&&document.dispatchEvent(new CustomEvent(cl,{detail:{tempEnableSubtitle:t}}))}async function UX(e){let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;Rn;let r=n.config.rules?.find(a=>Po(e.currentUrl,a));if(n.rule.id!=r?.id){I.debug("new rule.id",r?.id,"old id",n.rule.id),I.debug("url change newRule",r);try{Vo(!1);let
emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`marg
e provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u