#!/usr/bin/env python3
"""
反向去重工具
从文件结尾开始往上查找重复，保留业务代码
"""

import os
import json
from typing import List, Dict, Set, Tuple

class ReverseDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"✓ 加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_business_code_from_end(self, min_match_length: int = 50) -> Dict[str, int]:
        """从文件末尾开始查找业务代码的开始位置"""
        print(f"\n🔍 从文件末尾反向查找业务代码开始位置...")
        
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        business_start_positions = {}
        
        for i, (file_path, content) in enumerate(files):
            filename = file_names[i]
            print(f"\n  分析: {filename}")
            
            # 从文件末尾开始，向前查找
            file_length = len(content)
            business_start_pos = 0  # 默认整个文件都是业务代码
            
            # 从文件末尾开始，每次检查一个块
            for end_pos in range(file_length, min_match_length, -500):  # 步长500，向前搜索
                start_pos = max(0, end_pos - min_match_length)
                block = content[start_pos:end_pos]
                
                # 跳过主要是空白的块
                if len(block.strip()) < min_match_length * 0.3:
                    continue
                
                # 检查这个块是否在其他文件中存在
                found_in_others = []
                for j, (other_path, other_content) in enumerate(files):
                    if i == j:
                        continue
                    
                    other_filename = file_names[j]
                    if block in other_content:
                        found_in_others.append(other_filename)
                
                # 如果在其他文件中找到了这个块，说明这部分是重复的依赖代码
                if found_in_others:
                    print(f"    ✓ 在位置 {start_pos:,}-{end_pos:,} 找到重复块")
                    print(f"      重复出现在: {', '.join(found_in_others)}")
                    
                    # 继续向前查找，找到重复代码的真正开始位置
                    # 使用更大的块来确认
                    for larger_start in range(start_pos, -1, -1000):  # 向前扩展查找
                        larger_block = content[larger_start:end_pos]
                        
                        # 检查这个更大的块是否也重复
                        still_duplicate = False
                        for other_path, other_content in files:
                            if other_path != file_path and larger_block in other_content:
                                still_duplicate = True
                                break
                        
                        if still_duplicate:
                            business_start_pos = end_pos  # 业务代码从这个重复块之后开始
                            print(f"    📍 扩展重复区域到位置 {larger_start:,}")
                        else:
                            break
                    
                    break  # 找到第一个重复块就停止
            
            business_start_positions[filename] = business_start_pos
            
            if business_start_pos > 0:
                dependency_size = business_start_pos
                business_size = file_length - business_start_pos
                print(f"    📊 依赖代码: {dependency_size:,} 字符 ({dependency_size/file_length*100:.1f}%)")
                print(f"    📊 业务代码: {business_size:,} 字符 ({business_size/file_length*100:.1f}%)")
            else:
                print(f"    ⚠️  未找到重复块，保留整个文件")
        
        return business_start_positions
    
    def find_large_duplicate_strings(self) -> List[Dict]:
        """查找大型重复字符串"""
        print(f"\n🔍 查找大型重复字符串...")
        
        all_large_strings = []
        
        # 从所有文件中提取大型字符串
        for file_path, content in self.files_content.items():
            filename = os.path.basename(file_path)
            
            # 查找大型字符串字面量
            for quote in ['`', '"', "'"]:
                i = 0
                while i < len(content):
                    if content[i] == quote:
                        start = i + 1
                        i += 1
                        
                        # 查找字符串结束
                        while i < len(content):
                            if content[i] == quote and (i == 0 or content[i-1] != '\\'):
                                string_content = content[start:i]
                                if len(string_content) >= 500:  # 500字符以上的字符串
                                    all_large_strings.append({
                                        'content': string_content,
                                        'size': len(string_content),
                                        'file': filename,
                                        'quote': quote,
                                        'full': content[start-1:i+1]
                                    })
                                break
                            i += 1
                    else:
                        i += 1
        
        # 查找重复的字符串
        duplicate_strings = []
        processed_contents = set()
        
        for string_info in all_large_strings:
            content = string_info['content']
            if content in processed_contents:
                continue
            
            # 查找这个字符串在多少个文件中出现
            files_with_string = []
            for file_path, file_content in self.files_content.items():
                filename = os.path.basename(file_path)
                if content in file_content:
                    files_with_string.append(filename)
            
            if len(files_with_string) >= 2:
                duplicate_strings.append({
                    'content': content,
                    'size': len(content),
                    'files': files_with_string,
                    'quote': string_info['quote']
                })
                processed_contents.add(content)
        
        # 按大小排序
        duplicate_strings.sort(key=lambda x: x['size'], reverse=True)
        
        print(f"  📊 找到 {len(duplicate_strings)} 个重复字符串")
        for i, ds in enumerate(duplicate_strings[:10]):
            print(f"    {i+1}. {ds['size']:,}字符, {len(ds['files'])}个文件")
        
        return duplicate_strings
    
    def apply_reverse_removal(self, business_positions: Dict[str, int], 
                            duplicate_strings: List[Dict]) -> Dict:
        """应用反向移除策略"""
        print(f"\n🛠️  应用反向移除策略...")
        
        os.makedirs("reverse_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n  处理: {filename}")
            
            business_start_pos = business_positions.get(filename, 0)
            
            # 策略1: 移除前面的依赖代码
            if business_start_pos > 0:
                # 保留业务代码部分
                cleaned_content = original_content[business_start_pos:]
                
                # 检查是否需要保留一些上下文以保持语法完整性
                if cleaned_content.strip().startswith('}') or cleaned_content.strip().startswith(')'):
                    # 保留一些上下文
                    context_size = min(500, business_start_pos)
                    cleaned_content = original_content[business_start_pos - context_size:]
                    removed_dependency = business_start_pos - context_size
                else:
                    removed_dependency = business_start_pos
                
                operations = [f"前置依赖: -{removed_dependency:,}字符"]
                total_removed = removed_dependency
            else:
                cleaned_content = original_content
                total_removed = 0
                operations = []
            
            # 策略2: 移除重复的大型字符串
            string_removed = 0
            for i, ds in enumerate(duplicate_strings[:15]):  # 处理前15个最大的
                if filename in ds['files']:
                    quote = ds['quote']
                    full_string = f"{quote}{ds['content']}{quote}"
                    
                    if full_string in cleaned_content:
                        old_size = len(cleaned_content)
                        # 替换为空字符串，保持引号
                        cleaned_content = cleaned_content.replace(full_string, f"{quote}{quote}", 1)
                        removed = old_size - len(cleaned_content)
                        string_removed += removed
            
            if string_removed > 0:
                total_removed += string_removed
                operations.append(f"重复字符串: -{string_removed:,}字符")
            
            # 策略3: 额外清理明显的重复模式
            extra_removed = 0
            
            # 移除重复的函数定义
            import re
            
            # 查找重复的大型对象/数组定义
            large_objects = re.findall(r'\{[^{}]{1000,}\}', cleaned_content)
            for obj in large_objects[:3]:
                # 检查这个对象是否在其他文件中也存在
                appears_in_others = False
                for other_path, other_content in self.files_content.items():
                    other_filename = os.path.basename(other_path)
                    if other_filename != filename and obj in other_content:
                        appears_in_others = True
                        break
                
                if appears_in_others:
                    old_size = len(cleaned_content)
                    cleaned_content = cleaned_content.replace(obj, '{}', 1)
                    removed = old_size - len(cleaned_content)
                    extra_removed += removed
            
            if extra_removed > 0:
                total_removed += extra_removed
                operations.append(f"重复对象: -{extra_removed:,}字符")
            
            # 保存文件
            output_path = f"reverse_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'business_start_pos': business_start_pos,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行反向去重"""
        print("🚀 反向去重工具")
        print("💡 策略: 从文件末尾开始查找，保留业务代码")
        print("=" * 60)
        
        # 步骤1: 从末尾查找业务代码开始位置
        business_positions = self.find_business_code_from_end(50)
        
        # 步骤2: 查找大型重复字符串
        duplicate_strings = self.find_large_duplicate_strings()
        
        # 步骤3: 应用反向移除
        results = self.apply_reverse_removal(business_positions, duplicate_strings)
        
        return results, business_positions, duplicate_strings

def main():
    deduplicator = ReverseDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 反向去重工具")
    print("🔄 从文件末尾开始反向查找重复")
    print("💼 保留业务代码，移除依赖代码")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行反向去重
    results, business_positions, duplicate_strings = deduplicator.run()
    
    # 保存报告
    summary = {
        'business_start_positions': business_positions,
        'duplicate_strings_count': len(duplicate_strings),
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'business_start_pos': v['business_start_pos'],
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('reverse_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("✅ 反向去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 反向去重效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    print(f"   找到重复字符串: {len(duplicate_strings)} 个")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: reverse_dedup/clean_*.js")
    print(f"   详细报告: reverse_dedup_summary.json")
    
    print(f"\n🎯 反向策略效果:")
    if overall_compression >= 40:
        print(f"   🎉 反向策略非常成功！")
    elif overall_compression >= 20:
        print(f"   ✅ 反向策略效果良好")
    elif overall_compression >= 10:
        print(f"   ⚠️  反向策略有一定效果")
    else:
        print(f"   ❌ 反向策略效果有限")

if __name__ == "__main__":
    main()
