#!/usr/bin/env python3
"""
基于行的去重工具
从文件结尾开始，查找连续三行在所有文件中都出现的代码
删除这三行之前的所有代码
"""

import os
import json
from typing import List, Dict, Set, Tuple

class LineBasedDeduplicator:
    def __init__(self):
        self.files_content = {}
        self.files_lines = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件并按行分割"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    self.files_content[file_path] = content
                    self.files_lines[file_path] = lines
                    
                    filename = os.path.basename(file_path)
                    print(f"✓ 加载: {filename} ({len(content):,} 字符, {len(lines)} 行)")
    
    def find_common_three_lines_from_end(self) -> Dict[str, int]:
        """从文件末尾开始查找连续三行在所有文件中都出现的位置"""
        print(f"\n🔍 从文件末尾查找连续三行重复...")
        
        files = list(self.files_lines.items())
        file_names = [os.path.basename(path) for path, _ in files]
        business_start_lines = {}
        
        for i, (file_path, lines) in enumerate(files):
            filename = file_names[i]
            print(f"\n  分析: {filename} ({len(lines)} 行)")
            
            business_start_line = 0  # 默认从第一行开始都是业务代码
            
            # 从文件末尾开始，每次检查连续三行
            for end_line in range(len(lines), 2, -1):  # 至少需要3行
                start_line = end_line - 3
                if start_line < 0:
                    break
                
                # 获取连续三行
                three_lines = lines[start_line:end_line]
                
                # 跳过主要是空行或注释的三行
                non_empty_lines = [line.strip() for line in three_lines if line.strip() and not line.strip().startswith('//')]
                if len(non_empty_lines) < 2:  # 至少要有2行有效代码
                    continue
                
                # 检查这三行是否在所有其他文件中都存在
                found_in_all_others = True
                found_in_files = [filename]
                
                for j, (other_path, other_lines) in enumerate(files):
                    if i == j:
                        continue
                    
                    other_filename = file_names[j]
                    
                    # 在其他文件中查找这连续三行
                    found_in_this_file = False
                    for k in range(len(other_lines) - 2):
                        if other_lines[k:k+3] == three_lines:
                            found_in_this_file = True
                            break
                    
                    if found_in_this_file:
                        found_in_files.append(other_filename)
                    else:
                        found_in_all_others = False
                        break
                
                # 如果在所有其他文件中都找到了这三行
                if found_in_all_others and len(found_in_files) >= len(files):
                    business_start_line = end_line  # 业务代码从这三行之后开始
                    print(f"    ✓ 在第 {start_line+1}-{end_line} 行找到公共三行:")
                    for idx, line in enumerate(three_lines):
                        preview = line.strip()[:60]
                        print(f"      {start_line+idx+1}: {preview}")
                    print(f"    📍 业务代码从第 {business_start_line+1} 行开始")
                    break  # 找到第一个匹配就停止
            
            business_start_lines[filename] = business_start_line
            
            if business_start_line > 0:
                dependency_lines = business_start_line
                business_lines = len(lines) - business_start_line
                print(f"    📊 依赖代码: {dependency_lines} 行 ({dependency_lines/len(lines)*100:.1f}%)")
                print(f"    📊 业务代码: {business_lines} 行 ({business_lines/len(lines)*100:.1f}%)")
            else:
                print(f"    ⚠️  未找到公共三行，保留整个文件")
        
        return business_start_lines
    
    def find_additional_duplicate_lines(self) -> List[Dict]:
        """查找额外的重复行块"""
        print(f"\n🔍 查找额外的重复行块...")
        
        duplicate_blocks = []
        
        # 查找在多个文件中出现的大块连续行
        files = list(self.files_lines.items())
        file_names = [os.path.basename(path) for path, _ in files]
        
        # 使用第一个文件作为基准
        base_path, base_lines = files[0]
        base_name = file_names[0]
        
        # 查找不同大小的行块
        for block_size in [20, 10, 5]:
            print(f"  检查 {block_size} 行块...")
            found_count = 0
            
            for start in range(0, len(base_lines) - block_size + 1, 5):  # 步长5
                block_lines = base_lines[start:start + block_size]
                
                # 跳过主要是空行的块
                non_empty = [line for line in block_lines if line.strip()]
                if len(non_empty) < block_size // 2:
                    continue
                
                # 检查在其他文件中的存在
                found_in = [base_name]
                for i, (_, other_lines) in enumerate(files[1:], 1):
                    # 查找这个行块是否在其他文件中存在
                    for j in range(len(other_lines) - block_size + 1):
                        if other_lines[j:j + block_size] == block_lines:
                            found_in.append(file_names[i])
                            break
                
                # 如果在至少一半的文件中找到
                if len(found_in) >= len(files) // 2:
                    duplicate_blocks.append({
                        'lines': block_lines,
                        'size': block_size,
                        'files': found_in,
                        'start_line': start
                    })
                    found_count += 1
                    print(f"    ✓ 找到 {block_size} 行重复块: {len(found_in)}个文件")
                    
                    if found_count >= 3:  # 限制数量
                        break
        
        print(f"  📊 找到 {len(duplicate_blocks)} 个重复行块")
        return duplicate_blocks
    
    def apply_line_based_removal(self, business_start_lines: Dict[str, int], 
                                duplicate_blocks: List[Dict]) -> Dict:
        """应用基于行的移除策略"""
        print(f"\n🛠️  应用基于行的移除策略...")
        
        os.makedirs("line_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            lines = self.files_lines[file_path]
            print(f"\n  处理: {filename}")
            
            business_start_line = business_start_lines.get(filename, 0)
            operations = []
            total_removed_chars = 0
            
            # 策略1: 移除前置依赖行
            if business_start_line > 0:
                # 保留业务代码部分
                business_lines = lines[business_start_line:]
                cleaned_content = '\n'.join(business_lines)
                
                # 计算移除的字符数
                dependency_content = '\n'.join(lines[:business_start_line])
                removed_chars = len(dependency_content)
                total_removed_chars += removed_chars
                operations.append(f"前置依赖: -{business_start_line}行 ({removed_chars:,}字符)")
            else:
                cleaned_content = original_content
            
            # 策略2: 移除重复行块
            for i, block in enumerate(duplicate_blocks[:5]):  # 最多处理5个
                if filename in block['files']:
                    block_text = '\n'.join(block['lines'])
                    if block_text in cleaned_content:
                        old_size = len(cleaned_content)
                        replacement = f'/* REMOVED_DUPLICATE_LINES_BLOCK_{i} */'
                        cleaned_content = cleaned_content.replace(block_text, replacement, 1)
                        removed = old_size - len(cleaned_content)
                        total_removed_chars += removed
                        operations.append(f"重复行块{i}: -{block['size']}行 ({removed:,}字符)")
            
            # 策略3: 移除明显的大型字符串（保持简单）
            import re
            large_string_removed = 0
            
            # 查找并移除超大的字符串字面量
            for quote in ['`', '"', "'"]:
                pattern = f'{quote}([^{quote}]{{1000,}}){quote}'
                matches = re.findall(pattern, cleaned_content, re.DOTALL)
                for match in matches[:3]:  # 最多移除3个
                    if any(indicator in match[:100] for indicator in [
                        'background-color', 'font-family', '.immersive-translate'
                    ]):
                        old_size = len(cleaned_content)
                        cleaned_content = cleaned_content.replace(f'{quote}{match}{quote}', f'{quote}{quote}')
                        removed = old_size - len(cleaned_content)
                        large_string_removed += removed
            
            if large_string_removed > 0:
                total_removed_chars += large_string_removed
                operations.append(f"大型字符串: -{large_string_removed:,}字符")
            
            # 保存文件
            output_path = f"line_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed_chars,
                'business_start_line': business_start_line,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行基于行的去重"""
        print("🚀 基于行的去重工具")
        print("💡 策略: 从末尾查找连续三行重复，删除之前的依赖代码")
        print("=" * 60)
        
        # 步骤1: 查找公共三行
        business_start_lines = self.find_common_three_lines_from_end()
        
        # 步骤2: 查找额外重复行块
        duplicate_blocks = self.find_additional_duplicate_lines()
        
        # 步骤3: 应用移除
        results = self.apply_line_based_removal(business_start_lines, duplicate_blocks)
        
        return results, business_start_lines, duplicate_blocks

def main():
    deduplicator = LineBasedDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 基于行的去重工具")
    print("📝 高效的行级匹配策略")
    print("🔄 从文件末尾反向查找连续三行重复")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行基于行的去重
    results, business_start_lines, duplicate_blocks = deduplicator.run()
    
    # 保存报告
    summary = {
        'business_start_lines': business_start_lines,
        'duplicate_blocks_count': len(duplicate_blocks),
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'business_start_line': v['business_start_line'],
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('line_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("✅ 基于行的去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 基于行的去重效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    print(f"   找到重复行块: {len(duplicate_blocks)} 个")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: line_dedup/clean_*.js")
    print(f"   详细报告: line_dedup_summary.json")
    
    print(f"\n🎯 基于行策略效果:")
    if overall_compression >= 40:
        print(f"   🎉 基于行策略非常成功！")
    elif overall_compression >= 20:
        print(f"   ✅ 基于行策略效果良好")
    elif overall_compression >= 10:
        print(f"   ⚠️  基于行策略有一定效果")
    else:
        print(f"   ❌ 基于行策略效果有限")
    
    print(f"\n💡 优势:")
    print(f"   - 运行速度快，基于行匹配")
    print(f"   - 保持代码结构完整性")
    print(f"   - 精确识别依赖和业务代码边界")

if __name__ == "__main__":
    main()
