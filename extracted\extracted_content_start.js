(()=>{var Mp=Object.defineProperty;var Iu=(e,t)=>{for(var a in t)Mp(e,a,{get:t[a],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`${globalThis.location.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")||(e=`${globalThis.location.protocol}//${r}``\\x1B[${r.join(";")}m`,close:`\\x1B[${e}m`,regexp:new RegExp(`\\\\x1b\\\\[${e}m`,"g")}}function w(r,e){return ie?`${e.open}${r.replace(e.regexp,e.open)}${e.close}``https://config.${p}/`,rt=`https://app.${p}/`,u=S()||N()?`https://${p}/`:`https://test.${p}/`,G=`https://dash.${p}/`,ot=S()||N()?`https://api2.${p}/`:`https://test-api2.${p}/`,at=S()||N()?`https://ai.${p}/`:`https://test-ai.${p}/`,it=`https://assets.${le}.cn/`,ue=u+"accounts/login?from=plugin",X=u+"profile/",m=u+"auth/pricing/",x=u+"pricing/";Q()&&(m=u+"accounts/safari-iap/",x=u+"accounts/safari-iap/");var st=S()?`https://onboarding.${p}/`:`https://test-onboarding.${p}/`,Z=`https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${u}activities/components/image-pro`;var vr=50*1e4,Er=`[${Y}-ctx-divider]`,Cr=`${Y}_context_preview`;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+a("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${a("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${a("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${a("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${a("error.gemini.429")}<br/><br/> ${o}`:o=`${a("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${a("error.claude.403")}<br/><br/>${o}`:o=`${a("error.403")}<br/><br/>${o}`:this.status===400?o=`${a("error.400")}<br/><br/> ${o}`:this.status===502?o=`${a("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${a("error.subscriptionExpired")}<br/><br/> ${o}`,n="setting",r="configError",i=a("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${a("error.azure.401")}<br/><br/> ${o}`),{type:r,title:i,errMsg:o,action:n}}handleFetchError(t){let a=Ye.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let n=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${a("mangaQuotaError.solvedTitle")}<br/></br>
        ${r.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${n("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((p,x)=>`${x+1}. ${p}`).join("<br/>")}``Exceeded max retry count (${a})``${a.split("base64_""",r="";return a&&a.length===3&&(n=a[1],r=a[2]),{mimeType:n,base64:r}}var f0=Object.prototype.toString;function fi(e){switch(f0.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ot(e,Error)}}function sr(e,t){return f0.call(e)===`[object ${t}]``[${d[0]}="${d[1]}"]`)});else if(a.id&&n.push(`#${a.id}`),r=a.className,r&&ra(r))for(i=r.split(/\s+/),u=0;u<i.length;u++)n.push(`.${i[u]}`);let l=["type","name","title","alt"];for(u=0;u<l.length;u++)o=l[u],s=a.getAttribute(o),s&&n.push(`[${o}="${s}"]`);return n.join("")}function A0(){try{return location.href}catch{return""}}var _e=class extends Error{constructor(a){super(a);this.message=a;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var hy=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function fy(e){return e==="http"||e==="https"}function wn(e,t=!1){let{host:a,path:n,pass:r,port:i,projectId:o,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&r?`:${r}`:""}@${a}${i?`:${i}`:""}/${n&&`${n}/`}${o}`}function by(e){let t=hy.exec(e);if(!t)throw new _e(`Invalid Sentry Dsn: ${e}``Invalid Sentry Dsn: ${String(i)} missing`)}),!a.match(/^\d+$/))throw new _e(`Invalid Sentry Dsn: Invalid projectId ${a}`);if(!fy(n))throw new _e(`Invalid Sentry Dsn: Invalid protocol ${n}`);if(t&&isNaN(parseInt(t,10)))throw new _e(`Invalid Sentry Dsn: Invalid port ${t}``${e.substr(0,t)}...``${encodeURIComponent(t)}=${encodeURIComponent(e[t])}``
``Error while triggering instrumentation handler.
Type: ${e}
Name: ${Ft(a)}
Error:``${n.type}: ${n.value}``**non-serializable** (${n})``[Function: ${Ft(t)}]`:typeof t=="symbol"?`[${String(t)}]``
${JSON.stringify(s)}
``${e}`,10);if(!isNaN(a))return a*1e3;let n=Date.parse(`${e}``${t.did}``${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string""function""number""function")i(a);else{let u=s({...a},n);!1&&s.id&&u===null&&te.log(``${e.protocol}:`:"",a=e.port?`:${e.port}`:"";return`${t}//${e.host}${a}${e.path?`/${e.path}`:""}/api/`}function Hy(e){return`${Gy(e)}${e.projectId}/envelope/`}function Ky(e,t){return D0({sentry_key:e.publicKey,sentry_version:qy,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${a.name}`))}),t}var mm="Not capturing exception because it''s not included in the random sample (sampling rate = ${i})`))):this._prepareEvent(t,a,n).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new _e("An event processor returned null, will not send event.");if(a.data&&a.data.__sentry__===!0||o||!r)return s;let c=r(s,a);return Vy(c)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new _e("`beforeSend` returned `null`, will not send event.""Error while sending event:",a)}):!1&&te.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(a=>{let[n,r]=a.split(":");return{reason:n,category:r,quantity:t[a]}})}};function Vy(e){let t="`beforeSend` method has to return `null` or a valid event.";if(xn(e))return e.then(a=>{if(!(It(a)||a===null))throw new _e(t);return a},a=>{throw new _e(`beforeSend rejected with ${a}``Sentry responded with status code ${d.statusCode} to sent event.``${a}`,`${t}: ${a}`]}catch{return!1&&te.error(`Cannot extract message for event ${sa(e)}`),[]}return[]}function n3(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function r3(e=[]){for(let t=e.length-1;t>=0;t--){let a=e[t];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}function Si(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?r3(t):null}catch{return!1&&te.error(`Cannot extract url for event ${sa(e)}`),null}}function Ks(e,t){let a=Ws(e,t),n={type:t&&t.name,value:l3(t)};return a.length&&(n.stacktrace={frames:a}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function o3(e,t,a,n){let r={exception:{values:[{type:lr(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:`Non-Error ${n?"promise rejection":"exception"} captured with keys: ${C0(t)}``${s}: ${o.message}`:s;i=Hs(e,u,a,n),gr(i,u)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return fi(t)?Gs(e,t):It(t)||lr(t)?(i=o3(e,t,a,r),va(i,{synthetic:!0}),i):(i=Hs(e,t,a,n),gr(i,`${t}``ui.${a.name}`,message:n},{event:a.event,name:a.name,global:a.global})}return t}function d3(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:N0(e.level),message:ws(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${ws(e.args.slice(1)," ")||"console.assert"}``sentry.${t.type==="transaction"?"transaction":"event"}``safari-extension:${t}`:`safari-web-extension:${t}``Non-Error promise rejection captured with value: ${String(e)}``Global Handler attached: ${e}``${t}@${e}``Request timeout after ${s}ms``fail response: ${t} `,l);let d="";l&&(d=l.slice(0,500));let m=d,x=new URL(t).hostname.endsWith(`.${Le}``${j.year}${j.week}``[domain]` tag:** Apply strictly with absolute priority, ** overriding any other translation logic for the term**. (Use original Source Term if Source==Translation, else use listed Translation).\n    *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2D\u6587\uFF0C{{html_only}}\u4EC5\u8F93\u51FA\u7FFB\u8BD1\u3002\u5982\u679C\u67D0\u4E9B\u5185\u5BB9\u65E0\u9700\u7FFB\u8BD1\uFF08\u5982\u4E13\u6709\u540D\u8BCD\u3001\u4EE3\u7801\u7B49\uFF09\uFF0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u4E1C\u5317\u4EBA\u7684\u53E3\u543B\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u751F\u6D3B,\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA",multipleSystemPrompt:""},{id:"wyw2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u7CBE\u901A\u53E4\u6587\u7684\u5B66\u8005\uFF0C\u53EA\u8FD4\u56DE\u767D\u8BDD\u6587",prompt:`\u8BF7\u5C06\u6587\u8A00\u6587\u7528\u767D\u8BDD\u6587\u89E3\u91CA:

{{text}}`,multiplePrompt:`\u5C06\u4E0B\u9762 YAML \u683C\u5F0F\u4E2D\u6240\u6709\u7684 {{imt_source_field}} \u5B57\u6BB5\u4E2D\u7684\u6587\u8A00\u6587\u7FFB\u8BD1\u4E3A\u767D\u8BDD\u6587\uFF0C\u5E76\u5C06\u7FFB\u8BD1\u7ED3\u679C\u5199\u5728 {{imt_trans_field}} \u5B57\u6BB5\u4E2D

{{normal_result_yaml_example}}

\u5F00\u59CB\u7FFB\u8BD1:

{{yaml}}`,multipleSystemPrompt:""},{id:"auto2wyw",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u6587\u8A00\u6587\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u6587\u8A00\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are you"}
    }
  ],
  "contextual_analysis": "Analysis of the word''t ","n''m sorry, but I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``))return;let n=document.createElement("script");n.src=t,n.setAttribute("async","true"),n.id="imt-subtitles-inject",document.head?.insertBefore(n,document.head?.firstChild)}}function qv(e){if(!(!Xt()&&!Zt()))if(pe()){vu();return}else{let t=ie.runtime.getURL("browser-bridge/inject.js");if(document.querySelector(`script[src='${t}']`))return;let n=document.createElement("script");n.src=t,n.id="imt-browser-bridge-inject",n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}}function Gv(e){_i.handleMessages(({id:t,type:a})=>{if(a==="getConfig"){let n=e.rule.subtitleRule;_i.sendMessages({id:t,data:n})}})}Nv();var Fn=null;async function Hv(e,t){let a=Object.keys(t);if(Fn){let n={url:e,config:Fn.config,state:{...Fn.state,...t}};Fn=await mu(n)}else{let n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}return Fn}function Kv(){return pe()?aa():Wv({method:"getConfig",data:{userAgent:globalThis.navigator.userAgent}})}async function Wv(e){return await iu().sendMessage("background:main",e)}})();

