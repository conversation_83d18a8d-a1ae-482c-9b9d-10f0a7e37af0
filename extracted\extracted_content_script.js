(()=>{var ER=Object.defineProperty;var d6=(e,t)=>{for(var n in t)ER(e,n,{get:t[n],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`${globalThis.location.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")||(e=`${globalThis.location.protocol}//${r}``\\x1B[${r.join(";")}m`,close:`\\x1B[${e}m`,regexp:new RegExp(`\\\\x1b\\\\[${e}m`,"g")}}function w(r,e){return ie?`${e.open}${r.replace(e.regexp,e.open)}${e.close}``https://config.${p}/`,rt=`https://app.${p}/`,u=S()||N()?`https://${p}/`:`https://test.${p}/`,G=`https://dash.${p}/`,ot=S()||N()?`https://api2.${p}/`:`https://test-api2.${p}/`,at=S()||N()?`https://ai.${p}/`:`https://test-ai.${p}/`,it=`https://assets.${le}.cn/`,ue=u+"accounts/login?from=plugin",X=u+"profile/",m=u+"auth/pricing/",x=u+"pricing/";Q()&&(m=u+"accounts/safari-iap/",x=u+"accounts/safari-iap/");var st=S()?`https://onboarding.${p}/`:`https://test-onboarding.${p}/`,Z=`https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${u}activities/components/image-pro`;var vr=50*1e4,Er=`[${Y}-ctx-divider]`,Cr=`${Y}_context_preview`;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``${i}-${r}-${a}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${n("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${n("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${n("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${n("error.gemini.429")}<br/><br/> ${o}`:o=`${n("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${n("error.claude.403")}<br/><br/>${o}`:o=`${n("error.403")}<br/><br/>${o}`:this.status===400?o=`${n("error.400")}<br/><br/> ${o}`:this.status===502?o=`${n("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${n("error.subscriptionExpired")}<br/><br/> ${o}`,r="setting",a="configError",i=n("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${n("error.azure.401")}<br/><br/> ${o}`),{type:a,title:i,errMsg:o,action:r}}handleFetchError(t){let n=ye.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let r=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${n("mangaQuotaError.solvedTitle")}<br/></br>
        ${a.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${r("proQuotaError.solvedTitle")}
    <br/><br/>
    ${l.map((p,g)=>`${g+1}. ${p}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",a="";return n&&n.length===3&&(r=n[1],a=n[2]),{mimeType:r,base64:a}}function pl(){let e,t="pending",n=new Promise((r,a)=>{e={async resolve(i){await i,t="fulfilled",r(i)},reject(i){t="rejected",a(i)}}});return Object.defineProperty(n,"state",{get:()=>t}),Object.assign(n,e)}var s1=class extends Error{constructor(){super("Deadline"),this.name="DeadlineError"}};function dm(e,t){let n=pl(),r=setTimeout(()=>n.reject(new s1),t);return Promise.race([e,n]).finally(()=>clearTimeout(r))}function We(e,t={}){let{signal:n,persistent:r}=t;return n?.aborted?Promise.reject(new DOMException("Delay was aborted.","AbortError")):new Promise((a,i)=>{let o=()=>{clearTimeout(l),i(new DOMException("Delay was aborted.","AbortError"))},l=setTimeout(()=>{n?.removeEventListener("abort",o),a()},e);if(n?.addEventListener("abort""A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/"];function fc(e){let t=typeof e=="string"?new TextEncoder().encode(e):e instanceof Uint8Array?e:new Uint8Array(e),n="",r,a=t.length;for(r=2;r<a;r+=3)n+=hi[t[r-2]>>2],n+=hi[(t[r-2]&3)<<4|t[r-1]>>4],n+=hi[(t[r-1]&15)<<2|t[r]>>6],n+=hi[t[r]&63];return r===a+1&&(n+=hi[t[r-2]>>2],n+=hi[(t[r-2]&3)<<4],n+="=="),r===a&&(n+=hi[t[r-2]>>2],n+=hi[(t[r-2]&3)<<4|t[r-1]>>4],n+=hi[(t[r-1]&15)<<2],n+="=""string"&&l1.Space_Separator.test(e)},isIdStartChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||l1.ID_Start.test(e))},isIdContinueChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="\u200C"||e==="\u200D"||l1.ID_Continue.test(e))},isDigit(e){return typeof e=="string"&&/[0-9]/.test(e)},isHexDigit(e){return typeof e=="string"&&/[0-9A-Fa-f]/.test(e)}},c1,er,bi,pm,Zi,ia,wn,p1,yc,RF=function(e,t){c1=String(e),er="start",bi=[],pm=0,Zi=1,ia=0,wn=void 0,p1=void 0,yc=void 0;do wn=FF(),NF[er]();while(wn.type!=="eof");return typeof t=="function"?d1({"":yc},"",t):yc};function d1(e,t,n){let r=e[t];if(r!=null&&typeof r=="object")if(Array.isArray(r))for(let a=0;a<r.length;a++){let i=String(a),o=d1(r,i,n);o===void 0?delete r[i]:Object.defineProperty(r,i,{value:o,writable:!0,enumerable:!0,configurable:!0})}else for(let a in r){let i=d1(r,a,n);i===void 0?delete r[a]:Object.defineProperty(r,a,{value:i,writable:!0,enumerable:!0,configurable:!0})}return n.call(e,t,r)}var Ke,Ve,bc,fi,at;function FF(){for(Ke="default",Ve="",bc=!1,fi=1;;){at=yi();let e=w5[Ke]();if(e)return e}}function yi(){if(c1[pm])return String.fromCodePoint(c1.codePointAt(pm))}function X(){let e=yi();return e===`
`?(Zi++,ia=0):e?ia+=e.length:ia++,e&&(pm+=e.length),e}var w5={default(){switch(at){case"	":case"\v":case"\f":case" ":case"\xA0":case"\uFEFF":case`
``
``
`:case"\r":throw Ft(X());case"\u2028":case"\u2029":break;case void 0:throw Ft(X())}Ve+=X()},start(){switch(at){case"{":case"[":return It("punctuator",X())}Ke="value"},beforePropertyName(){switch(at){case"$":case"_":Ve=X(),Ke="identifierName";return;case"\\":X(),Ke="identifierNameStartEscape";return;case"}":return It("punctuator",X());case'"':case"'":bc=X()==='"''${OF(e)}' at ${Zi}:${ia}`)}function rs(){return g1(`JSON5: invalid end of input at ${Zi}:${ia}`)}function T5(){return ia-=5,g1(`JSON5: invalid identifier character at ${Zi}:${ia}``f${e}``),this._outcomes[r]=this._outcomes[r]+1||1}}_updateSessionFromEvent(t,n){let r=!1,a=!1,i=n.exception&&n.exception.values;if(i){a=!0;for(let l of i){let u=l.mechanism;if(u&&u.handled===!1){r=!0;break}}}let o=t.status==="ok";(o&&t.errors===0||o&&r)&&(Fa(t,{...r&&{status:"crashed""number""environment"in t||(t.environment="environment"in n?r:"production""SDK not enabled, will not capture event."));let o=t.type==="transaction";return!o&&typeof i=="number"&&Math.random()>i?(this.recordDroppedEvent("sample_rate","error"),Rc(new Wt(``[domain]` tag:** Apply strictly with absolute priority, ** overriding any other translation logic for the term**. (Use original Source Term if Source==Translation, else use listed Translation).\n    *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2D\u6587\uFF0C{{html_only}}\u4EC5\u8F93\u51FA\u7FFB\u8BD1\u3002\u5982\u679C\u67D0\u4E9B\u5185\u5BB9\u65E0\u9700\u7FFB\u8BD1\uFF08\u5982\u4E13\u6709\u540D\u8BCD\u3001\u4EE3\u7801\u7B49\uFF09\uFF0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u4E1C\u5317\u4EBA\u7684\u53E3\u543B\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u751F\u6D3B,\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA",multipleSystemPrompt:""},{id:"wyw2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u7CBE\u901A\u53E4\u6587\u7684\u5B66\u8005\uFF0C\u53EA\u8FD4\u56DE\u767D\u8BDD\u6587",prompt:`\u8BF7\u5C06\u6587\u8A00\u6587\u7528\u767D\u8BDD\u6587\u89E3\u91CA:

{{text}}`,multiplePrompt:`\u5C06\u4E0B\u9762 YAML \u683C\u5F0F\u4E2D\u6240\u6709\u7684 {{imt_source_field}} \u5B57\u6BB5\u4E2D\u7684\u6587\u8A00\u6587\u7FFB\u8BD1\u4E3A\u767D\u8BDD\u6587\uFF0C\u5E76\u5C06\u7FFB\u8BD1\u7ED3\u679C\u5199\u5728 {{imt_trans_field}} \u5B57\u6BB5\u4E2D

{{normal_result_yaml_example}}

\u5F00\u59CB\u7FFB\u8BD1:

{{yaml}}`,multipleSystemPrompt:""},{id:"auto2wyw",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u6587\u8A00\u6587\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u6587\u8A00\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are you"}
    }
  ],
  "contextual_analysis": "Analysis of the word''t ","n''m sorry, but I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``/g,"\\`")}${c}': '${(u.v||u.k).replace(/`/g,"\\`")}''"')&&i?.endsWith('"''/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}async signedRequest({secretId:t,secretKey:n,action:r,payload:a,service:i,version:o}){let s=new Date().toISOString(),l=Math.random().toString(36).slice(2),u={Action:r,Version:o,Format:"JSON",AccessKeyId:t,SignatureNonce:l,Timestamp:s,SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0"},d=(h=>{let f=Object.keys(h).sort().map(y=>`${this.encode(y)}=${this.encode(h[y])}`).join("&");return`POST&%2F&${this.encode(f)}`})(Object.assign({},u,a)),m=this.SHA1.b64_hmac(`${n}&`,d),p=new URLSearchParams(Object.assign({},u,{Signature:m})).toString(),g=await super.request({retry:this.retry,url:`https://${i}.aliyuncs.com?${p}``%${t.charCodeAt(0).toString(16).toUpperCase()}`)}catch{return""}},l2=e=>Object.keys(e).map(t=>{let n=e[t];if(typeof n>"u"||n===null)return;let r=s2(t);if(r)return Array.isArray(n)?`${r}=${n.map(s2).sort().join(`&${r}=`)}`:`${r}=${s2(n)}``${an.algorithm} Credential=${t.accessKeyId}/${a}`),r.push(`SignedHeaders=${this.signedHeaders()}`),r.push(`Signature=${await this.signature(t,n)}`),r.join(", ")}async getSignUrl(t,n){let r=this.getDateTime(n),a={...this.request.params},i=this.request.params,o=this.request.headers;t.sessionToken&&(a[an.tokenHeader]=t.sessionToken),a[an.dateHeader]=r,a[an.notSignBody]="",a[an.credential]=`${t.accessKeyId}/${this.credentialString(r)}``${this.canonicalHeaders()}
`),t.push(this.signedHeaders()),t.push(await this.hexEncodedBodyHash()),t.join(`
`)}canonicalHeaders(){let t=[];Object.keys(this.request.headers).forEach(r=>{t.push([r,this.request.headers[r]])}),t.sort((r,a)=>r[0].toLowerCase()<a[0].toLowerCase()?-1:1);let n=[];return t.forEach(r=>{let a=r[0].toLowerCase();if(this.isSignableHeader(a)){let i=r[1];if(typeof i>"u"||i===null||typeof i.toString!="function")throw new Y(`Header ${a} contains invalid value`);n.push(`${a}:${this.canonicalHeaderValues(i.toString())}`)}}),n.join(`
``${an.kDatePrefix}${t.secretKey}``
``Unsupported language: ${a}`);a=this.langMap.get(a);let i=await this.checkLang(r,n);if(!i)return{text:n,from:r,to:a};r=i;let o=this.handleRequest(n,r,a),s=await super.request(o);return{text:this.handleResponseText(s),from:r,to:a}}async translateList(t){if(!Object.keys(this.apiServiceConfig).length)throw new Y("serivce id not found config");let{text:n,from:r,to:a}=t;if(!this.langMap.has(a))throw new Y(`Unsupported language: ${a}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${r==="auto"?"":A2.get(r)+"-"}${A2.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.toString()}``https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${n}``
`+n+`
`+o+`
`+i}}async getCanonicalRequest(t){let n=await In(t),r="POST",a="/",i="",o=`content-type:application/json; charset=utf-8
host:hunyuan.tencentcloudapi.com
x-tc-action:`+this.action.toLowerCase()+`
`,s="content-type;host;x-tc-action";return{signedHeaders:s,canonicalRequest:r+`
`+a+`
`+i+`
`+o+`
`+s+`
`+n}}getDate(t){let n=t.getUTCFullYear(),r=("0"+(t.getUTCMonth()+1)).slice(-2),a=("0"+t.getUTCDate()).slice(-2);return`${n}-${r}-${a}``${Oe}silicon/get-token?deviceId=${this.deviceId}``${Oe}big-model/get-token?deviceId=${this.deviceId}``Bearer ${this.apiKey}`,...this.headerConfigs}),body:JSON.stringify(s,null,2),timeout:this.requestTimeout,retry:this.retry},u;try{return u=await this.rawRequest(l),{text:this.parseResponse(u),from:r,to:a}}catch(c){throw c}}};var M2=!1;async function mu(e,t=!1){if(J(!1,!0)||!M2&&!t)return null;try{let n=await vE(e);return I.debug("server language detect:",n),n}catch(n){return I.debug("server language detect error",n),ug(!1),null}}async function vE(e,t=1500){let n=new Promise((i,o)=>{setTimeout(()=>o(new Error(`Timeout after ${t}ms`)),t)}),r=Se({url:`https://lang-detect.${At}/api/predict/batch``
``\r
`).replace(/&nbsp;/g," ").replace(/&quot;/g,'"').replace(/&#39;/g,"'']?(\d+)[^\d>]*>([\s\S]*)/i.exec(c);if(d){let m={};m.type="caption",m.start=parseInt(d[1]),m.end=m.start+2e3,m.duration=m.end-m.start,m.content=d[2].replace(/^<\/SYNC[^>]*>/gi,"");let p=!0,g=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``${N}-new-user-guide`;function Rb(){return C("img",{class:`${gW}-img`,src:Wg("images/new_float_ball_intro.png")})}var Kg=`${N}-new-user-guide`;function Fb(){let{t:e}=ae();return C("div",{class:`${Kg}-select-service-guide`,children:C("div",{class:`${Kg}-select-service-guide-card`,children:[C("div",{class:`${Kg}-max-model`,children:e("translationServicesGroup.pro")}),C("div",{class:`${Kg}-model-example`,children:[C("img",{src:Hr("openai"),class:"service-icon"}),"GPT-4.1 mini",C("img",{src:Xa})]})]})})}var gk=`${N}-new-user-guide`;function Bb(){let{t:e}=ae();return C("div",{class:`${gk}-video-subtitle-guide`,children:C("div",{class:`${gk}-video-subtitle-guide-card`,children:C("div",{children:[C("img",{src:Cb,class:"service-icon"}),e("subtitle.quickButton.requestAiSubtitle")]})})})}var hk=`${N}-new-user-guide`;function _b(){let{t:e}=ae();return C("div",{class:`${hk}-video-subtitle-guide`,children:C("div",{class:`${hk}-video-subtitle-guide-card`,children:[C("div",{children:[C("img",{src:xb,class:"service-icon"}),e("autoEnableSubtitle")]}),C(Dg,{})]})})}var q0=`${N}-new-user-guide``${q0}-container`,style:d,children:[C("div",{class:`${q0}-close-icon`,onClick:e,children:C(pk,{})}),C("img",{class:`${q0}-bg ${n}`,src:fW}),C("div",{class:`${q0}-content ${n}`,children:[C(c,{}),C("div",{class:`${q0}-message text-red-500`,children:[i[r],Be().any&&r==="float-ball"?`
${a("floatBall.longPress")}`:""]})]}),C(Lb,{position:n})]})}var fW="";var hr=`${N}-new-user-guide`,bk=``;var G0=`${N}-new-user-guide-root``#${G0}`);a&&r.target!==a&&Nb(G0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function Nb(e){let t=document.querySelector(`#${e}``[data-${N}-walked]``.${vt}``[${Ud}='${n}''`*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function th(e){for(var t=9,n=e.length;n--;)t=Math.imul(t^e.charCodeAt(n),1597334677);return"#"+((t^t>>>9)>>>0).toString(36)}function nh(e,t="@media "){return t+Lr(e).map(n=>(typeof n=="string"&&(n={min:n}),n.raw||Object.keys(n).map(r=>`(${r}-width:${n[r]})``var(${r})`:n;if(e.includes("<alpha-value>"))return e.replace("<alpha-value>",a);if(e[0]=="#"&&(e.length==4||e.length==7)){let i=(e.length-1)/3,o=[17,1,.062272][i-1];return`rgba(${[Zb(e.substr(1,i),o),Zb(e.substr(1+i,i),o),Zb(e.substr(1+2*i,i),o),a]})`}return a=="1"?e:a=="0"?"#0000":e.replace(/^(rgb|hsl)(\([^)]+)\)$/,`$1a$2,${a})``])?(.+?)\1(?:\s*,\s*(["''ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"'.split(","),serif:'ui-serif,Georgia,Cambria,"Times New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(n){return n},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=_K(t.styleAliases||null),BK.indexOf(this.kind)===-1)throw new fr('Unknown kind "'+this.kind+'" is specified for "'+e+''"''there is a previously declared suffix for "'+r+'" tag handle'',''undeclared tag handle "'+a+'"''unidentified alias "'+n+'"''unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"''> tag; it should be "'+g.kind+'", not "'+e.kind+'"''unknown document directive "'+r+'"''\\"''"''""':"''";if(!e.noCompatMode&&(TY.indexOf(t)!==-1||wY.test(t)))return e.quotingType===X0?'"'+t+'"':"'"+t+"'";var i=e.indent*Math.max(1,n),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-i),s=r||e.flowLevel>-1&&n>=e.flowLevel;function l(u){return DY(e,u)}switch(LY(t,s,e.indent,o,l,e.quotingType,e.forceQuotes&&!r,a)){case LD:return t;case vy:return"'"+t.replace(/'/g,"''")+"'";case RD:return"|"+iD(t,e.indent)+oD(nD(t,i));case FD:return">"+iD(t,e.indent)+oD(nD(FY(t,o),i));case Ru:return'"'+BY(t)+'"''"'),l=i[o],u=n[l],e.replacer&&(u=e.replacer.call(n,l,u)),zi(e,t,l,!1,!1)&&(e.dump.length>1024&&(c+="? "),c+=e.dump+(e.condenseFlow?'"''> tag resolver accepts not "'+l+'" style''"（）【】《》「」『』,.!?:;"()[\]<>]/.test(l)){let u=l[0],c=a[s],d=l.slice(1),m=c+u;this.ctx.measureText(m).width<=n*1.1&&(a[s]=m,a[s+1]=d||" ")}}if(a.length>=2){let s=a[a.length-1];if((u=>!!(u.length===1||u.length===2&&!Hi.isCJK(u)||u.length===2&&Hi.isCJK(u[0])&&/[，。！？、：；'"）】》」』】,.!?:;"''[contenteditable="true"]''"+Da(t.document.activeElement)+"''"')||t.includes(`
`)?`"${t.replace(/"/g,'""''"''${xP}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(O){return l.test(O.trim())}let c=()=>p.value.trim()===""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(O){O.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      font-size: 14px;
      width: 303px;
      height: 45px;
      box-sizing: border-box;
      margin: 0 1px;
      outline: none;`),x.placeholder=s("reportInfo.emailPlaceholder"),et.get(gt,null).then(O=>{if(!O)return;let _=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(O.email);O.email&&!_&&(x.value=O.email)});let f=document.createElement("label");f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${Nn.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${N}-modal-root`,r=`${N}-modal`,a=document.getElementById(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=document.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySelector(`#${N}-modal-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-modal`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function eL(e,t,n="sameLang",r,a,i,o){let s=ye.bind(null,e.config.interfaceLanguage),l=t;n=="sameLang"&&(l=s("sameLangNoTranslate")),a.innerText=l,i.innerText=s("neverShowFuture"),n=="sameLang"&&(i.style.display="flex",i.onclick=async()=>{let u=await un();Yt({...u,sameLangCheck:!1}),o(i,!0)})}var tL=0;async function nL(e){let t=Date.now();if(t-tL<2e3||(tL=t,cX()))return;let n=`${N}-toast-root`,r=`${N}-toast`,a=document.getElementById(n),i=`${N}-toast-msg`,o=`${N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySelector(`#${N}-toast-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-toast`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function M9(e,t,n,r="retry",a,i,o,s,l,u){let c=ye.bind(null,e.config.interfaceLanguage),d=t||c("errorModalTitle"),m=c("unknownError");o.innerText=d,s.innerHTML=st.sanitize(n||m,{ADD_ATTR:["target"]}),l.innerText="";let p="",g=null,h=document.createElement("button");if(r=="retry"){p=c("retryAllButton"),h.setAttribute(`data-${N}-action``<span id="error-id-${m}">${u.message}</span>`),Zs[m]={ok:!1,sentence:d},TL(l,e,t,n,u)):c&&(g.innerHTML=st.sanitize(c.text),Zs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function SL(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Zs),a=[],i=[],o=a0(e,"");for(let s of r){let l=Zs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Zs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Gt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,d=Ol(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=st.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${Ud}='${r}''[contenteditable="true"], [contenteditable=""]'))}function IL({selectionRect:e,gap:t=10,modalWidth:n=450,modalHeight:r=480}){let[a,i]=V(null),o=Me(()=>{if(!e)return null;let s=globalThis.innerWidth,l=globalThis.innerHeight,u,c;u=e.left,Yu()&&(u=u+Math.max(0,(e.width-n)/2));let m=PL();if(m?.x)u=m.x-n/2;else{let x=LL();x?.x&&(u=x.x-n/2)}let p=u+n;p>s&&(u-=p-s-t);let g=e.bottom+t;return g+r<=l?c=g:c=l-r,c=Math.max(0,c),u=Math.max(0,u),{top:c,left:u}},[e,t,n,r]);return Q(()=>{if(a)return;let s=o();i(s?{position:"fixed",top:`${s.top}px`,left:`${s.left}px``;var FL={en:"en-US","zh-CN":"zh-CN","zh-TW":"zh-TW",yue:"zh-HK",af:"af-ZA",az:"az-AZ",be:"be-BY",bn:"bn-IN",bs:"bs-BA",ja:"ja-JP",ko:"ko-KR",fr:"fr-FR",de:"de-DE",es:"es-ES",it:"it-IT",ru:"ru-RU",pt:"pt-PT","pt-br":"pt-BR","pt-BR":"pt-BR","pt-PT":"pt-PT",nl:"nl-NL",pl:"pl-PL",ar:"ar-001",bg:"bg-BG",ca:"ca-ES",cs:"cs-CZ",da:"da-DK",el:"el-GR",fi:"fi-FI",he:"he-IL",hi:"hi-IN",hr:"hr-HR",id:"id-ID",vi:"vi-VN",sv:"sv-SE",ms:"ms-MY",th:"th-TH",ur:"ur-PK",ta:"ta-IN",te:"te-IN",ml:"ml-IN",kn:"kn-IN",gu:"gu-IN",mr:"mr-IN",pa:"pa-IN",si:"si-LK",km:"km-KH",lo:"lo-LA",my:"my-MM",ne:"ne-NP",mn:"mn-MN",sd:"sd-PK",fa:"fa-IR",ps:"ps-AF",ha:"ha-NG",ig:"ig-NG",yo:"yo-NG",zu:"zu-ZA",xh:"xh-ZA",st:"st-ZA",tn:"tn-ZA",sn:"sn-ZW",ny:"ny-MW",so:"so-SO",am:"am-ET",bo:"bo-CN",ceb:"ceb-PH",co:"co-FR",cy:"cy-GB",eo:"eo-EO",et:"et-EE",eu:"eu-ES",fil:"fil-PH",fj:"fj-FJ",fy:"fy-NL",ga:"ga-IE",gd:"gd-GB",gl:"gl-ES",haw:"haw-US",hmn:"hmn-US",ht:"ht-HT",hu:"hu-HU",hy:"hy-AM",is:"is-IS",jw:"jw-ID",ka:"ka-GE",kk:"kk-KZ",ku:"ku-TR",ky:"ky-KG",la:"la-VA",lb:"lb-LU",lt:"lt-LT",lv:"lv-LV",mg:"mg-MG",mi:"mi-NZ",mk:"mk-MK",mt:"mt-MT",mww:"mww-US",no:"no-NO",otq:"otq-MX",ro:"ro-RO",sa:"sa-IN",sk:"sk-SK",sl:"sl-SI",sm:"sm-WS",sq:"sq-AL","sr-Cyrl":"sr-CS","sr-Latn":"sr-CS",su:"su-ID",sw:"sw-TZ",tlh:"tlh-TLH","tlh-Qaak":"tlh-TLH",to:"to-TO",tr:"tr-TR",ty:"ty-PF",ug:"ug-CN",uk:"uk-UA",uz:"uz-UZ",yi:"yi-IL",yua:"yua-MX","zh-CN-NE":"zh-CN","ur-roman":"ur-PK"};var BL={rate:10,volume:100},Wh=[];globalThis.speechSynthesis&&(Wh=speechSynthesis.getVoices(),globalThis.speechSynthesis.onvoiceschanged=()=>{Wh=speechSynthesis.getVoices()});var AX={WebSpeech:B9,YouDao:kX};function Kh({provider:e,text:t,lang:n,onFinish:r,signal:a,onStart:i}){let o=BL.rate,s=BL.volume,l=AX[e??"WebSpeech"];return I.debug("[tts] call tts provider",e),I.debug("[tts] call tts text",t),I.debug("[tts] call tts lang",n),l({text:t,lang:n??"en",rate:o,volume:s,signal:a,onFinish:r,onStart:i})}function B9({text:e,lang:t,rate:n,volume:r,signal:a,onFinish:i,onStart:o}){if(!globalThis.speechSynthesis){I.warn("[tts] Web Speech API is not supported in this browser.");return}let s=(n??10)/10,l=r?r/100:1,u=FL[t]??"en-US",c=new SpeechSynthesisUtterance;i&&c.addEventListener("end",i,{once:!0}),c.text=e,c.lang=t,c.rate=s,c.volume=l;let d=Wh.find(m=>m.lang===u)??null;zn()&&u==="en-US"&&(d=Wh.find(m=>m.lang===u&&m.name==="Fred")??null),c.voice=d,a.addEventListener("abort",()=>{speechSynthesis.cancel()},{once:!0}),o?.(),speechSynthesis.speak(c)}async function kX({text:e,lang:t,rate:n,volume:r,signal:a,onFinish:i,onStart:o}){let l=`https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(e)}&type=2``icon-wrapper speaker-icon ${n??""}`,onClick:m=>d(m),children:C(Mu,{})})}function _L({ctx:e,translationResult:t,text:n,currentIndex:r,visible:a}){let[i,o]=V({}),s=u=>{if(r===void 0||i[r]===u)return;o(m=>({...m,[r]:u}));let d=`selection_translate_${u.toLowerCase()}``<span class="highlight">${R.slice(L,L+M.length)}</span>`,w=L+M.length}return P},D=async({service:R,model:M})=>{l(null),c(!0),m(!1),g(void 0);let P="",w=null;try{w=await qh({text:t.contextText,ctx:e})}catch(L){c(!1),l(L.message??o("translateFail"));return}v(w),kE({text:t.originalText,contextText:t.contextText,from:w,to:e.targetLanguage,url:e.url,signal:n,translationService:R,model:M,onMessage:L=>{I.debug("[selection-translation] stream message",L),P=P+L;try{P.startsWith("```json\n")&&(P=P.substring(8)),P.endsWith("\n```")&&(P=P.substring(0,P.length-4));let U=wc(P);I.debug("[selection-translation] stream parsedMsg",U),c(!1),g(U),F(U)}catch{}},onFinish:L=>{I.debug("[selection-translation] stream onFinish",L),m(!0),u&&c(!1),wo(!1);try{let U=wc(P);U&&x(z=>[...z,U])}catch{}},onError:L=>{if(I.debug("[selection-translation] stream onError",L),xt(e.user)){O();return}u&&c(!1),wo(!1),l(L.message??o("translateFail"))}},e)},F=R=>{e.rule.selectionTranslation?.enableAutoRead&&R?.phonetic&&y(!0)},O=()=>{I.debug(`[selection-translation] fallback to ${Vh.service}`),D(Vh)},_=()=>C("div",{className:"error-container",children:[C("div",{className:"error-text",children:s}),C("div",{style:{marginTop:"10px"},children:C("button",{onClick:()=>window.location.reload(),className:"error-button",children:o("error.retry")})})]}),B=()=>C("div",{class:`${N}-loading``${N}-modal-body notranslate ${S?"modal-body-rtl":""}`,children:[u&&B(),s&&_(),!s&&!u&&j()]}),C(_L,{visible:d,ctx:e,translationResult:p,text:T.originalText,currentIndex:r})]})}function OL({menu:e,children:t,onClick:n}){let[r,a]=V(!1),[i,o]=V(null),s=ge(null),l=ge(null),[u,c]=V("bottomLeft"),[d,m]=V("bottomLeft");Q(()=>{let x=`${N}-modal`,f=y=>{s.current&&!s.current.contains(y.target)&&(a(!1),o(null))};return St(`#${Xr} -> .${x}`)?.addEventListener("mousedown",f),()=>St(`#${Xr} -> .${x}`)?.removeEventListener("mousedown",f)},[]),Q(()=>{p()},[r]);let p=()=>{if(r&&s.current){let x=s.current.getBoundingClientRect(),y=globalThis.innerWidth-x.right;y<200?(c("bottomRight"),m("bottomRight")):y>=200&&y<400?(c("bottomLeft"),m("bottomRight")):(c("bottomLeft"),m("bottomLeft"))}},g=x=>{x.preventDefault(),a(!r)},h=x=>x.type==="group"?C("div",{class:`${N}-dropdown-group`,children:x.children?.map(f=>h(f))}):C("div",{className:`${N}-dropdown-item ${x.disabled?"disabled":""} ${x.children?"has-children":""}`,onMouseEnter:()=>x.children&&o(x.key),onClick:f=>{f.stopPropagation(),!x.disabled&&n?.(x)},children:[C("span",{className:`${N}-dropdown-item-label`,children:typeof x.label=="function"?x.label():x.label}),x.children&&i===x.key&&C("div",{className:`${N}-dropdown-submenu ${d}`,children:x.children.map(f=>h(f))})]});return C("div",{ref:s,className:`${N}-dropdown`,children:[C("div",{onClick:g,children:t}),r&&C("div",{ref:l,className:`${N}-dropdown-menu ${u}``<span class="link" id="open-options">${i("setting")}</span>``translate(${y.x}px, ${y.y}px)`};return C("div",{ref:d,className:`${N}-modal ${F?"modal-rtl":""} notranslate ${T?"dark":""}`,dir:F?"rtl":"ltr",style:{...m,...R},children:[C("div",{className:`${N}-modal-title notranslate`,style:x?{cursor:"grabbing"}:{cursor:"move"},children:[C("div",{className:`${N}-modal-title-left`,children:[C(Hh,{}),u.length>1&&C("div",{class:"toggle-container",children:[C("span",{class:"icon-wrapper toggle-icon left-icon",onClick:()=>j("prev"),children:C(Qk,{})}),C("span",{class:"toggle-text",children:[g+1,"/",u.length]}),C("span",{class:"icon-wrapper toggle-icon right-icon",onClick:()=>j("next"),children:C(Jg,{})})]})]}),C("div",{class:`${N}-modal-title-right``#${e}`);t&&t.remove()}function qL(e){IX(Zu),Yr({id:Zu,parent:document.documentElement,ctx:e,Component:PX,props:{ctx:e},style:LX})}function PX(e){return e.ctx.rule.selectionTranslation?.triggerMode==="icon"?C("div",{class:`${Js}-button icon-btn`,children:C(Hh,{})}):C("div",{class:`${Js}-button mini-btn``font.${vt}`).forEach(p=>{p.remove()}),m?.textContent?.slice(0,200)||""},o=a.previousElementSibling,s=a.nextElementSibling,l=i(o),u=i(s),c=i(a)||r;return`${l}
${c}
${u}`}function OX(e){let t=e.rule.selectionTranslation?.triggerMode;if(!t||!Kd.includes(t))return;let n=t.toLowerCase();$t("*",{scope:"selectionTranslation",element:globalThis.document,keyup:!0},r=>{if(r.type==="keydown"&&(Zh=$t.getPressedKeyCodes()),r.type==="keyup"){let a=Zh;if(I.debug(`[selection-translation] hotkeys: press keycodes: ${a}`),a.length>1){I.debug("[selection-translation] hotkeys: more than one keycodes"),Zh=[];return}if(a.length===0){I.debug("[selection-translation] hotkeys: no keycodes");return}let i=Wd[n]===a[0];I.debug(`[selection-translation] hotkeys: Whether the shortcut key pressed is ${n}: ${i}``${l}px`,n.style.left=`${s}px`,n.style.display="block";let u=()=>{z9({lastSelection:e,ctx:t})};t.rule.selectionTranslation?.triggerModeForIcon==="click"?(n.addEventListener("click",u),Vo.push(()=>{n?.removeEventListener("click",u)})):(n.addEventListener("mouseover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh(),U9({selection:e,ctx:t})}catch(n){throw n}}function U9({selection:e,ctx:t,trigger:n}){wo(!0),Te({key:"selection_translate",ctx:t,params:{trigger:n||t.rule.selectionTranslation?.triggerMode||"",translation_service:Qu(t).service}}),kr("translate_line_1",t);let r=document.querySelector(`#${Xr}``imt-dropdown ${t??""}`,style:{zIndex:Ia-10,...a},children:[!o&&C("div",{className:"imt-search-box",children:[C(zk,{class:"imt-search-icon"}),C("input",{ref:g,type:"text",placeholder:d("searchPlaceholder"),onClick:y=>{y.stopPropagation()},onInput:y=>{y.stopPropagation(),p({})}})]}),C("ul",{children:[x.map(y=>C("li",{className:`${n.includes(y.value)?"active":""} ${y.disabled?"disabled":""}``select-languages-box ${n??""}`,onClick:m=>{m.stopPropagation(),l(!s)},children:[C("div",{className:"select-languages-text",children:(()=>d?`${d} ${d&&`- ${c}`}`:c)()}),C(Xh,{count:r.length,maxCount:1}),C(ei,{class:"arrow-down""",disabled:!0}),g});return m.push({label:s("moreTranslationServices"),value:"more",icon:Hr("more"),disabled:!1,disableChecked:!0,onClick:()=>{gr(!0,"#services",!1)}}),m},[a]);return C("div",{className:`select-services ${n??""}``${e}`});return}globalThis.open(`${e}`,"_blank")}catch{globalThis.open(`${e}`,"_blank")}return}try{if(!J(!1,!0)&&$n()){te.tabs.create({url:`${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`});return}globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,"_blank")}catch{globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,"_blank")}}async function HX(){let{userValue:e,localValue:t}=await Qs("rewardCenterOpenTime");e||t||await qu("rewardCenterOpenTime",Date.now().toString())}function W9({visible:e,onClose:t,ctx:n,refreshKey:r=0,setSettings:a}){HX();let[i,o]=V(!1),[s,l]=V(!1),[u,c]=V({}),[d,m]=V(0),{t:p}=ae(),[g,h]=V(""""}let A=async B=>{if(I.debug("Task start:",B.taskConfig.taskKey),J(!1,!0)&&B.taskConfig.taskKey==="translate_line_1")return;let j=await Z8(),R=E(B.taskConfig.taskKey);if(!j){V9(`${el}&return_url=${encodeURIComponent(R)}``${M}${w}`:j==="pdf_token"?`${M}${P}`:M},O=B=>{let{taskConfig:j,taken:R,completed:M,enabled:P}=B,w=j.rewards[0],L=j.groupName==="\u9AD8\u7EA7\u4EFB\u52A1",U=M&&R,z=!P&&!M&&!R,G=p("rewardCenter.task.start"),q="reward-task-button",W=!1;return M&&!R?(G=p("rewardCenter.task.claim"),q+=" claim"):M&&R?(G=p("rewardCenter.task.claimed"),q+=" completed"):!P&&!M&&!R?(G=p("rewardCenter.task.start"),q+=" disabled"):L&&P&&!M&&!R&&($8(B,f)||(G=p("rewardCenter.task.start"),W=!0)),C("div",{className:`reward-task-item ${z?"unavailable":""} ${U?"completed":""}`,children:[C("div",{className:"reward-task-content",children:[C("div",{className:"reward-task-title",children:p(`rewardCenter.task.${j.taskKey}`)}),w&&C("div",{className:"reward-task-reward",children:[p("rewardCenter.reward.get"),"  ",C("span",{className:`${L?"reward-amount-advanced":"reward-amount"}`,children:F(w.rewardAmount,w.rewardType)})," ",D(w.rewardType,!0)]})]}),U?C(sM,{}):J(!1,!0)&&j.taskKey==="translate_line_1"?C(Tt,{text:p("rewardCenter.task.translate_line_1.warning",{1:{tag:"a",style:"color: #EA4C89;",href:Ff+"?utm_campaign=reward_center",target:"_blank"}}),position:"left",tipStyle:{width:"150px",maxWidth:"150px",whiteSpace:"normal",wordBreak:"break-word"},children:C("button",{className:`${q} disabled``completed-tasks-arrow ${u[B.groupName]?"expanded":""}`,children:C(ei,{})})]}),u[B.groupName]&&P.map(w=>O(w))]})]})]},B.groupName)};return!e&&!i?null:C(it,{children:C("div",{className:`reward-center-overlay ${s?"visible":""}`,onClick:S,children:C("div",{className:`reward-center-drawer ${s?"visible":""}``${j}%``${Yo}text#${y.sourceLang}/${b}/${v}``action-icon${i?" bounce-animate":""}`,children:C(Du,{})}),C("span",{className:"reward-center-text",children:a("rewardCenter.title")})]}),C("div",{className:"action-icon",onClick:()=>Xu(Yd,"help_center"),children:C($g,{})}),C("div",{className:"action-icon",onClick:()=>gr(!1,"",!1),children:C(Xg,{style:{width:18,height:18},fillColor:"#999"})})]})]})]})}function Xu(e,t){try{if(!J(!1,!0)&&$n()){te.tabs.create({url:`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`});return}globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,"_blank")}catch{globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}``${await JX()}px`,r="6px",a=document.getElementById(Z9),i=Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,o=Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;if(a)a.remove(),$L(),nf=!1;else{nf=!0,tf=!0,a=document.createElement("div"),a.id=Z9,a.setAttribute("style",`
      all: initial;
      position: fixed;
      top: 0px;
      right: 0px;
      width: ${n};
      height: 100%;
      z-index: ${Ia};
      background-color: white;
      display: flex;
      transform: translateX(100%);
      transition: transform ${rf}ms ease-out;
      ``${b}px`,YX(`${b}px`)},x=()=>{m&&(m=!1,u.style.pointerEvents="auto",a&&(a.style.transition=`transform ${rf}ms ease-out`),document.removeEventListener("mousemove",h),document.removeEventListener("mouseup",x))}}}async function QX(){let e=document.getElementById(Z9);e&&(e.remove(),$L(),nf=!1)}va.toggleMockSidePanel=KX;va.getIsOpenSidePanel=WX;va.closeMockSidePanel=QX;function YX(e){let t=document.body;t.style.setProperty("margin-right",e,"important"),t.style.setProperty("width","unset","important")}function ZX(e){let t=document.body;t.style.setProperty("transition",`margin-right ${rf}ms ease-out``detect page language: ${e.url} ${r}`),mo(r,e.targetLanguage,{ignoreZhCNandZhTW:e.rule.ignoreZhCNandZhTW})||r==="auto"||Kw(r,e.config.translationLanguagePattern)&&(i=!0,I.debug(`match language pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interfaceLanguage,"viewWithImmersiveTranslate")}</a>``init popup page error: ${n}``meta[name=${N}-options]``${N}-message`).addEventListener("change",r=>{try{let a=JSON.parse(r.target.value);a&&a.method==="removeStorageKey"&&a.data&&a.data.area&&a.data.keys&&(te.storage[a.data.area].remove(a.data.keys),e6(a.data.area))}catch(a){I.error("parse message error",a)}})}async function e6(e){let t=document.getElementById(N+"-status"),n=document.getElementById(`${N}-${e}-storage`);if(n){I.debug("init storage");let r=await te.storage[e].get(null);n.value=JSON.stringify(r),n.dispatchEvent(new Event("change")),n.addEventListener("change",a=>{try{let i=JSON.parse(a.target.value);te.storage[e].set(i)}catch(i){I.error("save to storage error",i)}}),n.addEventListener("refresh-"+e,async a=>{let i=await te.storage[e].get(null);n.value=JSON.stringify(i),I.debug("refresh ",e,"storage")})}else{I.error(`Could not find storage ${e} element`),t.innerText="Could not find storage local input element";return}}function P$(){try{document.dispatchEvent(new Event(_6))}catch{}}function yR(){na()||P$()}var vR=["textarea","input","button","select","option","iframe","strong","form","body"];async function L$(){tt()||WT(Ie()),yR(),await u5();let e=await tn();e.excludeTranslationHtmlTags&&(vR=e.excludeTranslationHtmlTags),Ps({}),st.addHook("beforeSanitizeElements",function(r,a,i){let o=(r.nodeName||"").toLowerCase();if(vR.includes(o)){let l=`<${o}>${r.textContent||""}</${o}>`,u=document.createTextNode(l);return r.replaceWith(u),r}return r}),st.addHook("uponSanitizeElement",function(r,a){let i=r.nodeName||"";/\d+$/.test(i)&&(a.allowedTags[a.tagName]=!0),Yc(r.tagName)&&(a.allowedTags[r.tagName.toLowerCase()]=!0)}),st.addHook("uponSanitizeAttribute",function(r,a){Yc(r.tagName)&&(a.allowedAttributes[a.attrName.toLowerCase()]=!0)});let t=Ie(),n=await Fn({config:e,url:t});Te({key:"init_page_daily",ctx:n}),mR.domready.then(()=>{if(fR())I.debug("detect web options page"),yd(n,window),bR();else{if(!n.config.enabled)return;if(n.rule.isInjectVersion){let a=Pt(),i=document.createElement("meta");i.name=N+"-version",i.content=a;try{document.head?.appendChild?.(i)}catch(o){I.warn("inject version failed",o)}}if(nt(n.url,n.config.blockUrls))return;yd(n,window),hR(n).then(()=>{sR(n).catch(a=>{a&&I.debug("translate page error",a.name,a.message,a)})}).catch(a=>{I.debug("can not detect a valid body: ",a)})}}).catch(r=>{r&&I.debug("translate dom ready detect error",r)})}L$().catch(e=>{I.debug("init error",e)});})();

