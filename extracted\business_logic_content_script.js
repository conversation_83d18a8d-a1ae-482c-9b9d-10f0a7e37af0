e",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;`
unction Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh(),U9({selection:e,ctx:t})}catch(n){throw n}}function U9({sele
e.display="block";let u=()=>{z9({lastSelection:e,ctx:t})};t.rule.selectionTranslation?.triggerModeForIcon==="click"?(n.addEventListener("click",u),Vo.push(()=>{n?.removeEventListener("click",u)})):(n.addEventListener("mouseover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=docu
entListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh(),U9({selection:e,ctx:t})}catch(n)
label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(O){O.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("
 t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySelector(`#${N}-toast-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-toast`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function M9(e,t,n,r="retry",a,i,o,s,l,u){let c=ye.bind(null,e.config.interfaceLanguage),d=t||c("errorModalTitle"),m=c("unknownEr
obalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globa
lex",i.onclick=async()=>{let u=await un();Yt({...u,sameLangCheck:!1}),o(i,!0)})}var tL=0;async function nL(e){let t=Date.now();if(t-tL<2e3||(tL=t,cX()))return;let n=`${N}-toast-root`,r=`${N}-toast`,a=document.getElementById(n),i=`${N}-toast-msg`,o=`${N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.
${N}-toast-root`,r=`${N}-toast`,a=document.getElementById(n),i=`${N}-toast-msg`,o=`${N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root
e("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySele
y was aborted.","AbortError")):new Promise((a,i)=>{let o=()=>{clearTimeout(l),i(new DOMException("Delay was aborted.","AbortError"))},l=setTimeout(()=>{n?.removeEventListener("abort",o),a()},e);if(n?.addEventListener("abort""A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v",
s:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2ts
cument.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySelector(`#${N}-modal-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-modal`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function eL(e,t,n="sameLang",r,a,i,o)
){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="bl
raphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="go
(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return 
al-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot
;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``${i}-${r}-${a}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For co
ll: initial;
      position: fixed;
      top: 0px;
      right: 0px;
      width: ${n};
      height: 100%;
      z-index: ${Ia};
      background-color: white;
      display: flex;
      transform: translateX(100%);
      transition: transform ${rf}ms ease-out;
      ``${b}px`,YX(`${b}px`)},x=()=>{m&&(m=!1,u.style.pointerEvents="auto",a&&(a.style.transition=`transform ${rf}ms ease-out`),document.removeEv
==""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style
itle"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackgrou
span",{class:"icon-wrapper toggle-icon right-icon",onClick:()=>j("next"),children:C(Jg,{})})]})]}),C("div",{class:`${N}-modal-title-right``#${e}`);t&&t.remove()}function qL(e){IX(Zu),Yr({id:Zu,parent:document.documentElement,ctx:e,Component:PX,props:{ctx:e},style:LX})}function PX(e){return e.ctx.rule.selectionTranslation?.triggerMode==="icon"?C("div",{class:`${Js}-button icon-btn`,children:C(Hh,{})}):C("di
a=document.getElementById(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}
s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip
n();e.excludeTranslationHtmlTags&&(vR=e.excludeTranslationHtmlTags),Ps({}),st.addHook("beforeSanitizeElements",function(r,a,i){let o=(r.nodeName||"").toLowerCase();if(vR.includes(o)){let l=`<${o}>${r.textContent||""}</${o}>`,u=document.createTextNode(l);return r.replaceWith(u),r}return r}),st.addHook("uponSanitizeElement",function(r,a){let i=r.nodeName||"";/\d+$/.test(i)&&(a.allowedTags[a.tagName]=!0),Yc(r.t
L="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=document.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);
{N}-new-user-guide`,bk=``;var G0=`${N}-new-user-guide-root``#${G0}`);a&&r.target!==a&&Nb(G0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function Nb(e){let t=document.querySelector(`#${e}``[data-${N}-walked]``.${vt}``[${Ud}='${n}''`*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function th(e){for(var t=9,n=e.length;n--;)t=Math.imul(t^e.charCodeAt(
style: normal;font-weight: 400;margin:12px; 16px 0;``${N}-modal-root`,r=`${N}-modal`,a=document.getElementById(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttr
=side_footer_${t}`,"_blank")}catch{globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}``${await JX()}px`,r="6px",a=document.getElementById(Z9),i=Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,o=Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;if(a)a.remove(),$L(),nf=!1;else{nf=!0,tf=!0,a=document.createElement("div"),a.id=Z9,a.setAttribute("style",`
      all: initial;
      positi
age pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interfaceLanguage,"viewWithImmersiveTranslate")}</a>``init popup page error: ${n}
!1,u.style.pointerEvents="auto",a&&(a.style.transition=`transform ${rf}ms ease-out`),document.removeEventListener("mousemove",h),document.removeEventListener("mouseup",x))}}}async function QX(){let e=document.getElementById(Z9);e&&(e.remove(),$L(),nf=!1)}va.toggleMockSidePanel=KX;va.getIsOpenSidePanel=WX;va.closeMockSidePanel=QX;function YX(e){let t=document.body;t.style.setProperty("margin-right",e,"impor
ent.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.
"_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interfaceLanguage,"viewWithImmersiveTranslate")}</a>``init popup page error: ${n}``meta[name=${N}-options]``${N}-message`).addEventListener("change",r=>{try{let a=JSON.parse(r.target.value);a&&a.method==="removeStorageKey"&&a.data&&a.data.area&&
slate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=document.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySe
-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1
 ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(O){O.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-ra
ey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${n("error.googleLimitI
iv",{style:{marginTop:"10px"},children:C("button",{onClick:()=>window.location.reload(),className:"error-button",children:o("error.retry")})})]}),B=()=>C("div",{class:`${N}-loading``${N}-modal-body notranslate ${S?"modal-body-rtl":""}`,children:[u&&B(),s&&_(),!s&&!u&&j()]}),C(_L,{visible:d,ctx:e,translationResult:p,text:T.originalText,currentIndex:r})]})}function OL({menu:e,children:t,onClick:n}){let[r,a]=
+"-version",i.content=a;try{document.head?.appendChild?.(i)}catch(o){I.warn("inject version failed",o)}}if(nt(n.url,n.config.blockUrls))return;yd(n,window),hR(n).then(()=>{sR(n).catch(a=>{a&&I.debug("translate page error",a.name,a.message,a)})}).catch(a=>{I.debug("can not detect a valid body: ",a)})}}).catch(r=>{r&&I.debug("translate dom ready detect error",r)})}L$().catch(e=>{I.debug("init error",e)});})(
t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySelector(`#${N}-modal-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-modal`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function eL(e,t,n="sameLang",r,a,i,o){let s=ye.bind(null,e.config.interfaceLanguage),l=t;n=="sameLang"&&(l=s("sameLangNoTranslat
f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style"
N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.q
i,onStart:o}){if(!globalThis.speechSynthesis){I.warn("[tts] Web Speech API is not supported in this browser.");return}let s=(n??10)/10,l=r?r/100:1,u=FL[t]??"en-US",c=new SpeechSynthesisUtterance;i&&c.addEventListener("end",i,{once:!0}),c.text=e,c.lang=t,c.rate=s,c.volume=l;let d=Wh.find(m=>m.lang===u)??null;zn()&&u==="en-US"&&(d=Wh.find(m=>m.lang===u&&m.name==="Fred")??null),c.voice=d,a.addEventListener("abort",(
bute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/
ther the shortcut key pressed is ${n}: ${i}``${l}px`,n.style.left=`${s}px`,n.style.display="block";let u=()=>{z9({lastSelection:e,ctx:t})};t.rule.selectionTranslation?.triggerModeForIcon==="click"?(n.addEventListener("click",u),Vo.push(()=>{n?.removeEventListener("click",u)})):(n.addEventListener("mouseover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}fu
|(tL=t,cX()))return;let n=`${N}-toast-root`,r=`${N}-toast`,a=document.getElementById(n),i=`${N}-toast-msg`,o=`${N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-sh
"/g,'""''"''${xP}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(O){return l.test(O.trim())}let c=()=>p.value.trim()===""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:f
value=JSON.stringify(r),n.dispatchEvent(new Event("change")),n.addEventListener("change",a=>{try{let i=JSON.parse(a.target.value);te.storage[e].set(i)}catch(i){I.error("save to storage error",i)}}),n.addEventListener("refresh-"+e,async a=>{let i=await te.storage[e].get(null);n.value=JSON.stringify(i),I.debug("refresh ",e,"storage")})}else{I.error(`Could not find storage ${e} element`),t.innerText="Could not find 
ce(L,L+M.length)}</span>`,w=L+M.length}return P},D=async({service:R,model:M})=>{l(null),c(!0),m(!1),g(void 0);let P="",w=null;try{w=await qh({text:t.contextText,ctx:e})}catch(L){c(!1),l(L.message??o("translateFail"));return}v(w),kE({text:t.originalText,contextText:t.contextText,from:w,to:e.targetLanguage,url:e.url,signal:n,translationService:R,model:M,onMessage:L=>{I.debug("[selection-translation] stream m
 language: ${e.url} ${r}`),mo(r,e.targetLanguage,{ignoreZhCNandZhTW:e.rule.ignoreZhCNandZhTW})||r==="auto"||Kw(r,e.config.translationLanguagePattern)&&(i=!0,I.debug(`match language pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria
L?"reward-amount-advanced":"reward-amount"}`,children:F(w.rewardAmount,w.rewardType)})," ",D(w.rewardType,!0)]})]}),U?C(sM,{}):J(!1,!0)&&j.taskKey==="translate_line_1"?C(Tt,{text:p("rewardCenter.task.translate_line_1.warning",{1:{tag:"a",style:"color: #EA4C89;",href:Ff+"?utm_campaign=reward_center",target:"_blank"}}),position:"left",tipStyle:{width:"150px",maxWidth:"150px",whiteSpace:"normal",wordBreak:"br
   padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),
innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&
"detect web options page"),yd(n,window),bR();else{if(!n.config.enabled)return;if(n.rule.isInjectVersion){let a=Pt(),i=document.createElement("meta");i.name=N+"-version",i.content=a;try{document.head?.appendChild?.(i)}catch(o){I.warn("inject version failed",o)}}if(nt(n.url,n.config.blockUrls))return;yd(n,window),hR(n).then(()=>{sR(n).catch(a=>{a&&I.debug("translate page error",a.name,a.message,a)})}).catch(a=
essage}</span>`),Zs[m]={ok:!1,sentence:d},TL(l,e,t,n,u)):c&&(g.innerHTML=st.sanitize(c.text),Zs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function SL(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r
oad-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interfaceLanguage,"viewWithImmersiveTranslate")}</a>``init popup page error: ${n}``meta[name=${N}-options]``${N}-message`).addEventListener("change",r=>{try{let a=JSON.parse(r.target.value);a&&a.method==="removeStorageKey"&&a.data&&a.data.area&&a.data.keys&&(te.storage[a.data.area].remove(a.data.keys),e6(a.data.area))}catch(a){I.error("p
a[s]=m,a[s+1]=d||" ")}}if(a.length>=2){let s=a[a.length-1];if((u=>!!(u.length===1||u.length===2&&!Hi.isCJK(u)||u.length===2&&Hi.isCJK(u[0])&&/[，。！？、：；'"）】》」』】,.!?:;"''[contenteditable="true"]''"+Da(t.document.activeElement)+"''"')||t.includes(`
`)?`"${t.replace(/"/g,'""''"''${xP}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(O){return l.test(O.trim())}let c=()=>p.value.tri
tById(n),i=`${N}-toast-msg`,o=`${N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySele
`background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${Nn.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="non
ast`,a=document.getElementById(n),i=`${N}-toast-msg`,o=`${N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e
nguage: ${a}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${r==="auto"?"":A2.get(r)+"-"}${A2.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.toString()}``https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${n}``
`+n+`
`+o+`
`+i}}async getCanonicalRequ
eateElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${N}-modal-root
ngPress")}`:""]})]}),C(Lb,{position:n})]})}var fW="";var hr=`${N}-new-user-guide`,bk=``;var G0=`${N}-new-user-guide-root``#${G0}`);a&&r.target!==a&&Nb(G0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function Nb(e){let t=document.querySelector(`#${e}``[data-${N}-walked]``.${vt}``[${Ud}='${n}''`*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function
let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#
   outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(
extension&utm_medium=extension&utm_campaign=side_footer_${t}`,"_blank")}catch{globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}``${await JX()}px`,r="6px",a=document.getElementById(Z9),i=Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,o=Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;if(a)a.remove(),$L(),nf=!1;else{nf=!0,tf=!0,a=document.createElement("div"),a.id=Z9,a.setAttribute
&a.data.area&&a.data.keys&&(te.storage[a.data.area].remove(a.data.keys),e6(a.data.area))}catch(a){I.error("parse message error",a)}})}async function e6(e){let t=document.getElementById(N+"-status"),n=document.getElementById(`${N}-${e}-storage`);if(n){I.debug("init storage");let r=await te.storage[e].get(null);n.value=JSON.stringify(r),n.dispatchEvent(new Event("change")),n.addEventListener("change",a=>{try
x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-rad
i,o]=V(null),s=ge(null),l=ge(null),[u,c]=V("bottomLeft"),[d,m]=V("bottomLeft");Q(()=>{let x=`${N}-modal`,f=y=>{s.current&&!s.current.contains(y.target)&&(a(!1),o(null))};return St(`#${Xr} -> .${x}`)?.addEventListener("mousedown",f),()=>St(`#${Xr} -> .${x}`)?.removeEventListener("mousedown",f)},[]),Q(()=>{p()},[r]);let p=()=>{if(r&&s.current){let x=s.current.getBoundingClientRect(),y=globalThis.innerWidth-x.right;
=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.que
(()=>{var ER=Object.defineProperty;var d6=(e,t)=>{for(var n in t)ER(e,n,{get:t[n],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.ad
document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySelector(`#${N}-toast-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-toast`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function M9(e,t,n,r="retry",a,i,o,s,l
,o.append("format","html"),o.append("lang",`${r==="auto"?"":A2.get(r)+"-"}${A2.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.toString()}``https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${n}``
`+n+`
`+o+`
`+i}}async getCanonicalRequest(t){let n=await In(t),r="POST",a=
&a.method==="removeStorageKey"&&a.data&&a.data.area&&a.data.keys&&(te.storage[a.data.area].remove(a.data.keys),e6(a.data.area))}catch(a){I.error("parse message error",a)}})}async function e6(e){let t=document.getElementById(N+"-status"),n=document.getElementById(`${N}-${e}-storage`);if(n){I.debug("init storage");let r=await te.storage[e].get(null);n.value=JSON.stringify(r),n.dispatchEvent(new Event("change
hTW})||r==="auto"||Kw(r,e.config.translationLanguagePattern)&&(i=!0,I.debug(`match language pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.
anslation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict P
.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh(),U9({selection:e,ctx:t})}catch(n){throw n}}function U9({selection:e,ctx:t,trigger:n}){wo(!0),Te({key:"selection_translate",ctx:t,params:{trigger:n||t.rule.selectionTranslation?.triggerMode||"",translation_service:Qu(t).service}}),kr("translate_line_1",t);let r=document.querySelector(`#${Xr}``imt-dropdown ${t??""}`,style
(Zs),a=[],i=[],o=a0(e,"");for(let s of r){let l=Zs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Zs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Gt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,d=Ol(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=st.sanitize(`<span id="erro
{r}`),mo(r,e.targetLanguage,{ignoreZhCNandZhTW:e.rule.ignoreZhCNandZhTW})||r==="auto"||Kw(r,e.config.translationLanguagePattern)&&(i=!0,I.debug(`match language pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="downloa
r`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style
ener("mouseover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if
}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interfaceLanguage,"viewWithImmersiveTranslate")}</a>``init popup page error: ${n}``meta[name=${N
color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(O){O.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackgrou

      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLab
o members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${
.langMap.get(a);let i=await this.checkLang(r,n);if(!i)return{text:n,from:r,to:a};r=i;let o=this.handleRequest(n,r,a),s=await super.request(o);return{text:this.handleResponseText(s),from:r,to:a}}async translateList(t){if(!Object.keys(this.apiServiceConfig).length)throw new Y("serivce id not found config");let{text:n,from:r,to:a}=t;if(!this.langMap.has(a))throw new Y(`Unsupported language: ${a}``${i.accessTo
o"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=document.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySelector(`#${N}
nd-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${Nn.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="htt
return e.ctx.rule.selectionTranslation?.triggerMode==="icon"?C("div",{class:`${Js}-button icon-btn`,children:C(Hh,{})}):C("div",{class:`${Js}-button mini-btn``font.${vt}`).forEach(p=>{p.remove()}),m?.textContent?.slice(0,200)||""},o=a.previousElementSibling,s=a.nextElementSibling,l=i(o),u=i(s),c=i(a)||r;return`${l}
${c}
${u}`}function OX(e){let t=e.rule.selectionTranslation?.triggerMode;if(!t||!Kd.includes(t
00px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h)
e,url:t});Te({key:"init_page_daily",ctx:n}),mR.domready.then(()=>{if(fR())I.debug("detect web options page"),yd(n,window),bR();else{if(!n.config.enabled)return;if(n.rule.isInjectVersion){let a=Pt(),i=document.createElement("meta");i.name=N+"-version",i.content=a;try{document.head?.appendChild?.(i)}catch(o){I.warn("inject version failed",o)}}if(nt(n.url,n.config.blockUrls))return;yd(n,window),hR(n).then(()=
ay: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-c
n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a
m sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'
Key:r=0,setSettings:a}){HX();let[i,o]=V(!1),[s,l]=V(!1),[u,c]=V({}),[d,m]=V(0),{t:p}=ae(),[g,h]=V(""""}let A=async B=>{if(I.debug("Task start:",B.taskConfig.taskKey),J(!1,!0)&&B.taskConfig.taskKey==="translate_line_1")return;let j=await Z8(),R=E(B.taskConfig.taskKey);if(!j){V9(`${el}&return_url=${encodeURIComponent(R)}``${M}${w}`:j==="pdf_token"?`${M}${P}`:M},O=B=>{let{taskConfig:j,taken:R,completed:M,enab
.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh(),U9({selection:e,ctx
ailPlaceholder"),et.get(gt,null).then(O=>{if(!O)return;let _=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(O.email);O.email&&!_&&(x.value=O.email)});let f=document.createElement("label");f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text}
}`:""]})]}),C(Lb,{position:n})]})}var fW="";var hr=`${N}-new-user-guide`,bk=``;var G0=`${N}-new-user-guide-root``#${G0}`);a&&r.target!==a&&Nb(G0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function Nb(e){let t=document.querySelector(`#${e}``[data-${N}-walked]``.${vt}``[${Ud}='${n}''`*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function th(e){for(var t
ser-guide`,bk=``;var G0=`${N}-new-user-guide-root``#${G0}`);a&&r.target!==a&&Nb(G0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function Nb(e){let t=document.querySelector(`#${e}``[data-${N}-walked]``.${vt}``[${Ud}='${n}''`*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function th(e){for(var t=9,n=e.length;n--;)t=Math.imul(t^e.charCodeAt(n),1597334677
.border};
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportI
ot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=document.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySelector(`#${N}-modal-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-modal`)
rdCenter.reward.get"),"  ",C("span",{className:`${L?"reward-amount-advanced":"reward-amount"}`,children:F(w.rewardAmount,w.rewardType)})," ",D(w.rewardType,!0)]})]}),U?C(sM,{}):J(!1,!0)&&j.taskKey==="translate_line_1"?C(Tt,{text:p("rewardCenter.task.translate_line_1.warning",{1:{tag:"a",style:"color: #EA4C89;",href:Ff+"?utm_campaign=reward_center",target:"_blank"}}),position:"left",tipStyle:{width:"150px",
replace(/"/g,'""''"''${xP}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(O){return l.test(O.trim())}let c=()=>p.value.trim()===""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("styl
removeEventListener("click",u)})):(n.addEventListener("mouseover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&G
;Te({key:"init_page_daily",ctx:n}),mR.domready.then(()=>{if(fR())I.debug("detect web options page"),yd(n,window),bR();else{if(!n.config.enabled)return;if(n.rule.isInjectVersion){let a=Pt(),i=document.createElement("meta");i.name=N+"-version",i.content=a;try{document.head?.appendChild?.(i)}catch(o){I.warn("inject version failed",o)}}if(nt(n.url,n.config.blockUrls))return;yd(n,window),hR(n).then(()=>{sR(n).catch
cument.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${
r='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``/g,"\\`")}${c}
ror-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Zs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Gt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,d=Ol(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=st.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${Ud}='${r}''[contenteditable="true"], [contenteditable=""]'))
er("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("mess
createElement("button");s.setAttribute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_609
n C("div",{ref:s,className:`${N}-dropdown`,children:[C("div",{onClick:g,children:t}),r&&C("div",{ref:l,className:`${N}-dropdown-menu ${u}``<span class="link" id="open-options">${i("setting")}</span>``translate(${y.x}px, ${y.y}px)`};return C("div",{ref:d,className:`${N}-modal ${F?"modal-rtl":""} notranslate ${T?"dark":""}`,dir:F?"rtl":"ltr",style:{...m,...R},children:[C("div",{className:`${N}-modal-title no
eElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",
lThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}``${await JX()}px`,r="6px",a=document.getElementById(Z9),i=Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,o=Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;if(a)a.remove(),$L(),nf=!1;else{nf=!0,tf=!0,a=document.createElement("div"),a.id=Z9,a.setAttribute("style",`
      all: initial;
      position: fixed;
      top: 0px;
      right: 
ext),Zs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function SL(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Zs),a=[],i=[],o=a0(e,"");for(let s of r){let l=Zs[s];if(!l.ok){let c=docume
ance;i&&c.addEventListener("end",i,{once:!0}),c.text=e,c.lang=t,c.rate=s,c.volume=l;let d=Wh.find(m=>m.lang===u)??null;zn()&&u==="en-US"&&(d=Wh.find(m=>m.lang===u&&m.name==="Fred")??null),c.voice=d,a.addEventListener("abort",()=>{speechSynthesis.cancel()},{once:!0}),o?.(),speechSynthesis.speak(c)}async function kX({text:e,lang:t,rate:n,volume:r,signal:a,onFinish:i,onStart:o}){let l=`https://dict.youdao.com/dictvo
(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createEl
et c=ye.bind(null,e.config.interfaceLanguage),d=t||c("errorModalTitle"),m=c("unknownError");o.innerText=d,s.innerHTML=st.sanitize(n||m,{ADD_ATTR:["target"]}),l.innerText="";let p="",g=null,h=document.createElement("button");if(r=="retry"){p=c("retryAllButton"),h.setAttribute(`data-${N}-action``<span id="error-id-${m}">${u.message}</span>`),Zs[m]={ok:!1,sentence:d},TL(l,e,t,n,u)):c&&(g.innerHTML=st.sanitize(c.t
tion:e,ctx:t,trigger:n}){wo(!0),Te({key:"selection_translate",ctx:t,params:{trigger:n||t.rule.selectionTranslation?.triggerMode||"",translation_service:Qu(t).service}}),kr("translate_line_1",t);let r=document.querySelector(`#${Xr}``imt-dropdown ${t??""}`,style:{zIndex:Ia-10,...a},children:[!o&&C("div",{className:"imt-search-box",children:[C(zk,{class:"imt-search-icon"}),C("input",{ref:g,type:"text",placeho
(){let e=document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySelector(`#${N}-toast-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-toast`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function M9(e,t,n,r="ret
=="auto"||Kw(r,e.config.translationLanguagePattern)&&(i=!0,I.debug(`match language pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interf
(()=>{n?.removeEventListener("click",u)})):(n.addEventListener("mouseover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.
ument.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Zs),a=[],i=[],o=a0(e,"");for(let s of r){let l=Zs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Zs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Gt({sentences:i},e,(s,l,u)=>{n+=
transform ${rf}ms ease-out;
      ``${b}px`,YX(`${b}px`)},x=()=>{m&&(m=!1,u.style.pointerEvents="auto",a&&(a.style.transition=`transform ${rf}ms ease-out`),document.removeEventListener("mousemove",h),document.removeEventListener("mouseup",x))}}}async function QX(){let e=document.getElementById(Z9);e&&(e.remove(),$L(),nf=!1)}va.toggleMockSidePanel=KX;va.getIsOpenSidePanel=WX;va.closeMockSidePanel=QX;functio
_${t}``${await JX()}px`,r="6px",a=document.getElementById(Z9),i=Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,o=Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;if(a)a.remove(),$L(),nf=!1;else{nf=!0,tf=!0,a=document.createElement("div"),a.id=Z9,a.setAttribute("style",`
      all: initial;
      position: fixed;
      top: 0px;
      right: 0px;
      width: ${n};
      height: 100%;
      z-index: ${Ia};
      background-colo
ns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`
(${y.x}px, ${y.y}px)`};return C("div",{ref:d,className:`${N}-modal ${F?"modal-rtl":""} notranslate ${T?"dark":""}`,dir:F?"rtl":"ltr",style:{...m,...R},children:[C("div",{className:`${N}-modal-title notranslate`,style:x?{cursor:"grabbing"}:{cursor:"move"},children:[C("div",{className:`${N}-modal-title-left`,children:[C(Hh,{}),u.length>1&&C("div",{class:"toggle-container",children:[C("span",{class:"icon-wrap
ate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySelector(`#${N}-t
Root?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.querySelector(`#${N}-toast-root`);if(e&&(e.style.display="flex",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-toast`);t&&(t.style.display="flex")}}function k9(e,t=!1){let n=document.querySelector(`#${N}-toast-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-toast`);
fallback to ${Vh.service}`),D(Vh)},_=()=>C("div",{className:"error-container",children:[C("div",{className:"error-text",children:s}),C("div",{style:{marginTop:"10px"},children:C("button",{onClick:()=>window.location.reload(),className:"error-button",children:o("error.retry")})})]}),B=()=>C("div",{class:`${N}-loading``${N}-modal-body notranslate ${S?"modal-body-rtl":""}`,children:[u&&B(),s&&_(),!s&&!u&&j(
x:t,trigger:n}){wo(!0),Te({key:"selection_translate",ctx:t,params:{trigger:n||t.rule.selectionTranslation?.triggerMode||"",translation_service:Qu(t).service}}),kr("translate_line_1",t);let r=document.querySelector(`#${Xr}``imt-dropdown ${t??""}`,style:{zIndex:Ia-10,...a},children:[!o&&C("div",{className:"imt-search-box",children:[C(zk,{class:"imt-search-icon"}),C("input",{ref:g,type:"text",placeholder:d("searc
le","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs
onFinish",L),m(!0),u&&c(!1),wo(!1);try{let U=wc(P);U&&x(z=>[...z,U])}catch{}},onError:L=>{if(I.debug("[selection-translation] stream onError",L),xt(e.user)){O();return}u&&c(!1),wo(!1),l(L.message??o("translateFail"))}},e)},F=R=>{e.rule.selectionTranslation?.enableAutoRead&&R?.phonetic&&y(!0)},O=()=>{I.debug(`[selection-translation] fallback to ${Vh.service}`),D(Vh)},_=()=>C("div",{className:"error-containe
tIsOpenSidePanel=WX;va.closeMockSidePanel=QX;function YX(e){let t=document.body;t.style.setProperty("margin-right",e,"important"),t.style.setProperty("width","unset","important")}function ZX(e){let t=document.body;t.style.setProperty("transition",`margin-right ${rf}ms ease-out``detect page language: ${e.url} ${r}`),mo(r,e.targetLanguage,{ignoreZhCNandZhTW:e.rule.ignoreZhCNandZhTW})||r==="auto"||Kw(r,e.conf
throw n}}function U9({selection:e,ctx:t,trigger:n}){wo(!0),Te({key:"selection_translate",ctx:t,params:{trigger:n||t.rule.selectionTranslation?.triggerMode||"",translation_service:Qu(t).service}}),kr("translate_line_1",t);let r=document.querySelector(`#${Xr}``imt-dropdown ${t??""}`,style:{zIndex:Ia-10,...a},children:[!o&&C("div",{className:"imt-search-box",children:[C(zk,{class:"imt-search-icon"}),C("input"
path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=documen
f(fR())I.debug("detect web options page"),yd(n,window),bR();else{if(!n.config.enabled)return;if(n.rule.isInjectVersion){let a=Pt(),i=document.createElement("meta");i.name=N+"-version",i.content=a;try{document.head?.appendChild?.(i)}catch(o){I.warn("inject version failed",o)}}if(nt(n.url,n.config.blockUrls))return;yd(n,window),hR(n).then(()=>{sR(n).catch(a=>{a&&I.debug("translate page error",a.name,a.messag
lTags&&(vR=e.excludeTranslationHtmlTags),Ps({}),st.addHook("beforeSanitizeElements",function(r,a,i){let o=(r.nodeName||"").toLowerCase();if(vR.includes(o)){let l=`<${o}>${r.textContent||""}</${o}>`,u=document.createTextNode(l);return r.replaceWith(u),r}return r}),st.addHook("uponSanitizeElement",function(r,a){let i=r.nodeName||"";/\d+$/.test(i)&&(a.allowedTags[a.tagName]=!0),Yc(r.tagName)&&(a.allowedTags[r
(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createEleme
path="url(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=docu
patchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Zs),a=[],i=[],o=a0(e,"");for(let s of r){let l=Zs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Zs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Gt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,
=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Gt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,d=Ol(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=st.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${Ud}='${r}''[contenteditable="true"], [contenteditable=""]'))}function IL({selectionRect:e,gap:t=10,modalWidth:n=450,modalHeight:r=480}){l
>{if(!O)return;let _=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(O.email);O.email&&!_&&(x.value=O.email)});let f=document.createElement("label");f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("di
rtInfo.emailPlaceholder"),et.get(gt,null).then(O=>{if(!O)return;let _=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(O.email);O.email&&!_&&(x.value=O.email)});let f=document.createElement("label");f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;col
: "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2
4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. 
Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(O){O.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackground};
      co
=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn
,c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=document.querySelector(`#${N}-modal-ro
ef:l,className:`${N}-dropdown-menu ${u}``<span class="link" id="open-options">${i("setting")}</span>``translate(${y.x}px, ${y.y}px)`};return C("div",{ref:d,className:`${N}-modal ${F?"modal-rtl":""} notranslate ${T?"dark":""}`,dir:F?"rtl":"ltr",style:{...m,...R},children:[C("div",{className:`${N}-modal-title notranslate`,style:x?{cursor:"grabbing"}:{cursor:"move"},children:[C("div",{className:`${N}-modal-ti
().any&&r==="float-ball"?`
${a("floatBall.longPress")}`:""]})]}),C(Lb,{position:n})]})}var fW="";var hr=`${N}-new-user-guide`,bk=``;var G0=`${N}-new-user-guide-root``#${G0}`);a&&r.target!==a&&Nb(G0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function Nb(e){let t=document.querySelector(`#${e}``[data-${N}-walked]``.${vt}``[${Ud}='${n}''`*+.,;:\\/<=>?@#$%&^|~()[\]{}]
 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackgr
nsform: translateX(100%);
      transition: transform ${rf}ms ease-out;
      ``${b}px`,YX(`${b}px`)},x=()=>{m&&(m=!1,u.style.pointerEvents="auto",a&&(a.style.transition=`transform ${rf}ms ease-out`),document.removeEventListener("mousemove",h),document.removeEventListener("mouseup",x))}}}async function QX(){let e=document.getElementById(Z9);e&&(e.remove(),$L(),nf=!1)}va.toggleMockSidePanel=KX;va.getIsOpenS
hanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveM
Selector(`#${N}-modal`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function eL(e,t,n="sameLang",r,a,i,o){let s=ye.bind(null,e.config.interfaceLanguage),l=t;n=="sameLang"&&(l=s("sameLangNoTranslate")),a.innerText=l,i.innerText=s("neverShowFuture"),n=="sameLang"&&(i.style.display="flex",i.onclick=async()=>{let u=await un();Yt({...u,sameLangCheck:!1}),o(i,!0)})}var tL=0;async function nL(e){let t
hadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.display=="flex":!1}function dX(){let e=document.querySelector(`#${N}-toast-root`);if
{N}-toast-content`,s=`${N}-toast-hidden`,l,u,c=()=>{u&&k9(u,!0)},d,m;if(a)m=a.shadowRoot.querySelector(`.${r}`),l=m.querySelector(`.${i}`),u=m.querySelector(`.${s}`),l.innerHTML="",e(m,l,u,k9);else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-toast-shadow-root``#${N}-toast-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-toast`)?.style.displ
,h=document.createElement("button");if(r=="retry"){p=c("retryAllButton"),h.setAttribute(`data-${N}-action``<span id="error-id-${m}">${u.message}</span>`),Zs[m]={ok:!1,sentence:d},TL(l,e,t,n,u)):c&&(g.innerHTML=st.sanitize(c.text),Zs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function SL(e){let t=0,n=0;kn
retryAllButton"),h.setAttribute(`data-${N}-action``<span id="error-id-${m}">${u.message}</span>`),Zs[m]={ok:!1,sentence:d},TL(l,e,t,n,u)):c&&(g.innerHTML=st.sanitize(c.text),Zs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function SL(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(J
,onClick:m=>d(m),children:C(Mu,{})})}function _L({ctx:e,translationResult:t,text:n,currentIndex:r,visible:a}){let[i,o]=V({}),s=u=>{if(r===void 0||i[r]===u)return;o(m=>({...m,[r]:u}));let d=`selection_translate_${u.toLowerCase()}``<span class="highlight">${R.slice(L,L+M.length)}</span>`,w=L+M.length}return P},D=async({service:R,model:M})=>{l(null),c(!0),m(!1),g(void 0);let P="",w=null;try{w=await qh({text:t
{let e=document.querySelector(`#${N}-modal-root`);if(e&&(e.style.display="block",e.shadowRoot)){let t=e.shadowRoot.querySelector(`#${N}-modal`);t&&(t.style.display="block")}}function bd(e,t=!1){let n=document.querySelector(`#${N}-modal-root`);if(!n||n.style.display=="none")return;let r=n.shadowRoot.querySelector(`#${N}-modal`);r&&r!==e&&(!t&&r.contains(e)||(r.style.display="none"))}function eL(e,t,n="sameL
=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${N}-modal-root`,r=`${N}-modal`,a=document.getElementById(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelect
,s,l,u){let c=ye.bind(null,e.config.interfaceLanguage),d=t||c("errorModalTitle"),m=c("unknownError");o.innerText=d,s.innerHTML=st.sanitize(n||m,{ADD_ATTR:["target"]}),l.innerText="";let p="",g=null,h=document.createElement("button");if(r=="retry"){p=c("retryAllButton"),h.setAttribute(`data-${N}-action``<span id="error-id-${m}">${u.message}</span>`),Zs[m]={ok:!1,sentence:d},TL(l,e,t,n,u)):c&&(g.innerHTML=st
de_footer_${t}``${await JX()}px`,r="6px",a=document.getElementById(Z9),i=Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,o=Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;if(a)a.remove(),$L(),nf=!1;else{nf=!0,tf=!0,a=document.createElement("div"),a.id=Z9,a.setAttribute("style",`
      all: initial;
      position: fixed;
      top: 0px;
      right: 0px;
      width: ${n};
      height: 100%;
      z-index: ${Ia};
      ba
setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackground};
      co
 *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Examp
h(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh(),U9({selection:e,ctx:t})}catch(n){throw n}}function U9({selection:e,ctx:t
ut I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="transla
modal-root`,r=`${N}-modal`,a=document.getElementById(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-err
seover",u),Vo.push(()=>{n?.removeEventListener("mouseover",u)}))}function Jh(){let e=St(`#${Zu}`);e&&e.remove()}function VL(e){let t=document.querySelector(`#${Zu}`);e.target!==t&&Jh();let n=document.querySelector(`#${Xr}`),r=document.querySelector(`#${N}-popup`),a=document.querySelector(`#${N}-modal-root`);n&&e.target!==n&&e.target!==r&&e.target!==a&&GL?.()}function z9({lastSelection:e,ctx:t}){if(!d0())try{Jh
itle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``${i}-${r}-${a}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translatio
tener("mouseup",x))}}}async function QX(){let e=document.getElementById(Z9);e&&(e.remove(),$L(),nf=!1)}va.toggleMockSidePanel=KX;va.getIsOpenSidePanel=WX;va.closeMockSidePanel=QX;function YX(e){let t=document.body;t.style.setProperty("margin-right",e,"important"),t.style.setProperty("width","unset","important")}function ZX(e){let t=document.body;t.style.setProperty("transition",`margin-right ${rf}ms ease-o
g.translationLanguagePattern)&&(i=!0,I.debug(`match language pattern ${r}, auto translate``
``font.notranslate.${vt}``${s}/`+o,responseType:"HEAD",method:"HEAD"});let l=document.createElement("li");l.innerHTML=`<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(e.config.interfaceLanguage,"viewW
lockUrls))return;yd(n,window),hR(n).then(()=>{sR(n).catch(a=>{a&&I.debug("translate page error",a.name,a.message,a)})}).catch(a=>{I.debug("can not detect a valid body: ",a)})}}).catch(r=>{r&&I.debug("translate dom ready detect error",r)})}L$().catch(e=>{I.debug("init error",e)});})();


button");s.setAttribute("style","margin-top:36px"),s.className=N+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function sX(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d
r"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`margin-left: 58px
sary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{
mentById(N+"-status"),n=document.getElementById(`${N}-${e}-storage`);if(n){I.debug("init storage");let r=await te.storage[e].get(null);n.value=JSON.stringify(r),n.dispatchEvent(new Event("change")),n.addEventListener("change",a=>{try{let i=JSON.parse(a.target.value);te.storage[e].set(i)}catch(i){I.error("save to storage error",i)}}),n.addEventListener("refresh-"+e,async a=>{let i=await te.storage[e].get(null);n.v
=e&&(!t&&r.contains(e)||(r.style.display="none"))}function M9(e,t,n,r="retry",a,i,o,s,l,u){let c=ye.bind(null,e.config.interfaceLanguage),d=t||c("errorModalTitle"),m=c("unknownError");o.innerText=d,s.innerHTML=st.sanitize(n||m,{ADD_ATTR:["target"]}),l.innerText="";let p="",g=null,h=document.createElement("button");if(r=="retry"){p=c("retryAllButton"),h.setAttribute(`data-${N}-action``<span id="error-id-${m
margin:12px; 16px 0;``${N}-modal-root`,r=`${N}-modal`,a=document.getElementById(n),i=`${N}-modal-title`,o=`${N}-modal-body`,s=`${N}-modal-footer`,l;if(a){l=a.shadowRoot.querySelector(`.${r}`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.class
fiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`
emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`marg
`);let u=l.querySelector(`.${i}`),c=l.querySelector(`.${s}`),d=l.querySelector(`.${o}`);u.innerHTML="",c.innerHTML="",d.innerHTML="",e(l,u,d,c,bd)}else{a=document.createElement("div"),a.setAttribute("translate","no"),a.className=`no-translate ${N}-error-modal-shadow-root``#${N}-modal-root`);return e&&e.shadowRoot?e.shadowRoot.querySelector(`#${N}-modal`)?.style.display=="block":!1}function uX(){let e=docum
dateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`
ull);n.value=JSON.stringify(i),I.debug("refresh ",e,"storage")})}else{I.error(`Could not find storage ${e} element`),t.innerText="Could not find storage local input element";return}}function P$(){try{document.dispatchEvent(new Event(_6))}catch{}}function yR(){na()||P$()}var vR=["textarea","input","button","select","option","iframe","strong","form","body"];async function L$(){tt()||WT(Ie()),yR(),await u5();
/s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCom