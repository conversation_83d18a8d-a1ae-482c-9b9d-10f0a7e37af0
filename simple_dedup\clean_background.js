(()=>{var Fg=Object.defineProperty;var ml=(e,t)=>{for(var a in t)Fg(e,a,{get:t[a],enumerable:!0})};var h={BUILD_TIME:"2025-07-25T09:38:29.000Z",VERSION:"1.19.6",PROD:"1",REDIRECT_URL:"https://dash.immersivetranslate.com/auth-done/",PROD_API:"1",BETA:"0",userscript_domains:'["google.com","translate.googleapis.com","api-edge.cognitive.microsofttranslator.com","edge.microsoft.com","transmart.qq.com","translate.yandex.net","tmt.tencentcloudapi.com","www2.deepl.com","w.deepl.com","immersive-translate/* MULTIPLE_REMOVED_BLOCKS */}

/* \u5DF2\u5B8C\u6210\u6309\u94AE\u6837\u5F0F */
.reward-task-item .reward-task-button.completed {
  background: #E8E8E8;
  color: #999;
  cursor: default;
}

/* \u5DF2\u5B8C\u6210\u4EFB\u52A1\u5206\u9694\u7B26\u6837\u5F0F */
.completed-tasks-divider {
  color: #ccc;
  font-size: 12px;
  padding: 8px 0;
  border-top: 1px solid #ECF0F7;
  position: relative;
  gap: 4px;
  display: flex;
  align-items: center;
}

.completed-tasks-divider.clickable {
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
}

.completed-tasks-divider.clickable:hover {
  color: #666;
}

.completed-tasks-text {
  font-weight: 500;
}

.completed-tasks-arrow {
  transition: transform 0.2s ease;
}

.completed-tasks-arrow.expanded {
  transform: rotate(-180deg);
}

.reward-center-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: ShadowRolling 1.5s linear infinite;
}

@keyframes ShadowRolling {
  0%, 100% {
    box-shadow: 0 0 rgba(255, 255, 255, 0);
  }
  12% {
    box-shadow: 100px 0 var(--loading-color);
  }
  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }
  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }
  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }
  62% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }
  75% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }
  87% {
    box-shadow: 130px 0 var(--loading-color);
  }
}

.reward-center-error {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 16px;
  word-break: break-all;
  color: #EA4C89;
}

.reward-center-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-center-footer-text {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  cursor: pointer;
}

/* \u4EFB\u52A1 Loading \u8F6C\u5708\u52A8\u753B */
.reward-task-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-task-loading::before {
  content: '';
  width: 8px;
  height: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: reward-task-spin 1s linear infinite;
}

@keyframes reward-task-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
`,OPTIONS_URL:"https://dash.immersivetranslate.com/",SHARE_DRAFT_URL:"https://immersivetranslate.com/preview",ASSETS_BASE_URL:"https://s.immersivetranslate.com/static/extension/",EBOOK_VIEWER_URL:"https://app.immersivetranslate.com/ebook/",EBOOK_BUILDER_URL:"https://app.immersivetranslate.com/ebook/make/",SUBTITLE_BUILDER_URL:"https://app.immersivetranslate.com/subtitle/",HTML_VIEWER_URL:"https://app.immersivetranslate.com/html/",MARKDOWN_VIEWER_URL:"https://app.immersivetranslate.com/markdown/",BABELDOC_URL:"https://app.immersivetranslate.com/babel-doc/",PDF_VIEWER_URL:"https://app.immersivetranslate.com/pdf/",PDF_PRO_URL:"https://app.immersivetranslate.com/pdf-pro/",TEXT_TRANSLATE_URL:"https://app.immersivetranslate.com/text/",TRANSLATE_FILE_URL:"https://app.immersivetranslate.com/"};var Rg=Object.create,xo=Object.defineProperty,_g=Object.getOwnPropertyDescriptor,gl=Object.getOwnPropertyNames,jg=Object.getPrototypeOf,Lg=Object.prototype.hasOwnProperty,Ng=(e,t)=>function(){return t||(0,e[gl(e)[0]])((t={exports:{}}).exports,t),t.exports},Og=(e,t)=>{for(var a in t)xo(e,a,{get:t[a],enumerable:!0})},vo=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of gl(t))!Lg.call(e,r)&&r!==a&&xo(e,r,{get:()=>t[r],enumerable:!(n=_g(t,r))||n.enumerable});return e},zg=(e,t,a)=>(vo(e,t,"default"),a&&vo(a,t,"default")),hl=(e,t,a)=>(a=e!=null?Rg(jg(e)):{},vo(t||!e||!e.__esModule?xo(a,"default",{value:e,enumerable:!0}):a,e)),fl=Ng({"../esmd/npm/webextension-polyfill@0.10.0/node_modules/.pnpm/webextension-polyfill@0.10.0/node_modules/webextension-polyfill/dist/browser-polyfill.js"(e,t){(function(a,n){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],n);else if(typeof e<"u")n(t);else{var r={exports:{}};n(r),a.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:e,function(a){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let n="The message port closed before a response was received.",r=i=>{let o={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:/* REMOVED_COMMON_BLOCK_377 */equest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(o).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class s extends WeakMap{constructor(x,k=void 0){super(k),this.createItem=x}get(x){return this.has(x)||this.set(x,this.createItem(x)),super.get(x)}}let u=E=>E&&typeof E=="object"&&typeof E.then=="function",c=(E,x)=>(...k)=>{i.runtime.lastError?E.reject(new Error(i.runtime.lastError.message)):x.singleCallbackArg||k.length<=1&&x.singleCallbackArg!==!1?E.resolve(k[0]):E.resolve(k)},l=E=>E==1?"argument":"arguments",d=(E,x)=>function(y,...j){if(j.length<x.minArgs)throw new Error(`Expected at least ${x.minArgs} ${l(x.minArgs)} for ${E}(), got ${j.length}`);if(j.length>x.maxArgs)throw new Error(`Expected at most ${x.maxArgs} ${l(x.maxArgs)} for ${E}(), got ${j.length}`);return new Promise((_,P)=>{if(x.fallbackToNoCallback)try{y[E](...j,c({resolve:_,reject:P},x))}catch{y[E](...j),x.fallbackToNoCallback=!1,x.noCallback=!0,_()}else x.noCallback?(y[E](...j),_()):y[E](...j,c({resolve:_,reject:P},x))})},m=(E,x,k)=>new Proxy(x,{apply(y,j,_){return k.call(j,E,..._)}}),p=Function.call.bind(Object.prototype.hasOwnProperty),v=(E,x={},k={})=>{let y=Object.create(null),j={has(P,N){return N in E||N in y},get(P,N,X){if(N in y)return y[N];if(!(N in E))return;let Y=E[N];if(typeof Y=="function")if(typeof x[N]=="function")Y=m(E,E[N],x[N]);else if(p(k,N)){let ke=d(N,k[N]);Y=m(E,E[N],ke)}else Y=Y.bind(E);else if(typeof Y=="object"&&Y!==null&&(p(x,N)||p(k,N)))Y=v(Y,x[N],k[N]);else if(p(k,"*"))Y=v(Y,x[N],k["*"]);else return Object.defineProperty(y,N,{configurable:!0,enumerable:!0,get(){return E[N]},set(ke){E[N]=ke}}),Y;return y[N]=Y,Y},set(P,N,X,Y){return N in y?y[N]=X:E[N]=X,!0},defineProperty(P,N,X){return Reflect.defineProperty(y,N,X)},deleteProperty(P,N){return Reflect.deleteProperty(y,N)}},_=Object.create(E);return new Proxy(_,j)},w=E=>({addListener(x,k,...y){x.addListener(E.get(k),...y)},hasListener(x,k){return x.hasListener(E.get(k))},removeListener(x,k){x.removeListener(E.get(k))}}),S=new s(E=>typeof E!="function"?E:function(k){let y=v(k,{},{getContent:{minArgs:0,maxArgs:0}});E(y)}),g=new s(E=>typeof E!="function"?E:function(k,y,j){let _=!1,P,N=new Promise(Me=>{P=function(G){_=!0,Me(G)}}),X;try{X=E(k,y,P)}catch(Me){X=Promise.reject(Me)}let Y=X!==!0&&u(X);if(X!==!0&&!Y&&!_)return!1;let ke=Me=>{Me.then(G=>{j(G)},G=>{let oe;G&&(G instanceof Error||typeof G.message=="string")?oe=G.message:oe="An unexpected error occurred",j({__mozWebExtensionPolyfillReject__:!0,message:oe})}).catch(G=>{})};return ke(Y?X:N),!0}),M=({reject:E,resolve:x},k)=>{i.runtime.lastError?i.runtime.lastError.message===n?x():E(new Error(i.runtime.lastError.message)):k&&k.__mozWebExtensionPolyfillReject__?E(new Error(k.message)):x(k)},f=(E,x,k,...y)=>{if(y.length<x.minArgs)throw new Error(`Expected at least ${x.minArgs} ${l(x.minArgs)} for ${E}(), got ${y.length}`);if(y.length>x.maxArgs)throw new Error(`Expected at most ${x.maxArgs} ${l(x.maxArgs)} for ${E}(), got ${y.length}`);return new Promise((j,_)=>{let P=M.bind(null,{resolve:j,reject:_});y.push(P),k.sendMessage(...y)})},T={devtools:{network:{onRequestFinished:w(S)}},runtime:{onMessage:w(g),onMessageExternal:w(g),sendMessage:f.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:f.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},L={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return o.privacy={network:{"*":L},services:{"*":L},websites:{"*":L}},v(i,T,o)};a.exports=r(chrome)}else a.exports=globalThis.browser})}}),bl={};Og(bl,{default:()=>Hr});var Ug=hl(fl());zg(bl,hl(fl()));var{default:pl,...qg}=Ug,Hr=pl!==void 0?pl:qg;globalThis.immersiveTranslateBrowserAPI=Hr;var{Deno:yl}=globalThis,Gg=typeof yl?.noColor=="boolean"?yl.noColor:!0,Hg=!Gg;function Kr(e,t){return{open:`\x1B[${e.join(";")}m`,close:`\x1B[${t}m`,regexp:new RegExp(`\\x1b\\[${t}m`,"g")}}function Wr(e,t){return Hg?`${t.open}${e.replace(t.regexp,t.open)}${t.close}`:e}function wo(e){return Wr(e,Kr([2],22))}function Vr(e){return Wr(e,Kr([31],39))}function Ao(e){return Wr(e,Kr([32],39))}function ko(e){return Wr(e,Kr([33],39))}var Cw=new RegExp(["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|"),"g");function ue(){return typeof process>"u"&&typeof Deno<"u"?Deno.env.toObject():h}var re=ue();function Kg(){return typeof location>"u"?!1:location.href.includes("side-panel")&&location.href.includes("extension://")}function fe(e,t){return!t&&Kg()?!0:e&&globalThis?.document?.querySelector("meta[name=immersive-translate-options]")?!!globalThis.document?.getElementById("immersive-translate-manifest")?.value?.includes("_isUserscript"):re.IMMERSIVE_TRANSLATE_USERSCRIPT==="1"}function zt(){return re.PROD==="1"}function Qr(){return re.PROD_API==="1"}function Ze(){if(re.IMMERSIVE_TRANSLATE_SAFARI==="1")return!0;if(typeof globalThis.immersiveTranslateBrowserAPI<"u"&&globalThis.immersiveTranslateBrowserAPI.runtime&&globalThis.immersiveTranslateBrowserAPI.runtime.getManifest){let t=globalThis.immersiveTranslateBrowserAPI.runtime.getManifest();return!!(t&&t._isSafari)}else return!1}function Yr(){return typeof Deno<"u"}var Fw=ue().PROD==="1",Bw=ue().PROD!=="1";function Eo(){if(typeof globalThis.immersiveTranslateBrowserAPI<"u"&&globalThis.immersiveTranslateBrowserAPI.runtime&&globalThis.immersiveTranslateBrowserAPI.runtime.getManifest){let e=globalThis.immersiveTranslateBrowserAPI.runtime.getManifest();return!!(e&&(e._isUserscript||e._isSafari))}else return!1}function ya(){return re.IMMERSIVE_TRANSLATE_JSSDK==="1"}var O="immersiveTranslate",Na="Immersive Translate",de="immersive-translate",xl="imt",Wg="immersivetranslate",wl="pdf/index.html";var De="immersivetranslate.com",Vg=`https://config.${De}/`,Lw=`https://app.${De}/`,B=zt()||Qr()?`https://${De}/`:`https://test.${De}/`,Jr=`https://dash.${De}/`,So=zt()||Qr()?`https://api2.${De}/`:`https://test-api2.${De}/`,To=zt()||Qr()?`https://ai.${De}/`:`https://test-ai.${De}/`,Qg=`https://assets.${Wg}.cn/`,Al=B+"accounts/login?from=plugin",Do=B+"profile/",ze=B+"auth/pricing/",ea=B+"pricing/";Ze()&&(ze=B+"accounts/safari-iap/",ea=B+"accounts/safari-iap/");function kl(e){e&&(B=`https://test.${De}/`,So=`https://test-api2.${De}/`,Al=B+"accounts/login?from=plugin",Do=B+"profile/",ze=B+"auth/pricing/",ea=B+"pricing/",Ze()&&(ze=B+"accounts/safari-iap/",ea=B+"accounts/safari-iap/"))}var Wn=zt()?`https://onboarding.${De}/`:`https://test-onboarding.${De}/`,El=`https://github.com/${de}/${de}/`,Nw=`https://s.${De}/`;var Ow=O+"DeeplGlobalState",zw=O+"BingGlobalState",Uw=O+"YandexGlobalState",qw=O+"BaiduQianfanGlobalConfigStorageKey",Gw=O+"SiliconCloudGlobalConfigStorageKey",Hw=O+"ZhipuGlobalConfigStorageKey",ti="************-mfm15s5nd77vfmo6e7lanof1emnanf0e.apps.googleusercontent.com",pn=O+"GoogleAccessToken",Sl=O+"AuthFlow",ai=de+"-config-latest.json",Kw=O+"AuthState",Ww=O+"IframeMessage",Vw=O+"WaitForRateLimit",Qw=O+"DocumentMessageAsk",Yg=O+"DocumentMessageTellThirdParty",Yw=O+"showError",Tl=O+"showModal",Jw=O+"showToast",Zw=O+"tokenUsageChange",Xw=O+"DocumentMessageThirdPartyTell",$w=O+"DocumentMessageEventUpload",eA=O+"DocumentMessageTypeStopJsSDK",tA=O+"DocumentMessageHandler",aA=O+"DocumentSetFloatBallActive",Dl=`${O}Share`,nA=`${O}ShowFloatBallGuide`,rA=`${O}ShowPopupModalGuide`,iA=O+"DocumentMessageTempEnableSubtitleChanged",oA=O+"DocumentMessageUpdateQuickButtonAiSubtitle",Cl=`${O}ToggleMouseHoverTranslateDirectly`,sA=`${O}ReqDraft`,uA=`${O}ResDraft`,Jg=`${O}Container`,Zg=`${O}SpecifiedContainer`,Co="buildinConfig",Vn="localConfig",Mo="openOptionsPage",Oa="translateMangaMenuId";var Io="translateLocalPdfFile",Po="openEbookViewer",Fo="openEbookBuilder",Ml="openSubtitleBuilder",Xg=`${O}PageTranslatedStatus`,$g=`${O}MangaTranslatedStatus`,lA=`${O}PageUrlChanged`,cA=`${O}ReceiveCommand`,dA=O+"LastUseMouseHoverTime",mA=O+"LastUseInputTime",ft=O+"LastUseManualTranslatePageTime",eh=`${O}PopupReceiveMessage`,pA=O+"DocumentMessageEventTogglePopup",th=`${Vg}default_config.json`,gA=`${O}Mark`,ah=`${O}Root`,hA=`${O}Walked`,fA=`data-${de}-walked`,bA=`${O}Paragraph`,yA=`data-${de}-paragraph`,vA=`data-${de}-translation-element-mark`,xA=`${O}TranslationElementMark`,wA=`${O}TranslatedMark`,nh=`${de}-input-injected-css`,rh=`${O}LoadingId`,ih=`data-${de}-loading-id`,AA=`${O}ErrorId`,kA=`data-${de}-error-id`,oh=`${O}AtomicBlockMark`,sh=`${O}ExcludeMark`,EA=`data-${de}-exclude-mark`,uh=`${O}StayOriginalMark`,SA=`${O}PreWhitespaceMark`,lh=`${O}InlineMark`,ch=`${O}BlockMark`,TA=`${O}Left`,DA=`${O}Right`,CA=`${O}Width`,MA=`${O}Height`,IA=`${O}Top`,PA=`${O}FontSize`;var ni="lastClearCacheTime",Bo="firstClearCacheTime",FA=`${O}GlobalStyleMark`,Ro=["@","#"];var dh=`${de}-target-wrapper`,BA=`${de}-pdf-target-container`,RA=`${de}-target-inner`,_A=`${de}-source-wrapper`,jA=`${de}-target-translation-block-wrapper`,LA=`${de}-root-translation-theme`,NA=`${O}RootTranslationTheme`,OA=`${de}-target-translation-vertical-block-wrapper`,zA=`${de}-target-translation-pdf-block-wrapper`,UA=`${de}-target-translation-pre-whitespace`,qA=`${de}-target-translation-inline-wrapper`;var GA=["https://immersive-translate.owenyoung.com/options/","https://immersive-translate.owenyoung.com/auth-done/",Jr,Jr+"auth-done/","http://localhost:8000/dist/userscript/options/","http://localhost:8000/auth-done/","http://************:8000/dist/userscript/options/","http://*************:8000/dist/userscript/options/","http://************:8000/dist/userscript/options/","https://www.deepl.com/translator","translate.google.com","http://localhost:8000/options/","http://************:8000/options/","http://*************:8000/options/","http://************:8000/options/"],ta="zh-CN",HA=B+"docs/communities/",KA=El+"issues/1809",WA=El+"issues/1179",VA={type:O+"ChildFrameToRootFrameIdentifier"};var ri=zt()?Jr+"#general":"http://localhost:8000/dist/userscript/options/#general";var bt="user_info",Il=Jr+"#general",QA=B+"accounts/login?from=plugin&return_url="+encodeURIComponent(Il),YA=Al+"&utm_source=extension&utm_medium=extension&utm_campaign=error_modal",mh=B+"download/",ph=B+"topup?type=open_ai&",gh=B+"topup?type=deepl&",Pl=B+"topup?type=comics&",JA=ea+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_more",ZA=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_guide",XA=mh+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",$A=ea+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_footer",ek=ea+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Fl=ea+"?utm_source=extension&utm_medium=extension&utm_campaign=max_",tk=ea+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",ak=Do+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",nk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=subtitle_download",rk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal_ai_subtitle",ik=ph+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",ok=gh+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",_o=B+"topup?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",sk=ea+"?utm_source=extension&utm_medium=extension&utm_campaign=option_sync_config",Bl=Do+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal&upgradeFromTrial=true",uk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_intro",lk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=image_intro",ck=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=image_client",dk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=yt_ai_asr",mk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=",pk=B+"accounts/usage",gk=B+"docs/usage/",hk=B+"docs/communities/",Qn=ue().TRANSLATE_FILE_URL,fk=Qn+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",bk=Qn+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",yk=`${Qn}download-subtitle/`,vk=`${Qn}pdf-pro/`,xk=`${Qn}text/`;var wk=B+"docs/usage/";var Rl="G-BHWL0KMJB8",_l="7pr-olTJR6GKAjIW48UD0Q",Zr="G-MKMD9LWFTR",Xr="sitc4WmvShWYwfU0dANM3Q",$r="G-V5H2F5MJFJ",ei="UBjpGOLISEaY5LVXNj3WvQ",jl="G-0LPWNREXBB",Ll="JVF4YeOaTVyDV27CTLhPtQ";function Nl(){return ya()?[{measurementId:Rl,apiSecret:_l}]:zt?[{measurementId:Zr,apiSecret:Xr},{measurementId:$r,apiSecret:ei}]:[{measurementId:Zr,apiSecret:Xr},{measurementId:$r,apiSecret:ei}]}function ii(){return ya()?[`https://www.google-analytics.com/mp/collect?measurement_id=${Rl}&api_secret=${_l}`]:zt?[`https://www.google-analytics.com/mp/collect?measurement_id=${Zr}&api_secret=${Xr}`,`https://www.google-analytics.com/mp/collect?measurement_id=${$r}&api_secret=${ei}`]:[`https://www.google-analytics.com/debug/mp/collect?measurement_id=${Zr}&api_secret=${Xr}`,`https://www.google-analytics.com/debug/mp/collect?measurement_id=${$r}&api_secret=${ei}`]}var za=`https://analytics.${De}/collect`,Ak=`https://analytics.${De}/internal`,kk=`${B}activities/components/image-pro`;var Ol="LdgzvqcdlDvNLdxrJVtZqxMTKaIgExlL",zl="0VmM83i2D1ICuYBf",Ek=50*1e4,Sk=`[${xl}-ctx-divider]`,hh=`${xl}_context_preview`,Ul=`\u{1F44B} \u563F\uFF0C\u4F60\u53D1\u73B0\u4E86\u5F69\u86CB\uFF01
    \u65E2\u7136\u88AB\u4F60\u53D1\u73B0\u4E86\uFF0C\u4E0D\u5982\u770B\u770B\u6211\u4EEC\u7684\u804C\u4F4D\uFF1F

    \u5173\u4E8E\u6211\u4EEC:
    \u2022 \u8FFD\u6C42\u6280\u672F\u521B\u65B0\u7684\u79D1\u6280\u516C\u53F8
    \u2022 \u4F18\u79C0\u7684\u56E2\u961F\uFF0C\u5F00\u653E\u7684\u6587\u5316
    \u2022 \u5177\u6709\u7ADE\u4E89\u529B\u7684\u85AA\u8D44\u5F85\u9047
    \u2022 \u5168\u5458\u8FDC\u7A0B\u529E\u516C

    \u6211\u4EEC\u5728\u5BFB\u627E:
{jobs}

    \u6B22\u8FCE\u52A0\u5165\u6211\u4EEC\uFF01

    \u{1F4EE} \u6295\u9012\u7B80\u5386: <EMAIL>
    \u{1FAE1} \u4E86\u89E3\u66F4\u591A: https://immersivetranslate.com/zh-Hans/docs/joinUs/`;var jo="fullLocalUserConfig";var ql="https://<EMAIL>/4506813369548800",Tk=`${O}_selection_update_params`,Dk=`data-${de}-subtitle-type`,Ck=`data-${de}-ai-subtitle-url`,Mk=`data-${de}-has-subtitle`;var Lo=["translationService","inputTranslationService","mouseHoverTranslationService","subtitleTranslateService","clientImageTranslationService"];var Ik=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=freeImageError";var fh=de+"-large-cache";var Pk=ze+"?utm_source=extension&utm_medium=extension&utm_campaign=live_subtitle_btn";var gn=console,No=class{#e=performance.now();reset(){this.#e=performance.now()}stop(t){let a=performance.now(),n=Math.round(a-this.#e),r=Ao;n>1e4?r=Vr:n>1e3&&(r=ko),gn.debug(wo(Na+" TIMING:"),t,"in",r(n+"ms")),this.#e=a}},Yn=class{#e=1;get level(){return this.#e}setLevel(t){switch(t){case"debug":this.#e=0;break;case"info":this.#e=1;break;case"warn":this.#e=2;break;case"error":this.#e=3;break;case"fatal":this.#e=4;break}}debug(...t){this.#e<=0&&gn.log(wo(Na+" DEBUG:"),...t)}v(...t){this.#e<=0}info(...t){this.#e<=1&&gn.log(Ao(Na+" INFO:"),...t)}l(...t){this.#e<=1}warn(...t){this.#e<=2&&gn.warn(ko(Na+" WARN:"),...t)}error(...t){this.#e<=3&&gn.error(Vr(Na+" ERROR:"),...t)}fatal(...t){this.#e<=4&&gn.error(Vr(Na+" FATAL:"),...t)}timing(){return this.level===0?new No:{reset:()=>{},stop:()=>{}}}},I=new Yn;var Jn=/* REMOVED_PATTERN_71 */,Gl={af:"Afrikaans",am:"Amharic",ar:"Arabic",auto:"Auto Detect",az:"Azerbaijani",be:"Belarusian",bg:"Bulgarian",tn:"Zana",bn:"Bengali",bs:"Bosnian",bo:"Tibetan",ca:"Catalan",ceb:"Cebuano",co:"Corsican",cs:"Czech",cy:"Welsh",da:"Danish",de:"German",el:"Greek",en:"English",eo:"Esperanto",es:"Spanish",et:"Estonian",eu:"Basque",sa:"Sanskrit",fa:"Persian",fi:"Finnish",fil:"Filipino",fj:"Fijian",fr:"French",fy:"Frisian",ga:"Irish",gd:"Scottish Gaelic",gl:"Galician",gu:"Gujarati",ha:"Hausa",haw:"Hawaiian",he:"Hebrew",hi:"Hindi",hmn:"Hmong",hr:"Croatian",ht:"Haitian Creole",hu:"Hungarian",hy:"Armenian",id:"Indonesian",ig:"Igbo",is:"Icelandic",it:"Italian",ja:"Japanese",jw:"Javanese",ka:"Georgian",kk:"Kazakh",km:"Khmer",kn:"Kannada",ko:"Korean",ku:"Kurdish",ky:"Kyrgyz",la:"Latin",lb:"Luxembourgish",lo:"Lao",lt:"Lithuanian",lv:"Latvian",mg:"Malagasy",mi:"Maori",mk:"Macedonian",ml:"Malayalam",mn:"Mongolian",mr:"Marathi",ms:"Malay",mt:"Maltese",mww:"Hmong Daw",my:"Burmese",ne:"Nepali",nl:"Dutch",no:"Norwegian",ny:"Chichewa",otq:"Quer\xE9taro Otomi",pa:"Punjabi",pl:"Polish",ps:"Pashto",pt:"Portuguese","pt-br":"Portuguese (Brazil)","pt-BR":"Portuguese (Brazil)","pt-PT":"Portuguese (Portugal)",ro:"Romanian",ru:"Russian",sd:"Sindhi",si:"Sinhala",sk:"Slovak",sl:"Slovenian",sm:"Samoan",sn:"Shona",so:"Somali",sq:"Albanian",sr:"Serbian","sr-Cyrl":"Serbian (Cyrillic)","sr-Latn":"Serbian (Latin)",st:"Sesotho",su:"Sundanese",sv:"Swedish",sw:"Swahili",ta:"Tamil",te:"Telugu",tg:"Tajik",th:"Thai",tlh:"Klingon","tlh-Qaak":"Klingon (piqaD)",to:"Tongan",tr:"Turkish",ty:"Tahitian",ug:"Uyghur",uk:"Ukrainian",ur:"Urdu",uz:"Uzbek",vi:"Vietnamese",wyw:"Classical Chinese",xh:"Xhosa",placeholder:"Please Select",yi:"Yiddish",yo:"Yoruba",yua:"Yucatec Maya",yue:"Cantonese (Traditional)","zh-CN":"Simplified Chinese","zh-TW":"Traditional Chinese (Taiwan)","zh-HK":"Traditional Chinese (Hong Kong)","zh-CN-NE":"Northeastern Chinese",zu:"Zulu","<all>":"All Languages","ur-roman":"Roman Urdu"},Hl={af:"Afrikaans",am:"\u12A0\u121B\u122D\u129B",ar:"\u0627\u0644\u0639\u0631\u0628\u064A\u0629",auto:"Auto Detect",az:"Az\u0259rbaycanca",be:"\u0411\u0435\u043B\u0430\u0440\u0443\u0441\u043A\u0430\u044F",bg:"\u0411\u044A\u043B\u0433\u0430\u0440\u0441\u043A\u0438",tn:"Setswana",bn:"\u09AC\u09BE\u0982\u09B2\u09BE",bs:"Bosanski",bo:"\u0F56\u0F7C\u0F51\u0F0B\u0F61\u0F72\u0F42",ca:"Catal\xE0",ceb:"Binisaya",co:"Corsu",cs:"\u010Ce\u0161tina",cy:"Cymraeg",da:"Dansk",de:"Deutsch",el:"\u0395\u03BB\u03BB\u03B7\u03BD\u03B9\u03BA\u03AC",en:"English",eo:"Esperanto",es:"Espa\xF1ol",et:"Eesti",eu:"Euskara",sa:"\u0938\u0902\u0938\u094D\u0915\u0943\u0924\u092E\u094D",fa:"\u0641\u0627\u0631\u0633\u06CC",fi:"Suomi",fil:"Filipino",fj:"Na Vosa Vakaviti",fr:"Fran\xE7ais",fy:"Frysk",ga:"Gaeilge",gd:"G\xE0idhlig",gl:"Galego",gu:"\u0A97\u0AC1\u0A9C\u0AB0\u0ABE\u0AA4\u0AC0",ha:"Hausa",haw:"\u02BB\u014Clelo Hawai\u02BBi",he:"\u05E2\u05D1\u05E8\u05D9\u05EA",hi:"\u0939\u093F\u0928\u094D\u0926\u0940",hmn:"Hmoob",hr:"Hrvatski",ht:"Krey\xF2l Ayisyen",hu:"Magyar",hy:"\u0540\u0561\u0575\u0565\u0580\u0565\u0576",id:"Bahasa Indonesia",ig:"As\u1EE5s\u1EE5 Igbo",is:"\xCDslenska",it:"Italiano",ja:"\u65E5\u672C\u8A9E",jw:"Basa Jawa",ka:"\u10E5\u10D0\u10E0\u10D7\u10E3\u10DA\u10D8",kk:"\u049A\u0430\u0437\u0430\u049B \u0422\u0456\u043B\u0456",km:"\u1797\u17B6\u179F\u17B6\u1781\u17D2\u1798\u17C2\u179A",kn:"\u0C95\u0CA8\u0CCD\u0CA8\u0CA1",ko:"\uD55C\uAD6D\uC5B4",ku:"Kurd\xEE",ky:"\u041A\u044B\u0440\u0433\u044B\u0437\u0447\u0430",la:"Latina",lb:"L\xEBtzebuergesch",lo:"\u0EA5\u0EB2\u0EA7",lt:"Lietuvi\u0173",lv:"Latvie\u0161u",mg:"Malagasy",mi:"M\u0101ori",mk:"\u041C\u0430\u043A\u0435\u0434\u043E\u043D\u0441\u043A\u0438",ml:"\u0D2E\u0D32\u0D2F\u0D3E\u0D33\u0D02",mn:"\u041C\u043E\u043D\u0433\u043E\u043B",mr:"\u092E\u0930\u093E\u0920\u0940",ms:"Bahasa Melayu",mt:"Malti",mww:"Hmong Daw",my:"\u1019\u103C\u1014\u103A\u1019\u102C\u1005\u102C",ne:"\u0928\u0947\u092A\u093E\u0932\u0940",nl:"Nederlands",no:"Norsk",ny:"Chichewa",otq:"H\xF1\xE4h\xF1u",pa:"\u0A2A\u0A70\u0A1C\u0A3E\u0A2C\u0A40",pl:"Polski",ps:"\u067E\u069A\u062A\u0648",pt:"Portugu\xEAs","pt-br":"Portugu\xEAs (Brasil)","pt-BR":"Portugu\xEAs (Brasil)","pt-PT":"Portugu\xEAs (Portugal)",ro:"Rom\xE2n\u0103",ru:"\u0420\u0443\u0441\u0441\u043A\u0438\u0439",sd:"\u0633\u0646\u068C\u064A",si:"\u0DC3\u0DD2\u0D82\u0DC4\u0DBD",sk:"Sloven\u010Dina",sl:"Sloven\u0161\u010Dina",sm:"Gagana Samoa",sn:"ChiShona",so:"Soomaali",sq:"Shqip",sr:"\u0421\u0440\u043F\u0441\u043A\u0438","sr-Cyrl":"\u0421\u0440\u043F\u0441\u043A\u0438 (\u040B\u0438\u0440\u0438\u043B\u0438\u0446\u0430)","sr-Latn":"Srpski (Latinica)",st:"Sesotho",su:"Basa Sunda",sv:"Svenska",sw:"Kiswahili",ta:"\u0BA4\u0BAE\u0BBF\u0BB4\u0BCD",te:"\u0C24\u0C46\u0C32\u0C41\u0C17\u0C41",tg:"\u0422\u043E\u04B7\u0438\u043A\u04E3",th:"\u0E44\u0E17\u0E22",tlh:"tlhIngan Hol","tlh-Qaak":"tlhIngan Hol (pIqaD)",to:"lea fakatonga",tr:"T\xFCrk\xE7e",ty:"Reo Tahiti",ug:"\u0626\u06C7\u064A\u063A\u06C7\u0631\u0686\u06D5",uk:"\u0423\u043A\u0440\u0430\u0457\u043D\u0441\u044C\u043A\u0430",ur:"\u0627\u0631\u062F\u0648",uz:"O\u02BBzbek",vi:"Ti\u1EBFng Vi\u1EC7t",wyw:"\u6587\u8A00\u6587",xh:"isiXhosa",placeholder:"Please Select",yi:"\u05D9\u05D9\u05B4\u05D3\u05D9\u05E9",yo:"\xC8d\xE8 Yor\xF9b\xE1",yua:"M\xE0aya T'\xE0an",yue:"\u7CB5\u8A9E","zh-CN":"\u7B80\u4F53\u4E2D\u6587","zh-TW":"\u7E41\u9AD4\u4E2D\u6587-\u53F0\u6E7E","zh-HK":"\u7E41\u9AD4\u4E2D\u6587-\u9999\u6E2F","zh-CN-NE":"\u6771\u5317\u5B98\u8A71",zu:"isiZulu","<all>":"All Languages","ur-roman":"Roman Urdu"},Kl={placeholder:"\u8BF7\u9009\u62E9",af:"\u5357\u975E\u8377\u5170\u8BED",am:"\u963F\u59C6\u54C8\u62C9\u8BED",ar:"\u963F\u62C9\u4F2F\u8BED",auto:"\u81EA\u52A8\u68C0\u6D4B",az:"\u963F\u585E\u62DC\u7586\u8BED",be:"\u767D\u4FC4\u7F57\u65AF\u8BED",bg:"\u4FDD\u52A0\u5229\u4E9A\u8BED",tn:"\u8D5E\u7EB3\u8BED",bn:"\u5B5F\u52A0\u62C9\u8BED",bs:"\u6CE2\u65AF\u5C3C\u4E9A\u8BED",bo:"\u85CF\u8BED",ca:"\u52A0\u6CF0\u7F57\u5C3C\u4E9A\u8BED",ceb:"\u5BBF\u52A1\u8BED",co:"\u79D1\u897F\u5609\u8BED",cs:"\u6377\u514B\u8BED",cy:"\u5A01\u5C14\u58EB\u8BED",da:"\u4E39\u9EA6\u8BED",de:"\u5FB7\u8BED",el:"\u5E0C\u814A\u8BED",en:"\u82F1\u8BED",eo:"\u4E16\u754C\u8BED",es:"\u897F\u73ED\u7259\u8BED",et:"\u7231\u6C99\u5C3C\u4E9A\u8BED",eu:"\u5DF4\u65AF\u514B\u8BED",sa:"\u68B5\u8BED",fa:"\u6CE2\u65AF\u8BED",fi:"\u82AC\u5170\u8BED",fil:"\u83F2\u5F8B\u5BBE\u8BED",fj:"\u6590\u6D4E\u8BED",fr:"\u6CD5\u8BED",fy:"\u5F17\u91CC\u65AF\u5170\u8BED",ga:"\u7231\u5C14\u5170\u8BED",gd:"\u82CF\u683C\u5170\u76D6\u5C14\u8BED",gl:"\u52A0\u5229\u897F\u4E9A\u8BED",gu:"\u53E4\u5409\u62C9\u7279\u8BED",ha:"\u8C6A\u8428\u8BED",haw:"\u590F\u5A01\u5937\u8BED",he:"\u5E0C\u4F2F\u6765\u8BED",hi:"\u5370\u5730\u8BED",hmn:"\u82D7\u8BED",hr:"\u514B\u7F57\u5730\u4E9A\u8BED",ht:"\u6D77\u5730\u514B\u91CC\u5965\u5C14\u8BED",hu:"\u5308\u7259\u5229\u8BED",hy:"\u4E9A\u7F8E\u5C3C\u4E9A\u8BED",id:"\u5370\u5EA6\u5C3C\u897F\u4E9A\u8BED",ig:"\u4F0A\u535A\u8BED",is:"\u51B0\u5C9B\u8BED",it:"\u610F\u5927\u5229\u8BED",ja:"\u65E5\u8BED",jw:"\u722A\u54C7\u8BED",ka:"\u683C\u9C81\u5409\u4E9A\u8BED",kk:"\u54C8\u8428\u514B\u8BED",km:"\u9AD8\u68C9\u8BED",kn:"\u5361\u7EB3\u8FBE\u8BED",ko:"\u97E9\u8BED",ku:"\u5E93\u5C14\u5FB7\u8BED",ky:"\u5409\u5C14\u5409\u65AF\u8BED",la:"\u62C9\u4E01\u8BED",lb:"\u5362\u68EE\u5821\u8BED",lo:"\u8001\u631D\u8BED",lt:"\u7ACB\u9676\u5B9B\u8BED",lv:"\u62C9\u8131\u7EF4\u4E9A\u8BED",mg:"\u9A6C\u62C9\u52A0\u65AF\u8BED",mi:"\u6BDB\u5229\u8BED",mk:"\u9A6C\u5176\u987F\u8BED",ml:"\u9A6C\u62C9\u96C5\u62C9\u59C6\u8BED",mn:"\u8499\u53E4\u8BED",mr:"\u9A6C\u62C9\u5730\u8BED",ms:"\u9A6C\u6765\u8BED",mt:"\u9A6C\u8033\u4ED6\u8BED",mww:"\u767D\u82D7\u8BED",my:"\u7F05\u7538\u8BED",ne:"\u5C3C\u6CCA\u5C14\u8BED",nl:"\u8377\u5170\u8BED",no:"\u632A\u5A01\u8BED",ny:"\u9F50\u5207\u74E6\u8BED\uFF08\u5C3C\u626C\u8D3E\u8BED\uFF09",otq:"\u514B\u96F7\u5854\u7F57\u5965\u6258\u7C73\u8BED",pa:"\u65C1\u906E\u666E\u8BED",pl:"\u6CE2\u5170\u8BED",ps:"\u666E\u4EC0\u56FE\u8BED",pt:"\u8461\u8404\u7259\u8BED","pt-br":"\u8461\u8404\u7259\u8BED\uFF08\u5DF4\u897F\uFF09","pt-BR":"\u8461\u8404\u7259\u8BED\uFF08\u5DF4\u897F\uFF09","pt-PT":"\u8461\u8404\u7259\u8BED\uFF08\u8461\u8404\u7259\uFF09",ro:"\u7F57\u9A6C\u5C3C\u4E9A\u8BED",ru:"\u4FC4\u8BED",sd:"\u4FE1\u5FB7\u8BED",si:"\u50E7\u4F3D\u7F57\u8BED",sk:"\u65AF\u6D1B\u4F10\u514B\u8BED",sl:"\u65AF\u6D1B\u6587\u5C3C\u4E9A\u8BED",sm:"\u8428\u6469\u4E9A\u8BED",sn:"\u4FEE\u7EB3\u8BED",so:"\u7D22\u9A6C\u91CC\u8BED",sq:"\u963F\u5C14\u5DF4\u5C3C\u4E9A\u8BED",sr:"\u585E\u5C14\u7EF4\u4E9A\u8BED","sr-Cyrl":"\u585E\u5C14\u7EF4\u4E9A\u8BED\uFF08\u897F\u91CC\u5C14\u6587\uFF09","sr-Latn":"\u585E\u5C14\u7EF4\u4E9A\u8BED\uFF08\u62C9\u4E01\u6587\uFF09",st:"\u585E\u7D22\u6258\u8BED",su:"\u5DFD\u4ED6\u8BED",sv:"\u745E\u5178\u8BED",sw:"\u65AF\u74E6\u5E0C\u91CC\u8BED",ta:"\u6CF0\u7C73\u5C14\u8BED",te:"\u6CF0\u5362\u56FA\u8BED",tg:"\u5854\u5409\u514B\u8BED",th:"\u6CF0\u8BED",tlh:"\u514B\u6797\u8D21\u8BED","tlh-Qaak":"\u514B\u6797\u8D21\u8BED\uFF08piqaD\uFF09",to:"\u6C64\u52A0\u8BED",tr:"\u571F\u8033\u5176\u8BED",ty:"\u5854\u5E0C\u63D0\u8BED",ug:"\u7EF4\u543E\u5C14\u8BED",uk:"\u4E4C\u514B\u5170\u8BED",ur:"\u4E4C\u5C14\u90FD\u8BED",uz:"\u4E4C\u5179\u522B\u514B\u8BED",vi:"\u8D8A\u5357\u8BED",wyw:"\u6587\u8A00\u6587",xh:"\u73ED\u56FE\u8BED",yi:"\u610F\u7B2C\u7EEA\u8BED",yo:"\u7EA6\u9C81\u5DF4\u8BED",yua:"\u5C24\u5361\u5766\u739B\u96C5\u8BED",yue:"\u7CA4\u8BED","zh-CN":"\u7B80\u4F53\u4E2D\u6587","zh-TW":"\u7E41\u4F53\u4E2D\u6587-\u53F0\u6E7E","zh-HK":"\u7E41\u4F53\u4E2D\u6587-\u9999\u6E2F","zh-CN-NE":"\u4E1C\u5317\u8BDD",zu:"\u7956\u9C81\u8BED","<all>":"\u6240\u6709\u8BED\u8A00","ur-roman":"\u7F57\u9A6C\u4E4C\u5C14\u90FD\u8BED"},Wl={placeholder:"\u8ACB\u9078\u64C7",af:"\u963F\u975E\u5229\u5361\u8A9E",am:"\u963F\u59C6\u54C8\u62C9\u8A9E",ar:"\u963F\u62C9\u4F2F\u8A9E",auto:"\u81EA\u52D5\u6AA2\u6E2C",az:"\u963F\u585E\u62DC\u7586\u8A9E",be:"\u767D\u4FC4\u7F85\u65AF\u8A9E",bg:"\u4FDD\u52A0\u5229\u4E9E\u8A9E",tn:"\u8D0A\u7D0D\u8A9E",bn:"\u5B5F\u52A0\u62C9\u8A9E",bs:"\u6CE2\u65AF\u5C3C\u4E9E\u8A9E",bo:"\u85CF\u8A9E",ca:"\u52A0\u6CF0\u862D\u8A9E",ceb:"\u5BBF\u9727\u8A9E",co:"\u79D1\u897F\u5609\u8A9E",cs:"\u6377\u514B\u8A9E",cy:"\u5A01\u723E\u65AF\u8A9E",da:"\u4E39\u9EA5\u8A9E",de:"\u5FB7\u8A9E",el:"\u5E0C\u81D8\u8A9E",en:"\u82F1\u8A9E",eo:"\u4E16\u754C\u8A9E",es:"\u897F\u73ED\u7259\u8A9E",et:"\u611B\u6C99\u5C3C\u4E9E\u8A9E",eu:"\u5DF4\u65AF\u514B\u8A9E",sa:"\u68B5\u8A9E",fa:"\u6CE2\u65AF\u8A9E",fi:"\u82AC\u862D\u8A9E",fil:"\u83F2\u5F8B\u8CD3\u8A9E",fj:"\u6590\u6FDF\u8A9E",fr:"\u6CD5\u8A9E",fy:"\u5F17\u91CC\u897F\u8A9E",ga:"\u611B\u723E\u862D\u8A9E",gd:"\u8607\u683C\u862D\u84CB\u723E\u8A9E",gl:"\u52A0\u5229\u897F\u4E9E\u8A9E",gu:"\u53E4\u5409\u62C9\u7279\u8A9E",ha:"\u8C6A\u6492\u8A9E",haw:"\u590F\u5A01\u5937\u8A9E",he:"\u5E0C\u4F2F\u4F86\u8A9E",hi:"\u5370\u5730\u8A9E",hmn:"\u82D7\u8A9E",hr:"\u514B\u7F85\u5730\u4E9E\u8A9E",ht:"\u6D77\u5730\u514B\u91CC\u5967\u723E\u8A9E",hu:"\u5308\u7259\u5229\u8A9E",hy:"\u4E9E\u7F8E\u5C3C\u4E9E\u8A9E",id:"\u5370\u5C3C\u8A9E",ig:"\u4F0A\u535A\u8A9E",is:"\u51B0\u5CF6\u8A9E",it:"\u610F\u5927\u5229\u8A9E",ja:"\u65E5\u8A9E",jw:"\u722A\u54C7\u8A9E",ka:"\u55AC\u6CBB\u4E9E\u8A9E",kk:"\u54C8\u85A9\u514B\u8A9E",km:"\u9AD8\u68C9\u8A9E",kn:"\u574E\u7D0D\u9054\u8A9E",ko:"\u97D3\u8A9E",ku:"\u5EAB\u723E\u5FB7\u8A9E",ky:"\u5409\u723E\u5409\u65AF\u8A9E",la:"\u62C9\u4E01\u8A9E",lb:"\u76E7\u68EE\u5821\u8A9E",lo:"\u8001\u64BE\u8A9E",lt:"\u7ACB\u9676\u5B9B\u8A9E",lv:"\u62C9\u812B\u7DAD\u4E9E\u8A9E",mg:"\u99AC\u62C9\u52A0\u65AF\u8A9E",mi:"\u6BDB\u5229\u8A9E",mk:"\u99AC\u5176\u9813\u8A9E",ml:"\u99AC\u62C9\u96C5\u62C9\u59C6\u8A9E",mn:"\u8499\u53E4\u8A9E",mr:"\u99AC\u62C9\u5730\u8A9E",ms:"\u99AC\u4F86\u8A9E",mt:"\u99AC\u723E\u4ED6\u8A9E",mww:"\u767D\u82D7\u8A9E",my:"\u7DEC\u7538\u8A9E",ne:"\u5C3C\u6CCA\u723E\u8A9E",nl:"\u8377\u862D\u8A9E",no:"\u632A\u5A01\u8A9E",ny:"\u9F4A\u5207\u74E6\u8A9E",otq:"\u594E\u96F7\u5854\u7F85\u5967\u6258\u7C73\u8A9E",pa:"\u65C1\u906E\u666E\u8A9E",pl:"\u6CE2\u862D\u8A9E",ps:"\u666E\u4EC0\u5716\u8A9E",pt:"\u8461\u8404\u7259\u8A9E","pt-br":"\u8461\u8404\u7259\u8A9E\uFF08\u5DF4\u897F\uFF09","pt-BR":"\u8461\u8404\u7259\u8A9E\uFF08\u5DF4\u897F\uFF09","pt-PT":"\u8461\u8404\u7259\u8A9E\uFF08\u8461\u8404\u7259\uFF09",ro:"\u7F85\u99AC\u5C3C\u4E9E\u8A9E",ru:"\u4FC4\u8A9E",sd:"\u4FE1\u5FB7\u8A9E",si:"\u50E7\u4F3D\u7F85\u8A9E",sk:"\u65AF\u6D1B\u4F10\u514B\u8A9E",sl:"\u65AF\u6D1B\u7DAD\u5C3C\u4E9E\u8A9E",sm:"\u85A9\u6469\u4E9E\u8A9E",sn:"\u7D39\u7D0D\u8A9E",so:"\u7D22\u99AC\u91CC\u8A9E",sq:"\u963F\u723E\u5DF4\u5C3C\u4E9E\u8A9E",sr:"\u585E\u723E\u7DAD\u4E9E\u8A9E","sr-Cyrl":"\u585E\u723E\u7DAD\u4E9E\u8A9E (\u897F\u91CC\u723E\u6587)","sr-Latn":"\u585E\u723E\u7DAD\u4E9E\u8A9E (\u62C9\u4E01\u6587)",st:"\u585E\u7D22\u6258\u8A9E",su:"\u5DFD\u4ED6\u8A9E",sv:"\u745E\u5178\u8A9E",sw:"\u65AF\u74E6\u5E0C\u91CC\u8A9E",ta:"\u6CF0\u7C73\u723E\u8A9E",te:"\u6CF0\u76E7\u56FA\u8A9E",tg:"\u5854\u5409\u514B\u8A9E",th:"\u6CF0\u8A9E",tlh:"\u514B\u6797\u8CA2\u8A9E","tlh-Qaak":"\u514B\u6797\u8CA2\u8A9E (piqaD)",to:"\u6771\u52A0\u8A9E",tr:"\u571F\u8033\u5176\u8A9E",ty:"\u5854\u5E0C\u63D0\u8A9E",ug:"\u7DAD\u543E\u723E\u8A9E",uk:"\u70CF\u514B\u862D\u8A9E",ur:"\u70CF\u723E\u90FD\u8A9E",uz:"\u70CF\u8332\u5225\u514B\u8A9E",vi:"\u8D8A\u5357\u8A9E",wyw:"\u6587\u8A00\u6587",xh:"\u79D1\u85A9\u8A9E",yi:"\u610F\u7B2C\u7DD2\u8A9E",yo:"\u7D04\u9B6F\u5DF4\u8A9E",yua:"\u5C24\u52A0\u6566\u99AC\u96C5\u8A9E",yue:"\u5EE3\u6771\u8A71 (\u50B3\u7D71)","zh-CN":"\u7C21\u9AD4\u4E2D\u6587","zh-TW":"\u7E41\u9AD4\u4E2D\u6587-\u53F0\u7063","zh-HK":"\u7E41\u9AD4\u4E2D\u6587-\u9999\u6E2F","zh-CN-NE":"\u6771\u5317\u8A71",zu:"\u7956\u9B6F\u8A9E","<all>":"\u6240\u6709\u8A9E\u8A00","ur-roman":"\u7F57\u9A6C\u4E4C\u723E\u90FD\u8A9E"};function Ua(e){if(typeof e!="string")return"auto";let t=e.toLowerCase();if(t==="und")return"auto";if(t==="zh"||t.startsWith("zh-hans"))return"zh-CN";if(t.startsWith("zh-hant")||t.startsWith("zh-hk")||t.startsWith("zh-tw")||t.startsWith("yue"))return"zh-TW";if(t.startsWith("zh-"))return"zh-CN";if(t==="iw")return"he";if(t==="jv")return"jw";let a=Jn.map(r=>r.toLowerCase()),n=a.indexOf(t);if(n===-1)if(t.indexOf("-")>=0){t=t.split("-")[0];let r=a.indexOf(t);return r===-1?"auto":Jn[r]}else return"auto";else return Jn[n]}var Oo=/iPhone/i,Vl=/iPod/i,Ql=/iPad/i,Yl=/\biOS-universal(?:.+)Mac\b/i,zo=/\bAndroid(?:.+)Mobile\b/i,Jl=/Android/i,hn=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,oi=/Silk/i,aa=/Windows Phone/i,Zl=/\bWindows(?:.+)ARM\b/i,Xl=/BlackBerry/i,$l=/BB10/i,ec=/Opera Mini/i,tc=/\b(CriOS|Chrome)(?:.+)Mobile/i,ac=/Mobile(?:.+)Firefox\b/i,nc=e=>typeof e<"u"&&e.platform==="MacIntel"&&typeof e.maxTouchPoints=="number"&&e.maxTouchPoints>1&&typeof globalThis.MSStream>"u";function bh(e){return t=>t.test(e)}function kt(e){let t={userAgent:"",platform:"",maxTouchPoints:0};!e&&typeof navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let a=t.userAgent,n=a.split("[FBAN");typeof n[1]<"u"&&(a=n[0]),n=a.split("Twitter"),typeof n[1]<"u"&&(a=n[0]);let r=bh(a),i={apple:{phone:r(Oo)&&!r(aa),ipod:r(Vl),tablet:!r(Oo)&&(r(Ql)||nc(t))&&!r(aa),universal:r(Yl),device:(r(Oo)||r(Vl)||r(Ql)||r(Yl)||nc(t))&&!r(aa)},amazon:{phone:r(hn),tablet:!r(hn)&&r(oi),device:r(hn)||r(oi)},android:{phone:!r(aa)&&r(hn)||!r(aa)&&r(zo),tablet:!r(aa)&&!r(hn)&&!r(zo)&&(r(oi)||r(Jl)),device:!r(aa)&&(r(hn)||r(oi)||r(zo)||r(Jl))||r(/\bokhttp\b/i)},windows:{phone:r(aa),tablet:r(Zl),device:r(aa)||r(Zl)},other:{blackberry:r(Xl),blackberry10:r($l),opera:r(ec),firefox:r(ac),chrome:r(tc),device:r(Xl)||r($l)||r(ec)||r(ac)||r(tc)},any:!1,phone:!1,tablet:!1};return i.any=i.apple.device||i.android.device||i.windows.device||i.other.device,i.phone=i.apple.phone||i.android.phone||i.windows.phone,i.tablet=i.apple.tablet||i.android.tablet||i.windows.tablet,i}var Uo="DENO",si="CHROME",ui="FIREFOX";function rc(e){let t;try{let a=navigator?.userAgent||"";/firefox/i.test(a)||typeof InstallTrigger<"u"?t=ui:/deno/i.test(a)?t=Uo:/chrome/i.test(a)&&(t=si)}catch{}return e===si&&t===si||e===ui&&t===ui||e===Uo&&t===Uo}function na(){return rc(si)}function ic(){return typeof Deno<"u"}function qa(){return ue().IMMERSIVE_TRANSLATE_FIREFOX==="1"?!0:rc(ui)}function qo(){return fe(!1,!0)?"monkey":na()?"chrome":qa()?"firefox":Ze()?"safari":null}var oc={addListener:()=>{},removeListener:()=>{},hasListener:()=>{}},sc={permissions:{contains:()=>{},request:()=>{}},runtime:{onMessage:oc,openOptionsPage:()=>{},lastError:{message:""}},storage:{sync:{get:()=>{},set:()=>{}},local:{map:new Map,async get(e){return new Promise(t=>{setTimeout(()=>{let a=this.map.get(e);t({[e]:a})},100)})},async set(e,t){return new Promise((a,n)=>{setTimeout(()=>{this.map.set(e,t),a("")},100)})}}},tabs:{onUpdated:oc,query:()=>{},sendMessage:()=>{}}};var A;ic()?A=sc:A=globalThis.immersiveTranslateBrowserAPI;var Ga=O+"CacheKey_";function uc(e,t){let a=Ga+e;return A.storage.local.get(a).then(n=>n[a]===void 0?t:n[a])}function yh(){let e=A.storage.local.refresh;e&&e()}function vh(e,t){let a=Ga+e;return A.storage.local.get(a).then(n=>{if(n[a]===void 0)return t;let{value:r,expired:i}=n[a];return i&&i<Date.now()?t:r})}function xh(e,t,a){let n=Ga+e,r=Date.now()+a;return A.storage.local.set({[n]:{value:t,expired:r}})}function lc(e,t){let a=Ga+e;return A.storage.local.set({[a]:t})}function wh(e){let t=Ga+e;return A.storage.local.remove(t)}async function Ah(){let e=await A.storage.local.get(null);if(e){let a=Object.keys(e).filter(n=>n.startsWith(Ga)).filter(n=>n!==Ga+bt);if(a.length>0)return A.storage.local.remove(a)}}var Ut={get:uc,set:lc,getExpired:vh,setExpired:xh,remove:wh,clear:Ah,refresh:yh};function fn(e){return!!(e&&e.subscription&&e.subscription.subscriptionStatus==="active")}function li(e){if(e){let t=new Date(e.createTime),a=kh(t),n="free",r="unknown";return e.subscription&&e.subscription.subscriptionStatus==="active"&&(n=e.subscription.subscriptionType),e.subscription&&e.subscription.subscriptionId&&(e.subscription.subscriptionId.startsWith("sub_")?r="stripe":r="admin"),e.subscription?.isTrial&&(n="trial"),{user_type:n,user_register_day:a,subscription_from:r}}else return null}function kh(e){try{let a=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];a.endsWith(",")&&(a=a.slice(0,-1));let[n,r,i]=a.split("/");return a=`${i}-${n}-${r}`,a}catch{return"unknown"}}async function cc(e){if(e?.isPro)return!0;let t=await Ut.get(bt,null);return fn(t)}var dc=O+"SyncStoreKey_";function Ne(e,t){let a=dc+e;return A.storage.sync.get(a).then(n=>n[a]===void 0?t:n[a])}function Se(e,t){let a=dc+e;return A.storage.sync.set({[a]:t})}var mc=O+"StoreKey_";function Ha(e,t){let a=mc+e;return A.storage.local.get(a).then(n=>n[a]===void 0?t:n[a])}function pc(e,t){let a=mc+e;return A.storage.local.set({[a]:t})}var wE=gc(2),Eh=gc(3);function gc(e){if(typeof e!="number"||Number.isNaN(e)||e<1||e===Number.POSITIVE_INFINITY)throw new Error("`"+e+"` is not a valid argument for `n-gram`");return t;function t(a){let n=[];if(a==null)return n;let r=typeof a.slice=="function"?a:String(a),i=r.length-e+1;if(i<1)return n;for(;i--;)n[i]=r.slice(i,i+e);return n}}var ME={}.hasOwnProperty;var Go={Latin:{spa:" de|de |os | la| a |la | y |\xF3n |i\xF3n|es |ere|rec|ien|o a|der|ci\xF3|a p|cho|ech|en |ent|a l|aci|e d|el |ona|na | co|as |al |da | to|ene|e l| en| el| pe|nte|tod|ho | su|per|ad | ti|a t|ers|tie| se|rso| pr|son|e s|te |oda|cia|n d|o d|dad|ida| in|ne | es|ion|cio|s d|con|est|a e| po|men| li|res|nci|su |to |tra| re|n e| lo|tad| na|los|a s| o |ia |que| pa|r\xE1 |pro| un|s y|ual|s e|lib|nac|do |ra |er |nal|ue | qu|e e|a d|ar |nes|ica|a c|sta|ser|/* MULTIPLE_REMOVED_BLOCKS */u0935\u0924\u0902|\u094D\u0935\u0924|\u093E \u092E|\u0935 \u0915|\u0947 \u0935|\u093E\u0925 |\u0938\u093E\u0925| \u0926\u094B|\u0939\u094B\u092C| \u092A\u093E|\u094B \u0915|\u0947 \u092C|\u094B\u0917 | \u0909\u092A|\u0938\u094D\u0924|\u092A\u0930\u093F|\u0928 \u092A|\u0947 \u0924|\u094D\u0924\u0930|\u0932\u0947\u0932|\u0947 \u0913|\u091A\u093E\u0939| \u091A\u093E|\u092F \u0915|\u0935\u093E |\u0947\u0936 |\u092F \u0938|\u0928 \u0939|\u0937\u0923 |\u093E \u092C|\u0964 \u0924|\u090F\u0915 |\u090F\u0932 |\u0940\u092F |\u0915\u0947\u0915|\u0947 \u0939|\u0930 \u0906|\u093F \u0915|\u0938\u094D\u0925|\u091C\u093F\u0915|\u093E\u091C\u093F|\u093E\u092E\u093E|\u0930\u0940\u092F|\u094D\u0930\u0940|\u0924\u093F\u0915|\u093E\u0924\u093F| \u092C\u093F|\u091A\u093E\u0930|\u0947 \u0906|\u093E\u0938 | \u0909\u091A|\u093E \u0924|\u092F\u0915\u094D|\u094D\u092F\u0915|\u093F\u0932 |\u092E\u092F |\u0938\u092E\u092F|\u0936\u093E\u0926|\u092A\u092F\u094B|\u0909\u092A\u092F|\u0947 \u0916|\u0930\u093F\u0935| \u092A\u0942|\u0947 \u0932|\u0947 \u091A|\u094C\u0928\u094B|\u0915\u094C\u0928| \u0915\u094C|\u0902 \u0915|\u0938\u0902\u0917|\u0928 \u0926|\u0902 \u0938|\u0923 \u092A|\u094D\u0937\u0923|\u0930 \u0928|\u0947 \u0928|\u094B \u092D|\u0915\u0930\u094B|\u093E \u0914|\u0930\u0924\u093E|\u093E\u0935 |\u092D\u093E\u0935|\u0915 \u0914|\u0930\u094D\u092E|\u094B\u0938\u0930|\u0926\u094B\u0938|\u0923 \u0915|\u0947 \u092A|\u0928 \u0914|\u092C \u0939|\u093F\u0915\u094D|\u0936\u093F\u0915| \u0936\u093F|\u093E\u092C\u0947|\u0928\u093F\u092F|\u091A\u093F\u0924|\u0909\u091A\u093F|\u093F\u0924\u094D|\u0917 \u0915|\u0947\u0964 |\u0924 \u0938|\u0940 \u0936|\u0902 \u0936|\u090F\u0915\u0930|\u0964 \u090F|\u0924\u0928 | \u0913 |\u0930\u0940 |\u094D\u0930 |\u091C\u0947 |\u0915 \u0915| \u0938\u0940|\u0938\u0928 |\u093F\u0935\u093E| \u0905\u0928|\u0942\u0930\u093E| \u092C\u091A|\u090F\u0964 | \u092C\u0947|\u0924 \u0939| \u0924\u0915| \u092E\u093F|\u0927\u093E\u0930|\u0925\u0935\u093E|\u0905\u0925\u0935| \u0905\u0925|\u093F\u0932\u093E|\u094D\u0935\u093E|\u093F \u092E| \u0906\u0926|\u0928\u0947 |\u0915\u090F\u0932| \u0915\u090F|\u094D\u092F\u093E"}};var hc={}.hasOwnProperty,Zn,fc={};for(Zn in Go)if(hc.call(Go,Zn)){let e=Go[Zn],t;fc[Zn]={};for(t in e)if(hc.call(e,t)){let a=e[t].split("|"),n={},r=a.length;for(;r--;)n[a[r]]=r;fc[Zn][t]=n}}var bc=[["afr","af"],["amh","am"],["arb","ar"],["azj","az"],["bel","be"],["bul","bg"],["ben","bn"],["bos","bs"],["cat","ca"],["ceb","ceb"],["ces","cs"],["dan","da"],["deu","de"],["ell","el"],["eng","en"],["epo","eo"],["spa","es"],["est","et"],["fas","fa"],["fin","fi"],["fra","fr"],["gax","ga"],["glg","gl"],["guj","gu"],["hau","ha"],["heb","he"],["hin","hi"],["hrv","hr"],["hun","hu"],["hye","hy"],["ind","id"],["ibo","ig"],["ita","it"],["jpn","ja"],["jav","jw"],["kat","ka"],["kaz","kk"],["khm","km"],["kan","kn"],["kor","ko"],["ckb","ku"],["lao","lo"],["lit","lt"],["lav","lv"],["min","mi"],["mkd","mk"],["mal","ml"],["mar","mr"],["mya","my"],["nep","ne"],["nld","nl"],["nob","no"],["nya","ny"],["pan","pa"],["pol","pl"],["pbu","ps"],["por","pt"],["ron","ro"],["rus","ru"],["sin","si"],["slk","sk"],["slv","sl"],["sna","sn"],["som","so"],["als","sq"],["srp","sr"],["sun","su"],["swe","sv"],["swh","sw"],["tam","ta"],["tel","te"],["tgk","tg"],["tha","th"],["toi","to"],["tur","tr"],["ukr","uk"],["urd","ur"],["uzn","uz"],["vie","vi"],["xho","xh"],["ydd","yi"],["yor","yo"],["cmn","zh-CN"],["zul","zu"]],GE=new Map(bc),HE=new Map(bc.map(([e,t])=>[t,e]));function va(e,t){var a=(e&65535)+(t&65535),n=(e>>16)+(t>>16)+(a>>16);return n<<16|a&65535}function Sh(e,t){return e<<t|e>>>32-t}function di(e,t,a,n,r,i){return va(Sh(va(va(t,e),va(n,i)),r),a)}function Xe(e,t,a,n,r,i,o){return di(t&a|~t&n,e,t,r,i,o)}function $e(e,t,a,n,r,i,o){return di(t&n|a&~n,e,t,r,i,o)}function et(e,t,a,n,r,i,o){return di(t^a^n,e,t,r,i,o)}function tt(e,t,a,n,r,i,o){return di(a^(t|~n),e,t,r,i,o)}function ci(e,t){e[t>>5]|=128<<t%32,e[(t+64>>>9<<4)+14]=t;var a,n,r,i,o,s=1732584193,u=-271733879,c=-1732584194,l=271733878;for(a=0;a<e.length;a+=16)n=s,r=u,i=c,o=l,s=Xe(s,u,c,l,e[a],7,-680876936),l=Xe(l,s,u,c,e[a+1],12,-389564586),c=Xe(c,l,s,u,e[a+2],17,606105819),u=Xe(u,c,l,s,e[a+3],22,-1044525330),s=Xe(s,u,c,l,e[a+4],7,-176418897),l=Xe(l,s,u,c,e[a+5],12,1200080426),c=Xe(c,l,s,u,e[a+6],17,-1473231341),u=Xe(u,c,l,s,e[a+7],22,-45705983),s=Xe(s,u,c,l,e[a+8],7,1770035416),l=Xe(l,s,u,c,e[a+9],12,-1958414417),c=Xe(c,l,s,u,e[a+10],17,-42063),u=Xe(u,c,l,s,e[a+11],22,-1990404162),s=Xe(s,u,c,l,e[a+12],7,1804603682),l=Xe(l,s,u,c,e[a+13],12,-40341101),c=Xe(c,l,s,u,e[a+14],17,-1502002290),u=Xe(u,c,l,s,e[a+15],22,1236535329),s=$e(s,u,c,l,e[a+1],5,-165796510),l=$e(l,s,u,c,e[a+6],9,-1069501632),c=$e(c,l,s,u,e[a+11],14,643717713),u=$e(u,c,l,s,e[a],20,-373897302),s=$e(s,u,c,l,e[a+5],5,-701558691),l=$e(l,s,u,c,e[a+10],9,38016083),c=$e(c,l,s,u,e[a+15],14,-660478335),u=$e(u,c,l,s,e[a+4],20,-405537848),s=$e(s,u,c,l,e[a+9],5,568446438),l=$e(l,s,u,c,e[a+14],9,-1019803690),c=$e(c,l,s,u,e[a+3],14,-187363961),u=$e(u,c,l,s,e[a+8],20,1163531501),s=$e(s,u,c,l,e[a+13],5,-1444681467),l=$e(l,s,u,c,e[a+2],9,-51403784),c=$e(c,l,s,u,e[a+7],14,1735328473),u=$e(u,c,l,s,e[a+12],20,-1926607734),s=et(s,u,c,l,e[a+5],4,-378558),l=et(l,s,u,c,e[a+8],11,-2022574463),c=et(c,l,s,u,e[a+11],16,1839030562),u=et(u,c,l,s,e[a+14],23,-35309556),s=et(s,u,c,l,e[a+1],4,-1530992060),l=et(l,s,u,c,e[a+4],11,1272893353),c=et(c,l,s,u,e[a+7],16,-155497632),u=et(u,c,l,s,e[a+10],23,-1094730640),s=et(s,u,c,l,e[a+13],4,681279174),l=et(l,s,u,c,e[a],11,-358537222),c=et(c,l,s,u,e[a+3],16,-722521979),u=et(u,c,l,s,e[a+6],23,76029189),s=et(s,u,c,l,e[a+9],4,-640364487),l=et(l,s,u,c,e[a+12],11,-421815835),c=et(c,l,s,u,e[a+15],16,530742520),u=et(u,c,l,s,e[a+2],23,-995338651),s=tt(s,u,c,l,e[a],6,-198630844),l=tt(l,s,u,c,e[a+7],10,1126891415),c=tt(c,l,s,u,e[a+14],15,-1416354905),u=tt(u,c,l,s,e[a+5],21,-57434055),s=tt(s,u,c,l,e[a+12],6,1700485571),l=tt(l,s,u,c,e[a+3],10,-1894986606),c=tt(c,l,s,u,e[a+10],15,-1051523),u=tt(u,c,l,s,e[a+1],21,-2054922799),s=tt(s,u,c,l,e[a+8],6,1873313359),l=tt(l,s,u,c,e[a+15],10,-30611744),c=tt(c,l,s,u,e[a+6],15,-1560198380),u=tt(u,c,l,s,e[a+13],21,1309151649),s=tt(s,u,c,l,e[a+4],6,-145523070),l=tt(l,s,u,c,e[a+11],10,-1120210379),c=tt(c,l,s,u,e[a+2],15,718787259),u=tt(u,c,l,s,e[a+9],21,-343485551),s=va(s,n),u=va(u,r),c=va(c,i),l=va(l,o);return[s,u,c,l]}function yc(e){var t,a="",n=e.length*32;for(t=0;t<n;t+=8)a+=String.fromCharCode(e[t>>5]>>>t%32&255);return a}function Ho(e){var t,a=[];for(a[(e.length>>2)-1]=void 0,t=0;t<a.length;t+=1)a[t]=0;var n=e.length*8;for(t=0;t<n;t+=8)a[t>>5]|=(e.charCodeAt(t/8)&255)<<t%32;return a}function Th(e){return yc(ci(Ho(e),e.length*8))}function Dh(e,t){var a,n=Ho(e),r=[],i=[],o;for(r[15]=i[15]=void 0,n.length>16&&(n=ci(n,e.length*8)),a=0;a<16;a+=1)r[a]=n[a]^909522486,i[a]=n[a]^1549556828;return o=ci(r.concat(Ho(t)),512+t.length*8),yc(ci(i.concat(o),512+128))}function vc(e){var t="0123456789abcdef",a="",n,r;for(r=0;r<e.length;r+=1)n=e.charCodeAt(r),a+=t.charAt(n>>>4&15)+t.charAt(n&15);return a}function Ko(e){return unescape(encodeURIComponent(e))}function xc(e){return Th(Ko(e))}function Ch(e){return vc(xc(e))}function wc(e,t){return Dh(Ko(e),Ko(t))}function Mh(e,t){return vc(wc(e,t))}function Ac(e,t,a){return t?a?wc(t,e):Mh(t,e):a?xc(e):Ch(e)}var kc=[{type:"select",name:"codename",labelKey:"field.translationEngine",default:"youdao",required:!1,options:[{label:"translationServices.google",value:"google"},{label:"translationServices.deepl",value:"deepl"},{label:"translationServices.youdao",value:"youdao"},{label:"translationServices.tencent",value:"tencent"},{label:"translationServices.aliyun",value:"aliyun"},{label:"translationServices.baidu",value:"baidu"},{label:"translationServices.caiyun",value:"caiyun"},{label:"translationServices.wechat",value:"wechat"},{label:"translationServices.ibm",value:"ibm"},{label:"translationServices.azure",value:"azure"},{label:"translationServices.aws",value:"aws"}]}],bn={ai:!0,name:"Custom AI",homepage:"https://openai.com/api/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,direction:"column",type:"text"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/ai/",default:"https://api.openai.com/v1/chat/completions"},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description/* REMOVED_COMMON_BLOCK_2451 */type:"number",default:"0",optional:!0}]},mi={bing:{name:"\u5FAE\u8F6F\u7FFB\u8BD1",homepage:"https://www.bing.com/translator"},google:{name:"Google",homepage:"https://translate.google.com/"},zhipu:{ai:!0,name:"zhipu",homepage:"https://open.bigmodel.cn/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"glm-4-flash (free)",value:"glm-4-flash"},{label:"glm-4-air",value:"glm-4-air"},{label:"glm-4-airx",value:"glm-4-airx"},{label:"glm-4",value:"glm-4"},{label:"glm-4-plus",value:"glm-4-plus"},{label:"glm-4-0520",value:"glm-4-0520"},{label:"glm-4-long",value:"glm-4-long"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column",descriptionKey:"description.zhipuCustomAPIKey",descriptionLink1:"https://open.bigmodel.cn/",descriptionLink2:B+"zh-Hans/docs/services/zhipu/"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",placeholder:"ep-20240512123030-kv128",descriptionKey:"description.generalLimitPerSecond",type:"number",default:10},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerReques/* REMOVED_COMMON_BLOCK_2465 */re",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},deepl:{name:"DeepL",homepage:"https://www.deepl.com/translator",docUrl:B+"docs/services/deepL/",link1:"https://www.deepl.com/blog/next-gen-language-model",link2:B+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_deepl",providers:[{name:"pro",nameKey:"deepLProName",descriptionKey:"deepLProDescription",descriptionKeyForNormal:"deeplProDescriptionForNormal",descriptionLink1:ze+"?utm_campaign=services"},{name:"custom",nameKey:"deepLCustomName",descriptionKey:"deepLCustomDescription",descriptionLink1:"https://www.deepl.com/translator",descriptionLink2:B+"docs/services/deepL/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"authKey",providers:["custom"],label:"Auth Key",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!1}]},deepseek:{ai:!0,name:"deepseek",homepage:"https://www.deepseek.com/",docUrl:B+"docs/services/deepseek/",link1:"",link2:"",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"serviceProDescriptionForNormal",descriptionLink1:ze+"?utm_campaign=services"},{name:"custom",nameKey:"openaiCustomName",descriptionKey:"deepseekCustomDescription",descriptionLink1:"https://www.deepseek.com/",descriptionLink2:B+"docs/services/deepseek/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column",providers:["custom"]},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!1,type:"model-select",providers:["custom"],default:"deepseek-chat",options:[{label:"deepseek-chat",value:"deepseek-chat"},{label:"deepseek-reasoner",value:"deepseek-reasoner"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:1,providers:["custom"]},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.deepseek.com/chat/completions",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/deepseek/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:`Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}`,optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},openai:{ai:!0,name:"Open AI",homepage:"https://openai.com/api/",docUrl:B+"docs/services/openai/",link1:"https://readit.plus/a/GKQas/understanding-chatgpt",link2:B+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_openai",providers:[{name:"pro",nameKey:"openaiProName",descriptionKey:"openaiProDescription",descriptionKeyForNormal:"openaiProDescriptionForNormal",descriptionLink1:ze+"?utm_campaign=services"},{name:"custom",nameKey:"openaiCustomName",descriptionKey:"openaiCustomDescription",descriptionLink1:B+"docs/services/openai/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlacehold/* REMOVED_COMMON_BLOCK_2468 */label:"gpt-4",value:"gpt-4"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.limitPerSecond",descriptionLink1:B+"docs/services/openai/",type:"number",default:10,providers:["custom"]},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.openai.com/v1/chat/completions",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/openai/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:`Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}`,optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},gemini:{ai:!0,name:"Gemini",homepage:"https://ai.google.dev/aistudio/",docUrl:B+"docs/services/gemini/",link2:B+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_gemini",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"serviceProDescriptionForNormal",descriptionLink1:ze+"?utm_campaign=services"},{name:"custom",nameKey:"serviceCustomName",descriptionKey:"geminiCustomDescription",descriptionLink1:"https://ai.google.dev/aistudio/",descriptionLink2:B+"docs/services/gemini/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",providers:["custom"],required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!1,type:"model-select",providers:["custom"],options:[{label:"gemini-1.5-pro-latest",value:"gemini-1.5-pro-latest"},{label:"gemini-1.5-flash",value:"gemini-1.5-flash"},{label:"gemini-2.0-flash",value:"gemini-2.0-flash"},{label:"gemini-2.0-flash-lite",value:"gemini-2.0-flash-lite"},{label:"gemini-2.0-flash-lite-preview-02-05",value:"gemini-2.0-flash-lite-preview-02-05"},{label:"gemini-2.5-pro-preview-05-06",value:"gemini-2.5-pro-preview-05-06"},{label:"gemini-2.5-flash-preview-05-20",value:"gemini-2.5-flash-preview-05-20"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:B+"docs/services/openai/",type:"number",providers:["custom"],default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:3,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={key}",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/gemini/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:/* REMOVED_COMMON_BLOCK_2469 */hropic.com/",docUrl:B+"docs/services/claude/",link2:B+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_claude",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"serviceProDescriptionForNormal",descriptionLink1:ze+"?utm_campaign=services"},{name:"custom",nameKey:"serviceCustomName",descriptionKey:"claudeCustomDescription",descriptionLink1:"https://www.anthropic.com/",descriptionLink2:B+"docs/services/claude/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",providers:["custom"],required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!1,type:"model-select",default:"claude-3-haiku-20240307",providers:["custom"],options:[{label:"claude-3-haiku-20240307",value:"claude-3-haiku-20240307"},{label:"claude-3-sonnet-20240229",value:"claude-3-sonnet-20240229"},{label:"claude-3-5-sonnet-20240620",value:"claude-3-5-sonnet-20240620"},{label:"claude-3-opus-20240229",value:"claude-3-opus-20240229"},{label:"claude-2.1",value:"claude-2.1"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",providers:["custom"],descriptionKey:"description.generalLimitPerSecond",descriptionLink1:B+"docs/services/openai/",type:"number",default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:10,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.anthropic.com/v1/messages",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/claude/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:"",optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},"zhipu-pro":{ai:!0,name:"Gemini",homepage:"https://open.bigmodel.cn/",docUrl:"",link2:B+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_zhipu_pro",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"translationServices.proOnly",descriptionLink1:ze+"?utm_campaign=services"}],allProps:[{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"fiel/* REMOVED_COMMON_BLOCK_2452 */9b-chat",value:"THUDM/glm-4-9b-chat"},{label:"google/gemma-2-9b-it",value:"google/gemma-2-9b-it"},{label:"01-ai/Yi-1.5-9B-Chat-16K",value:"01-ai/Yi-1.5-9B-Chat-16K"}]},{name:"APIKEY",descriptionKey:"description.siliconcloudCustomAPIKey",descriptionLink1:"https://siliconflow.cn/",providers:["custom"],required:!0,type:"password",sensitive:!0,direction:"column"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:B+"docs/serv/* REMOVED_COMMON_BLOCK_2466 */required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},yandex:{name:"Yandex",homepage:"https://translate.yandex.com/"},transmart:{name:"Transmart",homepage:"https://transmart.qq.com/"},grok:{ai:!0,name:"grok",homepage:"https://x.ai/",docUrl:B+"docs/services/grok/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/ai/",default:"https://api.x.ai/v1/chat/completions"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",provider/* REMOVED_COMMON_BLOCK_2453 */extarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},ollama:{...bn,name:"ollama",homepage:"https://ollama.com/",runningDocUrl:B+"docs/services/ollama/"},"azure-openai":{...bn,name:"azure-openai",homepage:"https://learn.microsoft.com/zh-cn/azure/cognitive-services/openai/chatgpt-quickstart?tabs=command-line&pivots=rest-api",docUrl:B+"docs/services/azure-openai/"},doubao:{ai:!0,name:"doubao",homepage:"https://www.volcengine.com/product/doubao",docUrl:B+"docs/services/doubao/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.accessPoint",descriptionKey:"description.accessPoint",placeholder:" ",required:!1,type:"text",default:""},{name:"limit",required:!1,labelKey:"field.limitPerSecond",placeholder:"ep-20240512123030-kv128",descriptionKey:"description.generalLimitPerSecond",type:"number",default:10},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:`Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}`,optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},"aliyun-bailian":{ai:!0,name:"aliyun-bailian",homepage:"https://bailian.console.aliyun.com/",docUrl:B+"docs/services/aliyun-bailian/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field./* REMOVED_COMMON_BLOCK_2454 */le Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},"qwen-mt":{name:"QwenMT",homepage:"https://help.aliyun.com/zh/model-studio/machine-translation",docUrl:B+"docs/services/qwen-mt/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"apiKey",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"qwen-mt-turbo",value:"qwen-mt-turbo"},{label:"qwen-mt-plus",value:"qwen-mt-plus"}]},{name:"domains",labelKey:"labelKey.domains",required:!1,placeholderKey:"description.qwenMtDomains",type:"textarea",optional:!0,default:""},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:10,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:10,optional:!0}]},qianfan:{ai:!0,name:"baidu-qianfan",homepage:"https://console.bce.baidu.com/qianfan/overview",docUrl:B+"docs/services/baidu-qianfan/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{label:"API Key",name:"AccessKeyID",placeholder:"API Key",required:!0,type:"text",sensitive:!0,direction:"column"},{label:"Secret Key",name:"AccessKeySecret",placeholder:"Secret Key",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"ERNIE-Speed-8K",value:"ernie_speed"},{label:"ERNIE-Speed-128K",value:"ernie-speed-128k"},{label:"ERNIE-4.0-8K",value:"completions_pro"},{label:"ERNIE-4.0-8K-Preview",value:"ernie-4.0-8k-preview"},{label:"ERNIE-3.5-8K",value:"completions"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:B+"docs/services/openai/",type:"number",providers:["custom"],default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:/* REMOVED_COMMON_BLOCK_2470 */extarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},qianfan2:{...bn,name:"baidu-qianfan2",homepage:"https://console.bce.baidu.com/qianfan/overview",docUrl:B+"docs/services/baidu-qianfan/"},hunyuan:{ai:!0,name:"hunyuan",homepage:"https://hunyuan.tencent.com/",docUrl:B+"docs/services/tencent-hunyuan/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{label:"Secret ID",name:"secret_id",placeholder:"Secret ID",required:!0,type:"text",sensitive:!0,direction:"column"},{label:"Secret Key",name:"secret_key",placeholder:"Secret Key",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"hunyuan-lite",value:"hunyuan-lite"},{label:"hunyuan-standard",value:"hunyuan-standard"},{label:"hunyuan-standard-256K",value:"hunyuan-standard-256K"},{label:"hunyuan-pro",value:"hunyuan-pro"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:B+"docs/services/openai/",type:"number",providers:["custom"],default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:3,optional:!0},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:"",optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},lingyiwanwu:{ai:!0,name:"lingyiwanwu",homepage:"https://platform.lingyiwanwu.com/docs",docUrl:B/* REMOVED_COMMON_BLOCK_2456 */:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},groq:{ai:!0,name:"groq",homepage:"https://groq.com/",docUrl:B+"docs/services/groq/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",descriptionKey:"description.apiUrl",descriptionLink1:B+"docs/services/ai/",default:"https://api.groq.com/openai/v1/chat/completions"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"gemma2-9b-it",value:"gemma2-9b-it"},{label:"llama-3.3-70b-versatile",value:"llama-3.3-70b-versatile"},{label:"llama-3.1-8b-instant",value:"llama-3.1-8b-instant"},{label:"llama-guard-3-8b",value:"llama-guard-3-8b"},{label:"llama3-70b-8192",value:"/* REMOVED_COMMON_BLOCK_2458 */re/cognitive-services/translator/text-translation-overview",docUrl:B+"docs/services/azure/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"region",required:!1,default:"eastasia",type:"text"},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.cognitive.microsofttranslator.com/",descriptionKey:"description.azureApiUrl",optional:!0},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0}]},volc:{name:"Volc",homepage:"https://www.volcengine.com/",docUrl:B+"docs/services/volcano/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"accessKeyId",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"secretAccessKey",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:5,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1800,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:8,optional:!0}]},aliyun:{name:"Aliyun",homepage:"https://translate.alibaba.com/",docUrl:B+"docs/services/aliyun/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"AccessKeyID",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"AccessKeySecret",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"scene",labelKey:"field.scene",descriptionKey:"description.scene",descriptionLink1:"https://help.aliyun.com/document_detail/158267.html",required:!1,optional:!0,type:"text",default:"general"}]},baidu:{name:"Baidu",homepage:"https://fanyi.baidu.com/",docUrl:B+"docs/services/baidu/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{/* REMOVED_COMMON_BLOCK_2471 */xt",default:"",optional:!0}]},tencent:{name:"Tencent",homepage:"https://fanyi.qq.com/translateapi",docUrl:B+"docs/services/tencent/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"secretId",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"secretKey",required:!0,type:"password",sensitive:!0,direction:"column"}]},"youdao-ziyue":{name:"YoudaoZiyue",homepage:"https://fanyi.youdao.com/#/AITranslate",docUrl:B+"docs/services/youdao-ziyue/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"appKey",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"appSecret",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"prompt",labelKey:"Prompt",required:!1,placeholderKey:"description.ziyuePromptMaxLength",type:"textarea",optional:!0,default:""},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:1,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:10,optional:!0}]},youdao:{name:"Youdao",homepage:"https://youdao.com/",docUrl:B+"docs/services/youdao/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"appId",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"appSecret",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:5,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:200,optional:!0},{name:"domain",required:!1,labelKey:"field.domain",descriptionKey:"description.domain",descriptionLink1:"https://fanyi.youdao.com/openapi/",type:"text",default:"general",optional:!0},{name:"vocabId",required:!1,labelKey:"field.vocabId",descriptionKey:"description.vocabId",type:"text",default:"",optional:!0}]},openrouter:{...bn,name:"OpenRouter",homepage:"https://openrouter.ai/",docUrl:B+"docs/services/openrouter/"},caiyun:{name:"Caiyun",homepage:"https://fanyi.caiyunapp.com/",docUrl:B+"docs/services/caiyun/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"token",required:!0,type:"password",sensitive:!0,direction:"column"}]},"custom-ai":bn,openl:{name:"Openl",homepage:"https://openl.club/",docUrl:B+"docs/services/openL/",beta:!0,allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},...kc,{type:"password",name:"apikey",required:!0,sensitive:!0,direction:"column"}],props:kc/* REMOVED_COMMON_BLOCK_2472 */ps://niutrans.com/",docUrl:B+"docs/services/niu/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"}]},custom:{name:"Custom",beta:!0,homepage:B+"docs/services/custom/",docUrl:B+"docs/services/custom/",titleKey:"description.custom",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"url",label:"API URL",required:!0,type:"text"},{name:"langs",required:!1,labelKey:"field.langs",type:"textarea",default:"zh-CN,en",optional:!0},{name:"placeholderDelimiters",required:!1,labelKey:"field.placeholderDelimiters",type:"text",default:Ro,optional:!0},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:5,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0}]},tenAlpha:{name:"TenAlpha",homepage:"https://fanyi.qq.com/",alpha:!0},cai:{name:"Cai",homepage:"https://fanyi.caiyunapp.com/",alpha:!0},you:{name:"You",alpha:!0,homepage:"https://youdao.com/"},volcAlpha:{name:"Volc Alpha",alpha:!0,homepage:"https://www.volcengine.com/"},d:{name:"D () ",canary:!0,homepage:"https://www.deepl.com/translator"},dpro:{name:"DPro (Canary) ",canary:!0,homepage:"https://www.deepl.com/translator"},papago:{name:"Papago",homepage:"https://translate.google.com/",canary:!0},mock:{name:"Mock",homepage:"https://www.google.com"},mock2:{name:"Mock2",homepage:"https://www.google.com"}};var Ec={manifest_version:3,name:"__MSG_brandName__",desc/* REMOVED_COMMON_BLOCK_2445 */.png",light:"icons/dark-48.png",size:48},{dark:"icons/64.png",light:"icons/dark-64.png",size:64},{dark:"icons/128.png",light:"icons/dark-128.png",size:128},{dark:"icons/256.png",light:"icons/dark-256.png",size:256}]},icons:{"32":"icons/32.png","48":"icons/48.png","64":"icons/64.png","128":"icons/128.png","256":"icons/256.png"},browser_specific_settings:{gecko:{id:"{5efceaa7-f3a2-4e59-a54b-85319448e305}",strict_min_version:"63.0"},gecko_android:{strict_min_version:"113.0"}},key:"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7JPn78UfqI3xIIOPPLPS74UTzLfJL1gQM8hlk/deKWvFP/WqUBnPJPdhQeF45sFpI1OjO70nFqdATT4/RwYAiZK7G/E6m27MDVnhHjszfzReOuoAEn9J3RnE2xEx5pFhRFcelhnwTTLrrn90aaPcaMtNsgXtZA1Ggz/SnX9I4ZygqpJYjx3Ql2t6SyNK222oRQiKMT93Rrjgyc8RFA7FKXsWglG0TvseRjbmG5Jk5gDx+2/YTcWGqCDotQnWnkPj/dBO23UAX7IpyJK3FGYdkvWFih6OVClHIIWY8mfCjjwSGbXNQNesaa9F2hrzBZ5MRTj4m7yj76mGxuPHPIE8mwIDAQAB"};var Ph="";function rt(){return Ph||Ec.version}function Xn(){return A.runtime.getManifest().version}var Tc="";function Ka(){return Tc||globalThis.navigator.userAgent}function Dc(e){Tc=e}function yn(){return Ka().includes("ImtFxiOS")}function Wo(){let t=Ka().match(/ImtFxiOS\/(\d+\.\d+\.\d+)/);return t?t[1]:null}function $n(){let e=Ka();return e.includes("ImtFxAndroid")||e.includes("ImtFxAOS")}function er(){let e=Ka();if(e.includes("ImtFxAndroid")){let t=e.match(/ImtFxAndroid\/(\d+\.\d+\.\d+)/);return t?t[1]:null}if(e.includes("ImtFxAOS")){let t=e.match(/ImtFxAOS\/(\d+\.\d+\.\d+)/);return t?t[1]:null}return null}function Wa(){let e=Ka();if(yn()||$n()){let t=e.match(/Imt[\w/.]+/);if(t){let a=t[0].split("/"),n=a[0];return a[2]&&(n+="_"+a[2]),{name:n,version:a[1]}}}return null}function Vo(){let e=Ka();if(!e.includes("iPhone"))return null;let t=e.match(/Version\/(\d+(?:\.\d+)?)/);return t?t[1]:null}function Cc(){try{let e=Vo();return e?parseFloat(e):0}catch{return 0}}function Mc(){let e=Ka();if(!e.includes("iPhone"))return null;let t=e.match(/iPhone OS (\d+(?:_\d+){1,2})/);return t&&t?t[1]:null}function tr(){let e,t="pending",a=new Promise((n,r)=>{e={async resolve(i){await i,t="fulfilled",n(i)},reject(i){t="rejected",r(i)}}});return Object.defineProperty(a,"state",{get:()=>t}),Object.assign(a,e)}function xa(e,t={}){let{signal:a,persistent:n}=t;return a?.aborted?Promise.reject(new DOMException("Delay was aborted.","AbortError")):new Promise((r,i)=>{let o=()=>{clearTimeout(u),i(new DOMException("Delay was aborted.","AbortError"))},u=setTimeout(()=>{a?.removeEventListener("abort",o),r()},e);if(a?.addEventListener("abort",o,{once:!0}),n===!1)try{Deno.unrefTimer(u)}catch(c){if(!(c instanceof ReferenceError))throw c}})}var Ic=class{#e=0;#t=[];#a=[];#n=tr();add(t){++this.#e,this.#r(t[Symbol.asyncIterator]())}async#r(t){try{let{value:a,done:n}=await t.next();n?--this.#e:this.#t.push({iterator:t,value:a})}catch(a){this.#a.push(a)}this.#n.resolve()}async*iterate(){for(;this.#e>0;){await this.#n;for(let t=0;t<this.#t.length;t++){let{iterator:a,value:n}=this.#t[t];yield n,this.#r(a)}if(this.#a.length){for(let t of this.#a)throw t;this.#a.length=0}this.#t.length=0,this.#n=tr()}}[Symbol.asyncIterator](){return this.iterate()}};var pi=globalThis||(typeof window<"u"?window:self),Bh=Object.create,Yo=Object.defineProperty,Rh=Object.getOwnPropertyDescriptor,_h=Object.getOwnPropertyNames,jh=Object.getPrototypeOf,Lh=Object.prototype.hasOwnProperty,Nh=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Oh=(e,t)=>{for(var a in t)Yo(e,a,{get:t[a],enumerable:!0})},Qo=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of _h(t))!Lh.call(e,r)&&r!==a&&Yo(e,r,{get:()=>t[r],enumerable:!(n=Rh(t,r))||n.enumerable});return e},zh=(e,t,a)=>(Qo(e,t,"default"),a&&Qo(a,t,"default")),Fc=(e,t,a)=>(a=e!=null?Bh(jh(e)):{},Qo(t||!e||!e.__esModule?Yo(a,"default",{value:e,enumerable:!0}):a,e)),Bc=Nh((e,t)=>{var a="Expected a function",n=NaN,r="[object Symbol]",i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt,l=typeof pi=="object"&&pi&&pi.Object===Object&&pi,d=typeof self=="object"&&self&&self.Object===Object&&self,m=l||d||Function("return this")(),p=Object.prototype,v=p.toString,w=Math.max,S=Math.min,g=function(){return m.Date.now()};function M(k,y,j){var _,P,N,X,Y,ke,Me=0,G=!1,oe=!1,qe=!0;if(typeof k!="function")throw new TypeError(a);y=x(y)||0,T(j)&&(G=!!j.leading,oe="maxWait"in j,N=oe?w(x(j.maxWait)||0,y):N,qe="trailing"in j?!!j.trailing:qe);function ge(W){var Q=_,ne=P;return _=P=void 0,Me=W,X=k.apply(ne,Q),X}function D(W){return Me=W,Y=setTimeout(ae,y),G?ge(W):X}function F(W){var Q=W-ke,ne=W-Me,se=y-Q;return oe?S(se,N-ne):se}function z(W){var Q=W-ke,ne=W-Me;return ke===void 0||Q>=y||Q<0||oe&&ne>=N}function ae(){var W=g();if(z(W))return J(W);Y=setTimeout(ae,F(W))}function J(W){return Y=void 0,qe&&_?ge(W):(_=P=void 0,X)}function he(){Y!==void 0&&clearTimeout(Y),Me=0,_=ke=P=Y=void 0}function ye(){return Y===void 0?X:J(g())}function ee(){var W=g(),Q=z(W);if(_=arguments,P=this,ke=W,Q){if(Y===void 0)return D(ke);if(oe)return Y=setTimeout(ae,y),ge(ke)}return Y===void 0&&(Y=setTimeout(ae,y)),X}return ee.cancel=he,ee.flush=ye,ee}function f(k,y,j){var _=!0,P=!0;if(typeof k!="function")throw new TypeError(a);return T(j)&&(_="leading"in j?!!j.leading:_,P="trailing"in j?!!j.trailing:P),M(k,y,{leading:_,maxWait:y,trailing:P})}function T(k){var y=typeof k;return!!k&&(y=="object"||y=="function")}function L(k){return!!k&&typeof k=="object"}function E(k){return typeof k=="symbol"||L(k)&&v.call(k)==r}function x(k){if(typeof k=="number")return k;if(E(k))return n;if(T(k)){var y=typeof k.valueOf=="function"?k.valueOf():k;k=T(y)?y+"":y}if(typeof k!="string")return k===0?k:+k;k=k.replace(i,"");var j=s.test(k);return j||u.test(k)?c(k.slice(2),j?2:8):o.test(k)?n:+k}t.exports=f}),Rc={};Oh(Rc,{default:()=>Jo});var Uh=Fc(Bc());zh(Rc,Fc(Bc()));var{default:Pc,...qh}=Uh,Jo=Pc!==void 0?Pc:qh;var gi=globalThis||(typeof window<"u"?window:self),Gh=Object.create,Xo=Object.defineProperty,Hh=Object.getOwnPropertyDescriptor,Kh=Object.getOwnPropertyNames,Wh=Object.getPrototypeOf,Vh=Object.prototype.hasOwnProperty,Qh=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Yh=(e,t)=>{for(var a in t)Xo(e,a,{get:t[a],enumerable:!0})},Zo=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Kh(t))!Vh.call(e,r)&&r!==a&&Xo(e,r,{get:()=>t[r],enumerable:!(n=Hh(t,r))||n.enumerable});return e},Jh=(e,t,a)=>(Zo(e,t,"default"),a&&Zo(a,t,"default")),jc=(e,t,a)=>(a=e!=null?Gh(Wh(e)):{},Zo(t||!e||!e.__esModule?Xo(a,"default",{value:e,enumerable:!0}):a,e)),Lc=Qh((e,t)=>{var a="Expected a function",n=NaN,r="[object Symbol]",i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt,l=typeof gi=="object"&&gi&&gi.Object===Object&&gi,d=typeof self=="object"&&self&&self.Object===Object&&self,m=l||d||Function("return this")(),p=Object.prototype,v=p.toString,w=Math.max,S=Math.min,g=function(){return m.Date.now()};function M(x,k,y){var j,_,P,N,X,Y,ke=0,Me=!1,G=!1,oe=!0;if(typeof x!="function")throw new TypeError(a);k=E(k)||0,f(y)&&(Me=!!y.leading,G="maxWait"in y,P=G?w(E(y.maxWait)||0,k):P,oe="trailing"in y?!!y.trailing:oe);function qe(ee){var W=j,Q=_;return j=_=void 0,ke=ee,N=x.apply(Q,W),N}function ge(ee){return ke=ee,X=setTimeout(z,k),Me?qe(ee):N}function D(ee){var W=ee-Y,Q=ee-ke,ne=k-W;return G?S(ne,P-Q):ne}function F(ee){var W=ee-Y,Q=ee-ke;return Y===void 0||W>=k||W<0||G&&Q>=P}function z(){var ee=g();if(F(ee))return ae(ee);X=setTimeout(z,D(ee))}function ae(ee){return X=void 0,oe&&j?qe(ee):(j=_=void 0,N)}function J(){X!==void 0&&clearTimeout(X),ke=0,j=Y=_=X=void 0}function he(){return X===void 0?N:ae(g())}function ye(){var ee=g(),W=F(ee);if(j=arguments,_=this,Y=ee,W){if(X===void 0)return ge(Y);if(G)return X=setTimeout(z,k),qe(Y)}return X===void 0&&(X=setTimeout(z,k)),N}return ye.cancel=J,ye.flush=he,ye}function f(x){var k=typeof x;return!!x&&(k=="object"||k=="function")}function T(x){return!!x&&typeof x=="object"}function L(x){return typeof x=="symbol"||T(x)&&v.call(x)==r}function E(x){if(typeof x=="number")return x;if(L(x))return n;if(f(x)){var k=typeof x.valueOf=="function"?x.valueOf():x;x=f(k)?k+"":k}if(typeof x!="string")return x===0?x:+x;x=x.replace(i,"");var y=s.test(x);return y||u.test(x)?c(x.slice(2),y?2:8):o.test(x)?n:+x}t.exports=M}),Nc={};Yh(Nc,{default:()=>ar});var Zh=jc(Lc());Jh(Nc,jc(Lc()));var{default:_c,...Xh}=Zh,ar=_c!==void 0?_c:Xh;var $h=Object.create,es=Object.defineProperty,ef=Object.getOwnPropertyDescriptor,tf=Object.getOwnPropertyNames,af=Object.getPrototypeOf,nf=Object.prototype.hasOwnProperty,rf=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),of=(e,t)=>{for(var a in t)es(e,a,{get:t[a],enumerable:!0})},$o=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of tf(t))!nf.call(e,r)&&r!==a&&es(e,r,{get:()=>t[r],enumerable:!(n=ef(t,r))||n.enumerable});return e},sf=(e,t,a)=>($o(e,t,"default"),a&&$o(a,t,"default")),zc=(e,t,a)=>(a=e!=null?$h(af(e)):{},$o(t||!e||!e.__esModule?es(a,"default",{value:e,enumerable:!0}):a,e)),Uc=rf((e,t)=>{(function(a,n){typeof e=="object"&&typeof t=="object"?t.exports=n():typeof define=="function"&&define.amd?define([],n):typeof e=="object"?e.notie=n():a.notie=n()})(e,function(){return function(a){function n(i){if(r[i])return r[i].exports;var o=r[i]={i,l:!1,exports:{}};return a[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}var r={};return n.m=a,n.c=r,n.i=function(i){return i},n.d=function(i,o,s){n.o(i,o)||Object.defineProperty(i,o,{configurable:!1,enumerable:!0,get:s})},n.n=function(i){var o=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(o,"a",o),o},n.o=function(i,o){return Object.prototype.hasOwnProperty.call(i,o)},n.p="",n(n.s=1)}([function(a,n){a.exports=function(r){return r.webpackPolyfill||(r.deprecate=function(){},r.paths=[],r.children||(r.children=[]),Object.defineProperty(r,"loaded",{enumerable:!0,get:function(){return r.l}}),Object.defineProperty(r,"id",{enumerable:!0,get:function(){return r.i}}),r.webpackPolyfill=1),r}},function(a,n,r){"use strict";(function(i){var o,s,u,c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(l){return typeof l}:function(l){return l&&typeof Symbol=="function"&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l};(function(l,d){c(n)==="object"&&c(i)==="object"?i.exports=d():(s=[],o=d,u=typeof o=="function"?o.apply(n,s):o,u!==void 0&&(i.exports=u))})(void 0,function(){return function(l){function d(p){if(m[p])return m[p].exports;var v=m[p]={i:p,l:!1,exports:{}};return l[p].call(v.exports,v,v.exports,d),v.l=!0,v.exports}var m={};return d.m=l,d.c=m,d.i=function(p){return p},d.d=function(p,v,w){d.o(p,v)||Object.defineProperty(p,v,{configurable:!1,enumerable:!0,get:w})},d.n=function(p){var v=p&&p.__esModule?function(){return p.default}:function(){return p};return d.d(v,"a",v),v},d.o=function(p,v){return Object.prototype.hasOwnProperty.call(p,v)},d.p="",d(d.s=0)}([function(l,d,m){function p(D,F){var z={};for(var ae in D)F.indexOf(ae)>=0||Object.prototype.hasOwnProperty.call(D,ae)&&(z[ae]=D[ae]);return z}Object.defineProperty(d,"__esModule",{value:!0});var v=typeof Symbol=="function"&&c(Symbol.iterator)==="symbol"?function(D){return typeof D>"u"?"undefined":c(D)}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D>"u"?"undefined":c(D)},w=Object.assign||function(D){for(var F=1;F<arguments.length;F++){var z=arguments[F];for(var ae in z)Object.prototype.hasOwnProperty.call(z,ae)&&(D[ae]=z[ae])}return D},S={top:"top",bottom:"bottom"},g={alertTime:3,dateMonths:["January","February","March","April","May","June","July","August","September","October","November","December"],overlayClickDismiss:!0,overlayOpacity:.75,transitionCurve:"ease",transitionDuration:.3,transitionSelector:"all",classes:{container:"notie-container",textbox:"notie-textbox",textboxInner:"notie-textbox-inner",button:"notie-button",element:"notie-element",elementHalf:"notie-element-half",elementThird:"notie-element-third",overlay:"notie-overlay",backgroundSuccess:"notie-background-success",backgroundWarning:"notie-background-warning",backgroundError:"notie-background-error",backgroundInfo:"notie-background-info",backgroundNeutral:"notie-background-neutral",backgroundOverlay:"notie-background-overlay",alert:"notie-alert",inputField:"notie-input-field",selectChoiceRepeated:"notie-select-choice-repeated",dateSelectorInner:"notie-date-selector-inner",dateSelectorUp:"notie-date-selector-up"},ids:{overlay:"notie-overlay"},positions:{alert:S.top,force:S.top,confirm:S.top,input:S.top,select:S.bottom,date:S.top}},M=d.setOptions=function(D){g=w({},g,D,{classes:w({},g.classes,D.classes),ids:w({},g.ids,D.ids),positions:w({},g.positions,D.positions)})},f=function(){return new Promise(function(D){return setTimeout(D,0)})},T=function(D){return new Promise(function(F){return setTimeout(F,1e3*D)})},L=function(){document.activeElement&&document.activeElement.blur()},E=function(){var D="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(F){var z=16*Math.random()|0,ae=F==="x"?z:3&z|8;return ae.toString(16)});return"notie-"+D},x={1:g.classes.backgroundSuccess,success:g.classes.backgroundSuccess,2:g.classes.backgroundWarning,warning:g.classes.backgroundWarning,3:g.classes.backgroundError,error:g.classes.backgroundError,4:g.classes.backgroundInfo,info:g.classes.backgroundInfo,5:g.classes.backgroundNeutral,neutral:g.classes.backgroundNeutral},k=function(){return g.transitionSelector+" "+g.transitionDuration+"s "+g.transitionCurve},y=function(D){return D.keyCode===13},j=function(D){return D.keyCode===27},_=function(D,F){D.classList.add(g.classes.container),D.style[F]="-10000px",document.body.appendChild(D),D.style[F]="-"+D.offsetHeight+"px",D.listener&&window.addEventListener("keydown",D.listener),f().then(function(){D.style.transition=k(),D.style[F]=0})},P=function(D,F){var z=document.getElementById(D);z&&(z.style[F]="-"+z.offsetHeight+"px",z.listener&&window.removeEventListener("keydown",z.listener),T(g.transitionDuration).then(function(){z.parentNode&&z.parentNode.removeChild(z)}))},N=function(D,F){var z=document.createElement("div");z.id=g.ids.overlay,z.classList.add(g.classes.overlay),z.classList.add(g.classes.backgroundOverlay),z.style.opacity=0,D&&g.overlayClickDismiss&&(z.onclick=function(){P(D.id,F),X()}),document.body.appendChild(z),f().then(function(){z.style.transition=k(),z.style.opacity=g.overlayOpacity})},X=function(){var D=document.getElementById(g.ids.overlay);D.style.opacity=0,T(g.transitionDuration).then(function(){D.parentNode&&D.parentNode.removeChild(D)})},Y=d.hideAlerts=function(D){var F=document.getElementsByClassName(g.classes.alert);if(F.length){for(var z=0;z<F.length;z++){var ae=F[z];P(ae.id,ae.position)}D&&T(g.transitionDuration).then(function(){return D()})}},ke=d.alert=function(D){var F=D.type,z=F===void 0?4:F,ae=D.text,J=D.time,he=J===void 0?g.alertTime:J,ye=D.stay,ee=ye!==void 0&&ye,W=D.position,Q=W===void 0?g.positions.alert||Q.top:W;L(),Y();var ne=document.createElement("div"),se=E();ne.id=se,ne.position=Q,ne.classList.add(g.classes.textbox),ne.classList.add(x[z]),ne.classList.add(g.classes.alert),ne.innerHTML='<div class="'+g.classes.textboxInner+'">'+ae+"</div>",ne.onclick=function(){return P(se,Q)},ne.listener=function(q){(y(q)||j(q))&&Y()},_(ne,Q),he&&he<1&&(he=1),!ee&&he&&T(he).then(function(){return P(se,Q)})},Me=d.force=function(D,F){var z=D.type,ae=z===void 0?5:z,J=D.text,he=D.buttonText,ye=he===void 0?"OK":he,ee=D.callback,W=D.position,Q=W===void 0?g.positions.force||Q.top:W;L(),Y();var ne=document.createElement("div"),se=E();ne.id=se;var q=document.createElement("div");q.classList.add(g.classes.textbox),q.classList.add(g.classes.backgroundInfo),q.innerHTML='<div class="'+g.classes.textboxInner+'">'+J+"</div>";var te=document.createElement("div");te.classList.add(g.classes.button),te.classList.add(x[ae]),te.innerHTML=ye,te.onclick=function(){P(se,Q),X(),ee?ee():F&&F()},ne.appendChild(q),ne.appendChild(te),ne.listener=function(xe){y(xe)&&te.click()},_(ne,Q),N()},G=d.confirm=function(D,F,z){var ae=D.text,J=D.submitText,he=J===void 0?"Yes":J,ye=D.cancelText,ee=ye===void 0?"Cancel":ye,W=D.submitCallback,Q=D.cancelCallback,ne=D.position,se=ne===void 0?g.positions.confirm||se.top:ne;L(),Y();var q=document.createElement("div"),te=E();q.id=te;var xe=document.createElement("div");xe.classList.add(g.classes.textbox),xe.classList.add(g.classes.backgroundInfo),xe.innerHTML='<div class="'+g.classes.textboxInner+'">'+ae+"</div>";var pe=document.createElement("div");pe.classList.add(g.classes.button),pe.classList.add(g.classes.elementHalf),pe.classList.add(g.classes.backgroundSuccess),pe.innerHTML=he,pe.onclick=function(){P(te,se),X(),W?W():F&&F()};var V=document.createElement("div");V.classList.add(g.classes.button),V.classList.add(g.classes.elementHalf),V.classList.add(g.classes.backgroundError),V.innerHTML=ee,V.onclick=function(){P(te,se),X(),Q?Q():z&&z()},q.appendChild(xe),q.appendChild(pe),q.appendChild(V),q.listener=function(Ee){y(Ee)?pe.click():j(Ee)&&V.click()},_(q,se),N(q,se)},oe=function(D,F,z){var ae=D.text,J=D.submitText,he=J===void 0?"Submit":J,ye=D.cancelText,ee=ye===void 0?"Cancel":ye,W=D.submitCallback,Q=D.cancelCallback,ne=D.position,se=ne===void 0?g.positions.input||se.top:ne,q=p(D,["text","submitText","cancelText","submitCallback","cancelCallback","position"]);L(),Y();var te=document.createElement("div"),xe=E();te.id=xe;var pe=document.createElement("div");pe.classList.add(g.classes.textbox),pe.classList.add(g.classes.backgroundInfo),pe.innerHTML='<div class="'+g.classes.textboxInner+'">'+ae+"</div>";var V=document.createElement("input");V.classList.add(g.classes.inputField),V.setAttribute("autocapitalize",q.autocapitalize||"none"),V.setAttribute("autocomplete",q.autocomplete||"off"),V.setAttribute("autocorrect",q.autocorrect||"off"),V.setAttribute("autofocus",q.autofocus||"true"),V.setAttribute("inputmode",q.inputmode||"verbatim"),V.setAttribute("max",q.max||""),V.setAttribute("maxlength",q.maxlength||""),V.setAttribute("min",q.min||""),V.setAttribute("minlength",q.minlength||""),V.setAttribute("placeholder",q.placeholder||""),V.setAttribute("spellcheck",q.spellcheck||"default"),V.setAttribute("step",q.step||"any"),V.setAttribute("type",q.type||"text"),V.value=q.value||"",q.allowed&&(V.oninput=function(){var ie=void 0;if(Array.isArray(q.allowed)){for(var Ie="",K=q.allowed,Qe=0;Qe<K.length;Qe++)K[Qe]==="an"?Ie+="0-9a-zA-Z":K[Qe]==="a"?Ie+="a-zA-Z":K[Qe]==="n"&&(Ie+="0-9"),K[Qe]==="s"&&(Ie+=" ");ie=new RegExp("[^"+Ie+"]","g")}else v(q.allowed)==="object"&&(ie=q.allowed);V.value=V.value.replace(ie,"")});var Ee=document.createElement("div");Ee.classList.add(g.classes.button),Ee.classList.add(g.classes.elementHalf),Ee.classList.add(g.classes.backgroundSuccess),Ee.innerHTML=he,Ee.onclick=function(){P(xe,se),X(),W?W(V.value):F&&F(V.value)};var je=document.createElement("div");je.classList.add(g.classes.button),je.classList.add(g.classes.elementHalf),je.classList.add(g.classes.backgroundError),je.innerHTML=ee,je.onclick=function(){P(xe,se),X(),Q?Q(V.value):z&&z(V.value)},te.appendChild(pe),te.appendChild(V),te.appendChild(Ee),te.appendChild(je),te.listener=function(ie){y(ie)?Ee.click():j(ie)&&je.click()},_(te,se),V.focus(),N(te,se)};d.input=oe;var qe=d.select=function(D,F){var z=D.text,ae=D.cancelText,J=ae===void 0?"Cancel":ae,he=D.cancelCallback,ye=D.choices,ee=D.position,W=ee===void 0?g.positions.select||W.top:ee;L(),Y();var Q=document.createElement("div"),ne=E();Q.id=ne;var se=document.createElement("div");se.classList.add(g.classes.textbox),se.classList.add(g.classes.backgroundInfo),se.innerHTML='<div class="'+g.classes.textboxInner+'">'+z+"</div>",Q.appendChild(se),ye.forEach(function(te,xe){var pe=te.type,V=pe===void 0?1:pe,Ee=te.text,je=te.handler,ie=document.createElement("div");ie.classList.add(x[V]),ie.classList.add(g.classes.button),ie.classList.add(g.classes.selectChoice);var Ie=ye[xe+1];Ie&&!Ie.type&&(Ie.type=1),Ie&&Ie.type===V&&ie.classList.add(g.classes.selectChoiceRepeated),ie.innerHTML=Ee,ie.onclick=function(){P(ne,W),X(),je()},Q.appendChild(ie)});var q=document.createElement("div");q.classList.add(g.classes.backgroundNeutral),q.classList.add(g.classes.button),q.innerHTML=J,q.onclick=function(){P(ne,W),X(),he?he():F&&F()},Q.appendChild(q),Q.listener=function(te){j(te)&&q.click()},_(Q,W),N(Q,W)},ge=d.date=function(D,F,z){var ae=D.value,J=ae===void 0?new Date:ae,he=D.submitText,ye=he===void 0?"OK":he,ee=D.cancelText,W=ee===void 0?"Cancel":ee,Q=D.submitCallback,ne=D.cancelCallback,se=D.position,q=se===void 0?g.positions.date||q.top:se;L(),Y();var te="&#9662",xe=document.createElement("div"),pe=document.createElement("div"),V=document.createElement("div"),Ee=function(_e){xe.innerHTML=g.dateMonths[_e.getMonth()],pe.innerHTML=_e.getDate(),V.innerHTML=_e.getFullYear()},je=function(_e){var Ae=new Date(J.getFullYear(),J.getMonth()+1,0).getDate(),dt=_e.target.textContent.replace(/^0+/,"").replace(/[^\d]/g,"").slice(0,2);Number(dt)>Ae&&(dt=Ae.toString()),_e.target.textContent=dt,Number(dt)<1&&(dt="1"),J.setDate(Number(dt))},ie=function(_e){var Ae=_e.target.textContent.replace(/^0+/,"").replace(/[^\d]/g,"").slice(0,4);_e.target.textContent=Ae,J.setFullYear(Number(Ae))},Ie=function(_e){Ee(J)},K=function(_e){var Ae=new Date(J.getFullYear(),J.getMonth()+_e+1,0).getDate();J.getDate()>Ae&&J.setDate(Ae),J.setMonth(J.getMonth()+_e),Ee(J)},Qe=function(_e){J.setDate(J.getDate()+_e),Ee(J)},Kn=function(_e){var Ae=J.getFullYear()+_e;Ae<0?J.setFullYear(0):J.setFullYear(J.getFullYear()+_e),Ee(J)},Ot=document.createElement("div"),ja=E();Ot.id=ja;var cn=document.createElement("div");cn.classList.add(g.classes.backgroundInfo);var Ye=document.createElement("div");Ye.classList.add(g.classes.dateSelectorInner);var At=document.createElement("div");At.classList.add(g.classes.button),At.classList.add(g.classes.elementThird),At.classList.add(g.classes.dateSelectorUp),At.innerHTML=te;var Je=document.createElement("div");Je.classList.add(g.classes.button),Je.classList.add(g.classes.elementThird),Je.classList.add(g.classes.dateSelectorUp),Je.innerHTML=te;var ct=document.createElement("div");ct.classList.add(g.classes.button),ct.classList.add(g.classes.elementThird),ct.classList.add(g.classes.dateSelectorUp),ct.innerHTML=te,xe.classList.add(g.classes.element),xe.classList.add(g.classes.elementThird),xe.innerHTML=g.dateMonths[J.getMonth()],pe.classList.add(g.classes.element),pe.classList.add(g.classes.elementThird),pe.setAttribute("contentEditable",!0),pe.addEventListener("input",je),pe.addEventListener("blur",Ie),pe.innerHTML=J.getDate(),V.classList.add(g.classes.element),V.classList.add(g.classes.elementThird),V.setAttribute("contentEditable",!0),V.addEventListener("input",ie),V.addEventListener("blur",Ie),V.innerHTML=J.getFullYear();var Jt=document.createElement("div");Jt.classList.add(g.classes.button),Jt.classList.add(g.classes.elementThird),Jt.innerHTML=te;var Zt=document.createElement("div");Zt.classList.add(g.classes.button),Zt.classList.add(g.classes.elementThird),Zt.innerHTML=te;var La=document.createElement("div");La.classList.add(g.classes.button),La.classList.add(g.classes.elementThird),La.innerHTML=te,At.onclick=function(){return K(1)},Je.onclick=function(){return Qe(1)},ct.onclick=function(){return Kn(1)},Jt.onclick=function(){return K(-1)},Zt.onclick=function(){return Qe(-1)},La.onclick=function(){return Kn(-1)};var at=document.createElement("div");at.classList.add(g.classes.button),at.classList.add(g.classes.elementHalf),at.classList.add(g.classes.backgroundSuccess),at.innerHTML=ye,at.onclick=function(){P(ja,q),X(),Q?Q(J):F&&F(J)};var Xt=document.createElement("div");Xt.classList.add(g.classes.button),Xt.classList.add(g.classes.elementHalf),Xt.classList.add(g.classes.backgroundError),Xt.innerHTML=W,Xt.onclick=function(){P(ja,q),X(),ne?ne(J):z&&z(J)},Ye.appendChild(At),Ye.appendChild(Je),Ye.appendChild(ct),Ye.appendChild(xe),Ye.appendChild(pe),Ye.appendChild(V),Ye.appendChild(Jt),Ye.appendChild(Zt),Ye.appendChild(La),cn.appendChild(Ye),Ot.appendChild(cn),Ot.appendChild(at),Ot.appendChild(Xt),Ot.listener=function(_e){y(_e)?at.click():j(_e)&&Xt.click()},_(Ot,q),N(Ot,q)};d.default={alert:ke,force:Me,confirm:G,input:oe,select:qe,date:ge,setOptions:M,hideAlerts:Y}}])})}).call(n,r(0)(a))}])})}),qc={};of(qc,{default:()=>hi});var uf=zc(Uc());sf(qc,zc(Uc()));var{default:Oc,...lf}=uf,hi=Oc!==void 0?Oc:lf;var fi=typeof navigator<"u"?navigator.userAgent.toLowerCase().indexOf("firefox")>0:!1;function bi(e,t,a,n){e.addEventListener?e.addEventListener(t,a,n):e.attachEvent&&e.attachEvent(`on${t}`,a)}function vn(e,t,a,n){e.removeEventListener?e.removeEventListener(t,a,n):e.detachEvent&&e.detachEvent(`on${t}`,a)}function ts(e,t){let a=t.slice(0,t.length-1);for(let n=0;n<a.length;n++)a[n]=e[a[n].toLowerCase()];return a}function as(e){typeof e!="string"&&(e=""),e=e.replace(/\s/g,"");let t=e.split(","),a=t.lastIndexOf("");for(;a>=0;)t[a-1]+=",",t.splice(a,1),a=t.lastIndexOf("");return t}function Gc(e,t){let a=e.length>=t.length?e:t,n=e.length>=t.length?t:e,r=!0;for(let i=0;i<a.length;i++)n.indexOf(a[i])===-1&&(r=!1);return r}var xn={backspace:8,"\u232B":8,tab:9,clear:12,enter:13,"\u21A9":13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,ins:45,insert:45,home:36,end:35,pageup:33,pagedown:34,capslock:20,num_0:96,num_1:97,num_2:98,num_3:99,num_4:100,num_5:101,num_6:102,num_7:103,num_8:104,num_9:105,num_multiply:106,num_add:107,num_enter:108,num_subtract:109,num_decimal:110,num_divide:111,"\u21EA":20,",":188,".":190,"/":191,"`":192,"-":fi?173:189,"=":fi?61:187,";":fi?59:186,"'":222,"[":219,"]":221,"\\":220},yt={"\u21E7":16,shift:16,"\u2325":18,alt:18,option:18,"\u2303":17,ctrl:17,control:17,"\u2318":91,cmd:91,command:91},wn={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey",shiftKey:16,ctrlKey:17,altKey:18,metaKey:91},Ke={16:!1,18:!1,17:!1,91:!1},Pe={};for(let e=1;e<20;e++)xn[`f${e}`]=111+e;var Fe=[],nr=null,Wc="all",ra=new Map,ir=e=>xn[e.toLowerCase()]||yt[e.toLowerCase()]||e.toUpperCase().charCodeAt(0),cf=e=>Object.keys(xn).find(t=>xn[t]===e),df=e=>Object.keys(yt).find(t=>yt[t]===e);function Vc(e){Wc=e||"all"}function rr(){return Wc||"all"}function mf(){return Fe.slice(0)}function pf(){return Fe.map(e=>cf(e)||df(e)||String.fromCharCode(e))}function gf(){let e=[];return Object.keys(Pe).forEach(t=>{Pe[t].forEach(({key:a,scope:n,mods:r,shortcut:i})=>{e.push({scope:n,shortcut:i,mods:r,keys:a.split("+").map(o=>ir(o))})})}),e}function hf(e){let t=e.target||e.srcElement,{tagName:a}=t,n=!0,r=a==="INPUT"&&!["checkbox","radio","range","button","file","reset","submit","color"].includes(t.type);return(t.isContentEditable||(r||a==="TEXTAREA"||a==="SELECT")&&!t.readOnly)&&(n=!1),n}function ff(e){return typeof e=="string"&&(e=ir(e)),Fe.indexOf(e)!==-1}function bf(e,t){let a,n;e||(e=rr());for(let r in Pe)if(Object.prototype.hasOwnProperty.call(Pe,r))for(a=Pe[r],n=0;n<a.length;)a[n].scope===e?a.splice(n,1).forEach(({element:o})=>rs(o)):n++;rr()===e&&Vc(t||"all")}function yf(e){let t=e.keyCode||e.which||e.charCode,a=Fe.indexOf(t);if(a>=0&&Fe.splice(a,1),e.key&&e.key.toLowerCase()==="meta"&&Fe.splice(0,Fe.length),(t===93||t===224)&&(t=91),t in Ke){Ke[t]=!1;for(let n in yt)yt[n]===t&&(wa[n]=!1)}}function Qc(e,...t){if(typeof e>"u")Object.keys(Pe).forEach(a=>{Array.isArray(Pe[a])&&Pe[a].forEach(n=>yi(n)),delete Pe[a]}),rs(null);else if(Array.isArray(e))e.forEach(a=>{a.key&&yi(a)});else if(typeof e=="object")e.key&&yi(e);else if(typeof e=="string"){let[a,n]=t;typeof a=="function"&&(n=a,a=""),yi({key:e,scope:a,method:n,splitKey:"+"})}}var yi=({key:e,scope:t,method:a,splitKey:n="+"})=>{as(e).forEach(i=>{let o=i.split(n),s=o.length,u=o[s-1],c=u==="*"?"*":ir(u);if(!Pe[c])return;t||(t=rr());let l=s>1?ts(yt,o):[],d=[];Pe[c]=Pe[c].filter(m=>{let v=(a?m.method===a:!0)&&m.scope===t&&Gc(m.mods,l);return v&&d.push(m.element),!v}),d.forEach(m=>rs(m))})};function Hc(e,t,a,n){if(t.element!==n)return;let r;if(t.scope===a||t.scope==="all"){r=t.mods.length>0;for(let i in Ke)Object.prototype.hasOwnProperty.call(Ke,i)&&(!Ke[i]&&t.mods.indexOf(+i)>-1||Ke[i]&&t.mods.indexOf(+i)===-1)&&(r=!1);(t.mods.length===0&&!Ke[16]&&!Ke[18]&&!Ke[17]&&!Ke[91]||r||t.shortcut==="*")&&(t.keys=[],t.keys=t.keys.concat(Fe),t.method(e,t)===!1&&(e.preventDefault?e.preventDefault():e.returnValue=!1,e.stopPropagation&&e.stopPropagation(),e.cancelBubble&&(e.cancelBubble=!0)))}}function Kc(e,t){let a=Pe["*"],n=e.keyCode||e.which||e.charCode;if(!wa.filter.call(this,e))return;if((n===93||n===224)&&(n=91),Fe.indexOf(n)===-1&&n!==229&&Fe.push(n),["metaKey","ctrlKey","altKey","shiftKey"].forEach(s=>{let u=wn[s];e[s]&&Fe.indexOf(u)===-1?Fe.push(u):!e[s]&&Fe.indexOf(u)>-1?Fe.splice(Fe.indexOf(u),1):s==="metaKey"&&e[s]&&(Fe=Fe.filter(c=>c in wn||c===n))}),n in Ke){Ke[n]=!0;for(let s in yt)if(Object.prototype.hasOwnProperty.call(yt,s)){let u=wn[yt[s]];wa[s]=e[u]}if(!a)return}for(let s in Ke)Object.prototype.hasOwnProperty.call(Ke,s)&&(Ke[s]=e[wn[s]]);e.getModifierState&&!(e.altKey&&!e.ctrlKey)&&e.getModifierState("AltGraph")&&(Fe.indexOf(17)===-1&&Fe.push(17),Fe.indexOf(18)===-1&&Fe.push(18),Ke[17]=!0,Ke[18]=!0);let r=rr();if(a)for(let s=0;s<a.length;s++)a[s].scope===r&&(e.type==="keydown"&&a[s].keydown||e.type==="keyup"&&a[s].keyup)&&Hc(e,a[s],r,t);if(!(n in Pe))return;let i=Pe[n],o=i.length;for(let s=0;s<o;s++)if((e.type==="keydown"&&i[s].keydown||e.type==="keyup"&&i[s].keyup)&&i[s].key){let u=i[s],{splitKey:c}=u,l=u.key.split(c),d=[];for(let m=0;m<l.length;m++)d.push(ir(l[m]));d.sort().join("")===Fe.sort().join("")&&Hc(e,u,r,t)}}function wa(e,t,a){Fe=[];let n=as(e),r=[],i="all",o=document,s=0,u=!1,c=!0,l="+",d=!1,m=!1;for(a===void 0&&typeof t=="function"&&(a=t),Object.prototype.toString.call(t)==="[object Object]"&&(t.scope&&(i=t.scope),t.element&&(o=t.element),t.keyup&&(u=t.keyup),t.keydown!==void 0&&(c=t.keydown),t.capture!==void 0&&(d=t.capture),typeof t.splitKey=="string"&&(l=t.splitKey),t.single===!0&&(m=!0)),typeof t=="string"&&(i=t),m&&Qc(e,i);s<n.length;s++)e=n[s].split(l),r=[],e.length>1&&(r=ts(yt,e)),e=e[e.length-1],e=e==="*"?"*":ir(e),e in Pe||(Pe[e]=[]),Pe[e].push({keyup:u,keydown:c,scope:i,mods:r,shortcut:n[s],method:a,key:n[s],splitKey:l,element:o});if(typeof o<"u"&&window){if(!ra.has(o)){let p=(w=window.event)=>Kc(w,o),v=(w=window.event)=>{Kc(w,o),yf(w)};ra.set(o,{keydownListener:p,keyupListenr:v,capture:d}),bi(o,"keydown",p,d),bi(o,"keyup",v,d)}if(!nr){let p=()=>{Fe=[]};nr={listener:p,capture:d},bi(window,"focus",p,d)}}}function vf(e,t="all"){Object.keys(Pe).forEach(a=>{Pe[a].filter(r=>r.scope===t&&r.shortcut===e).forEach(r=>{r&&r.method&&r.method()})})}function rs(e){let t=Object.values(Pe).flat();if(t.findIndex(({element:n})=>n===e)<0){let{keydownListener:n,keyupListenr:r,capture:i}=ra.get(e)||{};n&&r&&(vn(e,"keyup",r,i),vn(e,"keydown",n,i),ra.delete(e))}if((t.length<=0||ra.size<=0)&&(Object.keys(ra).forEach(r=>{let{keydownListener:i,keyupListenr:o,capture:s}=ra.get(r)||{};i&&o&&(vn(r,"keyup",o,s),vn(r,"keydown",i,s),ra.delete(r))}),ra.clear(),Object.keys(Pe).forEach(r=>delete Pe[r]),nr)){let{listener:r,capture:i}=nr;vn(window,"focus",r,i),nr=null}}var ns={getPressedKeyString:pf,setScope:Vc,getScope:rr,deleteScope:bf,getPressedKeyCodes:mf,getAllKeyCodes:gf,isPressed:ff,filter:hf,trigger:vf,unbind:Qc,keyMap:xn,modifier:yt,modifierMap:wn};for(let e in ns)Object.prototype.hasOwnProperty.call(ns,e)&&(wa[e]=ns[e]);if(typeof window<"u"){let e=window.hotkeys;wa.noConflict=t=>(t&&window.hotkeys===wa&&(window.hotkeys=e),wa),window.hotkeys=wa}var xf={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},Jc={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},Oe={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},it={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},Aa={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"},C=class An{static getFirstMatch(t,a){let n=a.match(t);return n&&n.length>0&&n[1]||""}static getSecondMatch(t,a){let n=a.match(t);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(t,a,n){if(t.test(a))return n}static getWindowsVersionName(t){switch(t){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(t){let a=t.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(a.push(0),a[0]===10)switch(a[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(t){let a=t.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(a.push(0),!(a[0]===1&&a[1]<5)){if(a[0]===1&&a[1]<6)return"Cupcake";if(a[0]===1&&a[1]>=6)return"Donut";if(a[0]===2&&a[1]<2)return"Eclair";if(a[0]===2&&a[1]===2)return"Froyo";if(a[0]===2&&a[1]>2)return"Gingerbread";if(a[0]===3)return"Honeycomb";if(a[0]===4&&a[1]<1)return"Ice Cream Sandwich";if(a[0]===4&&a[1]<4)return"Jelly Bean";if(a[0]===4&&a[1]>=4)return"KitKat";if(a[0]===5)return"Lollipop";if(a[0]===6)return"Marshmallow";if(a[0]===7)return"Nougat";if(a[0]===8)return"Oreo";if(a[0]===9)return"Pie"}}static getVersionPrecision(t){return t.split(".").length}static compareVersions(t,a,n=!1){let r=An.getVersionPrecision(t),i=An.getVersionPrecision(a),o=Math.max(r,i),s=0,u=An.map([t,a],c=>{let l=o-An.getVersionPrecision(c),d=c+new Array(l+1).join(".0");return An.map(d.split("."),m=>new Array(20-m.length).join("0")+m).reverse()});for(n&&(s=o-Math.min(r,i)),o-=1;o>=s;){if(u[0][o]>u[1][o])return 1;if(u[0][o]===u[1][o]){if(o===s)return 0;o-=1}else if(u[0][o]<u[1][o])return-1}}static map(t,a){let n=[],r;if(Array.prototype.map)return Array.prototype.map.call(t,a);for(r=0;r<t.length;r+=1)n.push(a(t[r]));return n}static find(t,a){let n,r;if(Array.prototype.find)return Array.prototype.find.call(t,a);for(n=0,r=t.length;n<r;n+=1){let i=t[n];if(a(i,n))return i}}static assign(t,...a){let n=t,r,i;if(Object.assign)return Object.assign(t,...a);for(r=0,i=a.length;r<i;r+=1){let o=a[r];typeof o=="object"&&o!==null&&Object.keys(o).forEach(s=>{n[s]=o[s]})}return t}static getBrowserAlias(t){return xf[t]}static getBrowserTypeByAlias(t){return Jc[t]||""}},Te=/version\/(\d+(\.?_?\d+)+)/i,wf=[{test:[/googlebot/i],describe(e){let t={name:"Googlebot"},a=C.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/opera/i],describe(e){let t={name:"Opera"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/opr\/|opios/i],describe(e){let t={name:"Opera"},a=C.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/SamsungBrowser/i],describe(e){let t={name:"Samsung Internet for Android"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/Whale/i],describe(e){let t={name:"NAVER Whale Browser"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/MZBrowser/i],describe(e){let t={name:"MZ Browser"},a=C.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/focus/i],describe(e){let t={name:"Focus"},a=C.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/swing/i],describe(e){let t={name:"Swing"},a=C.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/coast/i],describe(e){let t={name:"Opera Coast"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){let t={name:"Opera Touch"},a=C.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/yabrowser/i],describe(e){let t={name:"Yandex Browser"},a=C.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/ucbrowser/i],describe(e){let t={name:"UC Browser"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/Maxthon|mxios/i],describe(e){let t={name:"Maxthon"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/epiphany/i],describe(e){let t={name:"Epiphany"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/puffin/i],describe(e){let t={name:"Puffin"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/sleipnir/i],describe(e){let t={name:"Sleipnir"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/k-meleon/i],describe(e){let t={name:"K-Meleon"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/micromessenger/i],describe(e){let t={name:"WeChat"},a=C.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/qqbrowser/i],describe(e){let t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},a=C.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/msie|trident/i],describe(e){let t={name:"Internet Explorer"},a=C.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/\sedg\//i],describe(e){let t={name:"Microsoft Edge"},a=C.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/edg([ea]|ios)/i],describe(e){let t={name:"Microsoft Edge"},a=C.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/vivaldi/i],describe(e){let t={name:"Vivaldi"},a=C.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/seamonkey/i],describe(e){let t={name:"SeaMonkey"},a=C.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/sailfish/i],describe(e){let t={name:"Sailfish"},a=C.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return a&&(t.version=a),t}},{test:[/silk/i],describe(e){let t={name:"Amazon Silk"},a=C.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/phantom/i],describe(e){let t={name:"PhantomJS"},a=C.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/slimerjs/i],describe(e){let t={name:"SlimerJS"},a=C.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t={name:"BlackBerry"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/(web|hpw)[o0]s/i],describe(e){let t={name:"WebOS Browser"},a=C.getFirstMatch(Te,e)||C.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/bada/i],describe(e){let t={name:"Bada"},a=C.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/tizen/i],describe(e){let t={name:"Tizen"},a=C.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/qupzilla/i],describe(e){let t={name:"QupZilla"},a=C.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){let t={name:"Firefox"},a=C.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/electron/i],describe(e){let t={name:"Electron"},a=C.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/MiuiBrowser/i],describe(e){let t={name:"Miui"},a=C.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/chromium/i],describe(e){let t={name:"Chromium"},a=C.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/chrome|crios|crmo/i],describe(e){let t={name:"Chrome"},a=C.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/GSA/i],describe(e){let t={name:"Google Search"},a=C.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test(e){let t=!e.test(/like android/i),a=e.test(/android/i);return t&&a},describe(e){let t={name:"Android Browser"},a=C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/playstation 4/i],describe(e){let t={name:"PlayStation 4"},a=C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/safari|applewebkit/i],describe(e){let t={name:"Safari"},a=C.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/.*/i],describe(e){let t=/^(.*)\/(.*) /,a=/^(.*)\/(.*)[ \t]\((.*)/,n=e.search("\\(")!==-1?a:t;return{name:C.getFirstMatch(n,e),version:C.getSecondMatch(n,e)}}}],Af=wf,kf=[{test:[/Roku\/DVP/],describe(e){let t=C.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:it.Roku,version:t}}},{test:[/windows phone/i],describe(e){let t=C.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:it.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){let t=C.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),a=C.getWindowsVersionName(t);return{name:it.Windows,version:t,versionName:a}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){let t={name:it.iOS},a=C.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return a&&(t.version=a),t}},{test:[/macintosh/i],describe(e){let t=C.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),a=C.getMacOSVersionName(t),n={name:it.MacOS,version:t};return a&&(n.versionName=a),n}},{test:[/(ipod|iphone|ipad)/i],describe(e){let t=C.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:it.iOS,version:t}}},{test(e){let t=!e.test(/like android/i),a=e.test(/android/i);return t&&a},describe(e){let t=C.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),a=C.getAndroidVersionName(t),n={name:it.Android,version:t};return a&&(n.versionName=a),n}},{test:[/(web|hpw)[o0]s/i],describe(e){let t=C.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),a={name:it.WebOS};return t&&t.length&&(a.version=t),a}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t=C.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||C.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||C.getFirstMatch(/\bbb(\d+)/i,e);return{name:it.BlackBerry,version:t}}},{test:[/bada/i],describe(e){let t=C.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:it.Bada,version:t}}},{test:[/tizen/i],describe(e){let t=C.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:it.Tizen,version:t}}},{test:[/linux/i],describe(){return{name:it.Linux}}},{test:[/CrOS/],describe(){return{name:it.ChromeOS}}},{test:[/PlayStation 4/],describe(e){let t=C.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:it.PlayStation4,version:t}}}],Ef=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(e){let t=C.getFirstMatch(/(can-l01)/i,e)&&"Nova",a={type:Oe.mobile,vendor:"Huawei"};return t&&(a.model=t),a}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:Oe.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:Oe.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:Oe.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:Oe.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:Oe.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:Oe.tablet}}},{test(e){let t=e.test(/ipod|iphone/i),a=e.test(/like (ipod|iphone)/i);return t&&!a},describe(e){let t=C.getFirstMatch(/(ipod|iphone)/i,e);return{type:Oe.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:Oe.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:Oe.mobile}}},{test(e){return e.getBrowserName(!0)==="blackberry"},describe(){return{type:Oe.mobile,vendor:"BlackBerry"}}},{test(e){return e.getBrowserName(!0)==="bada"},describe(){return{type:Oe.mobile}}},{test(e){return e.getBrowserName()==="windows phone"},describe(){return{type:Oe.mobile,vendor:"Microsoft"}}},{test(e){let t=Number(String(e.getOSVersion()).split(".")[0]);return e.getOSName(!0)==="android"&&t>=3},describe(){return{type:Oe.tablet}}},{test(e){return e.getOSName(!0)==="android"},describe(){return{type:Oe.mobile}}},{test(e){return e.getOSName(!0)==="macos"},describe(){return{type:Oe.desktop,vendor:"Apple"}}},{test(e){return e.getOSName(!0)==="windows"},describe(){return{type:Oe.desktop}}},{test(e){return e.getOSName(!0)==="linux"},describe(){return{type:Oe.desktop}}},{test(e){return e.getOSName(!0)==="playstation 4"},describe(){return{type:Oe.tv}}},{test(e){return e.getOSName(!0)==="roku"},describe(){return{type:Oe.tv}}}],Sf=[{test(e){return e.getBrowserName(!0)==="microsoft edge"},describe(e){if(/\sedg\//i.test(e))return{name:Aa.Blink};let t=C.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:Aa.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){let t={name:Aa.Trident},a=C.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test(e){return e.test(/presto/i)},describe(e){let t={name:Aa.Presto},a=C.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test(e){let t=e.test(/gecko/i),a=e.test(/like gecko/i);return t&&!a},describe(e){let t={name:Aa.Gecko},a=C.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:Aa.Blink}}},{test:[/(apple)?webkit/i],describe(e){let t={name:Aa.WebKit},a=C.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}}],Tf=class{constructor(e,t=!1){if(e==null||e==="")throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},t!==!0&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};let e=C.find(Af,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};let e=C.find(kf,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){let{name:t}=this.getOS();return e?String(t).toLowerCase()||"":t||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){let{type:t}=this.getPlatform();return e?String(t).toLowerCase()||"":t||""}parsePlatform(){this.parsedResult.platform={};let e=C.find(Ef,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};let e=C.find(Sf,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return C.assign({},this.parsedResult)}satisfies(e){let t={},a=0,n={},r=0;if(Object.keys(e).forEach(i=>{let o=e[i];typeof o=="string"?(n[i]=o,r+=1):typeof o=="object"&&(t[i]=o,a+=1)}),a>0){let i=Object.keys(t),o=C.find(i,u=>this.isOS(u));if(o){let u=this.satisfies(t[o]);if(u!==void 0)return u}let s=C.find(i,u=>this.isPlatform(u));if(s){let u=this.satisfies(t[s]);if(u!==void 0)return u}}if(r>0){let i=Object.keys(n),o=C.find(i,s=>this.isBrowser(s,!0));if(o!==void 0)return this.compareVersion(n[o])}}isBrowser(e,t=!1){let a=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),r=C.getBrowserTypeByAlias(n);return t&&r&&(n=r.toLowerCase()),n===a}compareVersion(e){let t=[0],a=e,n=!1,r=this.getBrowserVersion();if(typeof r=="string")return e[0]===">"||e[0]==="<"?(a=e.substr(1),e[1]==="="?(n=!0,a=e.substr(2)):t=[],e[0]===">"?t.push(1):t.push(-1)):e[0]==="="?a=e.substr(1):e[0]==="~"&&(n=!0,a=e.substr(1)),t.indexOf(C.compareVersions(r,a,n))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,t=!1){return this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(t=>this.is(t))}},Yc=Tf,Df=class{static getParser(e,t=!1){if(typeof e!="string")throw new Error("UserAgent should be a string");return new Yc(e,t)}static parse(e){return new Yc(e).getResult()}static get BROWSER_MAP(){return Jc}static get ENGINE_MAP(){return Aa}static get OS_MAP(){return it}static get PLATFORMS_MAP(){return Oe}},ka=Df;var{entries:rd,setPrototypeOf:Zc,isFrozen:Cf,getPrototypeOf:Mf,getOwnPropertyDescriptor:If}=Object,{freeze:ot,seal:St,create:Pf}=Object,{apply:cs,construct:ds}=typeof Reflect<"u"&&Reflect;cs||(cs=function(e,t,a){return e.apply(t,a)});ot||(ot=function(e){return e});St||(St=function(e){return e});ds||(ds=function(e,t){return new e(...t)});var Ff=vt(Array.prototype.forEach),Xc=vt(Array.prototype.pop),or=vt(Array.prototype.push),wi=vt(String.prototype.toLowerCase),is=vt(String.prototype.toString),Bf=vt(String.prototype.match),Et=vt(String.prototype.replace),Rf=vt(String.prototype.indexOf),_f=vt(String.prototype.trim),mt=vt(RegExp.prototype.test),sr=jf(TypeError);function vt(e){return function(t){for(var a=arguments.length,n=new Array(a>1?a-1:0),r=1;r<a;r++)n[r-1]=arguments[r];return cs(e,t,n)}}function jf(e){return function(){for(var t=arguments.length,a=new Array(t),n=0;n<t;n++)a[n]=arguments[n];return ds(e,a)}}function le(e,t,a){var n;a=(n=a)!==null&&n!==void 0?n:wi,Zc&&Zc(e,null);let r=t.length;for(;r--;){let i=t[r];if(typeof i=="string"){let o=a(i);o!==i&&(Cf(t)||(t[r]=o),i=o)}e[i]=!0}return e}function kn(e){let t=Pf(null);for(let[a,n]of rd(e))t[a]=n;return t}function vi(e,t){for(;e!==null;){let n=If(e,t);if(n){if(n.get)return vt(n.get);if(typeof n.value=="function")return vt(n.value)}e=Mf(e)}function a(n){return null}return a}var $c=ot(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),os=ot(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),ss=ot(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Lf=ot(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),us=ot(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Nf=ot(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ed=ot(["#text"]),td=ot(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),ls=ot(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direc/* REMOVED_COMMON_BLOCK_2467 */length","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),ad=ot(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),xi=ot(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Of=St(/\{\{[\w\W]*|[\w\W]*\}\}/gm),zf=St(/<%[\w\W]*|[\w\W]*%>/gm),Uf=St(/\${[\w\W]*}/gm),qf=St(/^data-[\-\w.\u00B7-\uFFFF]/),Gf=St(/^aria-[\-\w]+$/),id=St(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Hf=St(/^(?:\w+script|data):/i),Kf=St(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),od=St(/^html$/i),nd=Object.freeze({__proto__:null,MUSTACHE_EXPR:Of,ERB_EXPR:zf,TMPLIT_EXPR:Uf,DATA_ATTR:qf,ARIA_ATTR:Gf,IS_ALLOWED_URI:id,IS_SCRIPT_OR_DATA:Hf,ATTR_WHITESPACE:Kf,DOCTYPE_NAME:od}),Wf=()=>typeof window>"u"?null:window,Vf=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let a=null,n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(a=t.getAttribute(n));let r="dompurify"+(a?"#"+a:"");try{return e.createPolicy(r,{createHTML(i){return i},createScriptURL(i){return i}})}catch{return null}};function sd(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Wf(),t=b=>sd(b);if(t.version="3.0.3",t.removed=[],!e||!e.document||e.document.nodeType!==9)return t.isSupported=!1,t;let a=e.document,n=a.currentScript,{document:r}=e,{DocumentFragment:i,HTMLTemplateElement:o,Node:s,Element:u,NodeFilter:c,NamedNodeMap:l=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:d,DOMParser:m,trustedTypes:p}=e,v=u.prototype,w=vi(v,"cloneNode"),S=vi(v,"nextSibling"),g=vi(v,"childNodes"),M=vi(v,"parentNode");if(typeof o=="function"){let b=r.createElement("template");b.content&&b.content.ownerDocument&&(r=b.content.ownerDocument)}let f,T="",{implementation:L,createNodeIterator:E,createDocumentFragment:x,getElementsByTagName:k}=r,{importNode:y}=a,j={};t.isSupported=typeof rd=="function"&&typeof M=="function"&&L&&L.createHTMLDocument!==void 0;let{MUSTACHE_EXPR:_,ERB_EXPR:P,TMPLIT_EXPR:N,DATA_ATTR:X,ARIA_ATTR:Y,IS_SCRIPT_OR_DATA:ke,ATTR_WHITESPACE:Me}=nd,{IS_ALLOWED_URI:G}=nd,oe=null,qe=le({},[...$c,...os,...ss,...us,...ed]),ge=null,D=le({},[...td,...ls,...ad,...xi]),F=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),z=null,ae=null,J=!0,he=!0,ye=!1,ee=!0,W=!1,Q=!1,ne=!1,se=!1,q=!1,te=!1,xe=!1,pe=!0,V=!1,Ee="user-content-",je=!0,ie=!1,Ie={},K=null,Qe=le({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Kn=null,Ot=le({},["audio","video","img","source","image","track"]),ja=null,cn=le({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ye="http://www.w3.org/1998/Math/MathML",At="http://www.w3.org/2000/svg",Je="http://www.w3.org/1999/xhtml",ct=Je,Jt=!1,Zt=null,La=le({},[Ye,At,Je],is),at,Xt=["application/xhtml+xml","text/html"],_e="text/html",Ae,dt=null,Dg=r.createElement("form"),al=function(b){return b instanceof RegExp||b instanceof Function},fo=function(b){if(!(dt&&dt===b)){if((!b||typeof b!="object")&&(b={}),b=kn(b),at=Xt.indexOf(b.PARSER_MEDIA_TYPE)===-1?at=_e:at=b.PARSER_MEDIA_TYPE,Ae=at==="application/xhtml+xml"?is:wi,oe="ALLOWED_TAGS"in b?le({},b.ALLOWED_TAGS,Ae):qe,ge="ALLOWED_ATTR"in b?le({},b.ALLOWED_ATTR,Ae):D,Zt="ALLOWED_NAMESPACES"in b?le({},b.ALLOWED_NAMESPACES,is):La,ja="ADD_URI_SAFE_ATTR"in b?le(kn(cn),b.ADD_URI_SAFE_ATTR,Ae):cn,Kn="ADD_DATA_URI_TAGS"in b?le(kn(Ot),b.ADD_DATA_URI_TAGS,Ae):Ot,K="FORBID_CONTENTS"in b?le({},b.FORBID_CONTENTS,Ae):Qe,z="FORBID_TAGS"in b?le({},b.FORBID_TAGS,Ae):{},ae="FORBID_ATTR"in b?le({},b.FORBID_ATTR,Ae):{},Ie="USE_PROFILES"in b?b.USE_PROFILES:!1,J=b.ALLOW_ARIA_ATTR!==!1,he=b.ALLOW_DATA_ATTR!==!1,ye=b.ALLOW_UNKNOWN_PROTOCOLS||!1,ee=b.ALLOW_SELF_CLOSE_IN_ATTR!==!1,W=b.SAFE_FOR_TEMPLATES||!1,Q=b.WHOLE_DOCUMENT||!1,q=b.RETURN_DOM||!1,te=b.RETURN_DOM_FRAGMENT||!1,xe=b.RETURN_TRUSTED_TYPE||!1,se=b.FORCE_BODY||!1,pe=b.SANITIZE_DOM!==!1,V=b.SANITIZE_NAMED_PROPS||!1,je=b.KEEP_CONTENT!==!1,ie=b.IN_PLACE||!1,G=b.ALLOWED_URI_REGEXP||id,ct=b.NAMESPACE||Je,F=b.CUSTOM_ELEMENT_HANDLING||{},b.CUSTOM_ELEMENT_HANDLING&&al(b.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(F.tagNameCheck=b.CUSTOM_ELEMENT_HANDLING.tagNameCheck),b.CUSTOM_ELEMENT_HANDLING&&al(b.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(F.attributeNameCheck=b.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),b.CUSTOM_ELEMENT_HANDLING&&typeof b.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(F.allowCustomizedBuiltInElements=b.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),W&&(he=!1),te&&(q=!0),Ie&&(oe=le({},[...ed]),ge=[],Ie.html===!0&&(le(oe,$c),le(ge,td)),Ie.svg===!0&&(le(oe,os),le(ge,ls),le(ge,xi)),Ie.svgFilters===!0&&(le(oe,ss),le(ge,ls),le(ge,xi)),Ie.mathMl===!0&&(le(oe,us),le(ge,ad),le(ge,xi))),b.ADD_TAGS&&(oe===qe&&(oe=kn(oe)),le(oe,b.ADD_TAGS,Ae)),b.ADD_ATTR&&(ge===D&&(ge=kn(ge)),le(ge,b.ADD_ATTR,Ae)),b.ADD_URI_SAFE_ATTR&&le(ja,b.ADD_URI_SAFE_ATTR,Ae),b.FORBID_CONTENTS&&(K===Qe&&(K=kn(K)),le(K,b.FORBID_CONTENTS,Ae)),je&&(oe["#text"]=!0),Q&&le(oe,["html","head","body"]),oe.table&&(le(oe,["tbody"]),delete z.tbody),b.TRUSTED_TYPES_POLICY){if(typeof b.TRUSTED_TYPES_POLICY.createHTML!="function")throw sr('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof b.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw sr('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');f=b.TRUSTED_TYPES_POLICY,T=f.createHTML("")}else f===void 0&&(f=Vf(p,n)),f!==null&&typeof T=="string"&&(T=f.createHTML(""));ot&&ot(b),dt=b}},nl=le({},["mi","mo","mn","ms","mtext"]),rl=le({},["foreignobject","desc","title","annotation-xml"]),Cg=le({},["title","style","font","a","script"]),qr=le({},os);le(qr,ss),le(qr,Lf);let bo=le({},us);le(bo,Nf);let Mg=function(b){let U=M(b);(!U||!U.tagName)&&(U={namespaceURI:ct,tagName:"template"});let R=wi(b.tagName),ve=wi(U.tagName);return Zt[b.namespaceURI]?b.namespaceURI===At?U.namespaceURI===Je?R==="svg":U.namespaceURI===Ye?R==="svg"&&(ve==="annotation-xml"||nl[ve]):!!qr[R]:b.namespaceURI===Ye?U.namespaceURI===Je?R==="math":U.namespaceURI===At?R==="math"&&rl[ve]:!!bo[R]:b.namespaceURI===Je?U.namespaceURI===At&&!rl[ve]||U.namespaceURI===Ye&&!nl[ve]?!1:!bo[R]&&(Cg[R]||!qr[R]):!!(at==="application/xhtml+xml"&&Zt[b.namespaceURI]):!1},dn=function(b){or(t.removed,{element:b});try{b.parentNode.removeChild(b)}catch{b.remove()}},yo=function(b,U){try{or(t.removed,{attribute:U.getAttributeNode(b),from:U})}catch{or(t.removed,{attribute:null,from:U})}if(U.removeAttribute(b),b==="is"&&!ge[b])if(q||te)try{dn(U)}catch{}else try{U.setAttribute(b,"")}catch{}},il=function(b){let U,R;if(se)b="<remove></remove>"+b;else{let nt=Bf(b,/^[\r\n\t ]+/);R=nt&&nt[0]}at==="application/xhtml+xml"&&ct===Je&&(b='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+b+"</body></html>");let ve=f?f.createHTML(b):b;if(ct===Je)try{U=new m().parseFromString(ve,at)}catch{}if(!U||!U.documentElement){U=L.createDocument(ct,"template",null);try{U.documentElement.innerHTML=Jt?T:ve}catch{}}let Ge=U.body||U.documentElement;return b&&R&&Ge.insertBefore(r.createTextNode(R),Ge.childNodes[0]||null),ct===Je?k.call(U,Q?"html":"body")[0]:Q?U.documentElement:Ge},ol=function(b){return E.call(b.ownerDocument||b,b,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Ig=function(b){return b instanceof d&&(typeof b.nodeName!="string"||typeof b.textContent!="string"||typeof b.removeChild!="function"||!(b.attributes instanceof l)||typeof b.removeAttribute!="function"||typeof b.setAttribute!="function"||typeof b.namespaceURI!="string"||typeof b.insertBefore!="function"||typeof b.hasChildNodes!="function")},Gr=function(b){return typeof s=="object"?b instanceof s:b&&typeof b=="object"&&typeof b.nodeType=="number"&&typeof b.nodeName=="string"},$t=function(b,U,R){j[b]&&Ff(j[b],ve=>{ve.call(t,U,R,dt)})},sl=function(b){let U;if($t("beforeSanitizeElements",b,null),Ig(b))return dn(b),!0;let R=Ae(b.nodeName);if($t("uponSanitizeElement",b,{tagName:R,allowedTags:oe}),b.hasChildNodes()&&!Gr(b.firstElementChild)&&(!Gr(b.content)||!Gr(b.content.firstElementChild))&&mt(/<[/\w]/g,b.innerHTML)&&mt(/<[/\w]/g,b.textContent))return dn(b),!0;if(!oe[R]||z[R]){if(!z[R]&&ll(R)&&(F.tagNameCheck instanceof RegExp&&mt(F.tagNameCheck,R)||F.tagNameCheck instanceof Function&&F.tagNameCheck(R)))return!1;if(je&&!K[R]){let ve=M(b)||b.parentNode,Ge=g(b)||b.childNodes;if(Ge&&ve){let nt=Ge.length;for(let ut=nt-1;ut>=0;--ut)ve.insertBefore(w(Ge[ut],!0),S(b))}}return dn(b),!0}return b instanceof u&&!Mg(b)||(R==="noscript"||R==="noembed")&&mt(/<\/no(script|embed)/i,b.innerHTML)?(dn(b),!0):(W&&b.nodeType===3&&(U=b.textContent,U=Et(U,_," "),U=Et(U,P," "),U=Et(U,N," "),b.textContent!==U&&(or(t.removed,{element:b.cloneNode()}),b.textContent=U)),$t("afterSanitizeElements",b,null),!1)},ul=function(b,U,R){if(pe&&(U==="id"||U==="name")&&(R in r||R in Dg))return!1;if(!(he&&!ae[U]&&mt(X,U))&&!(J&&mt(Y,U))){if(!ge[U]||ae[U]){if(!(ll(b)&&(F.tagNameCheck instanceof RegExp&&mt(F.tagNameCheck,b)||F.tagNameCheck instanceof Function&&F.tagNameCheck(b))&&(F.attributeNameCheck instanceof RegExp&&mt(F.attributeNameCheck,U)||F.attributeNameCheck instanceof Function&&F.attributeNameCheck(U))||U==="is"&&F.allowCustomizedBuiltInElements&&(F.tagNameCheck instanceof RegExp&&mt(F.tagNameCheck,R)||F.tagNameCheck instanceof Function&&F.tagNameCheck(R))))return!1}else if(!ja[U]&&!mt(G,Et(R,Me,""))&&!((U==="src"||U==="xlink:href"||U==="href")&&b!=="script"&&Rf(R,"data:")===0&&Kn[b])&&!(ye&&!mt(ke,Et(R,Me,"")))&&R)return!1}return!0},ll=function(b){return b.indexOf("-")>0},cl=function(b){let U,R,ve,Ge;$t("beforeSanitizeAttributes",b,null);let{attributes:nt}=b;if(!nt)return;let ut={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ge};for(Ge=nt.length;Ge--;){U=nt[Ge];let{name:He,namespaceURI:mn}=U;if(R=He==="value"?U.value:_f(U.value),ve=Ae(He),ut.attrName=ve,ut.attrValue=R,ut.keepAttr=!0,ut.forceKeepAttr=void 0,$t("uponSanitizeAttribute",b,ut),R=ut.attrValue,ut.forceKeepAttr||(yo(He,b),!ut.keepAttr))continue;if(!ee&&mt(/\/>/i,R)){yo(He,b);continue}W&&(R=Et(R,_," "),R=Et(R,P," "),R=Et(R,N," "));let dl=Ae(b.nodeName);if(ul(dl,ve,R)){if(V&&(ve==="id"||ve==="name")&&(yo(He,b),R=Ee+R),f&&typeof p=="object"&&typeof p.getAttributeType=="function"&&!mn)switch(p.getAttributeType(dl,ve)){case"TrustedHTML":{R=f.createHTML(R);break}case"TrustedScriptURL":{R=f.createScriptURL(R);break}}try{mn?b.setAttributeNS(mn,He,R):b.setAttribute(He,R),Xc(t.removed)}catch{}}}$t("afterSanitizeAttributes",b,null)},Pg=function b(U){let R,ve=ol(U);for($t("beforeSanitizeShadowDOM",U,null);R=ve.nextNode();)$t("uponSanitizeShadowNode",R,null),!sl(R)&&(R.content instanceof i&&b(R.content),cl(R));$t("afterSanitizeShadowDOM",U,null)};return t.sanitize=function(b){let U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},R,ve,Ge,nt;if(Jt=!b,Jt&&(b="<!-->"),typeof b!="string"&&!Gr(b))if(typeof b.toString=="function"){if(b=b.toString(),typeof b!="string")throw sr("dirty is not a string, aborting")}else throw sr("toString is not a function");if(!t.isSupported)return b;if(ne||fo(U),t.removed=[],typeof b=="string"&&(ie=!1),ie){if(b.nodeName){let mn=Ae(b.nodeName);if(!oe[mn]||z[mn])throw sr("root node is forbidden and cannot be sanitized in-place")}}else if(b instanceof s)R=il("<!---->"),ve=R.ownerDocument.importNode(b,!0),ve.nodeType===1&&ve.nodeName==="BODY"||ve.nodeName==="HTML"?R=ve:R.appendChild(ve);else{if(!q&&!W&&!Q&&b.indexOf("<")===-1)return f&&xe?f.createHTML(b):b;if(R=il(b),!R)return q?null:xe?T:""}R&&se&&dn(R.firstChild);let ut=ol(ie?b:R);for(;Ge=ut.nextNode();)sl(Ge)||(Ge.content instanceof i&&Pg(Ge.content),cl(Ge));if(ie)return b;if(q){if(te)for(nt=x.call(R.ownerDocument);R.firstChild;)nt.appendChild(R.firstChild);else nt=R;return(ge.shadowroot||ge.shadowrootmod)&&(nt=y.call(a,nt,!0)),nt}let He=Q?R.outerHTML:R.innerHTML;return Q&&oe["!doctype"]&&R.ownerDocument&&R.ownerDocument.doctype&&R.ownerDocument.doctype.name&&mt(od,R.ownerDocument.doctype.name)&&(He="<!DOCTYPE "+R.ownerDocument.doctype.name+`>
`+He),W&&(He=Et(He,_," "),He=Et(He,P," "),He=Et(He,N," ")),f&&xe?f.createHTML(He):He},t.setConfig=function(b){fo(b),ne=!0},t.clearConfig=function(){dt=null,ne=!1},t.isValidAttribute=function(b,U,R){dt||fo({});let ve=Ae(b),Ge=Ae(U);return ul(ve,Ge,R)},t.addHook=function(b,U){typeof U=="function"&&(j[b]=j[b]||[],or(j[b],U))},t.removeHook=function(b){if(j[b])return Xc(j[b])},t.removeHooks=function(b){j[b]&&(j[b]=[])},t.removeAllHooks=function(){j={}},t}var ms=sd();var Qf=Object.create,gs=Object.defineProperty,Yf=Object.getOwnPropertyDescriptor,Jf=Object.getOwnPropertyNames,Zf=Object.getPrototypeOf,Xf=Object.prototype.hasOwnProperty,hs=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),$f=(e,t)=>{for(var a in t)gs(e,a,{get:t[a],enumerable:!0})},ps=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Jf(t))!Xf.call(e,r)&&r!==a&&gs(e,r,{get:()=>t[r],enumerable:!(n=Yf(t,r))||n.enumerable});return e},e4=(e,t,a)=>(ps(e,t,"default"),a&&ps(a,t,"default")),ld=(e,t,a)=>(a=e!=null?Qf(Zf(e)):{},ps(t||!e||!e.__esModule?gs(a,"default",{value:e,enumerable:!0}):a,e)),t4=hs((e,t)=>{function a(n,r){if(r&&r.documentElement)n=r,r=arguments[2];else if(!n||!n.documentElement)throw new Error("First argument to Readability constructor should be a document object.");if(r=r||{},this._doc=n,this._docJSDOMParser=this._doc.firstChild.__JSDOMParser__,this._articleTitle=null,this._articleByline=null,this._articleDir=null,this._articleSiteName=null,this._attempts=[],this._debug=!!r.debug,this._maxElemsToParse=r.maxElemsToParse||this.DEFAULT_MAX_ELEMS_TO_PARSE,this._nbTopCandidates=r.nbTopCandidates||this.DEFAULT_N_TOP_CANDIDATES,this._charThreshold=r.charThreshold||this.DEFAULT_CHAR_THRESHOLD,this._classesToPreserve=this.CLASSES_TO_PRESERVE.concat(r.classesToPreserve||[]),this._keepClasses=!!r.keepClasses,this._serializer=r.serializer||function(i){return i.innerHTML},this._disableJSONLD=!!r.disableJSONLD,this._allowedVideoRegex=r.allowedVideoRegex||this.REGEXPS.videos,this._flags=this.FLAG_STRIP_UNLIKELYS|this.FLAG_WEIGHT_CLASSES|this.FLAG_CLEAN_CONDITIONALLY,this._debug){let i=function(o){if(o.nodeType==o.TEXT_NODE)return`${o.nodeName} ("${o.textContent}")`;let s=Array.from(o.attributes||[],function(u){return`${u.name}="${u.value}"`}).join(" ");return`<${o.localName} ${s}>`};this.log=function(){if(typeof console<"u")Array.from(arguments,u=>u&&u.nodeType==this.ELEMENT_NODE?i(u):u).unshift("Reader: (Readability)");else if(typeof dump<"u"){var o=Array.prototype.map.call(arguments,function(s){return s&&s.nodeName?i(s):s}).join(" ");dump("Reader: (Readability) "+o+`
`)}}}else this.log=function(){}}a.prototype={FLAG_STRIP_UNLIKELYS:1,FLAG_WEIGHT_CLASSES:2,FLAG_CLEAN_CONDITIONALLY:4,ELEMENT_NODE:1,TEXT_NODE:3,DEFAULT_MAX_ELEMS_TO_PARSE:0,DEFAULT_N_TOP_CANDIDATES:5,DEFAULT_TAGS_TO_SCORE:"section,h2,h3,h4,h5,h6,p,td,pre".toUpperCase().split(","),DEFAULT_CHAR_T/* REMOVED_COMMON_BLOCK_2459 */pacing","frame","hspace","rules","style","valign","vspace"],DEPRECATED_SIZE_ATTRIBUTE_ELEMS:["TABLE","TH","TD","HR","PRE"],PHRASING_ELEMS:["ABBR","AUDIO","B","BDO","BR","BUTTON","CITE","CODE","DATA","DATALIST","DFN","EM","EMBED","I","IMG","INPUT","KBD","LABEL","MARK","MATH","METER","NOSCRIPT","OBJECT","OUTPUT","PROGRESS","Q","RUBY","SAMP","SCRIPT","SELECT","SMALL","SPAN","STRONG","SUB","SUP","TEXTAREA","TIME","VAR","WBR"],CLASSES_TO_PRESERVE:["page"],HTML_ESCAPE_MAP:{lt:"<",gt:">",amp:"&",quot:'"',apos:"'"},_postProcessContent:function(n){this._fixRelativeUris(n),this._simplifyNestedElements(n),this._keepClasses||this._cleanClasses(n)},_removeNodes:function(n,r){if(this._docJSDOMParser&&n._isLiveNodeList)throw new Error("Do not pass live node lists to _removeNodes");for(var i=n.length-1;i>=0;i--){var o=n[i],s=o.parentNode;s&&(!r||r.call(this,o,i,n))&&s.removeChild(o)}},_replaceNodeTags:function(n,r){if(this._docJSDOMParser&&n._isLiveNodeList)throw new Error("Do not pass live node lists to _replaceNodeTags");for(let i of n)this._setNodeTag(i,r)},_forEachNode:function(n,r){Array.prototype.forEach.call(n,r,this)},_findNode:function(n,r){return Array.prototype.find.call(n,r,this)},_someNode:function(n,r){return Array.prototype.some.call(n,r,this)},_everyNode:function(n,r){return Array.prototype.every.call(n,r,this)},_concatNodeLists:function(){var n=Array.prototype.slice,r=n.call(arguments),i=r.map(function(o){return n.call(o)});return Array.prototype.concat.apply([],i)},_getAllNodesWithTag:function(n,r){return n.querySelectorAll?n.querySelectorAll(r.join(",")):[].concat.apply([],r.map(function(i){var o=n.getElementsByTagName(i);return Array.isArray(o)?o:Array.from(o)}))},_cleanClasses:function(n){var r=this._classesToPreserve,i=(n.getAttribute("class")||"").split(/\s+/).filter(function(o){return r.indexOf(o)!=-1}).join(" ");for(i?n.setAttribute("class",i):n.removeAttribute("class"),n=n.firstElementChild;n;n=n.nextElementSibling)this._cleanClasses(n)},_fixRelativeUris:function(n){var r=this._doc.baseURI,i=this._doc.documentURI;function o(c){if(r==i&&c.charAt(0)=="#")return c;try{return new URL(c,r).href}catch{}return c}var s=this._getAllNodesWithTag(n,["a"]);this._forEachNode(s,function(c){var l=c.getAttribute("href");if(l)if(l.indexOf("javascript:")===0)if(c.childNodes.length===1&&c.childNodes[0].nodeType===this.TEXT_NODE){var d=this._doc.createTextNode(c.textContent);c.parentNode.replaceChild(d,c)}else{for(var m=this._doc.createElement("span");c.firstChild;)m.appendChild(c.firstChild);c.parentNode.replaceChild(m,c)}else c.setAttribute("href",o(l))});var u=this._getAllNodesWithTag(n,["img","picture","figure","video","audio","source"]);this._forEachNode(u,function(c){var l=c.getAttribute("src"),d=c.getAttribute("poster"),m=c.getAttribute("srcset");if(l&&c.setAttribute("src",o(l)),d&&c.setAttribute("poster",o(d)),m){var p=m.replace(this.REGEXPS.srcsetUrl,function(v,w,S,g){return o(w)+(S||"")+g});c.setAttribute("srcset",p)}})},_simplifyNestedElements:function(n){for(var r=n;r;){if(r.parentNode&&["DIV","SECTION"].includes(r.tagName)&&!(r.id&&r.id.startsWith("readability"))){if(this._isElementWithoutContent(r)){r=this._removeAndGetNext(r);continue}else if(this._hasSingleTagInsideElement(r,"DIV")||this._hasSingleTagInsideElement(r,"SECTION")){for(var i=r.children[0],o=0;o<r.attributes.length;o++)i.setAttribute(r.attributes[o].name,r.attributes[o].value);r.parentNode.replaceChild(i,r),r=i;continue}}r=this._getNextNode(r)}},_getArticleTitle:function(){var n=this._doc,r="",i="";try{r=i=n.title.trim(),typeof r!="string"&&(r=i=this._getInnerText(n.getElementsByTagName("title")[0]))}catch{}var o=!1;function s(p){return p.split(/\s+/).length}if(/ [\|\-\\\/>»] /.test(r))o=/ [\\\/>»] /.test(r),r=i.replace(/(.*)[\|\-\\\/>»] .*/gi,"$1"),s(r)<3&&(r=i.replace(/[^\|\-\\\/>»]*[\|\-\\\/>»](.*)/gi,"$1"));else if(r.indexOf(": ")!==-1){var u=this._concatNodeLists(n.getElementsByTagName("h1"),n.getElementsByTagName("h2")),c=r.trim(),l=this._someNode(u,function(p){return p.textContent.trim()===c});l||(r=i.substring(i.lastIndexOf(":")+1),s(r)<3?r=i.substring(i.indexOf(":")+1):s(i.substr(0,i.indexOf(":")))>5&&(r=i))}else if(r.length>150||r.length<15){var d=n.getElementsByTagName("h1");d.length===1&&(r=this._getInnerText(d[0]))}r=r.trim().replace(this.REGEXPS.normalize," ");var m=s(r);return m<=4&&(!o||m!=s(i.replace(/[\|\-\\\/>»]+/g,""))-1)&&(r=i),r},_prepDocument:function(){var n=this._doc;this._removeNodes(this._getAllNodesWithTag(n,["style"])),n.body&&this._replaceBrs(n.body),this._replaceNodeTags(this._getAllNodesWithTag(n,["font"]),"SPAN")},_nextNode:function(n){for(var r=n;r&&r.nodeType!=this.ELEMENT_NODE&&this.REGEXPS.whitespace.test(r.textContent);)r=r.nextSibling;return r},_replaceBrs:function(n){this._forEachNode(this._getAllNodesWithTag(n,["br"]),function(r){for(var i=r.nextSibling,o=!1;(i=this._nextNode(i))&&i.tagName=="BR";){o=!0;var s=i.nextSibling;i.parentNode.removeChild(i),i=s}if(o){var u=this._doc.createElement("p");for(r.parentNode.replaceChild(u,r),i=u.nextSibling;i;){if(i.tagName=="BR"){var c=this._nextNode(i.nextSibling);if(c&&c.tagName=="BR")break}if(!this._isPhrasingContent(i))break;var l=i.nextSibling;u.appendChild(i),i=l}for(;u.lastChild&&this._isWhitespace(u.lastChild);)u.removeChild(u.lastChild);u.parentNode.tagName==="P"&&this._setNodeTag(u.parentNode,"DIV")}})},_setNodeTag:function(n,r){if(this.log("_setNodeTag",n,r),this._docJSDOMParser)return n.localName=r.toLowerCase(),n.tagName=r.toUpperCase(),n;for(var i=n.ownerDocument.createElement(r);n.firstChild;)i.appendChild(n.firstChild);n.parentNode.replaceChild(i,n),n.readability&&(i.readability=n.readability);for(var o=0;o<n.attributes.length;o++)try{i.setAttribute(n.attributes[o].name,n.attributes[o].value)}catch{}return i},_prepArticle:function(n){this._cleanStyles(n),this._markDataTables(n),this._fixLazyImages(n),this._cleanConditionally(n,"form"),this._cleanConditionally(n,"fieldset"),this._clean(n,"object"),this._clean(n,"embed"),this._clean(n,"footer"),this._clean(n,"link"),this._clean(n,"aside");var r=this.DEFAULT_CHAR_THRESHOLD;this._forEachNode(n.children,function(i){this._cleanMatchedNodes(i,function(o,s){return this.REGEXPS.shareElements.test(s)&&o.textContent.length<r})}),this._clean(n,"iframe"),this._clean(n,"input"),this._clean(n,"textarea"),this._clean(n,"select"),this._clean(n,"button"),this._cleanHeaders(n),this._cleanConditionally(n,"table"),this._cleanConditionally(n,"ul"),this._cleanConditionally(n,"div"),this._replaceNodeTags(this._getAllNodesWithTag(n,["h1"]),"h2"),this._removeNodes(this._getAllNodesWithTag(n,["p"]),function(i){var o=i.getElementsByTagName("img").length,s=i.getElementsByTagName("embed").length,u=i.getElementsByTagName("object").length,c=i.getElementsByTagName("iframe").length,l=o+s+u+c;return l===0&&!this._getInnerText(i,!1)}),this._forEachNode(this._getAllNodesWithTag(n,["br"]),function(i){var o=this._nextNode(i.nextSibling);o&&o.tagName=="P"&&i.parentNode.removeChild(i)}),this._forEachNode(this._getAllNodesWithTag(n,["table"]),function(i){var o=this._hasSingleTagInsideElement(i,"TBODY")?i.firstElementChild:i;if(this._hasSingleTagInsideElement(o,"TR")){var s=o.firstElementChild;if(this._hasSingleTagInsideElement(s,"TD")){var u=s.firstElementChild;u=this._setNodeTag(u,this._everyNode(u.childNodes,this._isPhrasingContent)?"P":"DIV"),i.parentNode.replaceChild(u,i)}}})},_initializeNode:function(n){switch(n.readability={contentScore:0},n.tagName){case"DIV":n.readability.contentScore+=5;break;case"PRE":case"TD":case"BLOCKQUOTE":n.readability.contentScore+=3;break;case"ADDRESS":case"OL":case"UL":case"DL":case"DD":case"DT":case"LI":case"FORM":n.readability.contentScore-=3;break;case"H1":case"H2":case"H3":case"H4":case"H5":case"H6":case"TH":n.readability.contentScore-=5;break}n.readability.contentScore+=this._getClassWeight(n)},_removeAndGetNext:function(n){var r=this._getNextNode(n,!0);return n.parentNode.removeChild(n),r},_getNextNode:function(n,r){if(!r&&n.firstElementChild)return n.firstElementChild;if(n.nextElementSibling)return n.nextElementSibling;do n=n.parentNode;while(n&&!n.nextElementSibling);return n&&n.nextElementSibling},_textSimilarity:function(n,r){var i=n.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean),o=r.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean);if(!i.length||!o.length)return 0;var s=o.filter(c=>!i.includes(c)),u=s.join(" ").length/o.join(" ").length;return 1-u},_checkByline:function(n,r){if(this._articleByline)return!1;if(n.getAttribute!==void 0)var i=n.getAttribute("rel"),o=n.getAttribute("itemprop");return(i==="author"||o&&o.indexOf("author")!==-1||this.REGEXPS.byline.test(r))&&this._isValidByline(n.textContent)?(this._articleByline=n.textContent.trim(),!0):!1},_getNodeAncestors:function(n,r){r=r||0;for(var i=0,o=[];n.parentNode&&(o.push(n.parentNode),!(r&&++i===r));)n=n.parentNode;return o},_grabArticle:function(n){this.log("**** grabArticle ****");var r=this._doc,i=n!==null;if(n=n||this._doc.body,!n)return this.log("No body found in document. Abort."),null;for(var o=n.innerHTML;;){this.log("Starting grabArticle loop");var s=this._flagIsActive(this.FLAG_STRIP_UNLIKELYS),u=[],c=this._doc.documentElement;let se=!0;for(;c;){c.tagName==="HTML"&&(this._articleLang=c.getAttribute("lang"));var l=c.className+" "+c.id;if(!this._isProbablyVisible(c)){this.log("Removing hidden node - "+l),c=this._removeAndGetNext(c);continue}if(c.getAttribute("aria-modal")=="true"&&c.getAttribute("role")=="dialog"){c=this._removeAndGetNext(c);continue}if(this._checkByline(c,l)){c=this._removeAndGetNext(c);continue}if(se&&this._headerDuplicatesTitle(c)){this.log("Removing header: ",c.textContent.trim(),this._articleTitle.trim()),se=!1,c=this._removeAndGetNext(c);continue}if(s){if(this.REGEXPS.unlikelyCandidates.test(l)&&!this.REGEXPS.okMaybeItsACandidate.test(l)&&!this._hasAncestorTag(c,"table")&&!this._hasAncestorTag(c,"code")&&c.tagName!=="BODY"&&c.tagName!=="A"){this.log("Removing unlikely candidate - "+l),c=this._removeAndGetNext(c);continue}if(this.UNLIKELY_ROLES.includes(c.getAttribute("role"))){this.log("Removing content with role "+c.getAttribute("role")+" - "+l),c=this._removeAndGetNext(c);continue}}if((c.tagName==="DIV"||c.tagName==="SECTION"||c.tagName==="HEADER"||c.tagName==="H1"||c.tagName==="H2"||c.tagName==="H3"||c.tagName==="H4"||c.tagName==="H5"||c.tagName==="H6")&&this._isElementWithoutContent(c)){c=this._removeAndGetNext(c);continue}if(this.DEFAULT_TAGS_TO_SCORE.indexOf(c.tagName)!==-1&&u.push(c),c.tagName==="DIV"){for(var d=null,m=c.firstChild;m;){var p=m.nextSibling;if(this._isPhrasingContent(m))d!==null?d.appendChild(m):this._isWhitespace(m)||(d=r.createElement("p"),c.replaceChild(d,m),d.appendChild(m));else if(d!==null){for(;d.lastChild&&this._isWhitespace(d.lastChild);)d.removeChild(d.lastChild);d=null}m=p}if(this._hasSingleTagInsideElement(c,"P")&&this._getLinkDensity(c)<.25){var v=c.children[0];c.parentNode.replaceChild(v,c),c=v,u.push(c)}else this._hasChildBlockElement(c)||(c=this._setNodeTag(c,"P"),u.push(c))}c=this._getNextNode(c)}var w=[];this._forEachNode(u,function(q){if(!(!q.parentNode||typeof q.parentNode.tagName>"u")){var te=this._getInnerText(q);if(!(te.length<25)){var xe=this._getNodeAncestors(q,5);if(xe.length!==0){var pe=0;pe+=1,pe+=te.split(",").length,pe+=Math.min(Math.floor(te.length/100),3),this._forEachNode(xe,function(V,Ee){if(!(!V.tagName||!V.parentNode||typeof V.parentNode.tagName>"u")){if(typeof V.readability>"u"&&(this._initializeNode(V),w.push(V)),Ee===0)var je=1;else Ee===1?je=2:je=Ee*3;V.readability.contentScore+=pe/je}})}}}});for(var S=[],g=0,M=w.length;g<M;g+=1){var f=w[g],T=f.readability.contentScore*(1-this._getLinkDensity(f));f.readability.contentScore=T,this.log("Candidate:",f,"with score "+T);for(var L=0;L<this._nbTopCandidates;L++){var E=S[L];if(!E||T>E.readability.contentScore){S.splice(L,0,f),S.length>this._nbTopCandidates&&S.pop();break}}}var x=S[0]||null,k=!1,y;if(x===null||x.tagName==="BODY"){for(x=r.createElement("DIV"),k=!0;n.firstChild;)this.log("Moving child out:",n.firstChild),x.appendChild(n.firstChild);n.appendChild(x),this._initializeNode(x)}else if(x){for(var j=[],_=1;_<S.length;_++)S[_].readability.contentScore/x.readability.contentScore>=.75&&j.push(this._getNodeAncestors(S[_]));var P=3;if(j.length>=P)for(y=x.parentNode;y.tagName!=="BODY";){for(var N=0,X=0;X<j.length&&N<P;X++)N+=Number(j[X].includes(y));if(N>=P){x=y;break}y=y.parentNode}x.readability||this._initializeNode(x),y=x.parentNode;for(var Y=x.readability.contentScore,ke=Y/3;y.tagName!=="BODY";){if(!y.readability){y=y.parentNode;continue}var Me=y.readability.contentScore;if(Me<ke)break;if(Me>Y){x=y;break}Y=y.readability.contentScore,y=y.parentNode}for(y=x.parentNode;y.tagName!="BODY"&&y.children.length==1;)x=y,y=x.parentNode;x.readability||this._initializeNode(x)}var G=r.createElement("DIV");i&&(G.id="readability-content");var oe=Math.max(10,x.readability.contentScore*.2);y=x.parentNode;for(var qe=y.children,ge=0,D=qe.length;ge<D;ge++){var F=qe[ge],z=!1;if(this.log("Looking at sibling node:",F,F.readability?"with score "+F.readability.contentScore:""),this.log("Sibling has score",F.readability?F.readability.contentScore:"Unknown"),F===x)z=!0;else{var ae=0;if(F.className===x.className&&x.className!==""&&(ae+=x.readability.contentScore*.2),F.readability&&F.readability.contentScore+ae>=oe)z=!0;else if(F.nodeName==="P"){var J=this._getLinkDensity(F),he=this._getInnerText(F),ye=he.length;(ye>80&&J<.25||ye<80&&ye>0&&J===0&&he.search(/\.( |$)/)!==-1)&&(z=!0)}}z&&(this.log("Appending node:",F),this.ALTER_TO_DIV_EXCEPTIONS.indexOf(F.nodeName)===-1&&(this.log("Altering sibling:",F,"to div."),F=this._setNodeTag(F,"DIV")),G.appendChild(F),qe=y.children,ge-=1,D-=1)}if(this._debug&&this.log("Article content pre-prep: "+G.innerHTML),this._prepArticle(G),this._debug&&this.log("Article content post-prep: "+G.innerHTML),k)x.id="readability-page-1",x.className="page";else{var ee=r.createElement("DIV");for(ee.id="readability-page-1",ee.className="page";G.firstChild;)ee.appendChild(G.firstChild);G.appendChild(ee)}this._debug&&this.log("Article content after paging: "+G.innerHTML);var W=!0,Q=this._getInnerText(G,!0).length;if(Q<this._charThreshold)if(W=!1,n.innerHTML=o,this._flagIsActive(this.FLAG_STRIP_UNLIKELYS))this._removeFlag(this.FLAG_STRIP_UNLIKELYS),this._attempts.push({articleContent:G,textLength:Q});else if(this._flagIsActive(this.FLAG_WEIGHT_CLASSES))this._removeFlag(this.FLAG_WEIGHT_CLASSES),this._attempts.push({articleContent:G,textLength:Q});else if(this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY))this._removeFlag(this.FLAG_CLEAN_CONDITIONALLY),this._attempts.push({articleContent:G,textLength:Q});else{if(this._attempts.push({articleContent:G,textLength:Q}),this._attempts.sort(function(q,te){return te.textLength-q.textLength}),!this._attempts[0].textLength)return null;G=this._attempts[0].articleContent,W=!0}if(W){var ne=[y,x].concat(this._getNodeAncestors(y));return this._someNode(ne,function(q){if(!q.tagName)return!1;var te=q.getAttribute("dir");return te?(this._articleDir=te,!0):!1}),G}}},_isValidByline:function(n){return typeof n=="string"||n instanceof String?(n=n.trim(),n.length>0&&n.length<100):!1},_unescapeHtmlEntities:function(n){if(!n)return n;var r=this.HTML_ESCAPE_MAP;return n.replace(/&(quot|amp|apos|lt|gt);/g,function(i,o){return r[o]}).replace(/&#(?:x([0-9a-z]{1,4})|([0-9]{1,4}));/gi,function(i,o,s){var u=parseInt(o||s,o?16:10);return String.fromCharCode(u)})},_getJSONLD:function(n){var r=this._getAllNodesWithTag(n,["script"]),i;return this._forEachNode(r,function(o){if(!i&&o.getAttribute("type")==="application/ld+json")try{var s=o.textContent.replace(/^\s*<!\[CDATA\[|\]\]>\s*$/g,""),u=JSON.parse(s);if(!u["@context"]||!u["@context"].match(/^https?\:\/\/schema\.org$/)||(!u["@type"]&&Array.isArray(u["@graph"])&&(u=u["@graph"].find(function(m){return(m["@type"]||"").match(this.REGEXPS.jsonLdArticleTypes)})),!u||!u["@type"]||!u["@type"].match(this.REGEXPS.jsonLdArticleTypes)))return;if(i={},typeof u.name=="string"&&typeof u.headline=="string"&&u.name!==u.headline){var c=this._getArticleTitle(),l=this._textSimilarity(u.name,c)>.75,d=this._textSimilarity(u.headline,c)>.75;d&&!l?i.title=u.headline:i.title=u.name}else typeof u.name=="string"?i.title=u.name.trim():typeof u.headline=="string"&&(i.title=u.headline.trim());u.author&&(typeof u.author.name=="string"?i.byline=u.author.name.trim():Array.isArray(u.author)&&u.author[0]&&typeof u.author[0].name=="string"&&(i.byline=u.author.filter(function(m){return m&&typeof m.name=="string"}).map(function(m){return m.name.trim()}).join(", "))),typeof u.description=="string"&&(i.excerpt=u.description.trim()),u.publisher&&typeof u.publisher.name=="string"&&(i.siteName=u.publisher.name.trim());return}catch(m){this.log(m.message)}}),i||{}},_getArticleMetadata:function(n){var r={},i={},o=this._doc.getElementsByTagName("meta"),s=/\s*(dc|dcterm|og|twitter)\s*:\s*(author|creator|description|title|site_name)\s*/gi,u=/^\s*(?:(dc|dcterm|og|twitter|weibo:(article|webpage))\s*[\.:]\s*)?(author|creator|description|title|site_name)\s*$/i;return this._forEachNode(o,function(c){var l=c.getAttribute("name"),d=c.getAttribute("property"),m=c.getAttribute("content");if(m){var p=null,v=null;d&&(p=d.match(s),p&&(v=p[0].toLowerCase().replace(/\s/g,""),i[v]=m.trim())),!p&&l&&u.test(l)&&(v=l,m&&(v=v.toLowerCase().replace(/\s/g,"").replace(/\./g,":"),i[v]=m.trim()))}}),r.title=n.title||i["dc:title"]||i["dcterm:title"]||i["og:title"]||i["weibo:article:title"]||i["weibo:webpage:title"]||i.title||i["twitter:title"],r.title||(r.title=this._getArticleTitle()),r.byline=n.byline||i["dc:creator"]||i["dcterm:creator"]||i.author,r.excerpt=n.excerpt||i["dc:description"]||i["dcterm:description"]||i["og:description"]||i["weibo:article:description"]||i["weibo:webpage:description"]||i.description||i["twitter:description"],r.siteName=n.siteName||i["og:site_name"],r.title=this._unescapeHtmlEntities(r.title),r.byline=this._unescapeHtmlEntities(r.byline),r.excerpt=this._unescapeHtmlEntities(r.excerpt),r.siteName=this._unescapeHtmlEntities(r.siteName),r},_isSingleImage:function(n){return n.tagName==="IMG"?!0:n.children.length!==1||n.textContent.trim()!==""?!1:this._isSingleImage(n.children[0])},_unwrapNoscriptImages:function(n){var r=Array.from(n.getElementsByTagName("img"));this._forEachNode(r,function(o){for(var s=0;s<o.attributes.length;s++){var u=o.attributes[s];switch(u.name){case"src":case"srcset":case"data-src":case"data-srcset":return}if(/\.(jpg|jpeg|png|webp)/i.test(u.value))return}o.parentNode.removeChild(o)});var i=Array.from(n.getElementsByTagName("noscript"));this._forEachNode(i,function(o){var s=n.createElement("div");if(s.innerHTML=o.innerHTML,!!this._isSingleImage(s)){var u=o.previousElementSibling;if(u&&this._isSingleImage(u)){var c=u;c.tagName!=="IMG"&&(c=u.getElementsByTagName("img")[0]);for(var l=s.getElementsByTagName("img")[0],d=0;d<c.attributes.length;d++){var m=c.attributes[d];if(m.value!==""&&(m.name==="src"||m.name==="srcset"||/\.(jpg|jpeg|png|webp)/i.test(m.value))){if(l.getAttribute(m.name)===m.value)continue;var p=m.name;l.hasAttribute(p)&&(p="data-old-"+p),l.setAttribute(p,m.value)}}o.parentNode.replaceChild(s.firstElementChild,u)}}})},_removeScripts:function(n){this._removeNodes(this._getAllNodesWithTag(n,["script","noscript"]))},_hasSingleTagInsideElement:function(n,r){return n.children.length!=1||n.children[0].tagName!==r?!1:!this._someNode(n.childNodes,function(i){return i.nodeType===this.TEXT_NODE&&this.REGEXPS.hasContent.test(i.textContent)})},_isElementWithoutContent:function(n){return n.nodeType===this.ELEMENT_NODE&&n.textContent.trim().length==0&&(n.children.length==0||n.children.length==n.getElementsByTagName("br").length+n.getElementsByTagName("hr").length)},_hasChildBlockElement:function(n){return this._someNode(n.childNodes,function(r){return this.DIV_TO_P_ELEMS.has(r.tagName)||this._hasChildBlockElement(r)})},_isPhrasingContent:function(n){return n.nodeType===this.TEXT_NODE||this.PHRASING_ELEMS.indexOf(n.tagName)!==-1||(n.tagName==="A"||n.tagName==="DEL"||n.tagName==="INS")&&this._everyNode(n.childNodes,this._isPhrasingContent)},_isWhitespace:function(n){return n.nodeType===this.TEXT_NODE&&n.textContent.trim().length===0||n.nodeType===this.ELEMENT_NODE&&n.tagName==="BR"},_getInnerText:function(n,r){r=typeof r>"u"?!0:r;var i=n.textContent.trim();return r?i.replace(this.REGEXPS.normalize," "):i},_getCharCount:function(n,r){return r=r||",",this._getInnerText(n).split(r).length-1},_cleanStyles:function(n){if(!(!n||n.tagName.toLowerCase()==="svg")){for(var r=0;r<this.PRESENTATIONAL_ATTRIBUTES.length;r++)n.removeAttribute(this.PRESENTATIONAL_ATTRIBUTES[r]);this.DEPRECATED_SIZE_ATTRIBUTE_ELEMS.indexOf(n.tagName)!==-1&&(n.removeAttribute("width"),n.removeAttribute("height"));for(var i=n.firstElementChild;i!==null;)this._cleanStyles(i),i=i.nextElementSibling}},_getLinkDensity:function(n){var r=this._getInnerText(n).length;if(r===0)return 0;var i=0;return this._forEachNode(n.getElementsByTagName("a"),function(o){var s=o.getAttribute("href"),u=s&&this.REGEXPS.hashUrl.test(s)?.3:1;i+=this._getInnerText(o).length*u}),i/r},_getClassWeight:function(n){if(!this._flagIsActive(this.FLAG_WEIGHT_CLASSES))return 0;var r=0;return typeof n.className=="string"&&n.className!==""&&(this.REGEXPS.negative.test(n.className)&&(r-=25),this.REGEXPS.positive.test(n.className)&&(r+=25)),typeof n.id=="string"&&n.id!==""&&(this.REGEXPS.negative.test(n.id)&&(r-=25),this.REGEXPS.positive.test(n.id)&&(r+=25)),r},_clean:function(n,r){var i=["object","embed","iframe"].indexOf(r)!==-1;this._removeNodes(this._getAllNodesWithTag(n,[r]),function(o){if(i){for(var s=0;s<o.attributes.length;s++)if(this._allowedVideoRegex.test(o.attributes[s].value))return!1;if(o.tagName==="object"&&this._allowedVideoRegex.test(o.innerHTML))return!1}return!0})},_hasAncestorTag:function(n,r,i,o){i=i||3,r=r.toUpperCase();for(var s=0;n.parentNode;){if(i>0&&s>i)return!1;if(n.parentNode.tagName===r&&(!o||o(n.parentNode)))return!0;n=n.parentNode,s++}return!1},_getRowAndColumnCount:function(n){for(var r=0,i=0,o=n.getElementsByTagName("tr"),s=0;s<o.length;s++){var u=o[s].getAttribute("rowspan")||0;u&&(u=parseInt(u,10)),r+=u||1;for(var c=0,l=o[s].getElementsByTagName("td"),d=0;d<l.length;d++){var m=l[d].getAttribute("colspan")||0;m&&(m=parseInt(m,10)),c+=m||1}i=Math.max(i,c)}return{rows:r,columns:i}},_markDataTables:function(n){for(var r=n.getElementsByTagName("table"),i=0;i<r.length;i++){var o=r[i],s=o.getAttribute("role");if(s=="presentation"){o._readabilityDataTable=!1;continue}var u=o.getAttribute("datatable");if(u=="0"){o._readabilityDataTable=!1;continue}var c=o.getAttribute("summary");if(c){o._readabilityDataTable=!0;continue}var l=o.getElementsByTagName("caption")[0];if(l&&l.childNodes.length>0){o._readabilityDataTable=!0;continue}var d=["col","colgroup","tfoot","thead","th"],m=function(v){return!!o.getElementsByTagName(v)[0]};if(d.some(m)){this.log("Data table because found data-y descendant"),o._readabilityDataTable=!0;continue}if(o.getElementsByTagName("table")[0]){o._readabilityDataTable=!1;continue}var p=this._getRowAndColumnCount(o);if(p.rows>=10||p.columns>4){o._readabilityDataTable=!0;continue}o._readabilityDataTable=p.rows*p.columns>10}},_fixLazyImages:function(n){this._forEachNode(this._getAllNodesWithTag(n,["img","picture","figure"]),function(r){if(r.src&&this.REGEXPS.b64DataUrl.test(r.src)){var i=this.REGEXPS.b64DataUrl.exec(r.src);if(i[1]==="image/svg+xml")return;for(var o=!1,s=0;s<r.attributes.length;s++){var u=r.attributes[s];if(u.name!=="src"&&/\.(jpg|jpeg|png|webp)/i.test(u.value)){o=!0;break}}if(o){var c=r.src.search(/base64\s*/i)+7,l=r.src.length-c;l<133&&r.removeAttribute("src")}}if(!((r.src||r.srcset&&r.srcset!="null")&&r.className.toLowerCase().indexOf("lazy")===-1)){for(var d=0;d<r.attributes.length;d++)if(u=r.attributes[d],!(u.name==="src"||u.name==="srcset"||u.name==="alt")){var m=null;if(/\.(jpg|jpeg|png|webp)\s+\d/.test(u.value)?m="srcset":/^\s*\S+\.(jpg|jpeg|png|webp)\S*\s*$/.test(u.value)&&(m="src"),m){if(r.tagName==="IMG"||r.tagName==="PICTURE")r.setAttribute(m,u.value);else if(r.tagName==="FIGURE"&&!this._getAllNodesWithTag(r,["img","picture"]).length){var p=this._doc.createElement("img");p.setAttribute(m,u.value),r.appendChild(p)}}}}})},_getTextDensity:function(n,r){var i=this._getInnerText(n,!0).length;if(i===0)return 0;var o=0,s=this._getAllNodesWithTag(n,r);return this._forEachNode(s,u=>o+=this._getInnerText(u,!0).length),o/i},_cleanConditionally:function(n,r){this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)&&this._removeNodes(this._getAllNodesWithTag(n,[r]),function(i){var o=function(y){return y._readabilityDataTable},s=r==="ul"||r==="ol";if(!s){var u=0,c=this._getAllNodesWithTag(i,["ul","ol"]);this._forEachNode(c,y=>u+=this._getInnerText(y).length),s=u/this._getInnerText(i).length>.9}if(r==="table"&&o(i)||this._hasAncestorTag(i,"table",-1,o)||this._hasAncestorTag(i,"code"))return!1;var l=this._getClassWeight(i);this.log("Cleaning Conditionally",i);var d=0;if(l+d<0)return!0;if(this._getCharCount(i,",")<10){for(var m=i.getElementsByTagName("p").length,p=i.getElementsByTagName("img").length,v=i.getElementsByTagName("li").length-100,w=i.getElementsByTagName("input").length,S=this._getTextDensity(i,["h1","h2","h3","h4","h5","h6"]),g=0,M=this._getAllNodesWithTag(i,["object","embed","iframe"]),f=0;f<M.length;f++){for(var T=0;T<M[f].attributes.length;T++)if(this._allowedVideoRegex.test(M[f].attributes[T].value))return!1;if(M[f].tagName==="object"&&this._allowedVideoRegex.test(M[f].innerHTML))return!1;g++}var L=this._getLinkDensity(i),E=this._getInnerText(i).length,x=p>1&&m/p<.5&&!this._hasAncestorTag(i,"figure")||!s&&v>m||w>Math.floor(m/3)||!s&&S<.9&&E<25&&(p===0||p>2)&&!this._hasAncestorTag(i,"figure")||!s&&l<25&&L>.2||l>=25&&L>.5||g===1&&E<75||g>1;if(s&&x){for(var k=0;k<i.children.length;k++)if(i.children[k].children.length>1)return x;let y=i.getElementsByTagName("li").length;if(p==y)return!1}return x}return!1})},_cleanMatchedNodes:function(n,r){for(var i=this._getNextNode(n,!0),o=this._getNextNode(n);o&&o!=i;)r.call(this,o,o.className+" "+o.id)?o=this._removeAndGetNext(o):o=this._getNextNode(o)},_cleanHeaders:function(n){let r=this._getAllNodesWithTag(n,["h1","h2"]);this._removeNodes(r,function(i){let o=this._getClassWeight(i)<0;return o&&this.log("Removing header with low class weight:",i),o})},_headerDuplicatesTitle:function(n){if(n.tagName!="H1"&&n.tagName!="H2")return!1;var r=this._getInnerText(n,!1);return this.log("Evaluating similarity of header:",r,this._articleTitle),this._textSimilarity(this._articleTitle,r)>.75},_flagIsActive:function(n){return(this._flags&n)>0},_removeFlag:function(n){this._flags=this._flags&~n},_isProbablyVisible:function(n){return(!n.style||n.style.display!="none")&&!n.hasAttribute("hidden")&&(!n.hasAttribute("aria-hidden")||n.getAttribute("aria-hidden")!="true"||n.className&&n.className.indexOf&&n.className.indexOf("fallback-image")!==-1)},parse:function(){if(this._maxElemsToParse>0){var n=this._doc.getElementsByTagName("*").length;if(n>this._maxElemsToParse)throw new Error("Aborting parsing document; "+n+" elements found")}this._unwrapNoscriptImages(this._doc);var r=this._disableJSONLD?{}:this._getJSONLD(this._doc);this._removeScripts(this._doc),this._prepDocument();var i=this._getArticleMetadata(r);this._articleTitle=i.title;var o=this._grabArticle();if(!o)return null;if(this.log("Grabbed: "+o.innerHTML),this._postProcessContent(o),!i.excerpt){var s=o.getElementsByTagName("p");s.length>0&&(i.excerpt=s[0].textContent.trim())}var u=o.textContent;return{title:this._articleTitle,byline:i.byline||this._articleByline,dir:this._articleDir,lang:this._articleLang,content:this._serializer(o),textContent:u,length:u.length,excerpt:i.excerpt,siteName:i.siteName||this._articleSiteName}}},typeof t=="object"&&(t.exports=a)}),a4=hs((e,t)=>{var a={unlikelyCandidates:/-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,okMaybeItsACandidate:/and|article|body|column|content|main|shadow/i};function n(i){return(!i.style||i.style.display!="none")&&!i.hasAttribute("hidden")&&(!i.hasAttribute("aria-hidden")||i.getAttribute("aria-hidden")!="true"||i.className&&i.className.indexOf&&i.className.indexOf("fallback-image")!==-1)}function r(i,o={}){typeof o=="function"&&(o={visibilityChecker:o});var s={minScore:20,minContentLength:140,visibilityChecker:n};o=Object.assign(s,o);var u=i.querySelectorAll("p, pre, article"),c=i.querySelectorAll("div > br");if(c.length){var l=new Set(u);[].forEach.call(c,function(m){l.add(m.parentNode)}),u=Array.from(l)}var d=0;return[].some.call(u,function(m){if(!o.visibilityChecker(m))return!1;var p=m.className+" "+m.id;if(a.unlikelyCandidates.test(p)&&!a.okMaybeItsACandidate.test(p)||m.matches("li p"))return!1;var v=m.textContent.trim().length;return v<o.minContentLength?!1:(d+=Math.sqrt(v-o.minContentLength),d>o.minScore)})}typeof t=="object"&&(t.exports=r)}),cd=hs((e,t)=>{var a=t4(),n=a4();t.exports={Readability:a,isProbablyReaderable:n}}),dd={};$f(dd,{Readability:()=>pd,default:()=>hd,isProbablyReaderable:()=>gd});var md=ld(cd());e4(dd,ld(cd()));var{Readability:pd,isProbablyReaderable:gd}=md,{default:ud,...n4}=md,hd=ud!==void 0?ud:n4;var fd=class extends Error{constructor(e,t){super(e),this.name="ParseError",this.type=t.type,this.field=t.field,this.value=t.value,this.line=t.line}};function fs(e){}function bs(e){if(typeof e=="function")throw new TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");let{onEvent:t=fs,onError:a=fs,onRetry:n=fs,onComment:r}=e,i="",o=!0,s,u="",c="";function l(w){let S=o?w.replace(/^\xEF\xBB\xBF/,""):w,[g,M]=r4(`${i}${S}`);for(let f of g)d(f);i=M,o=!1}function d(w){if(w===""){p();return}if(w.startsWith(":")){r&&r(w.slice(w.startsWith(": ")?2:1));return}let S=w.indexOf(":");if(S!==-1){let g=w.slice(0,S),M=w[S+1]===" "?2:1,f=w.slice(S+M);m(g,f,w);return}m(w,"",w)}function m(w,S,g){switch(w){case"event":c=S;break;case"data":u=`${u}${S}
`;break;case"id":s=S.includes("\0")?void 0:S;break;case"retry":/^\d+$/.test(S)?n(parseInt(S,10)):a(new fd(`Invalid \`retry\` value: "${S}"`,{type:"invalid-retry",value:S,line:g}));break;default:a(new fd(`Unknown field "${w.length>20?`${w.slice(0,20)}\u2026`:w}"`,{type:"unknown-field",field:w,value:S,line:g}));break}}function p(){u.length>0&&t({id:s,event:c||void 0,data:u.endsWith(`
`)?u.slice(0,-1):u}),s=void 0,u="",c=""}function v(w={}){i&&w.consume&&d(i),o=!0,s=void 0,u="",c="",i=""}return{feed:l,reset:v}}function r4(e){let t=[],a="",n=0;for(;n<e.length;){let r=e.indexOf("\r",n),i=e.indexOf(`
`,n),o=-1;if(r!==-1&&i!==-1?o=Math.min(r,i):r!==-1?o=r:i!==-1&&(o=i),o===-1){a=e.slice(n);break}else{let s=e.slice(n,o);t.push(s),n=o+1,e[n-1]==="\r"&&e[n]===`
`&&n++}}return[t,a]}var i4=Object.create,vs=Object.defineProperty,o4=Object.getOwnPropertyDescriptor,s4=Object.getOwnPropertyNames,u4=Object.getPrototypeOf,l4=Object.prototype.hasOwnProperty,c4=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),d4=(e,t)=>{for(var a in t)vs(e,a,{get:t[a],enumerable:!0})},ys=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of s4(t))!l4.call(e,r)&&r!==a&&vs(e,r,{get:()=>t[r],enumerable:!(n=o4(t,r))||n.enumerable});return e},m4=(e,t,a)=>(ys(e,t,"default"),a&&ys(a,t,"default")),yd=(e,t,a)=>(a=e!=null?i4(u4(e)):{},ys(t||!e||!e.__esModule?vs(a,"default",{value:e,enumerable:!0}):a,e)),vd=c4(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parse=void 0;function t(f){if(f!==void 0){if(f===null)return null;if(f==="")return"";f=f.replace(/\\+$/,T=>T.length%2===0?T:T.slice(0,-1));try{return JSON.parse(f)}catch(T){let[L,E]=f.trimLeft()[0]===":"?a(f,T):a(f,T,p);if(t.lastParseReminding=E,t.onExtraToken&&E.length>0){let x=E.trimRight();t.lastParseReminding=x,x.length>0&&t.onExtraToken(f,L,x)}return L}}}e.parse=t,function(f){f.onExtraToken=(T,L,E)=>{}}(t=e.parse||(e.parse={}));function a(f,T,L){let E=r[f[0]]||L;if(!E)throw T;return E(f,T)}function n(f,T,L){return f[0]==='"'?l(f):f[0]==="'"?m(f):p(f,T,L)}var r={};function i(f){return f.trimLeft()}r[" "]=o,r["\r"]=o,r[`
`]=o,r["	"]=o;function o(f,T){return f=i(f),a(f,T)}r["["]=s;function s(f,T){f=f.substr(1);let L=[];for(f=i(f);f.length>0;){if(f[0]==="]"){f=f.substr(1);break}let E=a(f,T,(x,k)=>p(x,k,[",","]"]));L.push(E[0]),f=E[1],f=i(f),f[0]===","&&(f=f.substring(1),f=i(f))}return[L,f]}for(let f of"0123456789.-".slice())r[f]=u;function u(f){for(let T=0;T<f.length;T++){let L=f[T];if(r[L]===u)continue;let E=f.substring(0,T);return f=f.substring(T),[c(E),f]}return[c(f),""]}function c(f){if(f==="-")return-0;let T=+f;return Number.isNaN(T)?f:T}r['"']=l;function l(f){for(let T=1;T<f.length;T++){let L=f[T];if(L==="\\"){T++;continue}if(L==='"'){let E=d(f.substring(0,T+1));return f=f.substring(T+1),[JSON.parse(E),f]}}return[JSON.parse(d(f)+'"'),""]}function d(f){return f.replace(/\n/g,"\\n").replace(/\t/g,"\\t").replace(/\r/g,"\\r")}r["'"]=m;function m(f){for(let T=1;T<f.length;T++){let L=f[T];if(L==="\\"){T++;continue}if(L==="'"){let E=d(f.substring(0,T+1));return f=f.substring(T+1),[JSON.parse('"'+E.slice(1,-1)+'"'),f]}}return[JSON.parse('"'+d(f.slice(1))+'"'),""]}function p(f,T,L=[" "]){let E=Math.min(...L.map(y=>{let j=f.indexOf(y);return j===-1?f.length:j})),x=f.substring(0,E).trim(),k=f.substring(E);return[x,k]}r["{"]=v;function v(f,T){f=f.substr(1);let L={};for(f=i(f);f.length>0;){if(f[0]==="}"){f=f.substr(1);break}let E=n(f,T,[":","}"]),x=E[0];if(f=E[1],f=i(f),f[0]!==":"){L[x]=void 0;break}if(f=f.substr(1),f=i(f),f.length===0){L[x]=void 0;break}let k=a(f,T);L[x]=k[0],f=k[1],f=i(f),f[0]===","&&(f=f.substr(1),f=i(f))}return[L,f]}r.t=w;function w(f,T){return M(f,"true",!0,T)}r.f=S;function S(f,T){return M(f,"false",!1,T)}r.n=g;function g(f,T){return M(f,"null",null,T)}function M(f,T,L,E){for(let x=T.length;x>=1;x--)if(f.startsWith(T.slice(0,x)))return[L,f.slice(x)];{let x=JSON.stringify(f.slice(0,T.length));throw E}}}),xd={};d4(xd,{__esModule:()=>p4,default:()=>kd,parse:()=>Ad});var wd=yd(vd());m4(xd,yd(vd()));var{__esModule:p4,parse:Ad}=wd,{default:bd,...g4}=wd,kd=bd!==void 0?bd:g4;function h4({allowIcannDomains:e=!0,allowPrivateDomains:t=!1,detectIp:a=!0,extractHostname:n=!0,mixedInputs:r=!0,validHosts:i=null,validateHostname:o=!0}){return{allowIcannDomains:e,allowPrivateDomains:t,detectIp:a,extractHostname:n,mixedInputs:r,validHosts:i,validateHostname:o}}var oT=h4({});function Ed(){return{domain:null,domainWithoutSuffix:null,hostname:null,isIcann:null,isIp:null,isPrivate:null,publicSuffix:null,subdomain:null}}var gT=function(){let e=[1,{}],t=[2,{}],a=[0,{city:e}];return[0,{ck:[0,{www:e}],jp:[0,{kawasaki:a,kitakyushu:a,kobe:a,nagoya:a,sapporo:a,sendai:a,yokohama:a}],dev:[0,{hrsn:[0,{psl:[0,{wc:[0,{ignored:t,sub:[0,{ignored:t}]}]}]}]}]}]}(),hT=function(){let e=[1,{}],t=[2,{}],a=[1,{com:e,edu:e,gov:e,net:e,org:e}],n=[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e}],r=[0,{"*":t}],i=[2,{s:r}],o=[0,{relay:t}],s=[2,{id:t}],u=[1,{gov:e}],c=[0,{"transfer-webapp":t}],l=[0,{notebook:t,studio:t}],d=[0,{labeling:t,notebook:t,studio:t}],m=[0,{notebook:t}],p=[0,{labeling:t,notebook:t,"notebook-fips":t,studio:t}],v=[0,{notebook:t,"notebook-fips":t,studio:t,"studio-fips":t}],w=[0,{"*":e}],S=[1,{co:t}],g=[0,{objects:t}],M=[2,{nodes:t}],f=[0,{my:r}],T=[0,{s3:t,"s3-accesspoint":t,"s3-website":t}],L=[0,{s3:t,"s3-accesspoint":t}],E=[0,{direct:t}],x=[0,{"webview-assets":t}],k=[0,{vfs:t,"webview-assets":t}],y=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":x,cloud9:k}],j=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:L,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":x,cloud9:k}],_=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":x,cloud9:k}],P=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t}],N=[0,{s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-website":t}],X=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:N,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":x,cloud9:k}],Y=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:N,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-deprecated":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":x,cloud9:k}],ke=[0,{s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t}],Me=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:ke,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t}],G=[0,{auth:t}],oe=[0,{auth:t,"auth-fips":t}],qe=[0,{"auth-fips":t}],ge=[0,{apps:t}],D=[0,{paas:t}],F=[2,{eu:t}],z=[0,{app:t}],ae=[0,{site:t}],J=[1,{com:e,edu:e,net:e,org:e}],he=[0,{j:t}],ye=[0,{dyn:t}],ee=[1,{co:e,com:e,edu:e,gov:e,net:e,org:e}],W=[0,{p:t}],Q=[0,{user:t}],ne=[0,{shop:t}],se=[0,{cdn:t}],q=[0,{cust:t,reservd:t}],te=[0,{cust:t}],xe=[0,{s3:t}],pe=[1,{biz:e,com:e,edu:e,gov:e,info:e,net:e,org:e}],V=[0,{ipfs:t}],Ee=[1,{framer:t}],je=[0,{forgot:t}],ie=[1,{gs:e}],Ie=[0,{nes:e}],K=[1,{k12:e,cc:e,lib:e}],Qe=[1,{cc:e,lib:e}];return[0,{ac:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,drr:t,feedback:t,forms:t}],ad:e,ae:[1,{ac:e,co:e,gov:e,mil:e,net:e,org:e,sch:e}],aero:[1,{airline:e,airport:e,"accident-investigation":e,"accident-prevention":e,aerobatic:e,aeroclub:e,aerodrome:e,agents:e,"air-surveillance":e,"air-traffic-control":e,aircraft:e,airtraffic:e,ambulance:e,association:e,author:e,ballooning:e,broker:e,caa:e,cargo:e,catering:e,certification:e,championship:e,charter:e,civilaviation:e,club:e,conference:e,consultant:e,consulting:e,control:e,council:e,crew:e,design:e,dgca:e,educator:e,emergency:e,engine:e,engineer:e,entertainment:e,equipment:e,exchange:e,express:e,federation:e,flight:e,freight:e,fuel:e,gliding:e,government:e,groundhandling:e,group:e,hanggliding:e,homebuilt:e,insurance:e,journal:e,journalist:e,leasing:e,logistics:e,magazine:e,maintenance:e,marketplace:e,media:e,microlight:e,modelling:e,navigation:e,parachuting:e,paragliding:e,"passenger-association":e,pilot:e,press:e,production:e,recreation:e,repbody:e,res:e,research:e,rotorcraft:e,safety:e,scientist:e,services:e,show:e,skydiving:e,software:e,student:e,taxi:e,trader:e,trading:e,trainer:e,union:e,workinggroup:e,works:e}],af:a,ag:[1,{co:e,com:e,net:e,nom:e,org:e,obj:t}],ai:[1,{com:e,net:e,off:e,org:e,uwu:t,framer:t}],al:n,am:[1,{co:e,com:e,commune:e,net:e,org:e,radio:t}],ao:[1,{co:e,ed:e,edu:e,gov:e,gv:e,it:e,og:e,org:e,pb:e}],aq:e,ar:[1,{bet:e,com:e,coop:e,edu:e,gob:e,gov:e,int:e,mil:e,musica:e,mutual:e,net:e,org:e,seg:e,senasa:e,tur:e}],arpa:[1,{e164:e,home:e,"in-addr":e,ip6:e,iris:e,uri:e,urn:e}],as:u,asia:[1,{cloudns:t,daemon:t,dix:t}],at:[1,{ac:[1,{sth:e}],co:e,gv:e,or:e,funkfeuer:[0,{wien:t}],futurecms:[0,{"*":t,ex:r,in:r}],futurehosting:t,futuremailing:t,ortsinfo:[0,{ex:r,kunden:r}],biz:t,info:t,"123webseite":t,priv:t,myspreadshop:t,"12hp":t,"2ix":t,"4lima":t,"lima-city":t}],au:[1,{asn:e,com:[1,{cloudlets:[0,{mel:t}],myspreadshop:t}],edu:[1,{act:e,catholic:e,nsw:[1,{schools:e}],nt:e,qld:e,sa:e,tas:e,vic:e,wa:e}],gov:[1,{qld:e,sa:e,tas:e,vic:e,wa:e}],id:e,net:e,org:e,conf:e,oz:e,act:e,nsw:e,nt:e,qld:e,sa:e,tas:e,vic:e,wa:e}],aw:[1,{com:e}],ax:e,az:[1,{biz:e,co:e,com:e,edu:e,gov:e,info:e,int:e,mil:e,name:e,net:e,org:e,pp:e,pro:e}],ba:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,rs:t}],bb:[1,{biz:e,co:e,com:e,edu:e,gov:e,info:e,net:e,org:e,store:e,tv:e}],bd:w,be:[1,{ac:e,cloudns:t,webhosting:t,interhostsolutions:[0,{cloud:t}],kuleuven:[0,{ezproxy:t}],"123website":t,myspreadshop:t,transurl:r}],bf:u,bg:[1,{0:e,1:e,2:e,3:e,4:e,5:e,6:e,7:e,8:e,9:e,a:e,b:e,c:e,d:e,e,f:e,g:e,h:e,i:e,j:e,k:e,l:e,m:e,n:e,o:e,p:e,q:e,r:e,s:e,t:e,u:e,v:e,w:e,x:e,y:e,z:e,barsy:t}],bh:a,bi:[1,{co:e,com:e,edu:e,or:e,org:e}],biz:[1,{activetrail:t,"cloud-ip":t,cloudns:t,jozi:t,dyndns:t,"for-better":t,"for-more":t,"for-some":t,"for-the":t,selfip:t,webhop:t,orx:t,mmafan:t,myftp:t,"no-ip":t,dscloud:t}],bj:[1,{africa:e,agro:e,architectes:e,assur:e,avocats:e,co:e,com:e,eco:e,econo:e,edu:e,info:e,loisirs:e,money:e,net:e,org:e,ote:e,restaurant:e,resto:e,tourism:e,univ:e}],bm:a,bn:[1,{com:e,edu:e,gov:e,net:e,org:e,co:t}],bo:[1,{com:e,edu:e,gob:e,int:e,mil:e,net:e,org:e,tv:e,web:e,academia:e,agro:e,arte:e,blog:e,bolivia:e,ciencia:e,cooperativa:e,democracia:e,deporte:e,ecologia:e,economia:e,empres/* REMOVED_COMMON_BLOCK_2473 */:t,rj:t,rn:t,ro:t,rr:t,rs:t,sc:t,se:t,sp:t,to:t}],leilao:e,lel:e,log:e,londrina:e,macapa:e,maceio:e,manaus:e,maringa:e,mat:e,med:e,mil:e,morena:e,mp:e,mus:e,natal:e,net:e,niteroi:e,nom:w,not:e,ntr:e,odo:e,ong:e,org:e,osasco:e,palmas:e,poa:e,ppg:e,pro:e,psc:e,psi:e,pvh:e,qsl:e,radio:e,rec:e,recife:e,rep:e,ribeirao:e,rio:e,riobranco:e,riopreto:e,salvador:e,sampa:e,santamaria:e,santoandre:e,saobernardo:e,saogonca:e,seg:e,sjc:e,slg:e,slz:e,sorocaba:e,srv:e,taxi:e,tc:e,tec:e,teo:e,the:e,tmp:e,trd:e,tur:e,tv:e,udi:e,vet:e,vix:e,vlog:e,wiki:e,zlg:e}],bs:[1,{com:e,edu:e,gov:e,net:e,org:e,we:t}],bt:a,bv:e,bw:[1,{ac:e,co:e,gov:e,net:e,org:e}],by:[1,{gov:e,mil:e,com:e,of:e,mediatech:t}],bz:[1,{co:e,com:e,edu:e,gov:e,net:e,org:e,za:t,mydns:t,gsj:t}],ca:[1,{ab:e,bc:e,mb:e,nb:e,nf:e,nl:e,ns:e,nt:e,nu:e,on:e,pe:e,qc:e,sk:e,yk:e,gc:e,barsy:t,awdev:r,co:t,"no-ip":t,myspreadshop:t,box:t}],cat:e,cc:[1,{cleverapps:t,cloudns:t,ftpaccess:t,"game-server":t,myphotos:t,scrapping:t,twmail:t,csx:t,fantasyleague:t,spawn:[0,{instances:t}]}],cd:u,cf:e,cg:e,ch:[1,{square7:t,cloudns:t,cloudscale:[0,{cust:t,lpg:g,rma:g}],objectstorage:[0,{lpg:t,rma:t}],flow:[0,{ae:[0,{alp1:t}],appengine:t}],"linkyard-cloud":t,gotdns:t,dnsking:t,"123website":t,myspreadshop:t,firenet:[0,{"*":t,svc:r}],"12hp":t,"2ix":t,"4lima":t,"lima-city":t}],ci:[1,{ac:e,"xn--aroport-bya":e,a\u00E9roport:e,asso:e,co:e,com:e,ed:e,edu:e,go:e,gouv:e,int:e,net:e,or:e,org:e}],ck:w,cl:[1,{co:e,gob:e,gov:e,mil:e,cloudns:t}],cm:[1,{co:e,com:e,gov:e,net:e}],cn:[1,{ac:e,com:[1,{amazonaws:[0,{"cn-north-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-deprecated":t,"s3-object-lambda":t,"s3-website":t}],"cn-northwest-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:L,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t}],compute:r,airflow:[0,{"cn-north-1":r,"cn-northwest-1":r}],eb:[0,{"cn-north-1":t,"cn-northwest-1":t}],elb:r}],sagemaker:[0,{"cn-north-1":l,"cn-northwest-1":l}]}],edu:e,gov:e,mil:e,net:e,org:e,"xn--55qx5d":e,\u516C\u53F8:e,"xn--od0alg":e,\u7DB2\u7D61:e,"xn--io0a7i":e,\u7F51\u7EDC:e,ah:e,bj:e,cq:e,fj:e,gd:e,gs:e,gx:e,gz:e,ha:e,hb:e,he:e,hi:e,hk:e,hl:e,hn:e,jl:e,js:e,jx:e,ln:e,mo:e,nm:e,nx:e,qh:e,sc:e,sd:e,sh:[1,{as:t}],sn:e,sx:e,tj:e,tw:e,xj:e,xz:e,yn:e,zj:e,"canva-apps":t,canvasite:f,myqnapcloud:t,quickconnect:E}],co:[1,{com:e,edu:e,gov:e,mil:e,net:e,nom:e,org:e,carrd:t,crd:t,otap:r,leadpages:t,lpages:t,mypi:t,xmit:r,firewalledreplit:s,repl:s,supabase:t}],com:[1,{a2hosted:t,cpserver:t,adobeaemcloud:[2,{dev:r}],africa:t,airkitapps:t,"airkitapps-au":t,aivencloud:t,alibabacloudcs:t,kasserver:t,amazonaws:[0,{"af-south-1":y,"ap-east-1":j,"ap-northeast-1":_,"ap-northeast-2":_,"ap-northeast-3":y,"ap-south-1":_,"ap-south-2":P,"ap-southeast-1":_,"ap-southeast-2":_,"ap-southeast-3":P,"ap-southeast-4":P,"ap-southeast-5":[0,{"execute-api":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-deprecated":t,"s3-object-lambda":t,"s3-website":t}],"ca-central-1":X,"ca-west-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:N,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t}],"eu-central-1":_,"eu-central-2":P,"eu-north-1":j,"eu-south-1":y,"eu-south-2":P,"eu-west-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-deprecated":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":x,cloud9:k}],"eu-west-2":j,"eu-west-3":y,"il-central-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:T,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":x,cloud9:[0,{vfs:t}]}],"me-central-1":P,"me-south-1":j,"sa-east-1":y,"us-east-1":[2,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:N,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-deprecated":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":x,cloud9:k}],"us-east-2":Y,"us-gov-east-1":Me,"us-gov-west-1":Me,"us-west-1":X,"us-west-2":Y,compute:r,"compute-1":r,airflow:[0,{"af-south-1":r,"ap-east-1":r,"ap-northeast-1":r,"ap-northeast-2":r,"ap-northeast-3":r,"ap-south-1":r,"ap-south-2":r,"ap-southeast-1":r,"ap-southeast-2":r,"ap-southeast-3":r,"ap-southeast-4":r,"ca-central-1":r,"ca-west-1":r,"eu-central-1":r,"eu-central-2":r,"eu-north-1":r,"eu-south-1":r,"eu-south-2":r,"eu-west-1":r,"eu-west-2":r,"eu-west-3":r,"il-central-1":r,"me-central-1":r,"me-south-1":r,"sa-east-1":r,"us-east-1":r,"us-east-2":r,"us-west-1":r,"us-west-2":r}],s3:t,"s3-1":t,"s3-ap-east-1":t,"s3-ap-northeast-1":t,"s3-ap-northeast-2":t,"s3-ap-northeast-3":t,"s3-ap-south-1":t,"s3-ap-southeast-1":t,"s3-ap-southeast-2":t,"s3-ca-central-1":t,"s3-eu-central-1":t,"s3-eu-north-1":t,"s3-eu-west-1":t,"s3-eu-west-2":t,"s3-eu-west-3":t,"s3-external-1":t,"s3-fips-us-gov-east-1":t,"s3-fips-us-gov-west-1":t,"s3-global":[0,{accesspoint:[0,{mrap:t}]}],"s3-me-south-1":t,"s3-sa-east-1":t,"s3-us-east-2":t,"s3-us-gov-east-1":t,"s3-us-gov-west-1":t,"s3-us-west-1":t,"s3-us-west-2":t,"s3-website-ap-northeast-1":t,"s3-website-ap-southeast-1":t,"s3-website-ap-southeast-2":t,"s3-website-eu-west-1":t,"s3-website-sa-east-1":t,"s3-website-us-east-1":t,"s3-website-us-gov-west-1":t,"s3-website-us-west-1":t,"s3-website-us-west-2":t,elb:r}],amazoncognito:[0,{"af-south-1":G,"ap-east-1":G,"ap-northeast-1":G,"ap-northeast-2":G,"ap-northeast-3":G,"ap-south-1":G,"ap-south-2":G,"ap-southeast-1":G,"ap-southeast-2":G,"ap-southeast-3":G,"ap-southeast-4":G,"ap-southeast-5":G,"ca-central-1":G,"ca-west-1":G,"eu-central-1":G,"eu-central-2":G,"eu-north-1":G,"eu-south-1":G,"eu-south-2":G,"eu-west-1":G,"eu-west-2":G,"eu-west-3":G,"il-central-1":G,"me-central-1":G,"me-south-1":G,"sa-east-1":G,"us-east-1":oe,"us-east-2":oe,"us-gov-east-1":qe,"us-gov-west-1":qe,"us-west-1":oe,"us-west-2":oe}],amplifyapp:t,awsapprunner:r,awsapps:t,elasticbeanstalk:[2,{"af-south-1":t,"ap-east-1":t,"ap-northeast-1":t,"ap-northeast-2":t,"ap-northeast-3":t,"ap-south-1":t,"ap-southeast-1":t,"ap-southeast-2":t,"ap-southeast-3":t,"ca-central-1":t,"eu-central-1":t,"eu-north-1":t,"eu-south-1":t,"eu-west-1":t,"eu-west-2":t,"eu-west-3":t,"il-central-1":t,"me-south-1":t,"sa-east-1":t,"us-east-1":t,"us-east-2":t,"us-gov-east-1":t,"us-gov-west-1":t,"us-west-1":t,"us-west-2":t}],awsglobalaccelerator:t,siiites:t,appspacehosted:t,appspaceusercontent:t,"on-aptible":t,myasustor:t,"balena-devices":t,boutir:t,bplaced:t,cafjs:t,"canva-apps":t,"cdn77-storage":t,br:t,cn:t,de:t,eu:t,jpn:t,mex:t,ru:t,sa:t,uk:t,us:t,za:t,"clever-cloud":[0,{services:r}],dnsabr:t,"ip-ddns":t,jdevcloud:t,wpdevcloud:t,"cf-ipfs":t,"cloudflare-ipfs":t,trycloudflare:t,co:t,devinapps:r,builtwithdark:t,datadetect:[0,{demo:t,instance:t}],dattolocal:t,dattorelay:t,dattoweb:t,mydatto:t,digitaloceanspaces:r,discordsays:t,discordsez:t,drayddns:t,dreamhosters:t,duru/* REMOVED_COMMON_BLOCK_2461 */t,"is-slick":t,"is-uberleet":t,"is-with-theband":t,"isa-geek":t,"isa-hockeynut":t,issmarterthanyou:t,"likes-pie":t,likescandy:t,"neat-url":t,"saves-the-whales":t,selfip:t,"sells-for-less":t,"sells-for-u":t,servebbs:t,"simple-url":t,"space-to-rent":t,"teaches-yoga":t,writesthisblog:t,ddnsfree:t,ddnsgeek:t,giize:t,gleeze:t,kozow:t,loseyourip:t,ooguy:t,theworkpc:t,mytuleap:t,"tuleap-partners":t,encoreapi:t,evennode:[0,{"eu-1":t,"eu-2":t,"eu-3":t,"eu-4":t,"us-1":t,"us-2":t,"us-3":t,"us-4":t}],onfabrica:t,"fastly-edge":t,"fastly-terrarium":t,"fastvps-server":t,mydobiss:t,firebaseapp:t,fldrv:t,forgeblocks:t,framercanvas:t,"freebox-os":t,freeboxos:t,freemyip:t,aliases121:t,gentapps:t,gentlentapis:t,githubusercontent:t,"0emm":r,appspot:[2,{r}],blogspot:t,codespot:t,googleapis:t,googlecode:t,pagespeedmobilizer:t,withgoogle:t,withyoutube:t,grayjayleagues:t,hatenablog:t,hatenadiary:t,herokuapp:t,gr:t,smushcdn:t,wphostedmail:t,wpmucdn:t,pixolino:t,"apps-1and1":t,"live-website":t,dopaas:t,"hosted-by-previder":D,hosteur:[0,{"rag-cloud":t,"rag-cloud-ch":t}],"ik-server":[0,{jcloud:t,"jcloud-ver-jpc":t}],jelastic:[0,{demo:t}],massivegrid:D,wafaicloud:[0,{jed:t,ryd:t}],webadorsite:t,joyent:[0,{cns:r}],lpusercontent:t,linode:[0,{members:t,nodebalancer:r}],linodeobjects:r,linodeusercontent:[0,{ip:t}],localtonet:t,lovableproject:t,barsycenter:t,barsyonline:t,modelscape:t,mwcloudnonprod:t,polyspace:t,mazeplay:t,miniserver:t,atmeta:t,fbsbx:ge,meteorapp:F,routingthecloud:t,mydbserver:t,hostedpi:t,"mythic-beasts":[0,{caracal:t,customer:t,fentiger:t,lynx:t,ocelot:t,oncilla:t,onza:t,sphinx:t,vs:t,x:t,yali:t}],nospamproxy:[0,{cloud:[2,{o365:t}]}],"4u":t,nfshost:t,"3utilities":t,blogsyte:t,ciscofreak:t,damnserver:t,ddnsking:t,ditchyourip:t,dnsiskinky:t,dynns:t,geekgalaxy:t,"health-carereform":t,homesecuritymac:t,homesecuritypc:t,myactivedirectory:t,mysecuritycamera:t,myvnc:t,"net-freaks":t,onthewifi:t,point2this:t,quicksytes:t,securitytactics:t,servebeer:t,servecounterstrike:t,serveexchange:t,serveftp:t,servegame:t,servehalflife:t,servehttp:t,servehumour:t,serveirc:t,servemp3:t,servep2p:t,servepics:t,servequake:t,servesarcasm:t,stufftoread:t,unusualperson:t,workisboring:t,myiphost:t,observableusercontent:[0,{static:t}],simplesite:t,orsites:t,operaunite:t,"customer-oci":[0,{"*":t,oci:r,ocp:r,ocs:r}],oraclecloudapps:r,oraclegovcloudapps:r,"authgear-staging":t,authgearapps:t,skygearapp:t,outsystemscloud:t,ownprovider:t,pgfog:t,pagexl:t,gotpantheon:t,paywhirl:r,upsunapp:t,"postman-echo":t,prgmr:[0,{xen:t}],pythonanywhere:F,qa2:t,"alpha-myqnapcloud":t,"dev-myqnapcloud":t,mycloudnas:t,mynascloud:t,myqnapcloud:t,qualifioapp:t,ladesk:t,qbuser:t,quipelements:r,rackmaze:t,"readthedocs-hosted":t,rhcloud:t,onrender:t,render:z,"subsc-pay":t,"180r":t,dojin:t,sakuratan:t,sakuraweb:t,x0:t,code:[0,{builder:r,"dev-builder":r,"stg-builder":r}],salesforce:[0,{platform:[0,{"code-builder-stg":[0,{test:[0,{"001":r}]}]}]}],logoip:t,scrysec:t,"firewall-gateway":t,myshopblocks:t,myshopify:t,shopitsite:t,"1kapp":t,appchizi:t,applinzi:t,sinaapp:t,vipsinaapp:t,streamlitapp:t,"try-snowplow":t,"playstation-cloud":t,myspreadshop:t,"w-corp-staticblitz":t,"w-credentialless-staticblitz":t,"w-staticblitz":t,"stackhero-network":t,stdlib:[0,{api:t}],strapiapp:[2,{media:t}],"streak-link":t,streaklinks:t,streakusercontent:t,"temp-dns":t,dsmynas:t,familyds:t,mytabit:t,taveusercontent:t,"tb-hosting":ae,reservd:t,thingdustdata:t,"townnews-staging":t,typeform:[0,{pro:t}],hk:t,it:t,"deus-canvas":t,vultrobjects:r,wafflecell:t,hotelwithflight:t,"reserve-online":t,cprapid:t,pleskns:t,remotewd:t,wiardweb:[0,{pages:t}],wixsite:t,wixstudio:t,messwithdns:t,"woltlab-demo":t,wpenginepowered:[2,{js:t}],xnbay:[2,{u2:t,"u2-local":t}],yolasite:t}],coop:e,cr:[1,{ac:e,co:e,ed:e,fi:e,go:e,or:e,sa:e}],cu:[1,{com:e,edu:e,gob:e,inf:e,nat:e,net:e,org:e}],cv:[1,{com:e,edu:e,id:e,int:e,net:e,nome:e,org:e,publ:e}],cw:J,cx:[1,{gov:e,cloudns:t,ath:t,info:t,assessments:t,calculators:t,funnels:t,paynow:t,quizzes:t,researched:t,tests:t}],cy:[1,{ac:e,biz:e,com:[1,{scaleforce:he}],ekloges:e,gov:e,ltd:e,mil:e,net:e,org:e,press:e,pro:e,tm:e}],cz:[1,{contentproxy9:[0,{rsc:t}],realm:t,e4:t,co:t,metacentrum:[0,{cloud:r,custom:t}],muni:[0,{cloud:[0,{flt:t,usr:t}]}]}],de:[1,{bplaced:t,square7:t,com:t,cosidns:ye,dnsupdater:t,"dynamisches-dns":t,"internet-dns":t,"l-o-g-i-n":t,ddnss:[2,{dyn:t,dyndns:t}],"dyn-ip24":t,dyndns1:t,"home-webserver":[2,{dyn:t}],"myhome-server":t,dnshome:t,fuettertdasnetz:t,isteingeek:t,istmein:t,lebtimnetz:t,leitungsen:t,traeumtgerade:t,frusky:r,goip:t,"xn--gnstigbestellen-zvb":t,g\u00FCnstigbestellen:t,"xn--gnstigliefern-wob":t,g\u00FCnstigliefern:t,"hs-heilbronn":[0,{it:[0,{pages:t,"pages-research":t}]}],"dyn-berlin":t,"in-berlin":t,"in-brb":t,"in-butter":t,"in-dsl":t,"in-vpn":t,iservschule:t,"mein-iserv":t,schulplattform:t,schulserver:t,"test-iserv":t,keymachine:t,"git-repos":t,"lcube-server":t,"svn-repos":t,barsy:t,webspaceconfig:t,"123webseite":t,rub:t,"ruhr-uni-bochum":[2,{noc:[0,{io:t}]}],logoip:t,"firewall-gateway":t,"my-gateway":t,"my-router":t,spdns:t,speedpartner:[0,{customer:t}],myspreadshop:t,"taifun-dns":t,"12hp":t,"2ix":t,"4lima":t,"lima-city":t,"dd-dns":t,"dray-dns":t,draydns:t,"dyn-vpn":t,dynvpn:t,"mein-vigor":t,"my-vigor":t,"my-wan":t,"syno-ds":t,"synology-diskstation":t,"synology-ds":t,uberspace:r,"virtual-user":t,virtualuser:t,"community-pro":t,diskussionsbereich:t}],dj:e,dk:[1,{biz:t,co:t,firm:t,reg:t,store:t,"123hjemmeside":t,myspreadshop:t}],dm:ee,do:[1,{art:e,com:e,edu:e,gob:e,gov:e,mil:e,net:e,org:e,sld:e,web:e}],dz:[1,{art:e,asso:e,com:e,edu:e,gov:e,net:e,org:e,pol:e,soc:e,tm:e}],ec:[1,{com:e,edu:e,fin:e,gob:e,gov:e,info:e,k12:e,med:e,mil:e,net:e,org:e,pro:e,base:t,official:t}],edu:[1,{rit:[0,{"git-pages":t}]}],ee:[1,{aip:e,com:e,edu:e,fie:e,gov:e,lib:e,med:e,org:e,pri:e,riik:e}],eg:[1,{ac:e,com:e,edu:e,eun:e,gov:e,info:e,me:e,mil:e,name:e,net:e,org:e,sci:e,sport:e,tv:e}],er:w,es:[1,{com:e,edu:e,gob:e,nom:e,org:e,"123miweb":t,myspreadshop:t}],et:[1,{biz:e,com:e,edu:e,gov:e,info:e,name:e,net:e,org:e}],eu:[1,{airkitapps:t,cloudns:t,dogado:[0,{jelastic:t}],barsy:t,spdns:t,transurl:r,diskstation:t}],fi:[1,{aland:e,dy:t,"xn--hkkinen-5wa":t,h\u00E4kkinen:t,iki:t,cloudplatform:[0,{fi:t}],datacenter:[0,{demo:t,paas:t}],kapsi:t,"123kotisivu":t,myspreadshop:t}],fj:[1,{ac:e,biz:e,com:e,gov:e,info:e,mil:e,name:e,net:e,org:e,pro:e}],fk:w,fm:[1,{com:e,edu:e,net:e,org:e,radio:t,user:r}],fo:e,fr:[1,{asso:e,com:e,gouv:e,nom:e,prd:e,tm:e,avoues:e,cci:e,greta:e,"huissier-justice":e,"en-root":t,"fbx-os":t,fbxos:t,"freebox-os":t,freeboxos:t,goupile:t,"123siteweb":t,"on-web":t,"chirurgiens-dentistes-en-france":t,dedibox:t,aeroport:t,avocat:t,chambagri:t,"chirurgiens-dentistes":t,"experts-comptables":t,medecin:t,notaires:t,pharmacien:t,port:t,veterinaire:t,myspreadshop:t,ynh:t}],ga:e,gb:e,gd:[1,{edu:e,gov:e}],ge:[1,{com:e,edu:e,gov:e,net:e,org:e,pvt:e,school:e}],gf:e,gg:[1,{co:e,net:e,org:e,botdash:t,kaas:t,stackit:t,panel:[2,{daemon:t}]}],gh:[1,{com:e,edu:e,gov:e,mil:e,org:e}],gi:[1,{com:e,edu:e,gov:e,ltd:e,mod:e,org:e}],gl:[1,{co:e,com:e,edu:e,net:e,org:e,biz:t}],gm:e,gn:[1,{ac:e,com:e,edu:e,gov:e,net:e,org:e}],gov:e,gp:[1,{asso:e,com:e,edu:e,mobi:e,net:e,org:e}],gq:e,gr:[1,{com:e,edu:e,gov:e,net:e,org:e,barsy:t,simplesite:t}],gs:e,gt:[1,{com:e,edu:e,gob:e,ind:e,mil:e,net:e,org:e}],gu:[1,{com:e,edu:e,gov:e,guam:e,info:e,net:e,org:e,web:e}],gw:e,gy:ee,hk:[1,{com:e,edu:e,gov:e,idv:e,net:e,org:e,"xn--ciqpn":e,\u4E2A\u4EBA:e,"xn--gmqw5a":e,\u500B\u4EBA:e,"xn--55qx5d":e,\u516C\u53F8:e,"xn--mxtq1m":e,\u653F\u5E9C:e,"xn--lcvr32d":e,\u654E\u80B2:e,"xn--wcvs22d":e,\u6559\u80B2:e,"xn--gmq050i":e,\u7B87\u4EBA:e,"xn--uc0atv":e,\u7D44\u7E54:e,"xn--uc0ay4a":e,\u7D44\u7EC7:e,"xn--od0alg":e,\u7DB2\u7D61:e,"xn--zf0avx":e,\u7DB2\u7EDC:e,"xn--mk0axi":e,\u7EC4\u7E54:e,"xn--tn0ag":e,\u7EC4\u7EC7:e,"xn--od0aq3b":e,\u7F51\u7D61:e,"xn--io0a7i":e,\u7F51\u7EDC:e,inc:t,ltd:t}],hm:e,hn:[1,{com:e,edu:e,gob:e,mil:e,net:e,org:e}],hr:[1,{com:e,from:e,iz:e,name:e,brendly:ne}],ht:[1,{adult:e,art:e,asso:e,com:e,coop:e,edu:e,firm:e,gouv:e,info:e,med:e,net:e,org:e,perso:e,pol:e,pro:e,rel:e,shop:e,rt:t}],hu:[1,{2e3:e,agrar:e,bolt:e,casino:e,city:e,co:e,erotica:e,erotika:e,film:e,forum:e,games:e,hotel:e,info:e,ingatlan:e,jogasz:e,konyvelo:e,lakas:e,media:e,news:e,org:e,priv:e,reklam:e,sex:e,shop:e,sport:e,suli:e,szex:e,tm:e,tozsde:e,utazas:e,video:e}],id:[1,{ac:e,biz:e,co:e,desa:e,go:e,mil:e,my:e,net:e,or:e,ponpes:e,sch:e,web:e,zone:t}],ie:[1,{gov:e,mysprea/* REMOVED_COMMON_BLOCK_2474 */t,dnsupdate:t,"v-info":t}],int:[1,{eu:e}],io:[1,{2038:t,co:e,com:e,edu:e,gov:e,mil:e,net:e,nom:e,org:e,"on-acorn":r,myaddr:t,apigee:t,"b-data":t,beagleboard:t,bitbucket:t,bluebite:t,boxfuse:t,brave:i,browsersafetymark:t,bubble:se,bubbleapps:t,bigv:[0,{uk0:t}],cleverapps:t,cloudbeesusercontent:t,dappnode:[0,{dyndns:t}],darklang:t,definima:t,dedyn:t,"fh-muenster":t,shw:t,forgerock:[0,{id:t}],github:t,gitlab:t,lolipop:t,"hasura-app":t,hostyhosting:t,hypernode:t,moonscale:r,beebyte:D,beebyteapp:[0,{sekd1:t}],jele:t,webthings:t,loginline:t,barsy:t,azurecontainer:r,ngrok:[2,{ap:t,au:t,eu:t,in:t,jp:t,sa:t,us:t}],nodeart:[0,{stage:t}],pantheonsite:t,pstmn:[2,{mock:t}],protonet:t,qcx:[2,{sys:r}],qoto:t,vaporcloud:t,myrdbx:t,"rb-hosting":ae,"on-k3s":r,"on-rio":r,readthedocs:t,resindevice:t,resinstaging:[0,{devices:t}],hzc:t,sandcats:t,scrypted:[0,{client:t}],"mo-siemens":t,lair:ge,stolos:r,musician:t,utwente:t,edugit:t,telebit:t,thingdust:[0,{dev:q,disrec:q,prod:te,testing:q}],tickets:t,webflow:t,webflowtest:t,editorx:t,wixstudio:t,basicserver:t,virtualserver:t}],iq:n,ir:[1,{ac:e,co:e,gov:e,id:e,net:e,org:e,sch:e,"xn--mgba3a4f16a":e,\u0627\u06CC\u0631\u0627\u0646:e,"xn--mgba3a4fra":e,\u0627\u064A\u0631\u0627\u0646:e,arvanedge:t}],is:e,it:[1,{edu:e,gov:e,abr:e,abruzzo:e,"aosta-valley":e,aostavalley:e,bas:e,basilicata:e,cal:e,calabria:e,cam:e,campania:e,"emilia-romagna":e,emiliaromagna:e,emr:e,"friuli-v-giulia":e,"friuli-ve-giulia":e,"friuli-vegiulia":e,"friuli-venezia-giulia":e,"friuli-/* REMOVED_COMMON_BLOCK_703 */,"16-b":t,"32-b":t,"64-b":t,myspreadshop:t,syncloud:t}],je:[1,{co:e,net:e,org:e,of:t}],jm:w,jo:[1,{agri:e,ai:e,com:e,edu:e,eng:e,fm:e,gov:e,mil:e,net:e,org:e,per:e,phd:e,sch:e,tv:e}],jobs:e,jp:[1,{ac:e,ad:e,co:e,ed:e,go:e,gr:e,lg:e,ne:[1,{aseinet:Q,gehirn:t,ivory:t,"mail-box":t,mints:t,mokuren:t,opal:t,sakura:t,sumomo:t,topaz:t}],or:e,aichi:[1,{aisai:e,ama:e,anjo:e,asuke:e,chiryu:e,chita:e,fuso:e,gamagori:e,handa:e,hazu:e,hekinan:e,higashiura:e,ichinomiya:e,inazawa:e,inuyama:e,isshiki:e,iwakura:/* MULTIPLE_REMOVED_BLOCKS */,hino:e,hinode:e,hinohara:e,inagi:e,itabashi:e,katsushika:e,kita:e,kiyose:e,kodaira:e,koganei:e,kokubunji:e,komae:e,koto:e,kouzushima:e,kunitachi:e,machida:e,meguro:e,minato:e,mitaka:e,mizuho:e,musashimurayama:e,musashino:e,nakano:e,nerima:e,ogasawara:e,okutama:e,ome:e,oshima:e,ota:e,setagaya:e,shibuya:e,shinagawa:e,shinjuku:e,suginami:e,sumida:e,tachikawa:e,taito:e,tama:e,toshima:e}],tottori:[1,{chizu:e,hino:e,kawahara:e,koge:e,kotoura:e,misasa:e,nanbu:e,nichinan:e,sakaiminato:e,tottori:e,wakasa:e,yazu:e,yonago:e}],toyama:[1,{asahi:e,fuchu:e,fukumitsu:e,funahashi:e,himi:e,imizu:e,inami:e,johana:e,kamiichi:e,kurobe:e,nakaniikawa:e,namerikawa:e,nanto:e,nyuzen:e,oyabe:e,taira:e,takaoka:e,tateyama:e,toga:e,tonami:e,toyama:e,unazuki:e,uozu:e,yamada:e}],wakayama:[1,{arida:e,aridagawa:e,gobo:e,hashimoto:e,hidaka:e,hirogawa:e,inami:e,iwade:e,kainan:e,kamitonda:e,katsuragi:e,kimino:e,kinokawa:e,kitayama:e,koya:e,koza:e,kozagawa:e,kudoyama:e,kushimoto:e,mihama:e,misato:e,nachikatsuura:e,shingu:e,shirahama:e,taiji:e,tanabe:e,wakayama:e,yuasa:e,yura:e}],yamagata:[1,{asahi:e,funagata:e,higashine:e,iide:e,kahoku:e,kaminoyama:e,kaneyama:e,kawanishi:e,mamurogawa:e,mikawa:e,murayama:e,nagai:e,nakayama:e,nanyo:e,nishikawa:e,obanazawa:e,oe:e,oguni:e,ohkura:e,oishida:e,sagae:e,sakata:e,sakegawa:e,shinjo:e,shirataka:e,shonai:e,takahata:e,tendo:e,tozawa:e,tsuruoka:e,yamagata:e,yamanobe:e,yonezawa:e,yuza:e}],yamaguchi:[1,{abu:e,hagi:e,hikari:e,hofu:e,iwakuni:e,kudamatsu:e,mitou:e,nagato:e,oshima:e,shimonoseki:e,shunan:e,tabuse:e,tokuyama:e,toyota:e,ube:e,yuu:e}],yamanashi:[1,{chuo:e,doshi:e,fuefuki:e,fujikawa:e,fujikawaguchiko:e,fujiyoshida:e,hayakawa:e,hokuto:e,ichikawamisato:e,kai:e,kofu:e,koshu:e,kosuge:e,"minami-alps":e,minobu:e,nakamichi:e,nanbu:e,narusawa:e,nirasaki:e,nishikatsura:e,oshino:e,otsuki:e,showa:e,tabayama:e,tsuru:e,uenohara:e,yamanakako:e,yamanashi:e}],"xn--ehqz56n":e,\u4E09\u91CD:e,"xn--1lqs03n":e,\u4EAC\u90FD:e,"xn--qqqt11m":e,\u4F50\u8CC0:e,"xn--f6qx53a":e,\u5175\u5EAB:e,"xn--djrs72d6uy":e,\u5317\u6D77\u9053:e,"xn--mkru45i":e,\u5343\u8449:e,"xn--0trq7p7nn":e,\u548C\u6B4C\u5C71:e,"xn--5js045d":e,\u57FC\u7389:e,"xn--kbrq7o":e,\u5927\u5206:e,"xn--pssu33l":e,\u5927\u962A:e,"xn--ntsq17g":e,\u5948\u826F:e,"xn--uisz3g":e,\u5BAE\u57CE:e,"xn--6btw5a":e,\u5BAE\u5D0E:e,"xn--1ctwo":e,\u5BCC\u5C71:e,"xn--6orx2r":e,\u5C71\u53E3:e,"xn--rht61e":e,\u5C71\u5F62:e,"xn--rht27z":e,\u5C71\u68A8:e,"xn--nit225k":e,\u5C90\u961C:e,"xn--rht3d":e,\u5CA1\u5C71:e,"xn--djty4k":e,\u5CA9\u624B:e,"xn--klty5x":e,\u5CF6\u6839:e,"xn--kltx9a":e,\u5E83\u5CF6:e,"xn--kltp7d":e,\u5FB3\u5CF6:e,"xn--c3s14m":e,\u611B\u5A9B:e,"xn--vgu402c":e,\u611B\u77E5:e,"xn--efvn9s":e,\u65B0\u6F5F:e,"xn--1lqs71d":e,\u6771\u4EAC:e,"xn--4pvxs":e,\u6803\u6728:e,"xn--uuwu58a":e,\u6C96\u7E04:e,"xn--zbx025d":e,\u6ECB\u8CC0:e,"xn--8pvr4u":e,\u718A\u672C:e,"xn--5rtp49c":e,\u77F3\u5DDD:e,"xn--ntso0iqx3a":e,\u795E\u5948\u5DDD:e,"xn--elqq16h":e,\u798F\u4E95:e,"xn--4it168d":e,\u798F\u5CA1:e,"xn--klt787d":e,\u798F\u5CF6:e,"xn--rny31h":e,\u79CB\u7530:e,"xn--7t0a264c":e,\u7FA4\u99AC:e,"xn--uist22h":e,\u8328\u57CE:e,"xn--8ltr62k":e,\u9577\u5D0E:e,"xn--2m4a15e":e,\u9577\u91CE:e,"xn--32vp30h":e,\u9752\u68EE:e,"xn--4it797k":e,\u9759\u5CA1:e,"xn--5rtq34k":e,\u9999\u5DDD:e,"xn--k7yn95e":e,\u9AD8\u77E5:e,"xn--tor131o":e,\u9CE5\u53D6:e,"xn--d5qv7z876c":e,\u9E7F\u5150\u5CF6:e,kawasaki:w,kitakyushu:w,kobe:w,nagoya:w,sapporo:w,sendai:w,yokohama:w,buyshop:t,fashionstore:t,handcrafted:t,kawaiishop:t,supersale:t,theshop:t,"0/* REMOVED_COMMON_BLOCK_2475 */,zombie:t,hateblo:t,hatenablog:t,hatenadiary:t,"2-d":t,bona:t,crap:t,daynight:t,eek:t,flop:t,halfmoon:t,jeez:t,matrix:t,mimoza:t,netgamers:t,nyanta:t,o0o0:t,rdy:t,rgr:t,rulez:t,sakurastorage:[0,{isk01:xe,isk02:xe}],saloon:t,sblo:t,skr:t,tank:t,"uh-oh":t,undo:t,webaccel:[0,{rs:t,user:t}],websozai:t,xii:t}],ke:[1,{ac:e,co:e,go:e,info:e,me:e,mobi:e,ne:e,or:e,sc:e}],kg:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,us:t}],kh:w,ki:pe,km:[1,{ass:e,com:e,edu:e,gov:e,mil:e,nom:e,org:e,prd:e,tm:e,asso:e,coop:e,gouv:e,medecin:e,notaires:e,pharmaciens:e,presse:e,veterinaire:e}],kn:[1,{edu:e,gov:e,net:e,org:e}],kp:[1,{com:e,edu:e,gov:e,org:e,rep:e,tra:e}],kr:[1,{ac:e,ai:e,co:e,es:e,go:e,hs:e,io:e,it:e,kg:e,me:e,mil:e,ms:e,ne:e,or:e,pe:e,re:e,sc:e,busan:e,chungbuk:e,chungnam:e,daegu:e,daejeon:e,gangwon:e,gwangju:e,gyeongbuk:e,gyeonggi:e,gyeongnam:e,incheon:e,jeju:e,jeonbuk:e,jeonnam:e,seoul:e,ulsan:e,c01:t,"eliv-dns":t}],kw:[1,{com:e,edu:e,emb:e,gov:e,ind:e,net:e,org:e}],ky:J,kz:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,jcloud:t}],la:[1,{com:e,edu:e,gov:e,info:e,int:e,net:e,org:e,per:e,bnr:t}],lb:a,lc:[1,{co:e,com:e,edu:e,gov:e,net:e,org:e,oy:t}],li:e,lk:[1,{ac:e,assn:e,com:e,edu:e,gov:e,grp:e,hotel:e,int:e,ltd:e,net:e,ngo:e,org:e,sch:e,soc:e,web:e}],lr:a,ls:[1,{ac:e,biz:e,co:e,edu:e,gov:e,info:e,net:e,org:e,sc:e}],lt:u,lu:[1,{"123website":t}],lv:[1,{asn:e,com:e,conf:e,edu:e,gov:e,id:e,mil:e,net:e,org:e}],ly:[1,{com:e,edu:e,gov:e,id:e,med:e,net:e,org:e,plc:e,sch:e}],ma:[1,{ac:e,co:e,gov:e,net:e,org:e,press:e}],mc:[1,{asso:e,tm:e}],md:[1,{ir:t}],me:[1,{ac:e,co:e,edu:e,gov:e,its:e,net:e,org:e,priv:e,c66:t,craft:t,edgestack:t,filegear:t,glitch:t,"filegear-sg":t,lohmus:t,barsy:t,mcdir:t,brasilia:t,ddns:t,dnsfor:t,hopto:t,loginto:t,noip:t,webhop:t,soundcast:t,tcp4:t,vp4:t,diskstation:t,dscloud:t,i234:t,myds:t,synology:t,transip:ae,nohost:t}],mg:[1,{co:e,com:e,edu:e,gov:e,mil:e,nom:e,org:e,prd:e}],mh:e,mil:e,mk:[1,{com:e,edu:e,gov:e,inf:e,name:e,net:e,org:e}],ml:[1,{ac:e,art:e,asso:e,com:e,edu:e,gouv:e,gov:e,info:e,inst:e,net:e,org:e,pr:e,presse:e}],mm:w,mn:[1,{edu:e,gov:e,org:e,nyc:t}],mo:a,mobi:[1,{barsy:t,dscloud:t}],mp:[1,{ju:t}],mq:e,mr:u,ms:[1,{com:e,edu:e,gov:e,net:e,org:e,minisite:t}],mt:J,mu:[1,{ac:e,co:e,com:e,gov:e,net:e,or:e,org:e}],museum:e,mv:[1,{aero:e,biz:e,com:e,coop:e,edu:e,gov:e,info:e,int:e,mil:e,museum:e,name:e,net:e,org:e,pro:e}],mw:[1,{ac:e,biz:e,co:e,com:e,coop:e,edu:e,gov:e,int:e,net:e,org:e}],mx:[1,{com:e,edu:e,gob:e,net:e,org:e}],my:[1,{biz:e,com:e,edu:e,gov:e,mil:e,name:e,net:e,org:e}],mz:[1,{ac:e,adv:e,co:e,edu:e,gov:e,mil:e,net:e,org:e}],na:[1,{alt:e,co:e,com:e,gov:e,net:e,org:e}],name:[1,{her:je,his:je}],nc:[1,{asso:e,nom:e}],ne:e,net:[1,{adobeaemcloud:t,"adobeio-static":t,adobeioruntime:t,akadns:t,akamai:t,"akamai-staging":t,akamaiedge:t,"akamaiedge-staging":t,akamaihd:t,"akamaihd-staging":t,akamaiorigin:t,"akamaiorigin-staging":t,akamaized:t,"akamaized-staging":t,edgekey:t,"edgekey-staging":t,edgesuite:t,"edgesuite-staging":t,alwaysdata:t,myamaze:t,cloudfront:t,appudo:t,"atlassian-dev":[0,{prod:se}],myfritz:t,onavstack:t,shopselect:t,blackbaudcdn:t,boomla:t,bplaced:t,square7:t,cdn77:[0,{r:t}],"cdn77-ssl":t,gb:t,hu:t,jp:t,se:t,uk:t,clickrising:t,"ddns-ip":t,"dns-cloud":t,"dns-dynamic":t,cloudaccess:t,cloudflare:[2,{cdn:t}],cloudflareanycast:se,cloudflarecn:se,cloudflareglobal:se,ctfcloud:t,"feste-ip":t,"knx-server":t,"static-access":t,cryptonomic:r,dattolocal:t,mydatto:t,debian:t,definima:t,deno:t,"at-band-camp":t,blogdns:t,"broke-it":t,buyshouses:t,dnsalias:t,dnsdojo:t,"does-it":t,dontexist:t,dynalias:t,dynathome:t,endofinternet:t,"from-az":t,"from-co":t,"from-la":t,"from-ny":t,"gets-it":t,"ham-radio-op":t,homeftp:t,homeip:t,homelinux:t,homeunix:t,"in-the-band":t,"is-a-chef":t,"is-a-geek":t,"isa-geek":t,"kicks-ass":t,"office-on-the":t,podzone:t,"scrapper-site":t,selfip:t,"sells-it":t,servebbs:t,serveftp:t,thruhere:t,webhop:t,casacam:t,dynu:t,dynv6:t,twmail:t,ru:t,channelsdvr:[2,{u:t}],fastly:[0,{freetls:t,map:t,prod:[0,{a:t,global:t}],ssl:[0,{a:t,b:t,global:t}]}],fastlylb:[2,{map:t}],edgeapp:t,"keyword-on":t,"live-on":t,"server-on":t,"cdn-edges":t,heteml:t,cloudfunctions:t,"grafana-dev":t,iobb:t,moonscale:t,"in-dsl":t,"in-vpn":t,oninferno:t,botdash:t,"apps-1and1":t,ipifony:t,cloudjiffy:[2,{"fra1-de":t,"west1-us":t}],elastx:[0,{"jls-sto1":t,"jls-sto2":t,"jls-sto3":t}],massivegrid:[0,{paas:[0,{"fr-1":t,"lon-1":t,"lon-2":t,"ny-1":t,"ny-2":t,"sg-1":t}]}],saveincloud:[0,{jelastic:t,"nordeste-idc":t}],scaleforce:he,kinghost:t,uni5:t,krellian:t,ggff:t,localcert:t,localhostcert:t,localto:r,barsy:t,luyani:t,memset:t,"azure-api":t,"azure-mobile":t,azureedge:t,azurefd:t,azurestaticapps:[2,{1:t,2:t,3:t,4:t,5:t,6:t,7:t,centralus:t,eastasia:t,eastus2:t,westeurope:t,westus2:t}],azurewebsites:t,cloudapp:t,trafficmanager:t,windows:[0,{core:[0,{blob:t}],servicebus:t}],mynetname:[0,{sn:t}],routingthecloud:t,bounceme:t,ddns:t,"eating-organic":t,mydissent:t,myeffect:t,mymediapc:t,mypsx:t,mysecuritycamera:t,nhlfan:t,"no-ip":t,pgafan:t,privatizehealthinsurance:t,redirectme:t,serveblog:t,serveminecraft:t,sytes:t,dnsup:t,hicam:t,"now-dns":t,ownip:t,vpndns:t,cloudycluster:t,ovh:[0,{hosting:r,webpaas:r}],rackmaze:t,myradweb:t,in:t,"subsc-pay":t,squares:t,schokokeks:t,"firewall-gateway":t,seidat:t,senseering:t,siteleaf:t,mafelo:t,myspreadshop:t,"vps-host":[2,{jelastic:[0,{atl:t,njs:t,ric:t}]}],srcf:[0,{soc:t,user:t}],supabase:t,dsmynas:t,familyds:t,ts:[2,{c:r}],torproject:[2,{pages:t}],vusercontent:t,"reserve-online":t,"community-pro":t,meinforum:t,yandexcloud:[2,{storage:t,website:t}],za:t}],nf:[1,{arts:e,com:e,firm:e,info:e,net:e,other:e,per:e,rec:e,store:e,web:e}],ng:[1,{com:e,edu:e,gov:e,i:e,mil:e,mobi:e,name:e,net:e,org:e,sch:e,biz:[2,{co:t,dl:t,go:t,lg:t,on:t}],col:t,firm:t,gen:t,ltd:t,ngo:t,plc:t}],ni:[1,{ac:e,biz:e,co:e,com:e,edu:e,gob:e,in:e,info:e,int:e,mil:e,net:e,nom:e,org:e,web:e}],nl:[1,{co:t,"hosting-cluster":t,gov:t,khplay:t,"123website":t,myspreadshop:t,transurl:r,cistron:t,demon:t}],no:[1,{fhs:e,folkebibl:e,fylkesbibl:e,idrett:e,museum:e,priv:e,vgs:e,dep:e,herad:e,kommune:e,mil:e,stat:e,aa:ie,ah:ie,bu:ie,fm:ie,hl:ie,hm:ie,"jan-mayen":ie,mr:ie,nl:ie,nt:ie,of:ie,ol:ie,oslo:ie,rl:ie,sf:ie,st:ie,svalbard:ie,tm:ie,tr:ie,va:ie,vf:ie,akrehamn:e,"xn--krehamn-dxa":e,\u00E5krehamn:e,algard:e,"xn--lgrd-poac":e,\u00E5lg\u00E5rd:e,arna:e,bronnoysund:e,"xn--brnnysund-m8ac":e,br\u00F8nn\u00F8ysund:e,brumunddal:e,bryne:e,drobak:e,"xn--drbak-wua":e,dr\u00F8bak:e,egersund:e,fetsund:e,floro:e,"xn--flor-jra":e,flor\u00F8:e,fredrikstad:e,hokksund:e,honefoss:e,"xn--hnefoss-q1a":e,h\u00F8nefoss:e,jessheim:e,jorpeland:e,"xn--jrpeland-54a":e,j\u00F8rpeland:e,kirkenes:e,kopervik:e,krokstadelva:e,langevag:e,"xn--langevg-jxa":e,langev\u00E5g:e,leirvik:e,mjondalen:e,"xn--mjndalen-64a":e,mj\u00F8ndalen:e,"mo-i-rana":e,mosjoen:e,"xn--mosjen-eya":e,mosj\u00F8en:e,nesoddtangen:e,orkanger:e,osoyro:e,"xn--osyro-wua":e,os\u00F8yro:e,raholt:e,"xn--rholt-mra":e,r\u00E5holt:e,sandnessjoen:e,"xn--sandnessjen-ogb":e,sandnessj\u00F8en:e,skedsmokorset:e,slattum:e,spjelkavik:e,stathelle:e,stavern:e,stjordalshalsen:e,"xn--stjrdalshalsen-sqb":e,stj\u00F8rdalshalsen:e,tananger:e,tranby:e,vossevangen:e,aarborte:e,aejrie:e,afjord:e,"xn--fjord-lra":e,\u00E5fjord:e,agdenes:e,akershus:Ie,aknoluokta:e,"xn--koluokta-7ya57h":e,\u00E1k\u014Boluokta:e,al:e,"xn--l-1fa":e,\u00E5l:e,alaheadju:e,"xn--laheadju-7ya":e,\u00E1laheadju:e,alesund:e,"xn--lesund-hua":e,\u00E5lesund:e,alstahaug:e,alta:e,"xn--lt-liac":e,\u00E1lt\u00/* REMOVED_COMMON_BLOCK_2476 */n--bearalvhki-y4a":e,bearalv\u00E1hki:e,beardu:e,beiarn:e,berg:e,bergen:e,berlevag:e,"xn--berlevg-jxa":e,berlev\u00E5g:e,bievat:e,"xn--bievt-0qa":e,biev\u00E1t:e,bindal:e,birkenes:e,bjarkoy:e,"xn--bjarky-fya":e,bjark\u00F8y:e,bjerkreim:e,bjugn:e,bodo:e,"xn--bod-2na":e,bod\u00F8:e,bokn:e,bomlo:e,"xn--bmlo-gra":e,b\u00F8mlo:e,bremanger:e,bronnoy:e,"xn--brnny-wuac":e,br\u00F8nn\u00F8y:e,budejju:e,buskerud:Ie,bygland:e,bykle:e,cahcesuolo:e,"xn--hcesuolo-7ya35b":e,\u010D\u00E1hcesuolo:e,davvenjarga:e/* REMOVED_COMMON_BLOCK_731 */tfold-9xa":[0,{"xn--vler-qoa":e}],\u00F8stfold:[0,{v\u00E5ler:e}],"ostre-toten":e,"xn--stre-toten-zcb":e,"\xF8stre-toten":e,overhalla:e,"ovre-eiker":e,"xn--vre-eiker-k8a":e,"\xF8vre-eiker":e,oyer:e,"xn--yer-zna":e,\u00F8yer:e,oygarden:e,"xn--ygarden-p1a":e,\u00F8ygarden:e,"oystre-slidre":e,"xn--ystre-slidre-ujb":e,"\xF8ystre-slidre":e,porsanger:e,porsangu:e,"xn--porsgu-sta26f":e,pors\u00E1\u014Bgu:e,porsgrunn:e,rade:e,"xn--rde-ula":e,r\u00E5de:e,radoy:e,"xn--rady-ira":e,rad\u00F8y:e,"xn--rlingen-mxa":e,r\u00E6lingen:e,rahkkeravju:e,"xn--rhkkervju-01af":e,r\u00E1hkker\u00E1vju:e,raisa:e,"xn--risa-5na":e,r\u00E1isa:e,rakkestad:e,ralingen:e,rana:e,randaberg:e,rauma:e,rendalen:e,rennebu:e,rennesoy:e,"xn--rennesy-v1a":e,rennes\u00F8y:e,rindal:e,ringebu:e,ringerike:e,ringsaker:e,risor:e,"xn--risr-ira":e,ris\u00F8r:e,rissa:e,roan:e,rodoy:e,"xn--rdy-0nab":e,r\u00F8d\u00F8y:e,rollag:e,romsa:e,romskog:e,"xn--rmskog-bya":e,r\u00F8mskog:e,roros:e,"xn--rros-gra":e,r\u00F8ros:e,rost:e,"xn--rst-0na":e,r\u00F8st:e,royken:e,"xn--ryken-vua":e,r\u00F8yken:e,royrvik:e,"xn--ryrvik-bya":e,r\u00F8yrvik:e,ruovat:e,rygge:e,salangen:e,salat:e,"xn--slat-5na":e,s\u00E1lat:e,"xn--slt-elab":e,s\u00E1l\u00E1t:e,saltdal:e,samnanger:e,sandefjord:e,sandnes:e,sandoy:e,"xn--sandy-yua":e,sand\u00F8y:e,sarpsborg:e,sauda:e,sauherad:e,sel:e,selbu:e,selje:e,seljord:e,siellak:e,sigdal:e,siljan:e,sirdal:e,skanit:e,"xn--sknit-yqa":e,sk\u00E1nit:e,skanland:e,"xn--sknland-fxa":e,sk\u00E5nland:e,skaun:e,skedsmo:e,ski:e,skien:e,skierva:e,"xn--skierv-uta":e,skierv\u00E1:e,skiptvet:e,skjak:e,"xn--skjk-soa":e,skj\u00E5k:e,skjervoy:e,"xn--skjervy-v1a":e,skjerv\u00F8y:e,skodje:e,smola:e,"xn--smla-hra":e,sm\u00F8la:e,snaase:e,"xn--snase-nra":e,sn\u00E5ase:e,snasa:e,"xn--snsa-roa":e,sn\u00E5sa:e,snillfjord:e,snoasa:e,sogndal:e,sogne:e,"xn--sgne-gra":e,s\u00F8gne:e,sokndal:e,sola:e,solund:e,somna:e,"xn--smna-gra":e,s\u00F8mna:e,"sondre-land":e,"xn--sndre-land-0cb":e,"s\xF8ndre-land":e,songdalen:e,"sor-aurdal":e,"xn--sr-aurdal-l8a":e,"s\xF8r-aurdal":e,"sor-fron":e,"xn--sr-fron-q1a":e,"s\xF8r-fron":e,"sor-odal":e,"xn--sr-odal-q1a":e,"s\xF8r-odal":e,"sor-varanger":e,"xn--sr-varanger-ggb":e,"s\xF8r-varanger":e,sorfold:e,"xn--srfold-bya":e,s\u00F8rfold:e,sorreisa:e,"xn--srreisa-q1a":e,s\u00F8rreisa:e,sortland:e,sorum:e,"xn--srum-gra":e,s\u00F8rum:e,spydeberg:e,stange:e,stavanger:e,steigen:e,steinkjer:e,stjordal:e,"xn--stjrdal-s1a":e,stj\u00F8rdal:e,stokke:e,"stor-elvdal":e,stord:e,stordal:e,storfjord:e,strand:e,stranda:e,stryn:e,sula:e,suldal:e,sund:e,sunndal:e,surnadal:e,sveio:e,svelvik:e,sykkylven:e,tana:e,telemark:[0,{bo:e,"xn--b-5ga":e,b\u00F8:e}],time:e,tingvoll:e,tinn:e,tjeldsund:e,tjome:e,"xn--tjme-hra":e,tj\u00F8me:e,tokke:e,tolga:e,tonsberg:e,"xn--tnsberg-q1a":e,t\u00F8nsberg:e,torsken:e,"xn--trna-woa":e,tr\u00E6na:e,trana:e,tranoy:e,"xn--trany-yua":e,tran\u00F8y:e,troandin:e,trogstad:e,"xn--trgstad-r1a":e,tr\u00F8gstad:e,tromsa:e,tromso:e,"xn--troms-zua":e,troms\u00F8:e,trondheim:e,trysil:e,tvedestrand:e,tydal:e,tynset:e,tysfjord:e,tysnes:e,"xn--tysvr-vra":e,tysv\u00E6r:e,tysvar:e,ullensaker:e,ullensvang:e,ulvik:e,unjarga:e,"xn--unjrga-rta":e,unj\u00E1rga:e,utsira:e,vaapste:e,vadso:e,"xn--vads-jra":e,vads\u00F8:e,"xn--vry-yla5g":e,v\u00E6r\u00F8y:e,vaga:e,"xn--vg-yiab":e,v\u00E5g\u00E5:e,vagan:e,"xn--vgan-qoa":e,v\u00E5gan:e,vagsoy:e,"xn--vgsy-qoa0j":e,v\u00E5gs\u00F8y:e,vaksdal:e,valle:e,vang:e,vanylven:e,vardo:e,"xn--vard-jra":e,vard\u00F8:e,varggat:e,"xn--vrggt-xqad":e,v\u00E1rgg\u00E1t:e,varoy:e,vefsn:e,vega:e,vegarshei:e,"xn--vegrshei-c0a":e,veg\u00E5rshei:e,vennesla:e,verdal:e,verran:e,vestby:e,vestfold:[0,{sande:e}],vestnes:e,"vestre-slidre":e,"vestre-toten":e,vestvagoy:e,"xn--vestvgy-ixa6o":e,vestv\u00E5g\u00F8y:e,vevelstad:e,vik:e,vikna:e,vindafjord:e,voagat:e,volda:e,voss:e,co:t,"123hjemmeside":t,myspreadshop:t}],np:w,nr:pe,nu:[1,{merseine:t,mine:t,shacknet:t,enterprisecloud:t}],nz:[1,{ac:e,co:e,cri:e,geek:e,gen:e,govt:e,health:e,iwi:e,kiwi:e,maori:e,"xn--mori-qsa":e/* REMOVED_COMMON_BLOCK_2477 */:t,"stuff-4-sale":t,webhop:t,accesscam:t,camdvr:t,freeddns:t,mywire:t,webredirect:t,twmail:t,eu:[2,{al:t,asso:t,at:t,au:t,be:t,bg:t,ca:t,cd:t,ch:t,cn:t,cy:t,cz:t,de:t,dk:t,edu:t,ee:t,es:t,fi:t,fr:t,gr:t,hr:t,hu:t,ie:t,il:t,in:t,int:t,is:t,it:t,jp:t,kr:t,lt:t,lu:t,lv:t,me:t,mk:t,mt:t,my:t,net:t,ng:t,nl:t,no:t,nz:t,pl:t,pt:t,ro:t,ru:t,se:t,si:t,sk:t,tr:t,uk:t,us:t}],fedorainfracloud:t,fedorapeople:t,fedoraproject:[0,{cloud:t,os:z,stg:[0,{os:z}]}],freedesktop:t,hatenadiary:t,hepforge:t,"in-dsl":t,"in-vpn":t,js:t,barsy:t,mayfirst:t,routingthecloud:t,bmoattachments:t,"cable-modem":t,collegefan:t,couchpotatofries:t,hopto:t,mlbfan:t,myftp:t,mysecuritycamera:t,nflfan:t,"no-ip":t,"read-books":t,ufcfan:t,zapto:t,dynserv:t,"now-dns":t,"is-local":t,httpbin:t,pubtls:t,jpn:t,"my-firewall":t,myfirewall:t,spdns:t,"small-web":t,dsmynas:t,familyds:t,teckids:xe,tuxfamily:t,diskstation:t,hk:t,us:t,toolforge:t,wmcloud:t,wmflabs:t,za:t}],pa:[1,{abo:e,ac:e,com:e,edu:e,gob:e,ing:e,med:e,net:e,nom:e,org:e,sld:e}],pe:[1,{com:e,edu:e,gob:e,mil:e,net:e,nom:e,org:e}],pf:[1,{com:e,edu:e,org:e}],pg:w,ph:[1,{com:e,edu:e,gov:e,i:e,mil:e,net:e,ngo:e,org:e,cloudns:t}],pk:[1,{ac:e,biz:e,com:e,edu:e,fam:e,gkp:e,gob:e,gog:e,gok:e,gop:e,gos:e,gov:e,net:e,org:e,web:e}],pl:[1,{com:e,net:e,org:e,agro:e,aid:e,atm:e,auto:e,biz:e,edu:e,gmina:e,gsm:e,info:e,mail:e,media:e,miasta:e,mil:e,nieruchomosci:e,nom:e,pc:e,powiat:e,priv:e,realestate:e,rel:e,sex:e,shop:e,sklep:e,sos:e,szkola:e,targi:e,tm:e,tourism:e,travel:e,turyst/* REMOVED_COMMON_BLOCK_2463 */,gov:e,net:e,org:e}],post:e,pr:[1,{biz:e,com:e,edu:e,gov:e,info:e,isla:e,name:e,net:e,org:e,pro:e,ac:e,est:e,prof:e}],pro:[1,{aaa:e,aca:e,acct:e,avocat:e,bar:e,cpa:e,eng:e,jur:e,law:e,med:e,recht:e,"12chars":t,cloudns:t,barsy:t,ngrok:t}],ps:[1,{com:e,edu:e,gov:e,net:e,org:e,plo:e,sec:e}],pt:[1,{com:e,edu:e,gov:e,int:e,net:e,nome:e,org:e,publ:e,"123paginaweb":t}],pw:[1,{gov:e,cloudns:t,x443:t}],py:[1,{com:e,coop:e,edu:e,gov:e,mil:e,net:e,org:e}],qa:[1,{com:e,edu:e,gov:e,mil:e,name:e,net:e,org:e,sch:e}],re:[1,{asso:e,com:e,netlib:t,can:t}],ro:[1,{arts:e,com:e,firm:e,info:e,nom:e,nt:e,org:e,rec:e,store:e,tm:e,www:e,co:t,shop:t,barsy:t}],rs:[1,{ac:e,co:e,edu:e,gov:e,in:e,org:e,brendly:ne,barsy:t,ox:t}],ru:[1,{ac:t,edu:t,gov:t,int:t,mil:t,eurodir:t,adygeya:t,bashkiria:t,bir:t,cbg:t,com:t,dagestan:t,grozny:t,kalmykia:t,kustanai:t,marine:t,mordovia:t,msk:t,mytis:t,nalchik:t,nov:t,pyatigorsk:t,spb:t,vladikavkaz:t,vladimir:t,na4u:t,mircloud:t,myjino:[2,{hosting:r,landing:r,spectrum:r,vps:r}],cldmail:[0,{hb:t}],mcdir:[2,{vps:t}],mcpre:t,net:t,org:t,pp:t,lk3:t,ras:t}],rw:[1,{ac:e,co:e,coop:e,gov:e,mil:e,net:e,org:e}],sa:[1,{com:e,edu:e,gov:e,med:e,net:e,org:e,pub:e,sch:e}],sb:a,sc:a,sd:[1,{com:e,edu:e,gov:e,info:e,med:e,net:e,org:e,tv:e}],se:[1,{a:e,ac:e,b:e,bd:e,brand:e,c:e,d:e,e,f:e,fh:e,fhsk:e,fhv:e,g:e,h:e,i:e,k:e,komforb:e,kommunalforbund:e,komvux:e,l:e,lanbib:e,m:e,n:e,naturbruksgymn:e,o:e,org:e,p:e,parti:e,pp:e,press:e,r:e,s:e,t:e,tm:e,u:e,w:e,x:e,y:e,z:e,com:t,iopsys:t,"123minsida":t,itcouldbewor:t,myspreadshop:t}],sg:[1,{com:e,edu:e,gov:e,net:e,org:e,enscaled:t}],sh:[1,{com:e,gov:e,mil:e,net:e,org:e,hashbang:t,botda:t,platform:[0,{ent:t,eu:t,us:t}],now:t}],si:[1,{f5:t,gitapp:t,gitpage:t}],sj:e,sk:e,sl:a,sm:e,sn:[1,{art:e,com:e,edu:e,gouv:e,org:e,perso:e,univ:e}],so:[1,{com:e,edu:e,gov:e,me:e,net:e,org:e,surveys:t}],sr:e,ss:[1,{biz:e,co:e,com:e,edu:e,gov:e,me:e,net:e,org:e,sch:e}],st:[1,{co:e,com:e,consulado:e,edu:e,embaixada:e,mil:e,net:e,org:e,principe:e,saotome:e,store:e,helioho:t,kirara:t,noho:t}],su:[1,{abkhazia:t,adygeya:t,aktyubinsk:t,arkhangelsk:t,armenia:t,ashgabad:t,azerbaijan:t,balashov:t,bashkiria:t,bryansk:t,bukhara:t,chimkent:t,dagestan:t,"east-kazakhstan":t,exnet:t,georgia:t,grozny:t,ivanovo:t,jambyl:t,kalmykia:t,kaluga:t,karacol:t,karaganda:t,karelia:t,khakassia:t,krasnodar:t,kurgan:t,kustanai:t,lenug:t,mangyshlak:t,mordovia:t,msk:t,murmansk:t,nalchik:t,navoi:t,"north-kazakhstan":t,nov:t,obninsk:t,penza:t,pokrovsk:t,sochi:t,spb:t,tashkent:t,termez:t,togliatti:t,troitsk:t,tselinograd:t,tula:t,tuva:t,vladikavkaz:t,vladimir:t,vologda:t}],sv:[1,{com:e,edu:e,gob:e,org:e,red:e}],sx:u,sy:n,sz:[1,{ac:e,co:e,org:e}],tc:e,td:e,tel:e,tf:[1,{sch:t}],tg:e,th:[1,{ac:e,co:e,go:e,in:e,mi:e,net:e,or:e,online:t,shop:t}],tj:[1,{ac:e,biz:e,co:e,com:e,edu:e,go:e,gov:e,int:e,mil:e,name:e,net:e,nic:e,org:e,test:e,web:e}],tk:e,tl:u,tm:[1,{co:e,com:e,edu:e,gov:e,mil:e,net:e,nom:e,org:e}],tn:[1,{com:e,ens:e,fin:e,gov:e,ind:e,info:e,intl:e,mincom:e,nat:e,net:e,org:e,perso:e,tourism:e,orangecloud:t}],to:[1,{611:t,com:e,edu:e,gov:e,mil:e,net:e,org:e,oya:t,x0:t,quickconnect:E,vpnplus:t}],tr:[1,{av:e,bbs:e,bel:e,biz:e,com:e,dr:e,edu:e,gen:e,gov:e,info:e,k12:e,kep:e,mil:e,name:e,net:e,org:e,pol:e,tel:e,tsk:e,tv:e,web:e,nc:u}],tt:[1,{biz:e,co:e,com:e,edu:e,gov:e,info:e,mil:e,name:e,net:e,org:e,pro:e}],tv:[1,{"better-than":t,dyndns:t,"on-the-web":t,"worse-than":t,from:t,sakura:t}],tw:[1,{club:e,com:[1,{mymailer:t}],ebiz:e,edu:e,game:e,gov:e,idv:e,mil:e,net:e,org:e,url:t,mydns:t}],tz:[1,{ac:e,co:e,go:e,hotel:e,info:e,me:e,mil:e,mobi:e,ne:e,or:e,sc:e,tv:e}],ua:[1,{com:e,edu:e,gov:e,in:e,net:e,org:e,cherkassy:e,cherkasy:e,chernigov:e,chernihiv:e,chernivtsi:e,chernovtsy:e,ck:e,cn:e,cr:e,crimea:e,cv:e,dn:e,dnepropetrovsk:e,dnipropetrovsk:e,donetsk:e,dp:e,if:e,"ivano-frankivsk":e,kh:e,kharkiv:e,kharkov:e,kherson:e,khmelnitskiy:e,khmelnytskyi:e,kiev:e,kirovograd:e,km:e,kr:e,kropyvnytskyi:e,krym:e,ks:e,kv:e,kyiv:e,lg:e,lt:e,lugansk:e,luhansk:e,lutsk:e,lv:e,lviv:e,mk:e,mykolaiv:e,nikolaev:e,od:e,odesa:e,odessa:e,pl:e,poltava:e,rivne:e,rovno:e,rv:e,sb:e,sebastopol:e,sevastopol:e,sm:e,sumy:e,te:e,ternopil:e,uz:e,uzhgorod:e,uzhhorod:e,vinnica:e,vinnytsia:e,vn:e,volyn:e,yalta:e,zakarpattia:e,zaporizhzhe:e,zaporizhzhia:e,zhitomir:e,zhytomyr:e,zp:e,zt:e,cc:t,inf:t,ltd:t,cx:t,biz:t,co:t,pp:t,v:t}],ug:[1,{ac:e,co:e,com:e,edu:e,go:e,gov:e,mil:e,ne:e,or:e,org:e,sc:e,us:e}],uk:[1,{ac:e,co:[1,{bytemark:[0,{dh:t,vm:t}],layershift:he,barsy:t,barsyonline:t,retrosnub:te,"nh-serv":t,"no-ip":t,adimo:t,myspreadshop:t}],gov:[1,{api:t,campaign:t,service:t}],ltd:e,me:e,net:e,nhs:e,org:[1,{glug:t,lug:t,lugs:t,affinitylottery:t,raffleentry:t,weeklylottery:t}],plc:e,police:e,sch:w,conn:t,copro:t,hosp:t,"independent-commission":t,"independent-inquest":t,"independent-inquiry":t,"independent-panel":t,"independent-review":t,"public-inquiry":t,"royal-commission":t,pymnt:t,barsy:t,nimsite:t,oraclegovcloudapps:r}],us:[1,{dni:e,isa:e,nsn:e,ak:K,al:K,ar:K,as:K,az:K,ca:K,co:K,ct:K,dc:K,de:[1,{cc:e,lib:t}],fl:K,ga:K,gu:K,hi:Qe,ia:K,id:K,il:K,in:K,ks:K,ky:K,la:K,ma:[1,{k12:[1,{chtr:e,paroch:e,pvt:e}],cc:e,lib:e}],md:K,me:K,mi:[1,{k12:e,cc:e,lib:e,"ann-arbor":e,cog:e,dst:e,eaton:e,gen:e,mus:e,tec:e,washtenaw:e}],mn:K,mo:K,ms:K,mt:K,nc:K,nd:Qe,ne:K,nh:K,nj:K,nm:K,nv:K,ny:K,oh:K,ok:K,or:K,pa:K,pr:K,ri:Qe,sc:K,sd:Qe,tn:K,tx:K,ut:K,va:K,vi:K,vt:K,wa:K,wi:K,wv:[1,{cc:e}],wy:K,cloudns:t,"is-by":t,"land-4-sale":t,"stuff-4-sale":t,heliohost:t,enscaled:[0,{phx:t}],mircloud:t,ngo:t,golffan:t,noip:t,pointto:t,freeddns:t,srv:[2,{gh:t,gl:t}],platterp:t,servername:t}],uy:[1,{com:e,edu:e,gub:e,mil:e,net:e,org:e}],uz:[1,{co:e,com:e,net:e,org:e}],va:e,vc:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,gv:[2,{d:t}],"0e":r,mydns:t}],ve:[1,{arts:e,bib:e,co:e,com:e,e12:e,edu:e,emprende:e,firm:e,gob:e,gov:e,info:e,int:e,mil:e,net:e,nom:e,org:e,rar:e,rec:e,store:e,tec:e,web:e}],vg:[1,{edu:e}],vi:[1,{co:e,com:e,k12:e,net:e,org:e}],vn:[1,{ac:e,ai:e,biz:e,com:e,edu:e,gov:e,health:e,id:e,info:e,int:e,io:e,name:e,net:e,org:e,pro:e,angiang:e,bacgiang:e,backan:e,baclieu:e,bacninh:e,"baria-vungtau":e,bentre:e,binhdinh:e,binhduong:e,binhphuoc:e,binhthuan:e,camau:e,cantho:e,caobang:e,daklak:e,daknong:e,danang:e,dienbien:e,dongnai:e,dongthap:e,gialai:e,hagiang:e,haiduong:e,haiphong:e,hanam:e,hanoi:e,hatinh:e,haugiang:e,hoabinh:e,hungyen:e,khanhhoa:e,kiengiang:e,kontum:e,laichau:e,lamdong:e,langson:e,laocai:e,longan:e,namdinh:e,nghean:e,ninhbinh:e,ninhthuan:e,phutho:e,phuyen:e,quangbinh:e,quangnam:e,quangngai:e,quangninh:e,quangtri:e,soctrang:e,sonla:e,tayninh:e,thaibinh:e,thainguyen:e,thanhhoa:e,thanhphohochiminh:e,thuathienhue:e,tiengiang:e,travinh:e,tuyenquang:e,vinhlong:e,vinhphuc:e,yenbai:e}],vu:J,wf:[1,{biz:t,sch:t}],ws:[1,{com:e,edu:e,gov:e,net:e,org:e,advisor:r,cloud66:t,dyndns:t,mypets:t}],yt:[1,{org:t}],"xn--mgbaam7a8h":e,\u0627\u0645\u0627\u0631\u0627\u062A:e,"xn--y9a3aq":e,\u0570\u0561\u0575:e,"xn--54b7fta0cc":e,\u09AC\u09BE\u0982\u09B2\u09BE:e,"xn--90ae":e,\u0431\/* REMOVED_COMMON_BLOCK_2447 */2g2a9gcd":e,\u0B9A\u0BBF\u0B99\u0BCD\u0B95\u0BAA\u0BCD\u0BAA\u0BC2\u0BB0\u0BCD:e,"xn--ogbpf8fl":e,\u0633\u0648\u0631\u064A\u0629:e,"xn--mgbtf8fl":e,\u0633\u0648\u0631\u064A\u0627:e,"xn--o3cw4h":[1,{"xn--o3cyx2a":e,"xn--12co0c3b4eva":e,"xn--m3ch0j3a":e,"xn--h3cuzk1di":e,"xn--12c1fe0br":e,"xn--12cfi8ixb8l":e}],\u0E44\u0E17\u0E22:[1,{\u0E17\u0E2B\u0E32\u0E23:e,\u0E18\u0E38\u0E23\u0E01\u0E34\u0E08:e,\u0E40\u0E19\u0E47\u0E15:e,\u0E23\u0E31\u0E10\u0E1A\u0E32\u0E25:e,\u0E28\u0E36\u0E01\u0E29\u0E32:e,\u0E2D\u0E07\u0E04\u0E4C\u0E01\u0E23:e}],"xn--pgbs0dh":e,\u062A\u0648\u0646\u0633:e,"xn--kpry57d":e,\u53F0\u7063:e,"xn--kprw13d":e,\u53F0\u6E7E:e,"xn--nnx388a":e,\u81FA\u7063:e,"xn--j1amh":e,\u0443\u043A\u0440:e,"xn--mgb2ddes":e,\u0627\u0644\u064A\u0645\u0646:e,xxx:e,ye:n,za:[0,{ac:e,agric:e,alt:e,co:e,edu:e,gov:e,grondar:e,law:e,mil:e,net:e,ngo:e,nic:e,nis:e,nom:e,org:e,school:e,tm:e,web:e}],zm:[1,{ac:e,biz:e,co:e,com:e,edu:e,gov:e,info:e,mil:e,net:e,org:e,sch:e}],zw:[1,{ac:e,co:e,gov:e,mil:e,org:e}],aaa:e,aarp:e,abb:e,abbott:e,abbvie:e,abc:e,able:e,abogado:e,abudhabi:e,academy:[1,{official:t}],accenture:e,accountant:e,accountants:e,aco:e,actor:e,ads:e,adult:e,aeg:e,aetna:e,afl:e,africa:e,agakhan:e,agency:e,aig:e,airbus:e,airforce:e,airtel:e,akdn:e,alibaba:e,alipay:e,allfinanz:e,allstate:e,ally:e,alsace:e,alstom:e,amazon:e,americanexpress:e,americanfamily:e,amex:e,amfam:e,amica:e,amsterdam:e,analytics:e,android:e,anquan:e,anz:e,aol:e,apartments:e,app:[1,{adaptable:t,aiven:t,beget:r,brave:i,clerk:t,clerkstage:t,wnext:t,csb:[2,{preview:t}],convex:t,deta:t,ondigitalocean:t,easypanel:t,encr:t,evervault:o,expo:[2,{staging:t}],edgecompute:t,"on-fleek":t,flutterflow:t,e2b:t,framer:t,hosted:r,run:r,web:t,hasura:t,botdash:t,loginline:t,lovable:t,luyani:t,medusajs:t,messerli:t,netfy:t,netlify:t,ngrok:t,"ngrok-free":t,developer:r,noop:t,northflank:r,upsun:r,replit:s,nyat:t,snowflake:[0,{"*":t,privatelink:r}],streamlit:t,storipress:t,telebit:t,typedream:t,vercel:t,bookonline:t,wdh:t,windsurf:t,zeabur:t,zerops:r}],apple:e,aquarelle:e,arab:e,aramco:e,archi:e,army:e,art:e,arte:e,asda:e,associates:e,athleta:e,attorney:e,auction:e,audi:e,audible:e,audio:e,auspost:e,author:e,auto:e,autos:e,aws:[1,{sagemaker:[0,{"ap-northeast-1":d,"ap-northeast-2":d,"ap-south-1":d,"ap-southeast-1":d,"ap-southeast-2":d,"ca-central-1":p,"eu-central-1":d,"eu-west-1":d,"eu-west-2":d,"us-east-1":p,"us-east-2":p,"us-west-2":p,"af-south-1":l,"ap-east-1":l,"ap-northeast-3":l,"ap-south-2":m,"ap-southeast-3":l,"ap-southeast-4":m,"ca-west-1":[0,{notebook:t,"notebook-fips":t}],"eu-central-2":l,"eu-north-1":l,"eu-south-1":l,"eu-south-2":l,"eu-west-3":l,"il-central-1":l,"me-central-1":l,"me-south-1":l,"sa-east-1":l,"us-gov-east-1":v,"us-gov-west-1":v,"us-west-1":[0,{notebook:t,"notebook-fips":t,studio:t}],experiments:r}],repost:[0,{private:r}],on:[0,{"ap-northeast-1":c,"ap-southeast-1":c,"ap-southeast-2":c,"eu-central-1":c,"eu-north-1":c,"eu-west-1":c,"us-east-1":c,"us-east-2":c,"us-west-2":c}]}],axa:e,azure:e,baby:e,baidu:e,banamex:e,band:e,bank:e,bar:e,barcelona:e,barclaycard:e,barclays:e,barefoot:e,bargains:e,baseball:e,basketball:[1,{aus:t,nz:t}],bauhaus:e,bayern:e,bbc:e,bbt:e,bbva:e,bcg:e,bcn:e,beats:e,beauty:e,beer:e,bentley:e,berlin:e,best:e,bestbuy:e,bet:e,bharti:e,bible:e,bid:e,bike:e,bing:e,bingo:e,bio:e,black:e,blackfriday:e,blockbuster:e,blog:e,bloomberg:e,blue:e,bms:e,bmw:e,bnpparibas:e,boats:e,boehringer:e,bofa:e,bom:e,bond:e,boo:e,book:e,booking:e,bosch:e,bostik:e,boston:e,bot:e,boutique:e,box:e,bradesco:e,bridgestone:e,broadway:e,broker:e,brother:e,brussels:e,build:[1,{v0:t,windsurf:t}],builders:[1,{cloudsite:t}],business:S,buy:e,buzz:e,bzh:e,cab:e,cafe:e,cal:e,call:e,calvinklein:e,cam:e,camera:e,camp:[1,{emf:[0,{at:t}]}],canon:e,capetown:e,capital:e,capitalone:e,car:e,caravan:e,cards:e,care:e,career:e,careers:e,cars:e,casa:[1,{nabu:[0,{ui:t}]}],case:e,cash:e,casino:e,catering:e,catholic:e,cba:e,cbn:e,cbre:e,center:e,ceo:e,cern:e,cfa:e,cfd:e,chanel:e,channel:e,charity:e,chase:e,chat:e,cheap:e,chintai:e,christmas:e,chrome:e,church:e,cipriani:e,circle:e,cisco:e,citadel:e,citi:e,citic:e,city:e,claims:e,cleaning:e,click:e,clinic:e,clinique:e,clothing:e,cloud:[1,{convex:t,elementor:t,encoway:[0,{eu:t}],statics:r,ravendb:t,axarnet:[0,{"es-1":t}],diadem:t,jelastic:[0,{vip:t}],jele:t,"jenv-aruba":[0,{aruba:[0,{eur:[0,{it1:t}]}],it1:t}],keliweb:[2,{cs:t}],oxa:[2,{tn:t,uk:t}],primetel:[2,{uk:t}],reclaim:[0,{ca:t,uk:t,us:t}],trendhosting:[0,{ch:t,de:t}],jotelulu:t,kuleuven:t,laravel:t,linkyard:t,magentosite:r,matlab:t,observablehq:t,perspecta:t,vapor:t,"on-rancher":r,scw:[0,{baremetal:[0,{"fr-par-1":t,"fr-par-2":t,"nl-ams-1":t}],"fr-par":[0,{cockpit:t,fnc:[2,{functions:t}],k8s:M,s3:t,"s3-website":t,whm:t}],instances:[0,{priv:t,pub:t}],k8s:t,"nl-ams":[0,{cockpit:t,k8s:M,s3:t,"s3-website":t,whm:t}],"pl-waw":[0,{cockpit:t,k8s:M,s3:t,"s3-website":t}],scalebook:t,smartlabeling:t}],servebolt:t,onstackit:[0,{runs:t}],trafficplex:t,"unison-services":t,urown:t,voorloper:t,zap:t}],club:[1,{cloudns:t,jele:t,barsy:t}],clubmed:e,coach:e,codes:[1,{owo:r}],coffee:e,college:e,cologne:e,commbank:e,community:[1,{nog:t,ravendb:t,myforum:t}],company:e,compare:e,computer:e,comsec:e,condos:e,construction:e,consulting:e,contact:e,contractors:e,cooking:e,cool:[1,{elementor:t,de:t}],corsica:e,country:e,coupon:e,coupons:e,courses:e,cpa:e,credit:e,creditcard:e,creditunion:e,cricket:e,crown:e,crs:e,cruise:e,cruises:e,cuisinella:e,cymru:e,cyou:e,dad:e,dance:e,data:e,date:e,dating:e,datsun:e,day:e,dclk:e,dds:e,deal:e,dealer:e,deals:e,degree:e,delivery:e,dell:e,deloitte:e,delta:e,democrat:e,dental:e,dentist:e,desi:e,design:[1,{graphic:t,bss:t}],dev:[1,{"12chars":t,myaddr:t,panel:t,lcl:r,lclstage:r,stg:r,stgstage:r,pages:t,r2:t,workers:t,deno:t,"deno-staging":t,deta:t,evervault:o,fly:t,githubpreview:t,gateway:r,hrsn:[2,{psl:[0,{sub:t,wc:[0,{"*":t,sub:r}]}]}],botdash:t,inbrowser:r,"is-a-good":t,"is-a":t,iserv:t,runcontainers:t,localcert:[0,{user:r}],loginline:t,barsy:t,mediatech:t,modx:t,ngrok:t,"ngrok-free":t,"is-a-fullstack":t,"is-cool":t,"is-not-a":t,localplayer:t,xmit:t,"platter-app":t,replit:[2,{archer:t,bones:t,canary:t,global:t,hacker:t,id:t,janeway:t,kim:t,kira:t,kirk:t,odo:t,paris:t,picard:t,pike:t,prerelease:t,reed:t,riker:t,sisko:t,spock:t,staging:t,sulu:t,tarpit:t,teams:t,tucker:t,wesley:t,worf:t}],crm:[0,{d:r,w:r,wa:r,wb:r,wc:r,wd:r,we:r,wf:r}],vercel:t,webhare:r}],dhl:e,diamonds:e,diet:e,digital:[1,{cloudapps:[2,{london:t}]}],direct:[1,{libp2p:t}],directory:e,discount:e,discover:e,dish:e,diy:e,dnp:e,docs:e,doctor:e,dog:e,domains:e,dot:e,download:e,drive:e,dtv:e,dubai:e,dunlop:e,dupont:e,durban:e,dvag:e,dvr:e,earth:e,eat:e,eco:e,edeka:e,education:S,email:[1,{crisp:[0,{on:t}],tawk:W,tawkto:W}],emerck:e,energy:e,engineer:e,engineering:e,enterprises:e,epson:e,equipment:e,ericsson:e,erni:e,esq:e,estate:[1,{compute:r}],eurovision:e,eus:[1,{party:Q}],events:[1,{koobin:t,co:t}],exchange:e,expert:e,exposed:e,express:e,extraspace:e,fage:e,fail:e,fairwinds:e,faith:e,family:e,fan:e,fans:e,farm:[1,{storj:t}],farmers:e,fashion:e,fast:e,fedex:e,feedback:e,ferrari:e,ferrero:e,fidelity:e,fido:e,film:e,final:e,finance:e,financial:S,fire:e,firestone:e,firmdale:e,fish:e,fishing:e,fit:e,fitness:e,flickr:e,flights:e,flir:e,florist:e,flowers:e,fly:e,foo:e,food:e,football:e,ford:e,forex:e,forsale:e,forum:e,foundation:e,fox:e,free:e,fresenius:e,frl:e,frogans:e,frontier:e,ftr:e,fujitsu:e,fun:e,fund:e,furniture:e,futbol:e,fyi:e,gal:e,gallery:e,gallo:e,gallup:e,game:e,games:[1,{pley:t,sheezy:t}],gap:e,garden:e,gay:[1,{pages:t}],gbiz:e,gdn:[1,{cnpy:t}],gea:e,gent:e,genting:e,george:e,ggee:e,gift:e,gifts:e,gives:e,giving:e,glass:e,gle:e,global:[1,{appwrite:t}],globo:e,gmail:e,gmbh:e,gmo:e,gmx:e,godaddy:e,gold:e,goldpoint:e,golf:e,goo:e,goodyear:e,goog:[1,{cloud:t,translate:t,usercontent:r}],google:e,gop:e,got:e,grainger:e,graphics:e,gratis:e,green:e,gripe:e,grocery:e,group:[1,{discourse:t}],gucci:e,guge:e,guide:e,guitars:e,guru:e,hair:e,hamburg:e,hang/* REMOVED_COMMON_BLOCK_2478 */edu:t}],kred:e,kuokgroup:e,kyoto:e,lacaixa:e,lamborghini:e,lamer:e,lancaster:e,land:e,landrover:e,lanxess:e,lasalle:e,lat:e,latino:e,latrobe:e,law:e,lawyer:e,lds:e,lease:e,leclerc:e,lefrak:e,legal:e,lego:e,lexus:e,lgbt:e,lidl:e,life:e,lifeinsurance:e,lifestyle:e,lighting:e,like:e,lilly:e,limited:e,limo:e,lincoln:e,link:[1,{myfritz:t,cyon:t,dweb:r,inbrowser:r,nftstorage:V,mypep:t,storacha:V,w3s:V}],live:[1,{aem:t,hlx:t,ewp:r}],living:e,llc:e,llp:e,loan:e,loans:e,locker:e,locus:e,lol:[1,{omg:t}],london:e,lotte:e,lotto:e,love:e,lpl:e,lplfinancial:e,ltd:e,ltda:e,lundbeck:e,luxe:e,luxury:e,madrid:e,maif:e,maison:e,makeup:e,man:e,management:e,mango:e,map:e,market:e,marketing:e,markets:e,marriott:e,marshalls:e,mattel:e,mba:e,mckinsey:e,med:e,media:Ee,meet:e,melbourne:e,meme:e,memorial:e,men:e,menu:[1,{barsy:t,barsyonline:t}],merck:e,merckmsd:e,miami:e,microsoft:e,mini:e,mint:e,mit:e,mitsubishi:e,mlb:e,mls:e,mma:e,mobile:e,moda:e,moe:e,moi:e,mom:[1,{ind:t}],monash:e,money:e,monster:e,mormon:e,mortgage:e,moscow:e,moto:e,motorcycles:e,mov:e,movie:e,msd:e,mtn:e,mtr:e,music:e,nab:e,nagoya:e,navy:e,nba:e,nec:e,netbank:e,netflix:e,network:[1,{alces:r,co:t,arvo:t,azimuth:t,tlon:t}],neustar:e,new:e,news:[1,{noticeable:t}],next:e,nextdirect:e,nexus:e,nfl:e,ngo:e,nhk:e,nico:e,nike:e,nikon:e,ninja:e,nissan:e,nissay:e,nokia:e,norton:e,now:e,nowruz:e,nowtv:e,nra:e,nrw:e,ntt:e,nyc:e,obi:e,observer:e,office:e,okinawa:e,olayan:e,olayangroup:e,ollo:e,omega:e,one:[1,{kin:r,service:t}],ong:[1,{obl:t}],onl:e,online:[1,{eero:t,"eero-stage":t,websitebuilder:t,barsy:t}],ooo:e,open:e,oracle:e,orange:[1,{tech:t}],organic:e,origins:e,osaka:e,otsuka:e,ott:e,ovh:[1,{nerdpol:t}],page:[1,{aem:t,hlx:t,hlx3:t,translated:t,codeberg:t,heyflow:t,prvcy:t,rocky:t,pdns:t,plesk:t}],panasonic:e,paris:e,pars:e,partners:e,parts:e,party:e,pay:e,pccw:e,pet:e,pfizer:e,pharmacy:e,phd:e,philips:e,phone:e,photo:e,photography:e,photos:Ee,physio:e,pics:e,pictet:e,pictures:[1,{1337:t}],pid:e,pin:e,ping:e,pink:e,pioneer:e,pizza:[1,{ngrok:t}],place:S,play:e,playstation:e,plumbing:e,plus:e,pnc:e,pohl:e,poker:e,politie:e,porn:e,pramerica:e,praxi:e,press:e,prime:e,prod:e,productions:e,prof:e,progressive:e,promo:e,properties:e,property:e,protection:e,pru:e,prudential:e,pub:[1,{id:r,kin:r,barsy:t}],pwc:e,qpon:e,quebec:e,quest:e,racing:e,radio:e,read:e,realestate:e,realtor:e,realty:e,recipes:e,red:e,redstone:e,redumbrella:e,rehab:e,reise:e,reisen:e,reit:e,reliance:e,ren:e,rent:e,rentals:e,repair:e,report:e,republican:e,rest:e,restaurant:e,review:e,reviews:e,rexroth:e,rich:e,richardli:e,ricoh:e,ril:e,rio:e,rip:[1,{clan:t}],rocks:[1,{myddns:t,stackit:t,"lima-city":t,webspace:t}],rodeo:e,rogers:e,room:e,rsvp:e,rugby:e,ruhr:e,run:[1,{appwrite:r,development:t,ravendb:t,liara:[2,{iran:t}],servers:t,build:r,code:r,database:r,migration:r,onporter:t,repl:t,stackit:t,val:[2,{web:t}],wix:t}],rwe:e,ryukyu:e,saarland:e,safe:e,safety:e,sakura:e,sale:e,salon:e,samsclub:e,samsung:e,sandvik:e,sandvikcoromant:e,sanofi:e,sap:e,sarl:e,sas:e,save:e,saxo:e,sbi:e,sbs:e,scb:e,schaeffler:e,schmidt:e,scholarships:e,school:e,schule:e,schwarz:e,science:e,scot:[1,{gov:[2,{service:t}]}],search:e,seat:e,secure:e,security:e,seek:e,select:e,sener:e,services:[1,{loginline:t}],seven:e,sew:e,sex:e,sexy:e,sfr:e,shangrila:e,sharp:e,shell:e,shia:e,shiksha:e,shoes:e,shop:[1,{base:t,hoplix:t,barsy:t,barsyonline:t,shopware:t}],shopping:e,shouji:e,show:e,silk:e,sina:e,singles:e,site:[1,{square:t,canva:f,cloudera:r,convex:t,cyon:t,fastvps:t,figma:t,heyflow:t,jele:t,jouwweb:t,loginline:t,barsy:t,notion:t,omniwe:t,opensocial:t,madethis:t,platformsh:r,tst:r,byen:t,srht:t,novecore:t,cpanel:t,wpsquared:t}],ski:e,skin:e,sky:e,skype:e,sling:e,smart:e,smile:e,sncf:e,soccer:e,social:e,softbank:e,software:e,sohu:e,solar:e,solutions:e,song:e,sony:e,soy:e,spa:e,space:[1,{myfast:t,heiyu:t,hf:[2,{static:t}],"app-ionos":t,project:t,uber:t,xs4all:t}],sport:e,spot:e,srl:e,stada:e,staples:e,star:e,statebank:e,statefarm:e,stc:e,stcgroup:e,stockholm:e,storage:e,store:[1,{barsy:t,sellfy:t,shopware:t,storebase:t}],stream:e,studio:e,study:e,style:e,sucks:e,supplies:e,supply:e,support:[1,{barsy:t}],surf:e,surgery:e,suzuki:e,swatch:e,swiss:e,sydney:e,systems:[1,{knightpoint:t}],tab:e,taipei:e,talk:e,taobao:e,target:e,tatamotors:e,tatar:e,tattoo:e,tax:e,taxi:e,tci:e,tdk:e,team:[1,{discourse:t,jelastic:t}],tech:[1,{cleverapps:t}],technology:S,temasek:e,tennis:e,teva:e,thd:e,theater:e,theatre:e,tiaa:e,tickets:e,tienda:e,tips:e,tires:e,tirol:e,tjmaxx:e,tjx:e,tkmaxx:e,tmall:e,today:[1,{prequalifyme:t}],tokyo:e,tools:[1,{addr:ye,myaddr:t}],top:[1,{ntdll:t,wadl:r}],toray:e,toshiba:e,total:e,tours:e,town:e,toyota:e,toys:e,trade:e,trading:e,training:e,travel:e,travelers:e,travelersinsurance:e,trust:e,trv:e,tube:e,tui:e,tunes:e,tushu:e,tvs:e,ubank:e,ubs:e,unicom:e,university:e,uno:e,uol:e,ups:e,vacations:e,vana:e,vanguard:e,vegas:e,ventures:e,verisign:e,versicherung:e,vet:e,viajes:e,video:e,vig:e,viking:e,villas:e,vin:e,vip:e,virgin:e,visa:e,vision:e,viva:e,vivo:e,vlaanderen:e,vodka:e,volvo:e,vote:e,voting:e,voto:e,voyage:e,wales:e,walmart:e,walter:e,wang:e,wanggou:e,watch:e,watches:e,weather:e,weatherchannel:e,webcam:e,weber:e,website:Ee,wed:e,wedding:e,weibo:e,weir:e,whoswho:e,wien:e,wiki:Ee,williamhill:e,win:e,windows:e,wine:e,winners:e,wme:e,wolterskluwer:e,woodside:e,work:e,works:e,world:e,wow:e,wtc:e,wtf:e,xbox:e,xerox:e,xihuan:e,xin:e,"xn--11b4c3d":e,\u0915\u0949\u092E:e,"xn--1ck2e1b":e,\u30BB\u30FC\u30EB:e,"xn--1qqw23a":e,\u4F5B\u5C/* REMOVED_COMMON_BLOCK_2449 */3E\u0447\u0438:t,\u0441\u043F\u0431:t,\u044F:t}],"xn--pssy2u":e,\u5927\u62FF:e,"xn--q9jyb4c":e,\u307F\u3093\u306A:e,"xn--qcka1pmc":e,\u30B0\u30FC\u30B0\u30EB:e,"xn--rhqv96g":e,\u4E16\u754C:e,"xn--rovu88b":e,\u66F8\u7C4D:e,"xn--ses554g":e,\u7F51\u5740:e,"xn--t60b56a":e,\uB2F7\uB137:e,"xn--tckwe":e,\u30B3\u30E0:e,"xn--tiq49xqyj":e,\u5929\u4E3B\u6559:e,"xn--unup4y":e,\u6E38\u620F:e,"xn--vermgensberater-ctb":e,verm\u00F6gensberater:e,"xn--vermgensberatung-pwb":e,verm\u00F6gensberatung:e,"xn--vhquv":e,\u4F01\u4E1A:e,"xn--vuq861b":e,\u4FE1\u606F:e,"xn--w4r85el8fhu5dnra":e,\u5609\u91CC\u5927\u9152\u5E97:e,"xn--w4rs40l":e,\u5609\u91CC:e,"xn--xhq521b":e,\u5E7F\u4E1C:e,"xn--zfr164b":e,\u653F\u52A1:e,xyz:[1,{botdash:t,telebit:r}],yachts:e,yahoo:e,yamaxun:e,yandex:e,yodobashi:e,yoga:e,yokohama:e,you:e,youtube:e,yun:e,zappos:e,zara:e,zero:e,zip:e,zone:[1,{cloud66:t,triton:r,stackit:t,lima:t}],zuerich:e}]}();var fT=Ed();var b4=hi.alert;function Ea(){try{let e=[ue().EBOOK_BUILDER_URL,ue().EBOOK_VIEWER_URL,ue().SUBTITLE_BUILDER_URL,ue().HTML_VIEWER_URL,ue().PDF_VIEWER_URL].filter(r=>!!r),t=["app."+De];zt()||t.push("localhost:38001");let a=globalThis.location.pathname;return e.find(r=>{let i=new URL(r);return a.startsWith(i.pathname)&&t.includes(i.host)})?!1:globalThis.self!==globalThis.top}catch{return!0}}var eD=fe();var Md,me,Id,y4,ur,Sd,Pd,Fd={},Bd=[],v4=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function Sa(e,t){for(var a in t)e[a]=t[a];return e}function Rd(e){var t=e.parentNode;t&&t.removeChild(e)}function xs(e,t,a,n,r){var i={type:e,props:t,key:a,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:r??++Id};return r==null&&me.vnode!=null&&me.vnode(i),i}function Va(e){return e.children}function Ai(e,t){this.props=e,this.context=t}function En(e,t){if(t==null)return e.__?En(e.__,e.__.__k.indexOf(e)+1):null;for(var a;t<e.__k.length;t++)if((a=e.__k[t])!=null&&a.__e!=null)return a.__e;return typeof e.type=="function"?En(e):null}function _d(e){var t,a;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((a=e.__k[t])!=null&&a.__e!=null){e.__e=e.__c.base=a.__e;break}return _d(e)}}function ws(e){(!e.__d&&(e.__d=!0)&&ur.push(e)&&!ki.__r++||Sd!==me.debounceRendering)&&((Sd=me.debounceRendering)||setTimeout)(ki)}function ki(){for(var e;ki.__r=ur.length;)e=ur.sort(function(t,a){return t.__v.__b-a.__v.__b}),ur=[],e.some(function(t){var a,n,r,i,o,s;t.__d&&(o=(i=(a=t).__v).__e,(s=a.__P)&&(n=[],(r=Sa({},i)).__v=i.__v+1,Od(s,i,r,a.__n,s.ownerSVGElement!==void 0,i.__h!=null?[o]:null,n,o??En(i),i.__h),w4(n,i),i.__e!=o&&_d(i)))})}function jd(e,t,a,n,r,i,o,s,u,c){var l,d,m,p,v,w,S,g=n&&n.__k||Bd,M=g.length;for(a.__k=[],l=0;l<t.length;l++)if((p=a.__k[l]=(p=t[l])==null||typeof p=="boolean"?null:typeof p=="string"||typeof p=="number"||typeof p=="bigint"?xs(null,p,null,null,p):Array.isArray(p)?xs(Va,{children:p},null,null,null):p.__b>0?xs(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)!=null){if(p.__=a,p.__b=a.__b+1,(m=g[l])===null||m&&p.key==m.key&&p.type===m.type)g[l]=void 0;else for(d=0;d<M;d++){if((m=g[d])&&p.key==m.key&&p.type===m.type){g[d]=void 0;break}m=null}Od(e,p,m=m||Fd,r,i,o,s,u,c),v=p.__e,(d=p.ref)&&m.ref!=d&&(S||(S=[]),m.ref&&S.push(m.ref,null,p),S.push(d,p.__c||v,p)),v!=null?(w==null&&(w=v),typeof p.type=="function"&&p.__k===m.__k?p.__d=u=Ld(p,u,e):u=Nd(e,p,m,g,v,u),typeof a.type=="function"&&(a.__d=u)):u&&m.__e==u&&u.parentNode!=e&&(u=En(m))}for(a.__e=w,l=M;l--;)g[l]!=null&&(typeof a.type=="function"&&g[l].__e!=null&&g[l].__e==a.__d&&(a.__d=En(n,l+1)),Ud(g[l],g[l]));if(S)for(l=0;l<S.length;l++)zd(S[l],S[++l],S[++l])}function Ld(e,t,a){for(var n,r=e.__k,i=0;r&&i<r.length;i++)(n=r[i])&&(n.__=e,t=typeof n.type=="function"?Ld(n,t,a):Nd(a,n,n,r,n.__e,t));return t}function Nd(e,t,a,n,r,i){var o,s,u;if(t.__d!==void 0)o=t.__d,t.__d=void 0;else if(a==null||r!=i||r.parentNode==null)e:if(i==null||i.parentNode!==e)e.appendChild(r),o=null;else{for(s=i,u=0;(s=s.nextSibling)&&u<n.length;u+=2)if(s==r)break e;e.insertBefore(r,i),o=i}return o!==void 0?o:r.nextSibling}function x4(e,t,a,n,r){var i;for(i in a)i==="children"||i==="key"||i in t||Ei(e,i,null,a[i],n);for(i in t)r&&typeof t[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||a[i]===t[i]||Ei(e,i,t[i],a[i],n)}function Td(e,t,a){t[0]==="-"?e.setProperty(t,a):e[t]=a==null?"":typeof a!="number"||v4.test(t)?a:a+"px"}function Ei(e,t,a,n,r){var i;e:if(t==="style")if(typeof a=="string")e.style.cssText=a;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)a&&t in a||Td(e.style,t,"");if(a)for(t in a)n&&a[t]===n[t]||Td(e.style,t,a[t])}else if(t[0]==="o"&&t[1]==="n")i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=a,a?n||e.addEventListener(t,i?Cd:Dd,i):e.removeEventListener(t,i?Cd:Dd,i);else if(t!=="dangerouslySetInnerHTML"){if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=a??"";break e}catch{}typeof a=="function"||(a!=null&&(a!==!1||t[0]==="a"&&t[1]==="r")?e.setAttribute(t,a):e.removeAttribute(t))}}function Dd(e){this.l[e.type+!1](me.event?me.event(e):e)}function Cd(e){this.l[e.type+!0](me.event?me.event(e):e)}function Od(e,t,a,n,r,i,o,s,u){var c,l,d,m,p,v,w,S,g,M,f,T,L,E=t.type;if(t.constructor!==void 0)return null;a.__h!=null&&(u=a.__h,s=t.__e=a.__e,t.__h=null,i=[s]),(c=me.__b)&&c(t);try{e:if(typeof E=="function"){if(S=t.props,g=(c=E.contextType)&&n[c.__c],M=c?g?g.props.value:c.__:n,a.__c?w=(l=t.__c=a.__c).__=l.__E:("prototype"in E&&E.prototype.render?t.__c=l=new E(S,M):(t.__c=l=new Ai(S,M),l.constructor=E,l.render=k4),g&&g.sub(l),l.props=S,l.state||(l.state={}),l.context=M,l.__n=n,d=l.__d=!0,l.__h=[]),l.__s==null&&(l.__s=l.state),E.getDerivedStateFromProps!=null&&(l.__s==l.state&&(l.__s=Sa({},l.__s)),Sa(l.__s,E.getDerivedStateFromProps(S,l.__s))),m=l.props,p=l.state,d)E.getDerivedStateFromProps==null&&l.componentWillMount!=null&&l.componentWillMount(),l.componentDidMount!=null&&l.__h.push(l.componentDidMount);else{if(E.getDerivedStateFromProps==null&&S!==m&&l.componentWillReceiveProps!=null&&l.componentWillReceiveProps(S,M),!l.__e&&l.shouldComponentUpdate!=null&&l.shouldComponentUpdate(S,l.__s,M)===!1||t.__v===a.__v){l.props=S,l.state=l.__s,t.__v!==a.__v&&(l.__d=!1),l.__v=t,t.__e=a.__e,t.__k=a.__k,t.__k.forEach(function(x){x&&(x.__=t)}),l.__h.length&&o.push(l);break e}l.componentWillUpdate!=null&&l.componentWillUpdate(S,l.__s,M),l.componentDidUpdate!=null&&l.__h.push(function(){l.componentDidUpdate(m,p,v)})}if(l.context=M,l.props=S,l.__v=t,l.__P=e,f=me.__r,T=0,"prototype"in E&&E.prototype.render)l.state=l.__s,l.__d=!1,f&&f(t),c=l.render(l.props,l.state,l.context);else do l.__d=!1,f&&f(t),c=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++T<25);l.state=l.__s,l.getChildContext!=null&&(n=Sa(Sa({},n),l.getChildContext())),d||l.getSnapshotBeforeUpdate==null||(v=l.getSnapshotBeforeUpdate(m,p)),L=c!=null&&c.type===Va&&c.key==null?c.props.children:c,jd(e,Array.isArray(L)?L:[L],t,a,n,r,i,o,s,u),l.base=t.__e,t.__h=null,l.__h.length&&o.push(l),w&&(l.__E=l.__=null),l.__e=!1}else i==null&&t.__v===a.__v?(t.__k=a.__k,t.__e=a.__e):t.__e=A4(a.__e,t,a,n,r,i,o,u);(c=me.diffed)&&c(t)}catch(x){t.__v=null,(u||i!=null)&&(t.__e=s,t.__h=!!u,i[i.indexOf(s)]=null),me.__e(x,t,a)}}function w4(e,t){me.__c&&me.__c(t,e),e.some(function(a){try{e=a.__h,a.__h=[],e.some(function(n){n.call(a)})}catch(n){me.__e(n,a.__v)}})}function A4(e,t,a,n,r,i,o,s){var u,c,l,d=a.props,m=t.props,p=t.type,v=0;if(p==="svg"&&(r=!0),i!=null){for(;v<i.length;v++)if((u=i[v])&&"setAttribute"in u==!!p&&(p?u.localName===p:u.nodeType===3)){e=u,i[v]=null;break}}if(e==null){if(p===null)return document.createTextNode(m);e=r?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,m.is&&m),i=null,s=!1}if(p===null)d===m||s&&e.data===m||(e.data=m);else{if(i=i&&Md.call(e.childNodes),c=(d=a.props||Fd).dangerouslySetInnerHTML,l=m.dangerouslySetInnerHTML,!s){if(i!=null)for(d={},v=0;v<e.attributes.length;v++)d[e.attributes[v].name]=e.attributes[v].value;(l||c)&&(l&&(c&&l.__html==c.__html||l.__html===e.innerHTML)||(e.innerHTML=l&&l.__html||""))}if(x4(e,m,d,r,s),l)t.__k=[];else if(v=t.props.children,jd(e,Array.isArray(v)?v:[v],t,a,n,r&&p!=="foreignObject",i,o,i?i[0]:a.__k&&En(a,0),s),i!=null)for(v=i.length;v--;)i[v]!=null&&Rd(i[v]);s||("value"in m&&(v=m.value)!==void 0&&(v!==e.value||p==="progress"&&!v||p==="option"&&v!==d.value)&&Ei(e,"value",v,d.value,!1),"checked"in m&&(v=m.checked)!==void 0&&v!==e.checked&&Ei(e,"checked",v,d.checked,!1))}return e}function zd(e,t,a){try{typeof e=="function"?e(t):e.current=t}catch(n){me.__e(n,a)}}function Ud(e,t,a){var n,r;if(me.unmount&&me.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||zd(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(i){me.__e(i,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(r=0;r<n.length;r++)n[r]&&Ud(n[r],t,typeof e.type!="function");a||e.__e==null||Rd(e.__e),e.__=e.__e=e.__d=void 0}function k4(e,t,a){return this.constructor(e,a)}function As(e,t){var a={__c:t="__cC"+Pd++,__:e,Consumer:function(n,r){return n.children(r)},Provider:function(n){var r,i;return this.getChildContext||(r=[],(i={})[t]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(o){this.props.value!==o.value&&r.some(ws)},this.sub=function(o){r.push(o);var s=o.componentWillUnmount;o.componentWillUnmount=function(){r.splice(r.indexOf(o),1),s&&s.call(o)}}),n.children}};return a.Provider.__=a.Consumer.contextType=a}Md=Bd.slice,me={__e:function(e,t,a,n){for(var r,i,o;t=t.__;)if((r=t.__c)&&!r.__)try{if((i=r.constructor)&&i.getDerivedStateFromError!=null&&(r.setState(i.getDerivedStateFromError(e)),o=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,n||{}),o=r.__d),o)return r.__E=r}catch(s){e=s}throw e}},Id=0,y4=function(e){return e!=null&&e.constructor===void 0},Ai.prototype.setState=function(e,t){var a;a=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=Sa({},this.state),typeof e=="function"&&(e=e(Sa({},a),this.props)),e&&Sa(a,e),e!=null&&this.__v&&(t&&this.__h.push(t),ws(this))},Ai.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),ws(this))},Ai.prototype.render=Va,ur=[],ki.__r=0,Pd=0;var E4,qt,ks,qd;var Yd=[],Es=[],Gd=me.__b,Hd=me.__r,Kd=me.diffed,Wd=me.__c,Vd=me.unmount;function S4(){for(var e;e=Yd.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Si),e.__H.__h.forEach(Ss),e.__H.__h=[]}catch(t){e.__H.__h=[],me.__e(t,e.__v)}}me.__b=function(e){typeof e.type!="function"||e.o||e.type===Va?e.o||(e.o=e.__&&e.__.o?e.__.o:""):e.o=(e.__&&e.__.o?e.__.o:"")+(e.__&&e.__.__k?e.__.__k.indexOf(e):0),qt=null,Gd&&Gd(e)},me.__r=function(e){Hd&&Hd(e),E4=0;var t=(qt=e.__c).__H;t&&(ks===qt?(t.__h=[],qt.__h=[],t.__.forEach(function(a){a.__N&&(a.__=a.__N),a.__V=Es,a.__N=a.i=void 0})):(t.__h.forEach(Si),t.__h.forEach(Ss),t.__h=[])),ks=qt},me.diffed=function(e){Kd&&Kd(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(Yd.push(t)!==1&&qd===me.requestAnimationFrame||((qd=me.requestAnimationFrame)||T4)(S4)),t.__H.__.forEach(function(a){a.i&&(a.__H=a.i),a.__V!==Es&&(a.__=a.__V),a.i=void 0,a.__V=Es})),ks=qt=null},me.__c=function(e,t){t.some(function(a){try{a.__h.forEach(Si),a.__h=a.__h.filter(function(n){return!n.__||Ss(n)})}catch(n){t.some(function(r){r.__h&&(r.__h=[])}),t=[],me.__e(n,a.__v)}}),Wd&&Wd(e,t)},me.unmount=function(e){Vd&&Vd(e);var t,a=e.__c;a&&a.__H&&(a.__H.__.forEach(function(n){try{Si(n)}catch(r){t=r}}),a.__H=void 0,t&&me.__e(t,a.__v))};var Qd=typeof requestAnimationFrame=="function";function T4(e){var t,a=function(){clearTimeout(n),Qd&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(a,100);Qd&&(t=requestAnimationFrame(a))}function Si(e){var t=qt,a=e.__c;typeof a=="function"&&(e.__c=void 0,a()),qt=t}function Ss(e){var t=qt;e.__c=e.__(),qt=t}var Zd=["*://*/*","*","*://*"],$d="immersive-translate-wildcard-placeholder.com";function M4(e,t){try{let a=[];if(!t||(t&&!Array.isArray(t)?a=[t]:a=t,a.length===0))return null;if(a.some(s=>Zd.includes(s)))return e;let n=new URL(e);n.hash="",n.search="";let r=n.href,i=n.hostname,o=n.port;if(a&&a.length>0){let s=a.find(u=>{if(!u)return!1;if(u===i)return!0;if(Zd.includes(u))return!0;if(!u.includes("*")&&u.includes("://")){try{let c=new URL(u);if(c.pathname==="/"&&!u.endsWith("/")){let l=c.hostname===i,d=c.port===o;return c.port?l&&d:l}else return P4(r,u)}catch{}return!1}else{let c,l=u;if(u.includes("://")){let S=u.split("://");c=S[0],c==="*"&&S.length>1&&(c="*",u="https://"+S[1])}else c="*",u="https://"+u;let d=u.replace(/\*/g,$d),m;try{m=new URL(d)}catch{return I.debug("invalid match pattern",d,"raw match value:",l),!1}let p=m.host,v=m.pathname;v==="/"&&(l.replace("://","").includes("/")||(v="/*"));let w=I4(c+":",Xd(p),Xd(v));return w?w.test(e):!1}});if(s)return s}return null}catch{return null}}function Xd(e){return e.replaceAll($d,"*")}function I4(e,t,a){let n="^";return e==="*:"?n+="(http:|https:|file:)":n+=e,n+="//",t&&(e==="file:"||(t==="*"?n+="[^/]+?":(t.match(/^\*\./)&&(n+="[^/]*?",t=t.substring(1)),n+=t.replace(/\./g,"\\.").replace(/\*/g,"[^/]*")))),a?a==="*"||a==="/*"?n+="(/.*)?":a.includes("*")?(n+=a.replace(/\*/g,".*?"),n+="/?"):n+=a.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):n+="/?",n+="$",new RegExp(n)}function Sn(e,t){return M4(e,t)!==null}function P4(e,t){let a=new URL(e),n=new URL(t);return a.hostname===n.hostname&&a.pathname===n.pathname&&a.protocol===n.protocol&&a.port===n.port}var Ti=function(){return Ti=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++){t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},Ti.apply(this,arguments)};function Tt(e,t,a,n){function r(i){return i instanceof a?i:new a(function(o){o(i)})}return new(a||(a=Promise))(function(i,o){function s(l){try{c(n.next(l))}catch(d){o(d)}}function u(l){try{c(n.throw(l))}catch(d){o(d)}}function c(l){l.done?i(l.value):r(l.value).then(s,u)}c((n=n.apply(e,t||[])).next())})}function Dt(e,t){var a={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,r,i,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(i=c[0]&2?r.return:c[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,c[1])).done)return i;switch(r=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(i=a.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){a=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){a.label=c[1];break}if(c[0]===6&&a.label<i[1]){a.label=i[1],i=c;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(c);break}i[2]&&a.ops.pop(),a.trys.pop();continue}c=t.call(e,a)}catch(l){c=[6,l],r=0}finally{n=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Ds(e,t,a){if(a||arguments.length===2)for(var n=0,r=t.length,i;n<r;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}var pm="4.5.0";function Ii(e,t){return new Promise(function(a){return setTimeout(a,e,t)})}function F4(){return new Promise(function(e){var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(null)})}function B4(e,t){t===void 0&&(t=1/0);var a=window.requestIdleCallback;return a?new Promise(function(n){return a.call(window,function(){return n()},{timeout:t})}):Ii(Math.min(e,t))}function gm(e){return!!e&&typeof e.then=="function"}function em(e,t){try{var a=e();gm(a)?a.then(function(n){return t(!0,n)},function(n){return t(!1,n)}):t(!0,a)}catch(n){t(!1,n)}}function tm(e,t,a){return a===void 0&&(a=16),Tt(this,void 0,void 0,function(){var n,r,i,o;return Dt(this,function(s){switch(s.label){case 0:n=Array(e.length),r=Date.now(),i=0,s.label=1;case 1:return i<e.length?(n[i]=t(e[i],i),o=Date.now(),o>=r+a?(r=o,[4,F4()]):[3,3]):[3,4];case 2:s.sent(),s.label=3;case 3:return++i,[3,1];case 4:return[2,n]}})})}function lr(e){return e.then(void 0,function(){}),e}function R4(e,t){for(var a=0,n=e.length;a<n;++a)if(e[a]===t)return!0;return!1}function _4(e,t){return!R4(e,t)}function Bs(e){return parseInt(e)}function Ct(e){return parseFloat(e)}function ia(e,t){return typeof e=="number"&&isNaN(e)?t:e}function gt(e){return e.reduce(function(t,a){return t+(a?1:0)},0)}function hm(e,t){if(t===void 0&&(t=1),Math.abs(t)>=1)return Math.round(e/t)*t;var a=1/t;return Math.round(e*a)/a}function j4(e){for(var t,a,n="Unexpected syntax '".concat(e,"'"),r=/^\s*([a-z-]*)(.*)$/i.exec(e),i=r[1]||void 0,o={},s=/([.:#][\w-]+|\[.+?\])/gi,u=function(m,p){o[m]=o[m]||[],o[m].push(p)};;){var c=s.exec(r[2]);if(!c)break;var l=c[0];switch(l[0]){case".":u("class",l.slice(1));break;case"#":u("id",l.slice(1));break;case"[":{var d=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(l);if(d)u(d[1],(a=(t=d[4])!==null&&t!==void 0?t:d[5])!==null&&a!==void 0?a:"");else throw new Error(n);break}default:throw new Error(n)}}return[i,o]}function L4(e){for(var t=new Uint8Array(e.length),a=0;a<e.length;a++){var n=e.charCodeAt(a);if(n>127)return new TextEncoder().encode(e);t[a]=n}return t}function Ta(e,t){var a=e[0]>>>16,n=e[0]&65535,r=e[1]>>>16,i=e[1]&65535,o=t[0]>>>16,s=t[0]&65535,u=t[1]>>>16,c=t[1]&65535,l=0,d=0,m=0,p=0;p+=i+c,m+=p>>>16,p&=65535,m+=r+u,d+=m>>>16,m&=65535,d+=n+s,l+=d>>>16,d&=65535,l+=a+o,l&=65535,e[0]=l<<16|d,e[1]=m<<16|p}function xt(e,t){var a=e[0]>>>16,n=e[0]&65535,r=e[1]>>>16,i=e[1]&65535,o=t[0]>>>16,s=t[0]&65535,u=t[1]>>>16,c=t[1]&65535,l=0,d=0,m=0,p=0;p+=i*c,m+=p>>>16,p&=65535,m+=r*c,d+=m>>>16,m&=65535,m+=i*u,d+=m>>>16,m&=65535,d+=n*c,l+=d>>>16,d&=65535,d+=r*u,l+=d>>>16,d&=65535,d+=i*s,l+=d>>>16,d&=65535,l+=a*c+n*u+r*s+i*o,l&=65535,e[0]=l<<16|d,e[1]=m<<16|p}function Tn(e,t){var a=e[0];t%=64,t===32?(e[0]=e[1],e[1]=a):t<32?(e[0]=a<<t|e[1]>>>32-t,e[1]=e[1]<<t|a>>>32-t):(t-=32,e[0]=e[1]<<t|a>>>32-t,e[1]=a<<t|e[1]>>>32-t)}function pt(e,t){t%=64,t!==0&&(t<32?(e[0]=e[1]>>>32-t,e[1]=e[1]<<t):(e[0]=e[1]<<t-32,e[1]=0))}function Be(e,t){e[0]^=t[0],e[1]^=t[1]}var N4=[4283543511,3981806797],O4=[3301882366,444984403];function am(e){var t=[0,e[0]>>>1];Be(e,t),xt(e,N4),t[1]=e[0]>>>1,Be(e,t),xt(e,O4),t[1]=e[0]>>>1,Be(e,t)}var Di=[2277735313,289559509],Ci=[1291169091,658871167],nm=[0,5],z4=[0,1390208809],U4=[0,944331445];function q4(e,t){var a=L4(e);t=t||0;var n=[0,a.length],r=n[1]%16,i=n[1]-r,o=[0,t],s=[0,t],u=[0,0],c=[0,0],l;for(l=0;l<i;l=l+16)u[0]=a[l+4]|a[l+5]<<8|a[l+6]<<16|a[l+7]<<24,u[1]=a[l]|a[l+1]<<8|a[l+2]<<16|a[l+3]<<24,c[0]=a[l+12]|a[l+13]<<8|a[l+14]<<16|a[l+15]<<24,c[1]=a[l+8]|a[l+9]<<8|a[l+10]<<16|a[l+11]<<24,xt(u,Di),Tn(u,31),xt(u,Ci),Be(o,u),Tn(o,27),Ta(o,s),xt(o,nm),Ta(o,z4),xt(c,Ci),Tn(c,33),xt(c,Di),Be(s,c),Tn(s,31),Ta(s,o),xt(s,nm),Ta(s,U4);u[0]=0,u[1]=0,c[0]=0,c[1]=0;var d=[0,0];switch(r){case 15:d[1]=a[l+14],pt(d,48),Be(c,d);case 14:d[1]=a[l+13],pt(d,40),Be(c,d);case 13:d[1]=a[l+12],pt(d,32),Be(c,d);case 12:d[1]=a[l+11],pt(d,24),Be(c,d);case 11:d[1]=a[l+10],pt(d,16),Be(c,d);case 10:d[1]=a[l+9],pt(d,8),Be(c,d);case 9:d[1]=a[l+8],Be(c,d),xt(c,Ci),Tn(c,33),xt(c,Di),Be(s,c);case 8:d[1]=a[l+7],pt(d,56),Be(u,d);case 7:d[1]=a[l+6],pt(d,48),Be(u,d);case 6:d[1]=a[l+5],pt(d,40),Be(u,d);case 5:d[1]=a[l+4],pt(d,32),Be(u,d);case 4:d[1]=a[l+3],pt(d,24),Be(u,d);case 3:d[1]=a[l+2],pt(d,16),Be(u,d);case 2:d[1]=a[l+1],pt(d,8),Be(u,d);case 1:d[1]=a[l],Be(u,d),xt(u,Di),Tn(u,31),xt(u,Ci),Be(o,u)}return Be(o,n),Be(s,n),Ta(o,s),Ta(s,o),am(o),am(s),Ta(o,s),Ta(s,o),("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)+("00000000"+(s[0]>>>0).toString(16)).slice(-8)+("00000000"+(s[1]>>>0).toString(16)).slice(-8)}function G4(e){var t;return Ti({name:e.name,message:e.message,stack:(t=e.stack)===null||t===void 0?void 0:t.split(`
`)},e)}function H4(e){return/^function\s.*?\{\s*\[native code]\s*}$/.test(String(e))}function K4(e){return typeof e!="function"}function W4(e,t){var a=lr(new Promise(function(n){var r=Date.now();em(e.bind(null,t),function(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];var s=Date.now()-r;if(!i[0])return n(function(){return{error:i[1],duration:s}});var u=i[1];if(K4(u))return n(function(){return{value:u,duration:s}});n(function(){return new Promise(function(c){var l=Date.now();em(u,function(){for(var d=[],m=0;m<arguments.length;m++)d[m]=arguments[m];var p=s+Date.now()-l;if(!d[0])return c({error:d[1],duration:p});c({value:d[1],duration:p})})})})})}));return function(){return a.then(function(n){return n()})}}function V4(e,t,a,n){var r=Object.keys(e).filter(function(o){return _4(a,o)}),i=lr(tm(r,function(o){return W4(e[o],t)},n));return function(){return Tt(this,void 0,void 0,function(){var o,s,u,c,l;return Dt(this,function(d){switch(d.label){case 0:return[4,i];case 1:return o=d.sent(),[4,tm(o,function(m){return lr(m())},n)];case 2:return s=d.sent(),[4,Promise.all(s)];case 3:for(u=d.sent(),c={},l=0;l<r.length;++l)c[r[l]]=u[l];return[2,c]}})})}}function fm(){var e=window,t=navigator;return gt(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])>=4}function Q4(){var e=window,t=navigator;return gt(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in t,"msSaveBlob"in t])>=3&&!fm()}function Pi(){var e=window,t=navigator;return gt(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,t.vendor.indexOf("Google")===0,"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function Mt(){var e=window,t=navigator;return gt(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,t.vendor.indexOf("Apple")===0,"RGBColor"in e,"WebKitMediaKeys"in e])>=4}function Rs(){var e=window,t=e.HTMLElement,a=e.Document;return gt(["safari"in e,!("ongestureend"in e),!("TouchEvent"in e),!("orientation"in e),t&&!("autocapitalize"in t.prototype),a&&"pointerLockElement"in a.prototype])>=4}function cr(){var e=window;return H4(e.print)&&String(e.browser)==="[object WebPageNamespace]"}function bm(){var e,t,a=window;return gt(["buildID"in navigator,"MozAppearance"in((t=(e=document.documentElement)===null||e===void 0?void 0:e.style)!==null&&t!==void 0?t:{}),"onmozfullscreenchange"in a,"mozInnerScreenX"in a,"CSSMozDocumentRule"in a,"CanvasCaptureMediaStream"in a])>=4}function Y4(){var e=window;return gt([!("MediaSettingsRange"in e),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3}function J4(){var e=window;return gt(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3}function dr(){var e=window,t=navigator,a=e.CSS,n=e.HTMLButtonElement;return gt([!("getStorageUpdates"in t),n&&"popover"in n.prototype,"CSSCounterStyleRule"in e,a.supports("font-size-adjust: ex-height 0.5"),a.supports("text-transform: full-width")])>=4}function Z4(){if(navigator.platform==="iPad")return!0;var e=screen,t=e.width/e.height;return gt(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,t>.65&&t<1.53])>=2}function X4(){var e=document;return e.fullscreenElement||e.msFullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement||null}function $4(){var e=document;return(e.exitFullscreen||e.msExitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen).call(e)}function _s(){var e=Pi(),t=bm(),a=window,n=navigator,r="connection";return e?gt([!("SharedWorker"in a),n[r]&&"ontypechange"in n[r],!("sinkId"in new window.Audio)])>=2:t?gt(["onorientationchange"in a,"orientation"in a,/android/i.test(navigator.appVersion)])>=2:!1}function eb(){return nb()?-4:tb()}function tb(){var e=window,t=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!t)return-2;if(ab())return-1;var a=4500,n=5e3,r=new t(1,n,44100),i=r.createOscillator();i.type="triangle",i.frequency.value=1e4;var o=r.createDynamicsCompressor();o.threshold.value=-50,o.knee.value=40,o.ratio.value=12,o.attack.value=0,o.release.value=.25,i.connect(o),o.connect(r.destination),i.start(0);var s=rb(r),u=s[0],c=s[1],l=lr(u.then(function(d){return ib(d.getChannelData(0).subarray(a))},function(d){if(d.name==="timeout"||d.name==="suspended")return-3;throw d}));return function(){return c(),l}}function ab(){return Mt()&&!Rs()&&!J4()}function nb(){return Mt()&&dr()&&cr()}function rb(e){var t=3,a=500,n=500,r=5e3,i=function(){},o=new Promise(function(s,u){var c=!1,l=0,d=0;e.oncomplete=function(v){return s(v.renderedBuffer)};var m=function(){setTimeout(function(){return u(rm("timeout"))},Math.min(n,d+r-Date.now()))},p=function(){try{var v=e.startRendering();switch(gm(v)&&lr(v),e.state){case"running":d=Date.now(),c&&m();break;case"suspended":document.hidden||l++,c&&l>=t?u(rm("suspended")):setTimeout(p,a);break}}catch(w){u(w)}};p(),i=function(){c||(c=!0,d>0&&m())}});return[o,i]}function ib(e){for(var t=0,a=0;a<e.length;++a)t+=Math.abs(e[a]);return t}function rm(e){var t=new Error(e);return t.name=e,t}function ym(e,t,a){var n,r,i;return a===void 0&&(a=50),Tt(this,void 0,void 0,function(){var o,s;return Dt(this,function(u){switch(u.label){case 0:o=document,u.label=1;case 1:return o.body?[3,3]:[4,Ii(a)];case 2:return u.sent(),[3,1];case 3:s=o.createElement("iframe"),u.label=4;case 4:return u.trys.push([4,10,11]),[4,new Promise(function(c,l){var d=!1,m=function(){d=!0,c()},p=function(S){d=!0,l(S)};s.onload=m,s.onerror=p;var v=s.style;v.setProperty("display","block","important"),v.position="absolute",v.top="0",v.left="0",v.visibility="hidden",t&&"srcdoc"in s?s.srcdoc=t:s.src="about:blank",o.body.appendChild(s);var w=function(){var S,g;d||(((g=(S=s.contentWindow)===null||S===void 0?void 0:S.document)===null||g===void 0?void 0:g.readyState)==="complete"?m():setTimeout(w,10))};w()})];case 5:u.sent(),u.label=6;case 6:return!((r=(n=s.contentWindow)===null||n===void 0?void 0:n.document)===null||r===void 0)&&r.body?[3,8]:[4,Ii(a)];case 7:return u.sent(),[3,6];case 8:return[4,e(s,s.contentWindow)];case 9:return[2,u.sent()];case 10:return(i=s.parentNode)===null||i===void 0||i.removeChild(s),[7];case 11:return[2]}})})}function ob(e){for(var t=j4(e),a=t[0],n=t[1],r=document.createElement(a??"div"),i=0,o=Object.keys(n);i<o.length;i++){var s=o[i],u=n[s].join(" ");s==="style"?sb(r.style,u):r.setAttribute(s,u)}return r}function sb(e,t){for(var a=0,n=t.split(";");a<n.length;a++){var r=n[a],i=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(r);if(i){var o=i[1],s=i[2],u=i[4];e.setProperty(o,s,u||"")}}}function ub(){for(var e=window;;){var t=e.parent;if(!t||t===e)return!1;try{if(t.location.origin!==e.location.origin)return!0}catch(a){if(a instanceof Error&&a.name==="SecurityError")return!0;throw a}e=t}}var lb="mmMwWLliI0O&1",cb="48px",Dn=["monospace","sans-serif","serif"],im=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function db(){var e=this;return ym(function(t,a){var n=a.document;return Tt(e,void 0,void 0,function(){var r,i,o,s,u,c,l,d,m,p,v,w;return Dt(this,function(S){for(r=n.body,r.style.fontSize=cb,i=n.createElement("div"),i.style.setProperty("visibility","hidden","important"),o={},s={},u=function(g){var M=n.createElement("span"),f=M.style;return f.position="absolute",f.top="0",f.left="0",f.fontFamily=g,M.textContent=lb,i.appendChild(M),M},c=function(g,M){return u("'".concat(g,"',").concat(M))},l=function(){return Dn.map(u)},d=function(){for(var g={},M=function(E){g[E]=Dn.map(function(x){return c(E,x)})},f=0,T=im;f<T.length;f++){var L=T[f];M(L)}return g},m=function(g){return Dn.some(function(M,f){return g[f].offsetWidth!==o[M]||g[f].offsetHeight!==s[M]})},p=l(),v=d(),r.appendChild(i),w=0;w<Dn.length;w++)o[Dn[w]]=p[w].offsetWidth,s[Dn[w]]=p[w].offsetHeight;return[2,im.filter(function(g){return m(v[g])})]})})})}function mb(){var e=navigator.plugins;if(e){for(var t=[],a=0;a<e.length;++a){var n=e[a];if(n){for(var r=[],i=0;i<n.length;++i){var o=n[i];r.push({type:o.type,suffixes:o.suffixes})}t.push({name:n.name,description:n.description,mimeTypes:r})}}return t}}function pb(){return gb(wb())}function gb(e){var t,a=!1,n,r,i=hb(),o=i[0],s=i[1];return fb(o,s)?(a=bb(s),e?n=r="skipped":(t=yb(o,s),n=t[0],r=t[1])):n=r="unsupported",{winding:a,geometry:n,text:r}}function hb(){var e=document.createElement("canvas");return e.width=1,e.height=1,[e,e.getContext("2d")]}function fb(e,t){return!!(t&&e.toDataURL)}function bb(e){return e.rect(0,0,10,10),e.rect(2,2,6,6),!e.isPointInPath(5,5,"evenodd")}function yb(e,t){vb(e,t);var a=Cs(e),n=Cs(e);if(a!==n)return["unstable","unstable"];xb(e,t);var r=Cs(e);return[r,a]}function vb(e,t){e.width=240,e.height=60,t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(100,1,62,20),t.fillStyle="#069",t.font='11pt "Times New Roman"';var a="Cwm fjordbank gly ".concat("\u{1F603}");t.fillText(a,2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText(a,4,45)}function xb(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var a=0,n=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];a<n.length;a++){var r=n[a],i=r[0],o=r[1],s=r[2];t.fillStyle=i,t.beginPath(),t.arc(o,s,40,0,Math.PI*2,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,Math.PI*2,!0),t.arc(60,60,20,0,Math.PI*2,!0),t.fill("evenodd")}function Cs(e){return e.toDataURL()}function wb(){return Mt()&&dr()&&cr()}function Ab(){var e=navigator,t=0,a;e.maxTouchPoints!==void 0?t=Bs(e.maxTouchPoints):e.msMaxTouchPoints!==void 0&&(t=e.msMaxTouchPoints);try{document.createEvent("TouchEvent"),a=!0}catch{a=!1}var n="ontouchstart"in window;return{maxTouchPoints:t,touchEvent:a,touchStart:n}}function kb(){return navigator.oscpu}function Eb(){var e=navigator,t=[],a=e.language||e.userLanguage||e.browserLanguage||e.systemLanguage;if(a!==void 0&&t.push([a]),Array.isArray(e.languages))Pi()&&Y4()||t.push(e.languages);else if(typeof e.languages=="string"){var n=e.languages;n&&t.push(n.split(","))}return t}function Sb(){return window.screen.colorDepth}function Tb(){return ia(Ct(navigator.deviceMemory),void 0)}function Db(){if(!(Mt()&&dr()&&cr()))return Cb()}function Cb(){var e=screen,t=function(n){return ia(Bs(n),null)},a=[t(e.width),t(e.height)];return a.sort().reverse(),a}var Mb=2500,Ib=10,Mi,Ms;function Pb(){if(Ms===void 0){var e=function(){var t=Ps();Fs(t)?Ms=setTimeout(e,Mb):(Mi=t,Ms=void 0)};e()}}function Fb(){var e=this;return Pb(),function(){return Tt(e,void 0,void 0,function(){var t;return Dt(this,function(a){switch(a.label){case 0:return t=Ps(),Fs(t)?Mi?[2,Ds([],Mi,!0)]:X4()?[4,$4()]:[3,2]:[3,2];case 1:a.sent(),t=Ps(),a.label=2;case 2:return Fs(t)||(Mi=t),[2,t]}})})}}function Bb(){var e=this;if(Mt()&&dr()&&cr())return function(){return Promise.resolve(void 0)};var t=Fb();return function(){return Tt(e,void 0,void 0,function(){var a,n;return Dt(this,function(r){switch(r.label){case 0:return[4,t()];case 1:return a=r.sent(),n=function(i){return i===null?null:hm(i,Ib)},[2,[n(a[0]),n(a[1]),n(a[2]),n(a[3])]]}})})}}function Ps(){var e=screen;return[ia(Ct(e.availTop),null),ia(Ct(e.width)-Ct(e.availWidth)-ia(Ct(e.availLeft),0),null),ia(Ct(e.height)-Ct(e.availHeight)-ia(Ct(e.availTop),0),null),ia(Ct(e.availLeft),null)]}function Fs(e){for(var t=0;t<4;++t)if(e[t])return!1;return!0}function Rb(){return ia(Bs(navigator.hardwareConcurrency),void 0)}function _b(){var e,t=(e=window.Intl)===null||e===void 0?void 0:e.DateTimeFormat;if(t){var a=new t().resolvedOptions().timeZone;if(a)return a}var n=-jb();return"UTC".concat(n>=0?"+":"").concat(n)}function jb(){var e=new Date().getFullYear();return Math.max(Ct(new Date(e,0,1).getTimezoneOffset()),Ct(new Date(e,6,1).getTimezoneOffset()))}function Lb(){try{return!!window.sessionStorage}catch{return!0}}function Nb(){try{return!!window.localStorage}catch{return!0}}function Ob(){if(!(fm()||Q4()))try{return!!window.indexedDB}catch{return!0}}function zb(){return!!window.openDatabase}function Ub(){return navigator.cpuClass}function qb(){var e=navigator.platform;return e==="MacIntel"&&Mt()&&!Rs()?Z4()?"iPad":"iPhone":e}function Gb(){return navigator.vendor||""}function Hb(){for(var e=[],t=0,a=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<a.length;t++){var n=a[t],r=window[n];r&&typeof r=="object"&&e.push(n)}return e.sort()}function Kb(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=e.cookie.indexOf("cookietest=")!==-1;return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch{return!1}}function Wb(){var e=atob;return{abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',e("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",e("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",e("LnNwb25zb3JpdA=="),".ylamainos",e("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi/* REMOVED_COMMON_BLOCK_739 */dmU7Il0="),e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[e("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),e("I2xpdmVyZUFkV3JhcHBlcg=="),e("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),e("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[e("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",e("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),e("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),e("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[e("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),e("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),e("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",e("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),e("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),e("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),e("ZGl2I3NrYXBpZWNfYWQ=")],ro:[e("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),e("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[e("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),e("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),e("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",e("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),e("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",e("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}}function Vb(e){var t=e===void 0?{}:e,a=t.debug;return Tt(this,void 0,void 0,function(){var n,r,i,o,s,u;return Dt(this,function(c){switch(c.label){case 0:return Qb()?(n=Wb(),r=Object.keys(n),i=(u=[]).concat.apply(u,r.map(function(l){return n[l]})),[4,Yb(i)]):[2,void 0];case 1:return o=c.sent(),a&&Jb(n,o),s=r.filter(function(l){var d=n[l],m=gt(d.map(function(p){return o[p]}));return m>d.length*.6}),s.sort(),[2,s]}})})}function Qb(){return Mt()||_s()}function Yb(e){var t;return Tt(this,void 0,void 0,function(){var a,n,r,i,u,o,s,u;return Dt(this,function(c){switch(c.label){case 0:for(a=document,n=a.createElement("div"),r=new Array(e.length),i={},om(n),u=0;u<e.length;++u)o=ob(e[u]),o.tagName==="DIALOG"&&o.show(),s=a.createElement("div"),om(s),s.appendChild(o),n.appendChild(s),r[u]=o;c.label=1;case 1:return a.body?[3,3]:[4,Ii(50)];case 2:return c.sent(),[3,1];case 3:a.body.appendChild(n);try{for(u=0;u<e.length;++u)r[u].offsetParent||(i[e[u]]=!0)}finally{(t=n.parentNode)===null||t===void 0||t.removeChild(n)}return[2,i]}})})}function om(e){e.style.setProperty("visibility","hidden","important"),e.style.setProperty("display","block","important")}function Jb(e,t){for(var a="DOM blockers debug:\n```",n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];a+=`
`.concat(i,":");for(var o=0,s=e[i];o<s.length;o++){var u=s[o];a+=`
  `.concat(t[u]?"\u{1F6AB}":"\u27A1\uFE0F"," ").concat(u)}}}function Zb(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var a=t[e];if(matchMedia("(color-gamut: ".concat(a,")")).matches)return a}}function Xb(){if(sm("inverted"))return!0;if(sm("none"))return!1}function sm(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function $b(){if(um("active"))return!0;if(um("none"))return!1}function um(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}var ey=100;function ty(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=ey;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}}function ay(){if(Cn("no-preference"))return 0;if(Cn("high")||Cn("more"))return 1;if(Cn("low")||Cn("less"))return-1;if(Cn("forced"))return 10}function Cn(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function ny(){if(lm("reduce"))return!0;if(lm("no-preference"))return!1}function lm(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function ry(){if(cm("reduce"))return!0;if(cm("no-preference"))return!1}function cm(e){return matchMedia("(prefers-reduced-transparency: ".concat(e,")")).matches}function iy(){if(dm("high"))return!0;if(dm("standard"))return!1}function dm(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}var we=Math,st=function(){return 0};function oy(){var e=we.acos||st,t=we.acosh||st,a=we.asin||st,n=we.asinh||st,r=we.atanh||st,i=we.atan||st,o=we.sin||st,s=we.sinh||st,u=we.cos||st,c=we.cosh||st,l=we.tan||st,d=we.tanh||st,m=we.exp||st,p=we.expm1||st,v=we.log1p||st,w=function(k){return we.pow(we.PI,k)},S=function(k){return we.log(k+we.sqrt(k*k-1))},g=function(k){return we.log(k+we.sqrt(k*k+1))},M=function(k){return we.log((1+k)/(1-k))/2},f=function(k){return we.exp(k)-1/we.exp(k)/2},T=function(k){return(we.exp(k)+1/we.exp(k))/2},L=function(k){return we.exp(k)-1},E=function(k){return(we.exp(2*k)-1)/(we.exp(2*k)+1)},x=function(k){return we.log(1+k)};return{acos:e(.12312423423423424),acosh:t(1e308),acoshPf:S(1e154),asin:a(.12312423423423424),asinh:n(1),asinhPf:g(1),atanh:r(.5),atanhPf:M(.5),atan:i(.5),sin:o(-1e300),sinh:s(1),sinhPf:f(1),cos:u(10.000000000123),cosh:c(1),coshPf:T(1),tan:l(-1e300),tanh:d(1),tanhPf:E(1),exp:m(1),expm1:p(1),expm1Pf:L(1),log1p:v(10),log1pPf:x(10),powPI:w(-100)}}var sy="mmMwWLliI0fiflO&1",Is={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]};function uy(){return ly(function(e,t){for(var a={},n={},r=0,i=Object.keys(Is);r<i.length;r++){var o=i[r],s=Is[o],u=s[0],c=u===void 0?{}:u,l=s[1],d=l===void 0?sy:l,m=e.createElement("span");m.textContent=d,m.style.whiteSpace="nowrap";for(var p=0,v=Object.keys(c);p<v.length;p++){var w=v[p],S=c[w];S!==void 0&&(m.style[w]=S)}a[o]=m,t.append(e.createElement("br"),m)}for(var g=0,M=Object.keys(Is);g<M.length;g++){var o=M[g];n[o]=a[o].getBoundingClientRect().width}return n})}function ly(e,t){return t===void 0&&(t=4e3),ym(function(a,n){var r=n.document,i=r.body,o=i.style;o.width="".concat(t,"px"),o.webkitTextSizeAdjust=o.textSizeAdjust="none",Pi()?i.style.zoom="".concat(1/n.devicePixelRatio):Mt()&&(i.style.zoom="reset");var s=r.createElement("div");return s.textContent=Ds([],Array(t/20<<0),!0).map(function(){return"word"}).join(" "),i.appendChild(s),e(r,i)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')}function cy(){return navigator.pdfViewerEnabled}function dy(){var e=new Float32Array(1),t=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],t[3]}function my(){var e=window.ApplePaySession;if(typeof e?.canMakePayments!="function")return-1;if(py())return-3;try{return e.canMakePayments()?1:0}catch(t){return gy(t)}}var py=ub;function gy(e){if(e instanceof Error&&e.name==="InvalidAccessError"&&/\bfrom\b.*\binsecure\b/i.test(e.message))return-2;throw e}function hy(){var e,t=document.createElement("a"),a=(e=t.attributionSourceId)!==null&&e!==void 0?e:t.attributionsourceid;return a===void 0?void 0:String(a)}var vm=-1,xm=-2,fy=new Set([10752,2849,2884,2885,2886,2928,2929,2930,2931,2932,2960,2961,2962,2963,2964,2965,2966,2967,2968,2978,3024,3042,3088,3089,3106,3107,32773,32777,32777,32823,32824,32936,32937,32938,32939,32968,32969,32970,32971,3317,33170,3333,3379,3386,33901,33902,34016,34024,34076,3408,3410,3411,3412,3413,3414,3415,34467,34816,34817,34818,34819,34877,34921,34930,35660,35661,35724,35738,35739,36003,36004,36005,36347,36348,36349,37440,37441,37443,7936,7937,7938]),by=new Set([34047,35723,36063,34852,34853,34854,34229,36392,36795,38449]),yy=["FRAGMENT_SHADER","VERTEX_SHADER"],vy=["LOW_FLOAT","MEDIUM_FLOAT","HIGH_FLOAT","LOW_INT","MEDIUM_INT","HIGH_INT"],wm="WEBGL_debug_renderer_info",xy="WEBGL_polygon_mode";function wy(e){var t,a,n,r,i,o,s=e.cache,u=Am(s);if(!u)return vm;if(!Em(u))return xm;var c=km()?null:u.getExtension(wm);return{version:((t=u.getParameter(u.VERSION))===null||t===void 0?void 0:t.toString())||"",vendor:((a=u.getParameter(u.VENDOR))===null||a===void 0?void 0:a.toString())||"",vendorUnmasked:c?(n=u.getParameter(c.UNMASKED_VENDOR_WEBGL))===null||n===void 0?void 0:n.toString():"",renderer:((r=u.getParameter(u.RENDERER))===null||r===void 0?void 0:r.toString())||"",rendererUnmasked:c?(i=u.getParameter(c.UNMASKED_RENDERER_WEBGL))===null||i===void 0?void 0:i.toString():"",shadingLanguageVersion:((o=u.getParameter(u.SHADING_LANGUAGE_VERSION))===null||o===void 0?void 0:o.toString())||""}}function Ay(e){var t=e.cache,a=Am(t);if(!a)return vm;if(!Em(a))return xm;var n=a.getSupportedExtensions(),r=a.getContextAttributes(),i=[],o=[],s=[],u=[],c=[];if(r)for(var l=0,d=Object.keys(r);l<d.length;l++){var m=d[l];o.push("".concat(m,"=").concat(r[m]))}for(var p=mm(a),v=0,w=p;v<w.length;v++){var S=w[v],g=a[S];s.push("".concat(S,"=").concat(g).concat(fy.has(g)?"=".concat(a.getParameter(g)):""))}if(n)for(var M=0,f=n;M<f.length;M++){var T=f[M];if(!(T===wm&&km()||T===xy&&Sy())){var L=a.getExtension(T);if(!L){i.push(T);continue}for(var E=0,x=mm(L);E<x.length;E++){var S=x[E],g=L[S];u.push("".concat(S,"=").concat(g).concat(by.has(g)?"=".concat(a.getParameter(g)):""))}}}for(var k=0,y=yy;k<y.length;k++)for(var j=y[k],_=0,P=vy;_<P.length;_++){var N=P[_],X=ky(a,j,N);c.push("".concat(j,".").concat(N,"=").concat(X.join(",")))}return u.sort(),s.sort(),{contextAttributes:o,parameters:s,shaderPrecisions:c,extensions:n,extensionParameters:u,unsupportedExtensions:i}}function Am(e){if(e.webgl)return e.webgl.context;var t=document.createElement("canvas"),a;t.addEventListener("webglCreateContextError",function(){return a=void 0});for(var n=0,r=["webgl","experimental-webgl"];n<r.length;n++){var i=r[n];try{a=t.getContext(i)}catch{}if(a)break}return e.webgl={context:a},a}function ky(e,t,a){var n=e.getShaderPrecisionFormat(e[t],e[a]);return n?[n.rangeMin,n.rangeMax,n.precision]:[]}function mm(e){var t=Object.keys(e.__proto__);return t.filter(Ey)}function Ey(e){return typeof e=="string"&&!e.match(/[^A-Z0-9_x]/)}function km(){return bm()}function Sy(){return Pi()||Mt()}function Em(e){return typeof e.getParameter=="function"}function Ty(){var e,t=_s()||Mt();return t?window.AudioContext&&(e=new AudioContext().baseLatency)!==null&&e!==void 0?e:-1:-2}var Dy={fonts:db,domBlockers:Vb,fontPreferences:uy,audio:eb,screenFrame:Bb,canvas:pb,osCpu:kb,languages:Eb,colorDepth:Sb,deviceMemory:Tb,screenResolution:Db,hardwareConcurrency:Rb,timezone:_b,sessionStorage:Lb,localStorage:Nb,indexedDB:Ob,openDatabase:zb,cpuClass:Ub,platform:qb,plugins:mb,touchSupport:Ab,vendor:Gb,vendorFlavors:Hb,cookiesEnabled:Kb,colorGamut:Zb,invertedColors:Xb,forcedColors:$b,monochrome:ty,contrast:ay,reducedMotion:ny,reducedTransparency:ry,hdr:iy,math:oy,pdfViewerEnabled:cy,architecture:dy,applePay:my,privateClickMeasurement:hy,audioBaseLatency:Ty,webGlBasics:wy,webGlExtensions:Ay};function Cy(e){return V4(Dy,e,[])}var My="$ if upgrade to Pro: https://fpjs.dev/pro";function Iy(e){var t=Py(e),a=Fy(t);return{score:t,comment:My.replace(/\$/g,"".concat(a))}}function Py(e){if(_s())return .4;if(Mt())return Rs()&&!(dr()&&cr())?.5:.3;var t="value"in e.platform?e.platform.value:"";return/^Win/.test(t)?.6:/^Mac/.test(t)?.5:.7}function Fy(e){return hm(.99+.01*e,1e-4)}function By(e){for(var t="",a=0,n=Object.keys(e).sort();a<n.length;a++){var r=n[a],i=e[r],o="error"in i?"error":JSON.stringify(i.value);t+="".concat(t?"|":"").concat(r.replace(/([:|\\])/g,"\\$1"),":").concat(o)}return t}function Ry(e){return JSON.stringify(e,function(t,a){return a instanceof Error?G4(a):a},2)}function Sm(e){return q4(By(e))}function _y(e){var t,a=Iy(e);return{get visitorId(){return t===void 0&&(t=Sm(this.components)),t},set visitorId(n){t=n},confidence:a,components:e,version:pm}}function jy(e){return e===void 0&&(e=50),B4(e,e*2)}function Ly(e,t){var a=Date.now();return{get:function(n){return Tt(this,void 0,void 0,function(){var r,i,o;return Dt(this,function(s){switch(s.label){case 0:return r=Date.now(),[4,e()];case 1:return i=s.sent(),o=_y(i),t||n?.debug,[2,o]}})})}}}function Ny(){if(!(window.__fpjs_d_m||Math.random()>=.001))try{var e=new XMLHttpRequest;e.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(pm,"/npm-monitoring"),!0),e.send()}catch{}}function Oy(e){var t;return e===void 0&&(e={}),Tt(this,void 0,void 0,function(){var a,n,r;return Dt(this,function(i){switch(i.label){case 0:return(!((t=e.monitoring)!==null&&t!==void 0)||t)&&Ny(),a=e.delayFallback,n=e.debug,[4,jy(a)];case 1:return i.sent(),r=Cy({cache:{},debug:n}),[2,Ly(r,n)]}})})}var js={load:Oy,hashComponents:Sm,componentsToDebugString:Ry};async function oa(){let e=await Ne("fakeUserId","");e||(e=await Ha("fakeUserId",""),e&&await Se("fakeUserId",e));let t=new Date,a=await Ne("installedAt","");return a||(a=await Ha("installedAt",""),a&&await Se("installedAt",a)),e?a||(a=new Date(0).toISOString(),await Se("installedAt",a)):(e=await qy(64),await Se("fakeUserId",e)),a||(a=t.toISOString(),await Se("installedAt",a)),{fakeUserId:e,installedAt:a}}var zy=10;async function Gt(){let e=await Ne("userTag","");if(e)return e;let{fakeUserId:t}=await oa(),n=t.charCodeAt(0)%zy;return e=String.fromCharCode(n+"a".charCodeAt(0)),await Se("userTag",e),e}function Uy(e){let t="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=a.length,r=0;for(;r<e;)t+=a.charAt(Math.floor(Math.random()*n)),r+=1;return t}async function qy(e){try{let a=await(await js.load()).get(),n=new Date().getTime(),r=Math.random().toString(36).substring(2,15),i=`${a.visitorId}-${n}-${r}`;return crypto?.subtle?.digest?crypto.subtle.digest("SHA-256",new TextEncoder().encode(i)).then(o=>Array.from(new Uint8Array(o)).map(c=>c.toString(16).padStart(2,"0")).join("").substring(0,e)):Gy(i,e)}catch{return Uy(64)}}function Gy(e,t=32){let a="";for(let r=0;r<e.length;r++)a+=(e.charCodeAt(r)*(r+1)).toString(36);a=a.repeat(Math.ceil(t/a.length)).substring(0,t);let n="";for(let r=0;r<t;r++){let i=Math.floor(Math.random()*a.length);n+=a[i]}return n}async function mr(){return await Ne("abGroup","")}async function Qa(){return await Ne("campaign","")}async function Tm(){return await Ne("installedAt","")}function pr(e){return $n()?"imtAndroid":yn()?"imtIOS":fe(e)?"userscript":Ze()?"safari":qa()?"firefox":na()?"chrome":"other"}function Dm(e){return pr(e)+"_"+(kt().any?"mobile":"pc")}var Hy={initial:0,buildContainer:0,consumeContainer:0,consumeParagraph:0,parseParagraph:0,translated:0,inserted:0},xC={...Hy};function Ht(e,t){let a=Mn(e),n=Mn(t);return a>=n}function Mn(e){let t=e.split(".").reverse(),a=0,n=1;for(let r=0;r<3;r++)a+=n*Number(t[r]||"0"),n*=100;return a}function Cm(e,t,a){if(!a||!e||Object.keys(e).length===0)return e;let n=t.translationServices||{},r=Object.values(n).filter(o=>o?.group==="pro").map(o=>o?.type),i=Object.values(n).filter(o=>o?.group==="free").map(o=>o?.type);return e.translationServices&&Object.keys(e.translationServices).forEach(o=>{if(o==="zhipu-pro"||o.startsWith("mock")||!e.translationServices)return;let s=e.translationServices?.[o];if(!s)return;let u=s?.provider==="custom";if(s={...s},delete s.provider,s.type==="custom-ai"){e.translationServices[o]={...s,group:"custom"};return}let c=mi[o];if(!c)return;let l=c.allProps?.filter(g=>g.sensitive===!0).map(g=>g.name)||[],d=i.includes(o),m=!1;if(d&&!e.translationServices[`${o}-free`]){let g=JSON.parse(JSON.stringify(s));l.forEach(M=>{delete g[M]}),delete g.name,m=!!(g.model&&n[o]?.freeModels?.includes(g.model)),!m&&g.model&&delete g.model,Object.keys(g).length>0&&(e.translationServices[`${o}-free`]={...g,migrateFrom:o})}let p=r.includes(o);if(p&&!e.translationServices[`${o}-pro`]){let g=JSON.parse(JSON.stringify(s));l.forEach(M=>{delete g[M]}),delete g.name,Object.keys(g).length>0&&(e.translationServices[`${o}-pro`]={...g,migrateFrom:o})}let v=l.some(g=>s[g]),w=!p&&!(d&&m)||v||u,S=e.translationServices[`${o}-custom`];w&&!S?.extends&&o!=="ai"&&(e.translationServices[`${o}-custom`]={...s,migrateFrom:o,extends:o,visible:!0,group:"custom",type:o})}),e}function Mm(e,t){e.userTranslationServices={...e.userTranslationServices};for(let a of Lo){let n=e[a];t?n&&!e.userTranslationServices[a]&&(e.userTranslationServices[a]=Ky(n,e)):e.userTranslationServices[a]=n}for(let a of Lo){let n=e.userTranslationServices[a];n&&(e[a]=n)}return e}function Ky(e,t){if(e==="inherit"||e.startsWith("mock")||e==="zhipu-pro"||t.translationServices?.[e]?.group)return e;let a=t.translationServices?.[e];return!a||a.type==="custom-ai"?e:Wy(e,t)==="pro"?`${e}-pro`:Qy(e,t)?`${e}-free`:`${e}-custom`}function Wy(e,t){let a=t.translationServices?.[e];return a?.provider?a?.provider:"custom"}function Vy(e,t){return Object.values(t.translationServices||{}).filter(n=>n?.group==="free").map(n=>n?.type).includes(e)}function Qy(e,t){if(!Vy(e,t))return!1;let n=t.translationServices?.[e];return n.model?n?.freeModels?.includes(n.model):!0}function Im(e,t){let a=e.translationServices?.[t],n=Re.bind(null,e.interfaceLanguage),r=a?.type||t,i=`translationServices.${r}`,o=n(i);return o==i&&(o=""),a?.name||o||r}function Ls(e,t){let a=e.translationServices?.[t];return a?.group==="pro"||a?.group==="max"}function Fi(e){return e.prompt?.trim().length>0}function Ns(e){if(Math.abs(e)<1024)return e+" Byte";let a=["KB","MB","GB","TB","PB","EB","ZB","YB"],n=-1;do e/=1024,++n;while(Math.abs(e)>=1024&&n<a.length-1);return e.toFixed(1)+" "+a[n]}function Pm(){let e=Xn();return Ht(e,"1.18.2")}var In=[],Jy=1e3*3600*24;async function Kt(e,t){return await new Promise((a,n)=>{let r=e,i=1,o=indexedDB.open(r,i);o.onsuccess=s=>{a(o.result)},o.onerror=s=>{n()},o.onupgradeneeded=s=>{let u=o.result;(t||["cache"]).forEach(l=>{u.objectStoreNames.contains(l)||u.createObjectStore(l,{keyPath:"key"})})}})}async function Os(e){let t=`${de}-${e.service}@${e.from}->${e.to}`;return await qs(t,{...e,createAt:new Date().getTime()})}async function zs(e){let t=Ac(e.originalText),a=`${de}-${e.service}@${e.from}->${e.to}`;return await Us(a,t)}async function Us(e,t){let a=await Kt(e);return await new Promise((n,r)=>{if(!a)return r();let i="cache",s=a.transaction([i],"readonly").objectStore(i).get(t);s.onsuccess=u=>{a.close();let c=s.result;n(c)},s.onerror=u=>{a.close(),r()}})}async function qs(e,t){let a=await Kt(e);return(await Gs()).includes(e)||await Zy(e),await new Promise(r=>{if(!a)return r(!1);let i="cache",s=a.transaction([i],"readwrite").objectStore(i).put(t);s.onsuccess=u=>{a.close(),r(!0)},s.onerror=u=>{a.close(),r(!1)}})}async function Zy(e){let t="cache_list",a=await Kt(de+"-cacheList",["cache_list"]),r=a.transaction([t],"readwrite").objectStore(t).put({key:e});r.onsuccess=i=>{a.close(),In.push(e)},r.onerror=i=>{a.close()}}async function Gs(){if(In&&In.length>0)return In;let e=await Kt(de+"-cacheList",["cache_list"]);return In=await new Promise(t=>{let a="cache_list",r=e.transaction([a],"readonly").objectStore(a).getAllKeys();r.onsuccess=i=>{e.close(),t(r.result)},r.onerror=i=>{e.close(),t([])}}),In}async function Fm(){try{let e=[];(await Gs()).forEach(n=>{e.push(Xy(n))});let a=await Promise.all(e);return Ns(a.reduce((n,r)=>n+r,0))}catch{return Ns(0)}}async function Xy(e){let t=await Kt(e),n=[...t.objectStoreNames].reduce((o,s)=>{let u=$y(t,s);return o.push(u),o},[]),r=await Promise.all(n);return t.close(),r.reduce((o,s)=>o+s,0)}async function $y(e,t){return await new Promise((a,n)=>{try{if(e==null)return n();let r=0,i=e.transaction([t]).objectStore(t).openCursor();i.onsuccess=o=>{try{let s=i.result;if(s){let u=s.value,c=JSON.stringify(u);r+=c.length,s.continue()}else a(r)}catch(s){n(s)}},i.onerror=o=>n("error in "+t+": "+o)}catch(r){n(r)}})}async function Bm(e,t){let a=new Date().getTime(),n=await Gs(),r=0;for(let s of n)await i(s),await new Promise(u=>setTimeout(u,0));return r;async function i(s){let u=await Kt(s);try{let c=Array.from(u.objectStoreNames);for(let l of c)await o(u,l)}finally{u.close()}}async function o(s,u){let c=s.transaction(u,"readwrite"),l=c.objectStore(u);return await new Promise((d,m)=>{let p=l.openCursor(),v=0,w=100;p.onsuccess=async function(S){try{let g=S.target.result;if(g){let M=g.value,f;M.createAt?f=M.createAt:f=t,(a-f)/Jy>e&&(r++,g.delete()),v++,v%w===0&&await new Promise(L=>setTimeout(L,0)),g.continue()}else d()}catch(g){m(g)}},p.onerror=m}),await new Promise((d,m)=>{c.oncomplete=d,c.onerror=m}),r}}async function Pn(e,t){if(t){if(!A?.storage?.local)return Promise.resolve(!1);try{let{id:a,glossaries:n,...r}=e,i={...r,id:a},o=`term_meta_${a}`,s=`term_glossary_${a}`,u={[o]:i};return n&&n.length>0&&(u[s]=n),await A.storage.local.set(u),Promise.resolve(!0)}catch{return Promise.resolve(!1)}}else{let a=await Pn(e,!0);if(a)return a;try{let{id:n,glossaries:r,...i}=e,o=await Kt(`${de}-terms`,["meta","glossaries"]);return new Promise((s,u)=>{let c=o.transaction(["meta","glossaries"],"readwrite"),l=c.objectStore("meta"),d=c.objectStore("glossaries"),m=l.put({key:n,id:n,...i});m.onsuccess=()=>{if(r&&r.length>0){let p=d.put({key:n,glossaries:r});p.onerror=v=>{}}},m.onerror=p=>{},c.oncomplete=()=>{s(!0),o.close()},c.onerror=p=>{u(new Error("Transaction failed: "+p.target?.error)),o.close()},c.onabort=p=>{u(new Error("Transaction aborted: "+(p.target?.error||"Unknown reason"))),o.close()}})}catch{return!1}}}async function gr(e,t,a=!1){if(t){if(!A?.storage?.local)return Promise.resolve(!1);try{let n=`term_meta_${e}`,r=`term_glossary_${e}`;return await A.storage.local.remove([n,r]),Promise.resolve(!0)}catch{return Promise.resolve(!1)}}else{a||await gr(e,!0);let n=await Kt(`${de}-terms`,["meta","glossaries"]);return new Promise((r,i)=>{let o=n.transaction(["meta","glossaries"],"readwrite"),s=o.objectStore("meta"),u=o.objectStore("glossaries"),c=s.delete(e);c.onsuccess=()=>{let l=u.delete(e);l.onsuccess=()=>{r(!0),n.close()},l.onerror=d=>{i(d),n.close()}},c.onerror=l=>{i(l),n.close()}})}}async function hr(e,t){if(t){if(!A?.storage?.local)return null;try{let a=`term_meta_${e}`,n=`term_glossary_${e}`,r=await A.storage.local.get([a,n]),i=r[a],o=r[n];if(!i)return null;let s=[];Array.isArray(o)&&(s=o.map((l,d)=>{if(typeof l=="object"&&l!==null&&"k"in l&&"v"in l)return l;if(typeof l=="object"&&l!==null){let m=Object.keys(l);return{k:m[0]||`key_${d}`,v:l[m[0]]||""}}return{k:`unknown_${d}`,v:String(l)}}));let{key:u,...c}=i;return{...c,glossaries:s}}catch{return null}}else{let a=await hr(e,!0);if(a)return a;let n=`${de}-terms`,r=await Kt(n,["meta","glossaries"]);return new Promise((i,o)=>{let s=r.transaction(["meta","glossaries"],"readonly"),u=s.objectStore("meta"),c=s.objectStore("glossaries"),l=u.get(e);l.onsuccess=()=>{let d=l.result;if(!d){i(null),r.close();return}let m=c.get(e);m.onsuccess=()=>{let{key:p,...v}=d||{},w={...v,glossaries:m.result?.glossaries||[]};Pn(w,!0),i(w),r.close()},m.onerror=p=>{o(p),r.close()}},l.onerror=d=>{o(d),r.close()}})}}async function Bi(e){if(e){if(!A?.storage?.local)return[];try{let t=await A.storage.local.get(null),a="term_meta_",n=[];for(let r in t)if(r.startsWith(a)){let i=t[r],{glossaries:o,...s}=i;n.push({...s})}return n}catch{return[]}}else{let t=await Bi(!0);if(t.length>0)return t;let a=`${de}-terms`,n=await Kt(a,["meta","glossaries"]),o=n.transaction(["meta"],"readonly").objectStore("meta").getAll();return new Promise((s,u)=>{o.onsuccess=c=>{n.close();let l=o.result;l.forEach(async d=>{let m=await hr(d.id,!1);Pn(m,!0),gr(d.id,!1,!0)}),s(l)},o.onerror=c=>{n.close(),u([])}})}}var z2=new String(/* MULTIPLE_REMOVED_BLOCKS */u6535\u4F51\u4EF2\u4EDD\u4FEA\u4F3E\u6C14\u4FB4\u50F3\u4EBF\u6002\u4ED1\u8D37\u948D\u9488\u9553\u956A\u94A9\u9486\u94F1\u94F3\u953F\u988C\u4F1B\u4F32\u65E8\u52FA\u5FFE\u5FC9\u6293\u605D\u6269\u7118\u64E2\u624E\u9091\u5457\u53ED\u5693\u5627\u53FB\u5514\u5459\u5565\u5423\u54D0\u9E2E\u55EC\u622E\u5C50\u6170\u5C39\u5201\u90B5\u5DF2\u84E5\u8363\u8314\u85D0\u84AF\u8484\u4E1A\u6B49\u60CE\u83B0\u8605\u84CF\u830B\u839E\u827E\u827D\u911A\u8327\u828B\u82C8\u911E\u8638\u828E\u830C\u82A5\u8288\u9100\u5E76\u8585\u66F2\u8335\u750D\u8359\u849F\u5C7A\u5C7E\u53B6\u59AB\u5E7B\u7EF7\u5A86\u6215\u7ED2\u598C\u7E9F\u56F0\u56D7\u624C\u6C69\u6E25\u5E86\u9E80\u9E38\u98DE\u6C3D\u4F08\u94A1\u69CA\u836E\u8FBE\u8FE5\u5955\u989C\u5934\u619D\u9057\u6C35\u5FD0\u4EAD\u70B9\u8FF8\u8182\u908B\u7248\u724C\u724D\u7247\u7252\u987E\u6539\u987E"),U2=new String("\u842C\u8207\u919C\u5C08\u696D\u53E2\u6771\u7D72\u4E1F\u5169\u56B4\u55AA\u500B\u723F\u8C50\u81E8\u70BA\u9E97\u8209\u9EBC\u7FA9\u70CF\u6A02\u55AC\u7FD2\u9109\u66F8\u8CB/* MULTIPLE_REMOVED_BLOCKS */u904E\u9031\u9D99\u671B\u6BC3\u9088\u737B\u904B\u5E1D\u777F\u906F\u8FF5\u750B\u7517\u9117\u6B4A\u6B33\u96E2\u6575\u9059\u9031\u557B\u8FE5\u8C9E\u6572\u65C1\u9F8D\u516D\u5546\u9D90\u9870\u4EA4\u5955\u5E1F\u6207\u5157\u889E\u5F08\u5DD2\u89AA\u9D89\u8668\u65B0\u5B70\u901F\u906B\u9055\u90ED\u6B51\u96DC\u6566\u6579\u56B2\u7763\u9316\u7CB2\u9024\u58D1\u97F0\u9910\u60C4\u88FB\u900D\u8FF7\u9074\u53D4\u9D81\u5C31\u52CD\u654A\u4E3B\u9035\u9076\u6BB6\u6C03\u893B\u8667\u8FFC\u9060\u9068\u9054\u8FEB\u9CEA\u9051\u4EB9\u900F\u9036\u890E\u8FD4\u9022\u9004\u9020\u5F65\u7522\u905B\u8922\u9002\u9041\u528C\u983B\u986A\u984F\u8FD1\u907E\u9005\u4EA2\u8FED\u9020\u8FFD\u65B9\u904D\u9080\u7FFD\u908A\u8863\u8FCE\u908D\u5EC9\u88D2\u905E\u80B2\u9021\u8FF0\u719F\u7385\u587E\u5145\u6594\u900B\u8FF0\u9011\u8FD6\u87A4\u7388\u7387\u8FE8\u88AC\u68C4\u906E\u7386\u6597\u9083\u8FA3\u9D6B\u8D1B\u5F70\u74E3\u7AF7\u8FA8\u8FA6\u9123\u902D\u8FAE\u9023\u5E76\u74F6\u8FAF\u6587\u6595\u8FF6\u907C\u9D41\u9CFC\u9DDF\u9E06\u6596\u9016\u901B\u6592\u6548\u8803\u981D\u5F65\u7522\u658C\u90CA\u6548\u5FDE\u619D\u9070\u6489\u541D\u8FE6\u7D0A\u9F7E\u5E02\u902E\u8877\u6B62\u907A\u6B65\u9003\u9063\u4EA6\u8FEA\u4E0A\u80AF\u8D07\u89B7\u8FFA\u8FC2\u76BD\u76BB\u9087\u8FCB\u6B6D\u9E07\u6C08\u6B72\u6B67\u8FD7\u52EF\u5277\u986B\u9090\u8A00\u901C\u8FFE\u6B54\u6B64\u5FD0\u96CC\u9017\u903C\u9010\u9F52\u8FD3\u9015\u9077\u5361\u9038\u9082\u8D0F\u905C\u9E01\u9079\u88A4\u901A\u8FFB\u88B2\u8FC5\u8803\u980F\u909F\u9086\u7FB8\u5B34\u81DD\u8912\u9032\u6BC5\u8FD5\u52BE\u523B\u5287\u9826\u9014\u903E\u903E\u8FC4\u591C\u6B2C\u5352\u8FE4\u8FEE\u864D\u8FFF\u7725\u8CB2\u922D\u67F4\u8FC6\u58DF\u9DFE\u9E17\u8655\u8656\u864E\u864E\u52F4\u882A\u9F91\u4E9B\u7961\u9F92\u7826\u7931\u9B33\u8C66\u89DC\u9B86\u80D4\u98FA\u8654\u8659\u5470\u865E\u865E\u807E\u8650\u8FE3\u9F94\u865B\u8661\u8FCD\u888C\u7D2B\u819A\u76E7\u865C\u6B76\u616E\u9F98\u8972\u8654\u8A3E\u8B8B\u901D\u5360\u8ADF\u8B4B\u8B95\u8B01\u88DB\u8B3E\u818F\u8ABF\u8AC2\u8AE2\u8A5B\u8AFC\u8A77\u4EAD\u8C6A\u8B20\u4EB3\u8ABF\u9AD8\u8A57\u8ACD\u4EAE\u6BEB\u8998\u8B11\u8A1F\u8AE1\u8AAA\u8AC3\u8A56\u8A0E\u8A23\u8AF1\u8ABA\u8AEB\u8ABB\u4EAC\u8A9A\u8B9C\u8AC7\u8A2C\u8A4A\u8B9F\u8A69\u8A7F\u8B4A\u8B78\u8A8C\u8A70\u8B46\u8B80\u8AFB\u8B3B\u8A98\u8AC9\u8B52\u8A7B\u901E\u8B6D\u8AA5\u8A75\u8B1D\u8A85\u8AC8\u8A71\u8A1E\u8B51\u8A22\u8A6C\u8A34\u8A2D\u8AF7\u8B57\u8A17\u8AD9\u8A46\u8A44\u8ADE\u8B06\u8B9A\u54C0\u8AC0\u8B25\u8B0F\u8ADB\u8ABD\u8B15\u8A8F\u8AF4\u8AA0\u8AD3\u8AA7\u8A39\u8A59\u8A27\u8A76\u8A60\u8A92\u8AEE\u8A66\u8A52\u8AA1\u8B67\u8B36\u8A08\u8B13\u8A96\u8AF5\u8ABC\u8A6B\u8B85\u8B53\u8AF8\u8AE0\u8A51\u8A41\u8A74\u8A7C\u8A91\u8B47\u8A87\u8A13\u8B31\u8A82\u8AB9\u5256\u8A0C\u8B23\u8A0F\u8A55\u901E\u8B1C\u8B88\u8A10\u8B40\u8A9E\u8AAB\u8A02\u8A36\u8AA3\u8AEE\u8AE8\u8AD1\u8B56\u8A1D\u8A99\u74FF\u8B24\u8AF2\u8B5A\u8A3C\u8B82\u8B6B\u4EAB\u70F9\u8B4E\u8AC2\u8AA6\u8A83\u8A7A\u8A0A\u8A95\u90E8\u8B94\u8A6D\u4EA8\u8ADD\u8B49\u8B0B\u8B92\u8AB8\u8A25\u8AB0\u8B22\u8B59\u8A3A\u8A45\u8B96\u8AD7\u8B12\u8A31\u6568\u8AD6\u8A6E\u8AED\u8B63\u8AED\u8A16\u8A1B\u8A50\u8B55\u8AA8\u8A62\u8A63\u8A11\u8B10\u8B7A\u8AE7\u8A4D\u8ADC\u8A30\u8B05\u8A84\u8B7F\u8ACB\u8B2E\u8AD8\u8B5F\u8B42\u8AE4\u907B\u8AFF\u907B\u8B54\u8944\u8AA4\u8B5D\u9050\u8A12\u8A54\u8B45\u8A8D\u8ACF\u8B98\u8B35\u8A86\u8B2C\u8A61\u8A5E\u8B33\u8A4E\u8A18\u8A8B\u8B18\u8B28\u8B6A\u8B7E\u8B5C\u8B1A\u8B70\u56C8\u8AFE\u8B8C\u8B39\u8A4C\u8B5C\u8AC6\u8B00\u8B41\u8AF6\u8B77\u8A73\u8B99\u8A81\u8B1B\u8B50\u8B68\u8B19\u8B0A\u8A15\u4E69\u9078\u8AEF\u8A58\u8B4F\u8A4F\u8B02\u8B16\u8AB2\u8B6F\u8AF0\u8B84\u8A03\u8AE6\u8B9E\u8B17\u8B2B\u8B2A\u8A68\u8B48\u6541\u8B0E\u8AD4\u8A3B\u8AFA\u8A2A\u8B58\u8B30\u8AFA\u8B74\u8A3F\u8AB6\u8B3C\u8B14\u8B2F\u8AD5\u8A40\u8B1E\u8AC4\u8AD2\u8B79\u8B93\u8AF3\u8B27\u8A72\u8B60\u8B2B\u8FC9\u9072\u892D\u8FE2\u4E0E\u74EC\u90A1\u65C3\u65D3\u65DF\u65C2\u65CC\u65BB\u65C4\u65C5\u65DB\u65C6\u653E\u65D6\u65D0\u65DA\u65BF\u65CB\u65DD\u65CD\u65CF\u65BD\u65CE\u65D7\u65BC\u65D2\u8FE1\u907F\u65DE\u7ACB\u97F3\u7ADF\u7AE0\u610F\u7AED\u7AEB\u9052\u7AE6\u9E15\u9D17\u98AF\u9053\u6232\u7AE3\u8F9B\u7AE4\u4F47\u9001\u7AD1\u903D\u9871\u9073\u9042\u7AD8\u9756\u7FCA\u7AEE\u8FF8\u9058\u9006\u6EAF\u7AEF\u59BE\u906D\u9081\u7AE5\u9075\u7AD9\u8FBF\u9F66\u9F5F\u8D19\u89A4\u5C0E\u53E1\u9DF2\u5F6A\u9F72\u9F57\u9F6F\u8665\u9F5D\u9F7B\u79BB\u9F6E\u9F65\u9044\u9F6C\u9F56\u9F61\u866A\u9F58\u9F55\u9F54\u9F63\u9F59\u9F5B\u9F7A\u9F5E\u9F76\u9F6A\u9F60\u9F71\u9F77\u9F70\u9F78\u751D\u9F6B\u9F75\u9F74\u9F5C\u8664\u7AF6\u4EA1\u8093\u8182\u88D4\u727D\u76F2\u6757\u58C5\u7515\u7F4B\u9954\u8841\u96CD\u5DDF\u4EA5\u74E4\u7384\u755C\u52F7\u902F\u9099\u8CCC\u5FD8\u6C13\u5984\u5DE1\u908B\u88F9\u88CF\u9E79\u9E75\u9047\u905D\u908F\u9084\u8931\u8870\u9110\u755D\u9E7C\u8FF4\u7A1F\u7A1F\u4EB6\u5363\u9E7A\u9F4A\u9F4D\u9F4E\u9F4B\u9F4C\u5291\u9F4F\u535E\u9034\u9049\u9069\u8FD2\u8DE1\u9019\u907D\u8FE0\u904A\u9067\u9085\u900C\u5159\u515B\u515E\u515D\u5161\u5163\u55E7\u74E9\u7CCE");var fr=class{from;to;constructor(t){this.from=t.from,this.to=t.to}sendMessages(t){let a={type:t.type,data:t.data,id:t.id||this.getRandomId(),isAsync:!1};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(a)}))}getRandomId(){return Math.random()*1e17+new Date().getTime()}sendAsyncMessages({type:t,data:a}){return new Promise(n=>{let r=this.handleMessages(s=>{s.id===i&&(n(s.data),r())}),i=this.getRandomId(),o={type:t,data:a,id:i,isAsync:!0};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(o)}))})}handleMessageOnce(t){return new Promise(a=>{let n=this.handleMessage(t,r=>{a(r.data),n()})})}handleMessage(t,a){return this.handleMessages(n=>{n.type===t&&a(n)})}handleMessages(t){let a=n=>{let i=JSON.parse(n.detail||"{}");i&&typeof i=="object"&&t(i)};return globalThis.document.addEventListener(this.from,a),()=>{globalThis.document.removeEventListener(this.from,a)}}},Rm={get(e,t,a){return t in e?(...n)=>{let r=e[t];return typeof r=="function"?r.apply(e,n):Reflect.get(e,t,a)}:n=>e.sendAsyncMessages({type:t,data:n})}};var _m="imt-browser-bridge-event-to-content-script",jm="imt-browser-bridge-event-to-inject",e3=new fr({from:_m,to:jm}),a6=new fr({from:jm,to:_m}),t3=new Proxy(e3,Rm);var a3="Original";function Hs(){return a3}var br=new Map,sa=class{fromType;logger;constructor(t,a=!1){this.logger=new Yn,a&&this.logger.setLevel("debug"),this.fromType=t,br.has(t)||(br.set(t,new Map),A.runtime.onMessage.addListener((n,r,i)=>{if(n.target==="offscreen")return;let o=n.from,s=n.to,u,c,l;r.tab&&r.tab.id&&(u=r.tab.id,o=`${o}:${u}`,c=r.tab.url,l=r.tab.active),this.logger.debug(`${n.to} received message [${n.payload.method}] from ${n.from}`,n.payload.data?n.payload.data:" ");let d=Ws(s),{type:m,name:p}=d;if(m!==t)return!1;let v=Ws(o),S=br.get(m).get(p);if(!S)return this.logger.debug(`no message handler for ${m}:${s}, but it's ok`),!1;let{messageHandler:g,sync:M}=S,f={type:t,name:v.name,id:u,url:c,active:l};if(M){try{let T=g(n.payload,f);i({ok:!0,data:T})}catch(T){i({ok:!1,errorName:T.name,errorMessage:T.message,errorDetails:T.details,errorStatus:T.status})}return!1}else return g(n.payload,f).then(T=>{i({ok:!0,data:T})}).catch(T=>{i({ok:!1,errorName:T.name,errorMessage:T.message,errorDetails:T.message,errorStatus:T.status})}),!0}))}getConnection(t,a,n){let r=!1;n&&n.sync&&(r=!0);let i=this.fromType,o=br.get(i);if(o.has(t))return o.get(t).connectionInstance;{let s=new Ks(`${i}:${t}`,this.logger);return br.get(i).set(t,{messageHandler:a,sync:r,connectionInstance:s}),s}}},Ks=class{from;logger;constructor(t,a){this.from=t,this.logger=a}async sendMessage(t,a){let n=Ws(t),{type:r,id:i}=n;if(r!=="content_script"){let o={to:t,from:this.from,payload:a};this.logger.debug(`${o.from} send message [${o.payload.method}] to ${o.to}`,o.payload.data?o.payload.data:" ");try{let s=await A.runtime.sendMessage(o);return Lm(o,s,this.logger)}catch(s){if(r==="popup"){let u=`popup ${t} is not active, so the message does not send, ignore this error, ${JSON.stringify(a)}`;return this.logger.debug(u,a,t,s),Promise.resolve({message:u})}else throw s}}else{let o={from:this.from,to:t,payload:a};this.logger.debug(`${o.from} send message [${o.payload.method}] to ${o.to}`,o.payload.data?o.payload.data:" ");let s=await A.tabs.sendMessage(i,o);return Lm(o,s,this.logger)}}};function Lm(e,t,a){if(t){if(t.ok)return a.debug(`${e.from} received response from ${e.to}:`,t.data?t.data:" "),t.data;throw new It(t.errorName||"UnknownError",t.errorMessage||"Unknown error").initNetWork(t.errorStatus)}else throw new It("noResponse","Unknown error")}function Ws(e){let t=e.split(":");if(t.length<2)throw new Error("not a valid to string");let a={type:t[0],name:t[1]};if(t[0]==="content_script"){let n=parseInt(t[2]);if(!isNaN(n))a.id=n;else throw new Error("tab id not a valid number")}return a}function Vs(e){return(e?.id?.endsWith("pdfWebPage")||!1)&&globalThis.top==globalThis.self}async function Nm(){return globalThis.top!==globalThis.self||!document.body?!1:document.body.children.length===0&&document.body.hasChildNodes()?!!(await fetch(location.href)).headers.get("content-type")?.includes("application/pdf"):!1}function Om(e){try{if(!e||!Vs(e))return"";let t="",a=e.pdfUrlExtractRule,n=a.selectors||[];a.selector&&n.push(a.selector);let r=a.attributes||[];a.attribute&&r.push(a.attribute);let i=a.queries||[];a.query&&i.push(a.query);for(let s of n)if(!document.querySelectorAll(s))return"";for(let s of n){let u=document.querySelectorAll(s);if(u.length){for(let c of u){for(let l of r)if(t=c.getAttribute(l)||"",t)break;if(t)break}if(t)break}}t||(t=document.querySelector("embed[type='application/pdf']")?.getAttribute("src")||"");let o=n3(location.href,t);for(let s of i){let c=new URL(o).searchParams.get(s)||"";if(c)return c}return o}catch{return""}}function n3(e,t){if(t.startsWith("about:"))return"";if(t.startsWith("//"))try{return new URL(e).protocol+t}catch{return"https:"+t}if(t.startsWith("http://")||t.startsWith("https://"))return t;try{if(t.startsWith("/")){let a=new URL(e);return a.protocol+"//"+a.host+t}else{let a=new URL(e);return new URL(t,a.href).href}}catch{return""}}var ce={},zm=async function(e,t){let{method:a,data:n}=e;if(a==="getIsDulSubtitle")return ce.getIsDulSubtitle();if(a==="getPageStatus")return Hs();if(a==="toggleSidePanel"){let o=ce.getPureGlobalContext();Gm(o,"shortcut",n?.isOpen);return}a==="updateContextState"&&await ce.updateContextState(n);let r=await ce.updateGlobalContext(),i=Date.now();if(I.debug(`content script received message: ${a}`,n||" "),a==="translateTheWholePage")await ce.translateTheWholePage(n),Se(ft,i);else if(a==="translateTheMainPage")await ce.translateTheMainPage(n),Se(ft,i);else if(a==="translateToThePageEndImmediately")await ce.translateToThePageEndImmediately(n),Se(ft,i);else if(a==="toggleTranslateManga")await ce.toggleTranslateManga(),Se(ft,i);else if(a==="toggleTranslatePage"){let o=location.href;if(Vs(r?.rule)||await Nm()){let s=Om(r.rule);qm(!0,s||o);return}await ce.toggleTranslatePage(n),Se(ft,i)}else if(a==="toggleTranslateTheWholePage")await ce.toggleTranslateTheWholePage(n),Se(ft,i);else if(a==="toggleTranslateTheMainPage")await ce.toggleTranslateTheMainPage(n),Se(ft,i);else if(a==="toggleOnlyTransation")await ce.ensureSwitchTranslationMode(n),Se(ft,i);else if(a=="toggleEnableEditTranslation")ce.toggleEnableEditTranslation();else if(a==="translatePage")await ce.translatePage(r,n),Se(ft,i);else if(a==="toggleTranslationMask")await ce.toggleTranslationMask(n);else if(a==="restorePage")ce.restorePage();else if(a==="retryFailedParagraphs")ce.retryFailedParagraphs();else if(a=="change_translate_service")ce.reportTranslateService(r,n);else if(a==="switchTranslationMode"){if(r.rule.isPdf)return;n&&n.mode&&(await ce.switchTranslationMode(n.mode),await ce.reloadSubtitleWithTranslationModeChanged())}else if(a==="autoEnableSubtitleChanged")ce.autoEnableSubtitleChanged(r,n);else if(a==="tempDisableSubtitleChanged")ce.tempDisableSubtitleChanged(r,n);else if(a=="shareToDraft")globalThis.document.dispatchEvent(new CustomEvent(Dl,{detail:n}));else if(a=="toggleTranslateToThePageEndImmediately")await ce.toggleTranslateToThePageEndImmediately(n);else if(a==="toggleMouseHoverTranslateDirectly")globalThis.document.dispatchEvent(new CustomEvent(Cl,{detail:n}));else if(a==="translateWithOpenAI")await ce.translatePageWithTranslationService("openai",n);else if(a==="translateWithGoogle")await ce.translatePageWithTranslationService("google",n);else if(a==="translateWithDeepL")await ce.translatePageWithTranslationService("deepl",n);else if(a==="translateWithBing")await ce.translatePageWithTranslationService("bing",n);else if(a==="translateWithTransmart")await ce.translatePageWithTranslationService("transmart",n);else if(a==="translateWithGemini")await ce.translatePageWithTranslationService("gemini",n);else if(a==="translateWithClaude")await ce.translatePageWithTranslationService("claude",n);else if(a.startsWith("translateWithCustom"))await ce.translatePageWithTranslationService(r.config.rawUserConfig?.shortcuts?.translateWithCustomServices?.[a]??"bing",n);else if(a==="translateInputBox")await ce.translateInputBoxWithShortcut(r);else if(a!=="updateGlobalCtx")if(a==="toggleVideoSubtitlePreTranslation")Ea()||await ce.toggleVideoSubtitlePreTranslation();else if(a==="getAsyncContextString"){if(!Ea())return JSON.stringify(r);await xa(5e3)}else if(a==="inputSelectedTextTranslate")await ce.inputSelectedTextTranslate(r,n);else{if(a==="popupEventReport")return ce.popupEventReport(r,n);if(a==="updateFloatBallEnable")return ce.updateFloatBallEnable();if(a==="selectionTranslate")return ce.selectionTranslate(r,n);a==="webReport"?document.dispatchEvent(new CustomEvent(Tl,{detail:{type:"webReport"}})):a===Oa&&await ce.translateSelectImage(r,n)}};var Ri;function Um(){return Ri||(Ri=new sa("content_script",!1).getConnection("main",zm),Ri)}var i3=[["auto","auto"],["zh-CN","zh"],["zh-TW","zh-TW"],["yue","ct"],["de","de"],["en","en"],["es","es"],["fr","fr"],["id","id"],["it","it"],["ja","ja"],["ko","ko"],["ms","ms"],["pt","pt"],["ru","ru"],["th","th"],["tr","tr"],["vi","vi"]],o3="https://transmart.qq.com/api/imt",Qs=class e{static langMapReverse=new Map(i3.map(([t,a])=>[a,t]));static getClientKey(){return"tencent_transmart_crx_"+btoa(navigator.userAgent).slice(0,100)}static async detectLanguageRemotelyByTransmart(t){let a={header:{fn:"text_analysis",client_key:e.getClientKey()},text:t.slice(0,280)},n=await wt({url:s3(o3),method:"POST",body:JSON.stringify(a)});if(n.header.ret_code!=="succ")throw new Error(n.message||n.header.ret_code);let r=n.language,i=e.langMapReverse.get(r);return i||r}};function s3(e){if(!fe())return e;let t=new URL(e);return t.searchParams.set("timestamp",Date.now().toString()),t.toString()}function u3(e,t){return Math.floor(Math.random()*(t-e+1))+e}var l3=30,Ys="RATE_LIMITER_TICKS",yr=class e{static strictTicks={};static clearStrictTicks(){vr(Ys,"{}")}static async getStrictTicks(t,a){if(a)return this.strictTicks[t]||[];try{let n=await Fn(Ys)||"";n&&(this.strictTicks=JSON.parse(n)||{})}catch(n){I.debug("Error setting rate limiter ticks",n),this.strictTicks[t]=[]}finally{return this.strictTicks[t]||[]}}static wait(t,a){return new Promise((n,r)=>{e.getDelay(t,a).then(i=>{setTimeout(()=>{n(i)},i)})})}static async setStrictTicks(t,a,n){try{if(this.strictTicks[t]=a,n)return;await vr(Ys,JSON.stringify(this.strictTicks))}catch(r){I.debug("Error setting rate limiter ticks",r)}}static async getDelay(t,a,n=!1){if(!n){let c=u3(4,l3);await xa(c)}let r=await this.getStrictTicks(t,n)||[],{limit:i,interval:o}=a,s=Date.now();if(r.length<i)return r.push(s),await this.setStrictTicks(t,r,n),0;let u=r.shift()+o;return s>=u?(r.push(s),await this.setStrictTicks(t,r,n),0):(r.push(u),await this.setStrictTicks(t,r,n),u-s)}};function Wt(e){let a=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];return a.endsWith(",")&&(a=a.slice(0,-1)),a}function Bn(e){try{let a=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];a.endsWith(",")&&(a=a.slice(0,-1));let[n,r,i]=a.split("/");return a=`${i}-${n}-${r}`,a}catch{return"unknown"}}function _i(e){let[t,a,n]=e.split("-");return`${t}-${a.padStart(2,"0")}-${n.padStart(2,"0")}`}function ji(e){e.setMinutes(e.getMinutes()+e.getTimezoneOffset()+8*60),e.setDate(e.getDate()+4-(e.getDay()||7));let t=new Date(e.getFullYear(),0,1);return{week:Math.ceil(((e.getTime()-t.getTime())/864e5+1)/7),year:e.getFullYear()}}function Hm(e){let t=new Date(e),a=Wt(t),r=Wt(new Date);return a===r}async function Km(e,t,a){try{let n=await window.crypto.subtle.importKey("raw",Js(t),"AES-GCM",!0,["encrypt","decrypt"]),r=Js(e),i=await globalThis.crypto.subtle.encrypt({name:"AES-GCM",iv:Js(a)},n,r);return c3(i)}catch{return Promise.reject("Encryption failed")}}function c3(e){let t="",a=new Uint8Array(e),n=a.byteLength;for(let r=0;r<n;r++)t+=String.fromCharCode(a[r]);return globalThis.btoa(t)}function Js(e){return new TextEncoder().encode(e)}var Wm={ai_assistant:"ex_char_arg1",ai_assistant_use:"ex_char_arg2",selection_text:"ex_char_arg3",translation_result:"ex_char_arg4",has_pined:"ex_char_arg5",user_id:"ex_int_arg1",enable_side_panel:"ex_char_arg7",popup_switch_extend_field_enabled:"ex_int_arg2",popup_switch_extend_field:"ex_char_arg2"};function xr(e){return e?.install_day&&(e.install_day=_i(e.install_day)),e.temp_translate_domain_minutes&&(e.temp_translate_domain_minutes=parseInt(e.temp_translate_domain_minutes.toString()),e.temp_translate_domain_minutes>1e8&&(e.temp_translate_domain_minutes=1e8)),Object.keys(Wm).forEach(t=>{e[t]!==void 0&&(e[Wm[t]]=e[t],delete e[t])}),e.site_host&&delete e.site_host,e}var Rn="input is invalid type",Zs=typeof window=="object",Da=Zs?window:{};Da.JS_SHA256_NO_WINDOW&&(Zs=!1);var d3=!Zs&&typeof self=="object",m3=!Da.JS_SHA256_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;m3?Da=global:d3&&(Da=self);var j9=!Da.JS_SHA256_NO_COMMON_JS&&typeof module=="object"&&module.exports,L9=typeof define=="function"&&define.amd,wr=!Da.JS_SHA256_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",H="0123456789abcdef".split(""),p3=[-2147483648,8388608,32768,128],Pt=[24,16,8,0],Li=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ni=["hex","array","digest","arrayBuffer"],We=[];(Da.JS_SHA256_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"});wr&&(Da.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(e){return typeof e=="object"&&e.buffer&&e.buffer.constructor===ArrayBuffer});var Vm=function(e,t){return function(a){return new Ve(t,!0).update(a)[e]()}},Ym=function(e){var t=Vm("hex",e);t.create=function(){return new Ve(e)},t.update=function(r){return t.create().update(r)};for(var a=0;a<Ni.length;++a){var n=Ni[a];t[n]=Vm(n,e)}return t},Qm=function(e,t){return function(a,n){return new Oi(a,t,!0).update(n)[e]()}},Jm=function(e){var t=Qm("hex",e);t.create=function(r){return new Oi(r,e)},t.update=function(r,i){return t.create(r).update(i)};for(var a=0;a<Ni.length;++a){var n=Ni[a];t[n]=Qm(n,e)}return t};function Ve(e,t){t?(We[0]=We[16]=We[1]=We[2]=We[3]=We[4]=We[5]=We[6]=We[7]=We[8]=We[9]=We[10]=We[11]=We[12]=We[13]=We[14]=We[15]=0,this.blocks=We):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e?(this.h0=3238371032,this.h1=914150663,this.h2=812702999,this.h3=4144912697,this.h4=4290775857,this.h5=1750603025,this.h6=1694076839,this.h7=3204075428):(this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=e}Ve.prototype.update=function(e){if(!this.finalized){var t,a=typeof e;if(a!=="string"){if(a==="object"){if(e===null)throw new Error(Rn);if(wr&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!wr||!ArrayBuffer.isView(e)))throw new Error(Rn)}else throw new Error(Rn);t=!0}for(var n,r=0,i,o=e.length,s=this.blocks;r<o;){if(this.hashed&&(this.hashed=!1,s[0]=this.block,s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),t)for(i=this.start;r<o&&i<64;++r)s[i>>2]|=e[r]<<Pt[i++&3];else for(i=this.start;r<o&&i<64;++r)n=e.charCodeAt(r),n<128?s[i>>2]|=n<<Pt[i++&3]:n<2048?(s[i>>2]|=(192|n>>6)<<Pt[i++&3],s[i>>2]|=(128|n&63)<<Pt[i++&3]):n<55296||n>=57344?(s[i>>2]|=(224|n>>12)<<Pt[i++&3],s[i>>2]|=(128|n>>6&63)<<Pt[i++&3],s[i>>2]|=(128|n&63)<<Pt[i++&3]):(n=65536+((n&1023)<<10|e.charCodeAt(++r)&1023),s[i>>2]|=(240|n>>18)<<Pt[i++&3],s[i>>2]|=(128|n>>12&63)<<Pt[i++&3],s[i>>2]|=(128|n>>6&63)<<Pt[i++&3],s[i>>2]|=(128|n&63)<<Pt[i++&3]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.block=s[16],this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}};Ve.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=p3[t&3],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}};Ve.prototype.hash=function(){var e=this.h0,t=this.h1,a=this.h2,n=this.h3,r=this.h4,i=this.h5,o=this.h6,s=this.h7,u=this.blocks,c,l,d,m,p,v,w,S,g,M,f;for(c=16;c<64;++c)p=u[c-15],l=(p>>>7|p<<25)^(p>>>18|p<<14)^p>>>3,p=u[c-2],d=(p>>>17|p<<15)^(p>>>19|p<<13)^p>>>10,u[c]=u[c-16]+l+u[c-7]+d<<0;for(f=t&a,c=0;c<64;c+=4)this.first?(this.is224?(S=300032,p=u[0]-1413257819,s=p-150054599<<0,n=p+24177077<<0):(S=704751109,p=u[0]-210244248,s=p-1521486534<<0,n=p+143694565<<0),this.first=!1):(l=(e>>>2|e<<30)^(e>>>13|e<<19)^(e>>>22|e<<10),d=(r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7),S=e&t,m=S^e&a^f,w=r&i^~r&o,p=s+d+w+Li[c]+u[c],v=l+m,s=n+p<<0,n=p+v<<0),l=(n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10),d=(s>>>6|s<<26)^(s>>>11|s<<21)^(s>>>25|s<<7),g=n&e,m=g^n&t^S,w=s&r^~s&i,p=o+d+w+Li[c+1]+u[c+1],v=l+m,o=a+p<<0,a=p+v<<0,l=(a>>>2|a<<30)^(a>>>13|a<<19)^(a>>>22|a<<10),d=(o>>>6|o<<26)^(o>>>11|o<<21)^(o>>>25|o<<7),M=a&n,m=M^a&e^g,w=o&s^~o&r,p=i+d+w+Li[c+2]+u[c+2],v=l+m,i=t+p<<0,t=p+v<<0,l=(t>>>2|t<<30)^(t>>>13|t<<19)^(t>>>22|t<<10),d=(i>>>6|i<<26)^(i>>>11|i<<21)^(i>>>25|i<<7),f=t&a,m=f^t&n^M,w=i&o^~i&s,p=r+d+w+Li[c+3]+u[c+3],v=l+m,r=e+p<<0,e=p+v<<0;this.h0=this.h0+e<<0,this.h1=this.h1+t<<0,this.h2=this.h2+a<<0,this.h3=this.h3+n<<0,this.h4=this.h4+r<<0,this.h5=this.h5+i<<0,this.h6=this.h6+o<<0,this.h7=this.h7+s<<0};Ve.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,a=this.h2,n=this.h3,r=this.h4,i=this.h5,o=this.h6,s=this.h7,u=H[e>>28&15]+H[e>>24&15]+H[e>>20&15]+H[e>>16&15]+H[e>>12&15]+H[e>>8&15]+H[e>>4&15]+H[e&15]+H[t>>28&15]+H[t>>24&15]+H[t>>20&15]+H[t>>16&15]+H[t>>12&15]+H[t>>8&15]+H[t>>4&15]+H[t&15]+H[a>>28&15]+H[a>>24&15]+H[a>>20&15]+H[a>>16&15]+H[a>>12&15]+H[a>>8&15]+H[a>>4&15]+H[a&15]+H[n>>28&15]+H[n>>24&15]+H[n>>20&15]+H[n>>16&15]+H[n>>12&15]+H[n>>8&15]+H[n>>4&15]+H[n&15]+H[r>>28&15]+H[r>>24&15]+H[r>>20&15]+H[r>>16&15]+H[r>>12&15]+H[r>>8&15]+H[r>>4&15]+H[r&15]+H[i>>28&15]+H[i>>24&15]+H[i>>20&15]+H[i>>16&15]+H[i>>12&15]+H[i>>8&15]+H[i>>4&15]+H[i&15]+H[o>>28&15]+H[o>>24&15]+H[o>>20&15]+H[o>>16&15]+H[o>>12&15]+H[o>>8&15]+H[o>>4&15]+H[o&15];return this.is224||(u+=H[s>>28&15]+H[s>>24&15]+H[s>>20&15]+H[s>>16&15]+H[s>>12&15]+H[s>>8&15]+H[s>>4&15]+H[s&15]),u};Ve.prototype.toString=Ve.prototype.hex;Ve.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,a=this.h2,n=this.h3,r=this.h4,i=this.h5,o=this.h6,s=this.h7,u=[e>>24&255,e>>16&255,e>>8&255,e&255,t>>24&255,t>>16&255,t>>8&255,t&255,a>>24&255,a>>16&255,a>>8&255,a&255,n>>24&255,n>>16&255,n>>8&255,n&255,r>>24&255,r>>16&255,r>>8&255,r&255,i>>24&255,i>>16&255,i>>8&255,i&255,o>>24&255,o>>16&255,o>>8&255,o&255];return this.is224||u.push(s>>24&255,s>>16&255,s>>8&255,s&255),u};Ve.prototype.array=Ve.prototype.digest;Ve.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(this.is224?28:32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),this.is224||t.setUint32(28,this.h7),e};function Oi(e,t,a){var n,r=typeof e;if(r==="string"){var i=[],o=e.length,s=0,u;for(n=0;n<o;++n)u=e.charCodeAt(n),u<128?i[s++]=u:u<2048?(i[s++]=192|u>>6,i[s++]=128|u&63):u<55296||u>=57344?(i[s++]=224|u>>12,i[s++]=128|u>>6&63,i[s++]=128|u&63):(u=65536+((u&1023)<<10|e.charCodeAt(++n)&1023),i[s++]=240|u>>18,i[s++]=128|u>>12&63,i[s++]=128|u>>6&63,i[s++]=128|u&63);e=i}else if(r==="object"){if(e===null)throw new Error(Rn);if(wr&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!wr||!ArrayBuffer.isView(e)))throw new Error(Rn)}else throw new Error(Rn);e.length>64&&(e=new Ve(t,!0).update(e).array());var c=[],l=[];for(n=0;n<64;++n){var d=e[n]||0;c[n]=92^d,l[n]=54^d}Ve.call(this,t,a),this.update(l),this.oKeyPad=c,this.inner=!0,this.sharedMemory=a}Oi.prototype=new Ve;Oi.prototype.finalize=function(){if(Ve.prototype.finalize.call(this),this.inner){this.inner=!1;var e=this.array();Ve.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(e),Ve.prototype.finalize.call(this)}};var _n=Ym();_n.sha256=_n;_n.sha224=Ym(!0);_n.sha256.hmac=Jm();_n.sha224.hmac=Jm(!0);var Zm=_n;var g3=Zm.sha256;function Xm(e){return Promise.resolve(g3(e))}function $m({key:e,params:t,ctx:a,forceDaily:n,ignoreGA:r}){if(e=="init_page_daily"){let i="-1";a?.config?.pcFloatBall?.enableSidePanel==!0?i="1":a?.config?.pcFloatBall?.enableSidePanel==!1&&(i="0"),t={...t,enable_side_panel:i}}return h3(e,[{name:e,params:t}],a,n,r)}async function h3(e,t,a,n=!1,r=!1){if(a&&!a.config?.rawUserConfig?.disableReport)try{let i=Wa(),o=ue(),s=o.INSTALL_FROM==="firefox_store";i?.name?.startsWith("ImtFx")&&(s=!1);let u=Ea(),c=`report_${e}`,l=e.endsWith("_daily");if(l){if(u)return;let w=await Ha(c,0),S=Wt(new Date(w)),g=Date.now(),M=Wt(new Date(g));if(S===M&&!n)return;await pc(c,g)}else if(!a.config.telemetry)return;let{fakeUserId:d,installedAt:m}=await oa(),p=ii(),v=await x3({events:t,ctx:a,env:o,isDaily:l,isInIframe:u,installedAt:m,imtBrowser:i});if(s)return;r||p.forEach(async w=>{let S=await wt({responseType:"text",url:Xs(w),method:"POST",body:JSON.stringify({client_id:d,user_id:d,events:v})})}),a.config.enableSelfServiceReport&&f3(a,d,v,a.config.disableReportHash),a.config.enablePerformanceReport&&v3(a,v)}catch(i){I.debug("report error",i)}}function f3(e,t,a,n){try{if(ya())return;a.forEach(async r=>{let i={...r.params,event_name:r.name,device_id:t};e.user?.id&&(i.user_id=e.user.id),i.site_host&&!n&&(i.ex_char_arg6=await Xm(i.site_host)),xr(i);let o=Date.now()+(Math.random()*100).toFixed(0);wt({url:Xs(za),method:"POST",responseType:"text",body:JSON.stringify({nonce:o,subject:"user_behaviour",logs:[JSON.stringify(i)]})})})}catch(r){I.debug("report self service error",r)}}var b3=Date.now(),y3=["translate_page","translate_video_subtitle"];function v3(e,t){try{if(ya())return;t.forEach(async a=>{if(!y3.includes(a.name)||e.config.performanceBlockUrls?.some(c=>Sn(globalThis.location.href,c)))return;let i={...a.params,event_name:"performance"};xr(i);let o={type:a.name,u:globalThis.location.href,preload_time:Date.now()-b3},s=await Km(JSON.stringify(o),Ol,zl);i.ex_char_arg1=s;let u=Date.now()+(Math.random()*100).toFixed(0);wt({url:Xs(za),method:"POST",responseType:"text",body:JSON.stringify({nonce:u,subject:"user_behaviour",logs:[JSON.stringify(i)]})})})}catch(a){I.debug("report self service error",a)}}function Xs(e){if(!fe())return e;let t=new URL(e);return t.searchParams.set("timestamp",Date.now().toString()),t.toString()}async function x3(e){let{events:t,ctx:a,env:n,isDaily:r,isInIframe:i,installedAt:o,imtBrowser:s}=e,u=rt(),c=new Date,l=await Gt(),d=await mr(),m=await Qa(),p=fe(),v=new Date(o),w=Wt(v),S=Wt(c),g=w===S,M=24*60*60*1e3,f=c.getTime()-v.getTime()<7*M,T=c.getTime()-v.getTime()<30*M,L=c.getTime()-v.getTime()<365*M,E=ka.parse(window.navigator.userAgent);return t.map(k=>{let y=k.params||{};if(E.os&&(y.os_name=E.os.name||"unknown",y.os_version=E.os.version||"unknown",y.os_version_name=E.os.versionName||"unknown"),E.browser&&(y.browser_name=E.browser.name||"unknown",y.browser_version=E.browser.version||"unknown",s&&(y.browser_name=s.name,y.browser_version=s.version)),E.platform&&(y.platform_type=E.platform.type||"unknown"),E.engine&&(y.engine_name=E.engine.name||"unknown",y.engine_version=E.engine.version||"unknown"),a.translationService){y.translation_service||(y.translation_service=a.translationService);let _=a.config.translationServices?.[y.translation_service];_?.type&&(y.translation_service=_.type),!y.ai_assistant&&y.translation_service&&a.specialAiAssistant?.applyTranslationService==y.translation_service&&(y.ai_assistant=a?.specialAiAssistant.id),_&&(_.enableAIContext&&(y.ai_assistant="ai_context"),_.provider&&(y.translation_service_provider=_.provider),_.group=="max"&&(y.translation_service_provider="max"),a.translationService==="openai"&&_.provider==="custom"&&(_.apiUrl?_.apiUrl.startsWith("https://api.openai.com/")?y.openai_is_official="1":y.openai_is_official="0":y.openai_is_official="1"),Fi(_)&&!y.ai_assistant&&(y.ai_assistant="common"))}if(y.translation_service&&y.translation_service.startsWith("custom")&&(y.translation_service="custom-ai"),y.ai_assistant&&y.ai_assistant.startsWith("custom")&&(y.ai_assistant="custom"),y.ai_assistant_use&&y.ai_assistant_use.startsWith("custom")&&(y.ai_assistant_use="custom"),a.targetLanguage&&!y.target_language&&(y.target_language=a.targetLanguage),a.config.interfaceLanguage&&(y.interface_language=a.config.interfaceLanguage),a.config.enableDefaultAlwaysTranslatedUrls?y.enable_default_always_translated_urls=a.config.enableDefaultAlwaysTranslatedUrls?"1":"0":y.enable_default_always_translated_urls="0",u&&(y.version=u),a.config.enableInputTranslation?y.enable_input_translation=a.config.enableInputTranslation?"1":"0":y.enable_input_translation="0",a.rule.selectionTranslation?.enable?y.enable_selection_translation=a.rule.selectionTranslation.enable?"1":"0":y.enable_selection_translation="0",a.config.translationTheme&&(y.translation_theme=a.config.translationTheme),a.config.alpha&&(y.alpha=a.config.alpha.toString()),a.config.translationLanguagePattern&&a.config.translationLanguagePattern.matches?.length>0?y.always_translate_languages=a.config.translationLanguagePattern.matches.join(","):y.always_translate_languages="none",n.INSTALL_FROM&&(y.install_from=n.INSTALL_FROM),a.config.beta&&(y.beta=a.config.alpha.toString()),a.config.translationArea&&(y.translation_area=a.config.translationArea),w){y.install_day=Bn(v);let _=ji(v);y.install_week=`${_.year}${_.week}`}if(a.user){let _=li(a.user);_&&Object.keys(_).forEach(P=>{y[P]=_[P]})}else y.user_type="anonymous";a.config.translationMode&&(y.translation_mode=a.config.translationMode),y.userscript=p.toString(),g?y.is_new_user_today="1":y.is_new_user_today="0",y.is_new_user_this_week=f?"1":"0",y.is_new_user_this_month=T?"1":"0",y.is_new_user_this_year=L?"1":"0",a.config.tempTranslateDomainMinutes?y.temp_translate_domain_minutes=a.config.tempTranslateDomainMinutes.toString():y.temp_translate_domain_minutes="0";let j="html";if(a.rule.pageType&&(j=a.rule.pageType),y.page_type=j,i?y.main_frame=0:y.main_frame=1,!r){let _=a.url;try{let P=new URL(_);y.site_host=P.hostname}catch{y.site_host="unknown"}a.sourceLanguage&&(y.source_language=a.sourceLanguage)}return l&&(y.ab_tag=l),d&&(y.ab_group=d),y.campaign=m||"none",{...k,params:y}})}var Ar=!1;function e0(){return Ar}async function zi({isOpen:e,tabId:t,forceOpen:a,openRewardCenter:n}){let r=t?e:await $s();return a!=null&&(r=!a),!fe(!1,!0)&&na()?(chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!1}),r?t0():(chrome.sidePanel.setOptions({path:`side-panel.html?openRewardCenter=${n}`,enabled:!0}),t?chrome.sidePanel.open({tabId:t}):chrome.windows.getCurrent({populate:!0},async i=>{chrome.sidePanel.open({windowId:i.id})}),Ar=!0),!0):!1}async function $s(){return Ar=(await chrome.runtime.getContexts({contextTypes:[chrome.runtime.ContextType.SIDE_PANEL]})).length>0,Ar}function t0(){chrome.sidePanel.setOptions({path:"side-panel.html",enabled:!1}),Ar=!1}var eu={getIsOpenSidePanel:()=>!1,toggleMockSidePanel:e=>{},closeMockSidePanel:()=>{}};async function Ya(e){return await Um().sendMessage("background:main",e)}function wt(e){return fe()||Yr()?(e.fetchPolyfill=globalThis.GM_fetch,Ca(e)):Ya({method:"fetch",data:e})}function tu(){return fe()?Ja():Ya({method:"getLocalConfig"})}function a0(e){return fe()?Ma(e):Ya({method:"setLocalConfig",data:e})}function qm(e=!1,t){return fe()?(A.extra.openPdfViewerPage(e),Promise.resolve()):Ya({method:"openPdfViewerPage",data:{url:t}})}async function Gm(e,t,a){let n=e.config,r=document.body.clientWidth;if(setTimeout(()=>{document.body.clientWidth<r&&$m({key:"openSidePanel",ctx:e,params:{trigger:t}})},1500),fe()||!na()||eu.getIsOpenSidePanel()){eu.toggleMockSidePanel(n);return}let i=a??await Ya({method:"isOpenSidePanel"});if(!(t==="shortcut"&&!i))return setTimeout(async()=>{let o=document.body.clientWidth,s=await Ya({method:"isOpenSidePanel"});if(o==r&&!s){eu.toggleMockSidePanel(n);return}},500),Ya({method:"toggleSidePanel",data:{isOpen:i}})}var n0={nologin:"\u672A\u767B\u5F55",loginForSafari:"\u767B\/* MULTIPLE_REMOVED_BLOCKS */\u514D\u8D39\u4F53\u9A8C {free_trial_minutes} \u5206\u949F","subtitle.liveFreeTrialEndTip":"\u76F4\u64AD\u89C6\u9891\u5B57\u5E55\u7FFB\u8BD1\u8BD5\u7528\u5DF2\u7ED3\u675F\uFF0C\u5F00\u901A\u4F1A\u5458\u53EF\u7EE7\u7EED\u4F7F\u7528","subtitle.meetingAutoEnableSubtitle":"\u4F1A\u8BAE\u5E73\u53F0\u81EA\u52A8\u5F00\u542F\u53CC\u8BED\u5B57\u5E55","subtitle.meetingAutoEnableSubtitleDescription":"\u5F00\u542F\u540E\uFF0CTeams\u3001Zoom\u3001Google Meet \u4F1A\u8BAE\u5E73\u53F0\u4F1A\u81EA\u52A8\u5F00\u542F\u53CC\u8BED\u5B57\u5E55","translationServices.qwen-mt":"Qwen-MT","labelKey.domains":"domains \u9886\u57DF\u63D0\u793A","description.qwenMtDomains":"\u5982\u679C\u60A8\u5E0C\u671B\u7FFB\u8BD1\u7684\u98CE\u683C\u66F4\u7B26\u5408\u67D0\u4E2A\u9886\u57DF\u7684\u7279\u6027\uFF0C\u5982\u6CD5\u5F8B\u3001\u653F\u52A1\u9886\u57DF\u7FFB\u8BD1\u7528\u8BED\u5E94\u5F53\u4E25\u8083\u6B63\u5F0F\uFF0C\u793E\u4EA4\u9886\u57DF\u7528\u8BED\u5E94\u5F53\u53E3\u8BED\u5316\uFF0C\u53EF\u4EE5\u7528\u4E00\u6BB5\u81EA\u7136\u8BED\u8A00\u6587\u672C\u63CF\u8FF0\u60A8\u7684\u9886\u57DF\uFF0C\u5C06\u5176\u63D0\u4F9B\u7ED9\u5927\u6A21\u578B\u4F5C\u4E3A\u63D0\u793A\u3002\u9886\u57DF\u63D0\u793A\u8BED\u53E5\u6682\u65F6\u53EA\u652F\u6301\u82F1\u6587\u3002"};var r0={nologin:"\u672A\u767B\u5165",loginForSafari:"\u767B\u5165\u6216\u8A3B\u518A",login:"\u767B\u5165",goLogin:"\u53BB\u767B\u5165",manageAccount:"\u7BA1\u7406\u5E33\u6236",openPremium:"\u5347\u7D1A\u6210\u5C08\u696D\u7248",logout:"\u767B\u51FA",curentPlan:"\/* MULTIPLE_REMOVED_BLOCKS */sageTips":"\u6C89\u6D78\u5F0F\u7FFB\u8B6F\u7528\u91CF\u63D0\u793A","reportInfo.emailPlaceholder":"\u806F\u7D61\u4FE1\u7BB1\uFF08\u5FC5\u586B\uFF09","subtitle.quickButton.upgradePro":"\u5347\u7D1A\u70BA\u6703\u54E1","subtitle.quickButton.liveOnlyPro":"\u76F4\u64AD\u5B57\u5E55\u7FFB\u8B6F\u529F\u80FD\u50C5\u6703\u54E1\u53EF\u7528","subtitle.liveFreeTrialTip":"\u7FFB\u8B6F\u76F4\u64AD\u5B57\u5E55\u662F\u6703\u54E1\u5C08\u5C6C\u529F\u80FD\uFF0C\u60A8\u53EF\u514D\u8CBB\u9AD4\u9A57{free_trial_minutes} \u5206\u9418","subtitle.liveFreeTrialEndTip":"\u76F4\u64AD\u8996\u8A0A\u5B57\u5E55\u7FFB\u8B6F\u8A66\u7528\u5DF2\u7D50\u675F\uFF0C\u958B\u901A\u6703\u54E1\u53EF\u7E7C\u7E8C\u4F7F\u7528","subtitle.meetingAutoEnableSubtitle":"\u6703\u8B70\u5E73\u53F0\u81EA\u52D5\u958B\u555F\u96D9\u8A9E\u5B57\u5E55","subtitle.meetingAutoEnableSubtitleDescription":"\u958B\u555F\u5F8C\uFF0CTeams\u3001Zoom\u3001Google Meet \u6703\u8B70\u5E73\u53F0\u6703\u81EA\u52D5\u958B\u555F\u96D9\u8A9E\u5B57\u5E55","translationServices.qwen-mt":"Qwen-MT","labelKey.domains":"domains \u9818\u57DF\u63D0\u793A","description.qwenMtDomains":"\u5982\u679C\u60A8\u5E0C\u671B\u7FFB\u8B6F\u7684\u98A8\u683C\u66F4\u7B26\u5408\u67D0\u500B\u9818\u57DF\u7684\u7279\u6027\uFF0C\u5982\u6CD5\u5F8B\u3001\u653F\u52D9\u9818\u57DF\u7FFB\u8B6F\u7528\u8A9E\u61C9\u7576\u56B4\u8085\u6B63\u5F0F\uFF0C\u793E\u4EA4\u9818\u57DF\u7528\u8A9E\u61C9\u7576\u53E3\u8A9E\u5316\uFF0C\u53EF\u4EE5\u7528\u4E00\u6BB5\u81EA\u7136\u8A9E\u8A00\u6587\u5B57\u63CF\u8FF0\u60A8\u7684\u9818\u57DF\uFF0C\u5C07\u5176\u63D0\u4F9B\u7D66\u5927\u6A21\u578B\u4F5C\u70BA\u63D0\u793A\u3002\u9818\u57DF\u63D0\u793A\u8A9E\u53E5\u66AB\u6642\u53EA\u652F\u63F4\u82F1\u6587\u3002"};var i0={nologin:"Not logged in",loginForSafari:"Sign In or Sign Up",login:"Log In",goLogin:"Sign In",manageAccount:"Manage Account",openPremium:"Upgrade to Pro",logout:"Logout",lineBreakMaxTextCount:"Maximum characters per sentence after line break","translate-pdf":"Translate PDF","noSupportTranslate-pdf":/* MULTIPLE_REMOVED_BLOCKS */e.requestAiSubtitleMaxDurationLimitTip":"AI subtitle generation is not supported if the current video is longer than {maxDurationHours}.",disableOpenUpgradePage:"Disable major version upgrade notification","field.enableSingleTranslate":"Enable single translation","description.enableSingleTranslate":"When enabled, translation will stop immediately when the URL changes","field.enableSiteAutoTranslate":"Enable site auto-translation","description.enableSiteAutoTranslate":"When enabled, after translating the current page, navigation to other pages within the same site will also auto-translate",sidePanelTooltip:"Toggle Side Panel","error.maxQuotaError":"Insufficient Max translation quota",disableRewardCenter:"Hide reward center button",iKnow:"I Know",notShowAgain:"No more reminders","error.usageTips":"Immersive Translate usage tips","subtitle.filterAmbientSounds":"AI subtitles focus on dialogue content","error.maxQuotaUsageTips":"Your usage of the <span style='color: #FF7D00;'>Top Model</span> ({maxAIQuota} token limit) has exceeded <span style='color: #FF7D00;'>{maxAIUsed}</span> this month. <1>View usage</1>","subtitle.filterAmbientSoundsDescription":"After enabling this option, the subtitles will primarily display the spoken content of the speaker. Environmental sound annotations in the recognition results will be filtered out as much as possible, such as: (snoring), (thunder).","reportInfo.emailPlaceholder":"Contact email (required)","subtitle.quickButton.upgradePro":"Upgrade to membership","subtitle.quickButton.liveOnlyPro":"Live subtitle translation feature is only available to members.","subtitle.liveFreeTrialTip":"Live subtitle translation is a member-only feature. You can try it for free for {free_trial_minutes} minutes.","subtitle.liveFreeTrialEndTip":"The trial period for live video subtitle translation has ended. Please subscribe to continue using this service.","subtitle.meetingAutoEnableSubtitleDescription":"Once enabled, Teams, Zoom, and Google Meet meeting platforms will automatically enable bilingual subtitles.","subtitle.meetingAutoEnableSubtitle":"The conference platform automatically activates bilingual subtitles.","translationServices.qwen-mt":"Qwen-MT Model","labelKey.domains":"domains field prompt","description.qwenMtDomains":"If you want the translation style to better match the characteristics of a specific field, such as legal and government domain translations should be serious and formal, while social domain language should be colloquial, you can use natural language text to describe your domain and provide it to the large model as a prompt. Domain prompt statements currently only support English."};var E3=[{code:"zh-CN",messages:n0},{code:"zh-TW",messages:r0},{code:"en",messages:i0}],o0=fe(!1,!0)?["zh-CN","zh-TW","en"]:["zh-CN","zh-TW","en","ja","ar","de","es","fa","fr","he","hi","hu","it","ru","ko","pt-PT","pt-BR","tr"],Ia={};for(let e of E3)Ia[e.code]=e.messages;function S3(e,t){let a=e;return t&&Object.keys(t).forEach(n=>{let r=t[n];if(r===void 0)return;let i=D3(n);if(typeof r=="object"||i){let o=r;i&&typeof o=="string"&&(o={tag:"a",href:o,target:"_blank",class:de+"-link"});let s=`<${n}>`,u=a.indexOf(s);if(u!==-1){let c=o.tag||"a",l=a.indexOf(`</${n}>`);if(l!==-1){let d=a.substring(u+s.length,l),m=Object.keys(o).filter(p=>p!=="tag").map(p=>`${p}="${o[p]}"`).join(" ");a=a.replace(`${s}${d}</${n}>`,`<${c} ${m}>${d}</${c}>`)}}}else if(r){let o=new RegExp("{"+n+"}","gm");a=a.replace(o,r.toString())}}),a}function s0(e,t,a){let n=e[t];if(!n)return a;if(!a)return"";let r=a.split("."),i="";do{i+=r.shift();let o=n[i];o!==void 0&&(typeof o=="object"||!r.length)?(n=o,i=""):r.length?i+=".":n=a}while(r.length);return n}function T3(e,t,a,n){if(!Ia.hasOwnProperty(t)&&!Ia.hasOwnProperty(a))return e;let r=s0(Ia,t,e);return r===e&&t!==a&&(r=s0(Ia,a,e)),S3(r,n)}function D3(e){if(typeof e=="number")return!0;if(e){let t=parseInt(e);return!isNaN(t)}else return!1}function Re(e,t,a){return T3(t,e,"en",a)}function u0(e,t){let a=new Date(e),n=a.getFullYear().toString(),r=(a.getMonth()+1).toString().padStart(2,"0"),i=a.getDate().toString().padStart(2,"0"),o=a.getHours().toString().padStart(2,"0"),s=a.getMinutes().toString().padStart(2,"0"),u=a.getSeconds().toString().padStart(2,"0");return t.replace("YYYY",n).replace("MM",r).replace("DD",i).replace("HH",o).replace("mm",s).replace("ss",u)}function l0(e){return new Date(e).getTime()}var It=class extends Error{status;errorUIConfig;serviceName;serviceId="";data;constructor(t,a){if(t&&a){super(a),this.name=t;return}super(t)}initNetWork(t){return t&&(this.status=t),this}initStack(t){return t&&(this.stack=t),this}initData(t){return this.data=t,this}uiConfig(t){if(this.errorUIConfig)return this.errorUIConfig;if(!this.message)return{};let a=Re.bind(null,t.config.interfaceLanguage);this.serviceId=this.data?.translationService||t.translationService,this.serviceName=Im(t.config,this.serviceId);let n=null;if(n=this.handleServiceDiscontinued(t)||this.handleContextInvalidatedError(t)||this.handleMangaError(t)||this.handleProQuota(t)||this.handleUnavailableError(t)||this.handleProUser(t)||this.handleServiceMissingConfig(t)||this.handleNetwork(t)||this.handleFetchError(t),!n){let r=this.getErrorMsg(t);n={type:"error",title:`[${this.serviceName}] `+a("networkError"),errMsg:a("error.serveUnavailable",{serverName:this.serviceName,1:ri,2:B+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error",3:B+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error"})+"<br/><br/>"+a("errorReason",{message:r}),action:"changeService"}}return n.translationService=this.serviceName,n}getErrorMsg(t){return this.status?this.status<0?this.message=="Failed to fetch"?Re.bind(null,t.config.interfaceLanguage)("error.failToFetch"):this.message:`${this.status}: ${this.message}`:this.message}handleUnavailableError(t){let a=Re.bind(null,t.config.interfaceLanguage),n=this.message.startsWith("bingAuth"),r=this.data?.translationService==="transmart"&&this.message.startsWith("Server is busy now");if(n||r)return this.message=this.message.replace("bingAuth:",""),{type:"network",title:`[${this.serviceName}] `+a("networkError"),errMsg:a("error.serveUnavailable",{serverName:this.serviceName,1:ri,2:B+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error",3:B+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error"})+"<br/><br/>"+a("errorReason",{message:this.message}),action:"changeService"}}handleServiceMissingConfig(t){let a=Re.bind(null,t.config.interfaceLanguage);if(this.message.endsWith(" are required")||this.message.includes("You didn't provide an API key"))return{type:"configError",title:a("error.serveConfigError"),errMsg:this.getErrorMsg(t)+"<br /><br />"+a("error.reloadPageOfSetting"),action:"setting"}}handleNetwork(t){let a=Re.bind(null,t.config.interfaceLanguage),n="retry",r="network",i=`[${this.serviceName}] `+a("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${a("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${a("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${a("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${a("error.gemini.429")}<br/><br/> ${o}`:o=`${a("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${a("error.claude.403")}<br/><br/>${o}`:o=`${a("error.403")}<br/><br/>${o}`:this.status===400?o=`${a("error.400")}<br/><br/> ${o}`:this.status===502?o=`${a("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${a("error.subscriptionExpired")}<br/><br/> ${o}`,n="setting",r="configError",i=a("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${a("error.azure.401")}<br/><br/> ${o}`),{type:r,title:i,errMsg:o,action:n}}handleFetchError(t){let a=Re.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let n=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] `+a("networkError"),errMsg:a("error.serveUnavailable",{serverName:this.serviceName,1:ri,2:B+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error",3:B+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error"})+"<br/><br/>"+a("errorReason",{message:n}),action:"changeService"}}handleProUser(t){let a=Re.bind(null,t.config.interfaceLanguage);if(Ls(t.config,this.serviceId)){if(this.message.indexOf("token invalid")>=0||this.message.indexOf("Login required")>=0)return{type:"notLogin",title:a("notLoginPro"),errMsg:a("error.proTokenInvalid"),action:"login"};if(this.message.indexOf("activate Pro")>=0)return t.user?{type:"upgrade",title:a("upgradeToProErrorTitle",{service:this.serviceName}),errMsg:a("error.proUpgrade"),action:"upgrade"}:{type:"notLogin",title:a("notLoginPro"),errMsg:a("error.proTokenInvalid"),action:"login"};if(this.message.indexOf("subscription not found")>=0)return{type:"subscriptionExpires",title:a("error.subscriptionExpiredTitle"),errMsg:a("error.subscriptionExpired"),action:"upgrade"};if(t.config.translationServices?.[this.serviceId]?.provider=="pro"){let n=this.getErrorMsg(t);return{type:"network",title:"",errMsg:a("error.serveProUnavailable",{serverName:this.serviceName})+"<br/><br/>"+a("errorReason",{message:n}),action:"changeProService"}}}}handleMangaError(t){if(this.name!="manga")return;let a=Re.bind(null,t.config.interfaceLanguage);if(this.message.includes("Comics quota exceeded")){let r=[a("mangaQuotaError.package",{1:Pl})];c0(t.user,r,a);let i=a("errorReason",{message:`
        ${a("mangaQuotaError.solvedTitle")}<br/></br>
        ${r.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        `});return{type:"ProQuotaExceeded",title:a("intro.mangaImageTitle"),errMsg:i,action:"none"}}if(this.message.includes("ProQuota")){let r=this.getNewProQuotaError(t,this.message);return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:r,action:"none"}}if(this.message.includes("Image too large"))return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:a("error.imageTooLarge"),action:"none"};if(this.message.includes("Tainted canvases may not be exported"))return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:a("error.siteDisableManga"),action:"none"};let n=a("errorReason",{message:this.message});return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:n,action:"none"}}handleProQuota(t){if(!this.message.includes("ProQuota:")||!t.user?.subscription)return;this.message.includes("NewProQuota")&&(this.message=this.message.replace("NewProQuota:",""));let a=this.message;try{a=JSON.parse(this.message).error}catch{}let{errMsg:n,title:r}=this.getNewProQuotaError(t,a);return{type:"ProQuotaExceeded",title:r,errMsg:n,action:"changeProService"}}handleContextInvalidatedError(t){return this.name!=="contextInvalidated"?void 0:{type:"ContextInvalidated",title:"",errMsg:Re.bind(null,t.config.interfaceLanguage)("ctxInvalidatedError"),action:"refreshPage"}}getNewProQuotaError(t,a){let n=Re.bind(null,t.config.interfaceLanguage),r=t.user.subscription,{isTrial:i,memberShip:o}=r,s="",u=[],c="";c=_o;let l=/Max translation quota/ig.test(a);l&&(c=_o+"&type=top_model",s=n("error.maxQuotaError"));let d=o=="pro"&&l;d&&u.push(n("translationServices.upgradeMaxUser",{1:Fl+"error_modal"})),u.push(n("proQuotaError.newPackage",{1:c})),i&&!d&&u.push(n("proQuotaError.trail",{1:Bl})),!i&&!d&&c0(t.user,u,n);let m=a+`${n("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((p,v)=>`${v+1}. ${p}`).join("<br/>")}`;return{title:s,errMsg:n("errorReason",{message:m})}}handleServiceDiscontinued(t){let a=Re.bind(null,t.config.interfaceLanguage);if(this.name==="translationServiceDiscontinued")return{type:"network",title:a("error.serviceDiscontinued"),errMsg:a("error.serviceDiscontinuedMessage",{translationService:this.serviceName}),action:"changeService"}}};function c0(e,t,a){if(!e||!e?.subscription)return;let n=e?.subscription,{isTrial:r,openAITokenUsedCountResetTime:i,subscriptionTo:o}=n;!r&&(n.cancelAtPeriodEnd==="false"||l0(o)>i)&&t.push(a("proQuotaError.resetTime",{resetTime:u0(i,"YYYY-MM-DD HH:mm:ss")}))}var au=class extends Error{constructor(t,a){super(`Exceeded max retry count (${a})`),this.name="RetryError",this.cause=t}},C3={multiplier:2,maxTimeout:6e4,maxAttempts:5,minTimeout:1e3};async function d0(e,t){let a={...C3,...t};if(a.maxTimeout>=0&&a.minTimeout>a.maxTimeout)throw new RangeError("minTimeout is greater than maxTimeout");let n=a.minTimeout,r;for(let i=0;i<a.maxAttempts;i++)try{return await e()}catch(o){if(o.message.includes("Request timeout")||o.message.includes("User subscription not found"))throw o;await new Promise(s=>setTimeout(s,n)),n*=a.multiplier,n=Math.max(n,a.minTimeout),a.maxTimeout>=0&&(n=Math.min(n,a.maxTimeout)),r=o}throw new au(r,a.maxAttempts)}function m0(e){if(!e)return e;let t=new FormData;return Object.entries(e).forEach(([a,n])=>{if(a.startsWith("base64_")){let r=nu(n);t.append(`${a.split("base64_")[1]}`,r)}else t.append(a,n)}),t}function p0(e){if(!e)return e;let t=new URLSearchParams;return Object.entries(e).forEach(([a,n])=>{if(Array.isArray(n))for(let r of n)t.append(a,r);else t.append(a,n)}),t.toString()}function nu(e){let{mimeType:t,base64:a}=M3(e),n=atob(a),r=[],i=512;for(let s=0;s<n.length;s+=i){let u=n.slice(s,s+i),c=new Array(u.length);for(let d=0;d<u.length;d++)c[d]=u.charCodeAt(d);let l=new Uint8Array(c);r.push(l)}return new Blob(r,{type:t})}function M3(e){let t=/^data:(.+?);base64,(.*)$/,a=e.match(t),n="",r="";return a&&a.length===3&&(n=a[1],r=a[2]),{mimeType:n,base64:r}}var g0=Object.prototype.toString;function Ui(e){switch(g0.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Vt(e,Error)}}function kr(e,t){return g0.call(e)===`[object ${t}]`}function qi(e){return kr(e,"ErrorEvent")}function h0(e){return kr(e,"DOMException")}function la(e){return kr(e,"String")}function Er(e){return e===null||typeof e!="object"&&typeof e!="function"}function Bt(e){return kr(e,"Object")}function Sr(e){return typeof Event<"u"&&Vt(e,Event)}function f0(e){return!1}function b0(e){return kr(e,"RegExp")}function jn(e){return!!(e&&e.then&&typeof e.then=="function")}function y0(e){return Bt(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function v0(e){return typeof e=="number"&&e!==e}function Vt(e,t){try{return e instanceof t}catch{return!1}}function Tr(e,t){try{let a=e,n=5,r=80,i=[],o=0,s=0,u=" > ",c=u.length,l;for(;a&&o++<n&&(l=I3(a,t),!(l==="html"||o>1&&s+i.length*c+l.length>=r));)i.push(l),s+=l.length,a=a.parentNode;return i.reverse().join(u)}catch{return"<unknown>"}}function I3(e,t){let a=e,n=[],r,i,o,s,u;if(!a||!a.tagName)return"";n.push(a.tagName.toLowerCase());let c=t&&t.length?t.filter(d=>a.getAttribute(d)).map(d=>[d,a.getAttribute(d)]):null;if(c&&c.length)c.forEach(d=>{n.push(`[${d[0]}="${d[1]}"]`)});else if(a.id&&n.push(`#${a.id}`),r=a.className,r&&la(r))for(i=r.split(/\s+/),u=0;u<i.length;u++)n.push(`.${i[u]}`);let l=["type","name","title","alt"];for(u=0;u<l.length;u++)o=l[u],s=a.getAttribute(o),s&&n.push(`[${o}="${s}"]`);return n.join("")}function x0(){try{return location.href}catch{return""}}var Le=class extends Error{constructor(a){super(a);this.message=a;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var P3=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function F3(e){return e==="http"||e==="https"}function Ln(e,t=!1){let{host:a,path:n,pass:r,port:i,projectId:o,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&r?`:${r}`:""}@${a}${i?`:${i}`:""}/${n&&`${n}/`}${o}`}function B3(e){let t=P3.exec(e);if(!t)throw new Le(`Invalid Sentry Dsn: ${e}`);let[a,n,r="",i,o="",s]=t.slice(1),u="",c=s,l=c.split("/");if(l.length>1&&(u=l.slice(0,-1).join("/"),c=l.pop()),c){let d=c.match(/^\d+/);d&&(c=d[0])}return w0({host:i,pass:r,path:u,projectId:c,port:o,protocol:a,publicKey:n})}function w0(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function R3(e){if(!!1)return;let{port:t,projectId:a,protocol:n}=e;if(["protocol","publicKey","host","projectId"].forEach(i=>{if(!e[i])throw new Le(`Invalid Sentry Dsn: ${String(i)} missing`)}),!a.match(/^\d+$/))throw new Le(`Invalid Sentry Dsn: Invalid projectId ${a}`);if(!F3(n))throw new Le(`Invalid Sentry Dsn: Invalid protocol ${n}`);if(t&&isNaN(parseInt(t,10)))throw new Le(`Invalid Sentry Dsn: Invalid port ${t}`);return!0}function ru(e){let t=typeof e=="string"?B3(e):w0(e);return R3(t),t}var _3={};function Ce(){return typeof window<"u"?window:typeof self<"u"?self:_3}function Nn(e,t,a){let n=a||Ce(),r=n.__SENTRY__=n.__SENTRY__||{};return r[e]||(r[e]=t())}var Dr=["debug","info","warn","error","log","assert","trace"];function iu(e){if(!("console"in Ce()))return e();let a=console,n={};Dr.forEach(r=>{let i=a[r]&&a[r].__sentry_original__;r in console&&i&&(n[r]=a[r],a[r]=i)});try{return e()}finally{Object.keys(n).forEach(r=>{a[r]=n[r]})}}function A0(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1}};return!1?Dr.forEach(a=>{t[a]=(...n)=>{e&&iu(()=>{})}}):Dr.forEach(a=>{t[a]=()=>{}}),t}var $;!1?$=Nn("logger",A0):$=A0();function Za(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.substr(0,t)}...`}function ou(e,t){if(!Array.isArray(e))return"";let a=[];for(let n=0;n<e.length;n++){let r=e[n];try{a.push(String(r))}catch{a.push("[value cannot be serialized]")}}return a.join(t)}function Gi(e,t){return la(e)?b0(t)?t.test(e):typeof t=="string"?e.indexOf(t)!==-1:!1:!1}function Rt(e,t,a){if(!(t in e))return;let n=e[t],r=a(n);if(typeof r=="function")try{uu(r,n)}catch{}e[t]=r}function Cr(e,t,a){Object.defineProperty(e,t,{value:a,writable:!0,configurable:!0})}function uu(e,t){let a=t.prototype||{};e.prototype=t.prototype=a,Cr(e,"__sentry_original__",t)}function On(e){return e.__sentry_original__}function S0(e){return Object.keys(e).map(t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`).join("&")}function lu(e){if(Ui(e))return{message:e.message,name:e.name,stack:e.stack,...E0(e)};if(Sr(e)){let t={type:e.type,target:k0(e.target),currentTarget:k0(e.currentTarget),...E0(e)};return typeof CustomEvent<"u"&&Vt(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function k0(e){try{return f0(e)?Tr(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function E0(e){if(typeof e=="object"&&e!==null){let t={};for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}else return{}}function T0(e,t=40){let a=Object.keys(lu(e));if(a.sort(),!a.length)return"[object has no keys]";if(a[0].length>=t)return Za(a[0],t);for(let n=a.length;n>0;n--){let r=a.slice(0,n).join(", ");if(!(r.length>t))return n===a.length?r:Za(r,t)}return""}function Xa(e){return su(e,new Map)}function su(e,t){if(Bt(e)){let a=t.get(e);if(a!==void 0)return a;let n={};t.set(e,n);for(let r of Object.keys(e))typeof e[r]<"u"&&(n[r]=su(e[r],t));return n}if(Array.isArray(e)){let a=t.get(e);if(a!==void 0)return a;let n=[];return t.set(e,n),e.forEach(r=>{n.push(su(r,t))}),n}return e}var j3=50;function du(...e){let t=e.sort((a,n)=>a[0]-n[0]).map(a=>a[1]);return(a,n=0)=>{let r=[];for(let i of a.split(`
`).slice(n))for(let o of t){let s=o(i);if(s){r.push(s);break}}return L3(r)}}function D0(e){return Array.isArray(e)?du(...e):e}function L3(e){if(!e.length)return[];let t=e,a=t[0].function||"",n=t[t.length-1].function||"";return(a.indexOf("captureMessage")!==-1||a.indexOf("captureException")!==-1)&&(t=t.slice(1)),n.indexOf("sentryWrapped")!==-1&&(t=t.slice(0,-1)),t.slice(0,j3).map(r=>({...r,filename:r.filename||t[0].filename,function:r.function||"?"})).reverse()}var cu="<anonymous>";function _t(e){try{return!e||typeof e!="function"?cu:e.name||cu}catch{return cu}}function C0(){if(!("fetch"in Ce()))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function M0(e){return e&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function I0(){return!0}var ca=Ce(),Mr={},P0={};function N3(e){if(!P0[e])switch(P0[e]=!0,e){case"console":O3();break;case"fetch":z3();break;case"error":G3();break;case"unhandledrejection":H3();break;default:!1&&$.warn("unknown instrumentation type:",e);return}}function jt(e,t){Mr[e]=Mr[e]||[],Mr[e].push(t),N3(e)}function zn(e,t){if(!(!e||!Mr[e]))for(let a of Mr[e]||[])try{a(t)}catch(n){!1&&$.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${_t(a)}
Error:`,n)}}function O3(){"console"in ca&&Dr.forEach(function(e){e in console&&Rt(console,e,function(t){return function(...a){zn("console",{args:a,level:e}),t&&t.apply(console,a)}})})}function z3(){I0()&&Rt(ca,"fetch",function(e){return function(...t){let a={args:t,fetchData:{method:U3(t),url:q3(t)},startTimestamp:Date.now()};return zn("fetch",{...a}),e.apply(ca,t).then(n=>(zn("fetch",{...a,endTimestamp:Date.now(),response:n}),n),n=>{throw zn("fetch",{...a,endTimestamp:Date.now(),error:n}),n})}})}function U3(e=[]){return"Request"in ca&&Vt(e[0],Request)&&e[0].method?String(e[0].method).toUpperCase():e[1]&&e[1].method?String(e[1].method).toUpperCase():"GET"}function q3(e=[]){return typeof e[0]=="string"?e[0]:"Request"in ca&&Vt(e[0],Request)?e[0].url:String(e[0])}var mu=null;function G3(){mu=ca.onerror,ca.onerror=function(e,t,a,n,r){return zn("error",{column:n,error:r,line:a,msg:e,url:t}),mu?mu.apply(this,arguments):!1}}var pu=null;function H3(){pu=ca.onunhandledrejection,ca.onunhandledrejection=function(e){return zn("unhandledrejection",e),pu?pu.apply(this,arguments):!0}}function F0(){let e=typeof WeakSet=="function",t=e?new WeakSet:[];function a(r){if(e)return t.has(r)?!0:(t.add(r),!1);for(let i=0;i<t.length;i++)if(t[i]===r)return!0;return t.push(r),!1}function n(r){if(e)t.delete(r);else for(let i=0;i<t.length;i++)if(t[i]===r){t.splice(i,1);break}}return[a,n]}function da(){if(crypto&&crypto.randomUUID)return crypto.randomUUID().replace(/-/g,"");let e=crypto&&crypto.getRandomValues?()=>crypto.getRandomValues(new Uint8Array(1))[0]:()=>Math.random()*16;return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,t=>(t^(e()&15)>>t/4).toString(16))}function Hi(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let a=t[6]||"",n=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],relative:t[5]+a+n}}function B0(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function ma(e){let{message:t,event_id:a}=e;if(t)return t;let n=B0(e);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||a||"<unknown>":a||"<unknown>"}function Ir(e,t,a){let n=e.exception=e.exception||{},r=n.values=n.values||[],i=r[0]=r[0]||{};i.value||(i.value=t||""),i.type||(i.type=a||"Error")}function Pa(e,t){let a=B0(e);if(!a)return;let n={type:"generic",handled:!0},r=a.mechanism;if(a.mechanism={...n,...r,...t},t&&"data"in t){let i={...r&&r.data,...t.data};a.mechanism.data=i}}function gu(e){if(e&&e.__sentry_captured__)return!0;try{Cr(e,"__sentry_captured__",!0)}catch{}return!1}function R0(){return!1}function pa(e,t=1/0,a=1/0){try{return hu("",e,t,a)}catch(n){return{ERROR:`**non-serializable** (${n})`}}}function fu(e,t=3,a=100*1024){let n=pa(e,t);return V3(n)>a?fu(e,t-1,a):n}function hu(e,t,a=1/0,n=1/0,r=F0()){let[i,o]=r;if(t===null||["number","boolean","string"].includes(typeof t)&&!v0(t))return t;let s=K3(e,t);if(!s.startsWith("[object "))return s;if(t.__sentry_skip_normalization__)return t;if(a===0)return s.replace("object ","");if(i(t))return"[Circular ~]";let u=t;if(u&&typeof u.toJSON=="function")try{let m=u.toJSON();return hu("",m,a-1,n,r)}catch{}let c=Array.isArray(t)?[]:{},l=0,d=lu(t);for(let m in d){if(!Object.prototype.hasOwnProperty.call(d,m))continue;if(l>=n){c[m]="[MaxProperties ~]";break}let p=d[m];c[m]=hu(m,p,a-1,n,r),l+=1}return o(t),c}function K3(e,t){try{return e==="domain"&&t&&typeof t=="object"&&t._events?"[Domain]":e==="domainEmitter"?"[DomainEmitter]":typeof window<"u"&&t===window?"[Window]":y0(t)?"[SyntheticEvent]":typeof t=="number"&&t!==t?"[NaN]":t===void 0?"[undefined]":typeof t=="function"?`[Function: ${_t(t)}]`:typeof t=="symbol"?`[${String(t)}]`:typeof t=="bigint"?`[BigInt: ${String(t)}]`:`[object ${Object.getPrototypeOf(t).constructor.name}]`}catch(a){return`**non-serializable** (${a})`}}function W3(e){return~-encodeURI(e).split(/%..|./).length}function V3(e){return W3(JSON.stringify(e))}function ht(e){return new Qt(t=>{t(e)})}function Pr(e){return new Qt((t,a)=>{a(e)})}var Qt=class e{_state=0;_handlers=[];_value;constructor(t){try{t(this._resolve,this._reject)}catch(a){this._reject(a)}}then(t,a){return new e((n,r)=>{this._handlers.push([!1,i=>{if(!t)n(i);else try{n(t(i))}catch(o){r(o)}},i=>{if(!a)r(i);else try{n(a(i))}catch(o){r(o)}}]),this._executeHandlers()})}catch(t){return this.then(a=>a,t)}finally(t){return new e((a,n)=>{let r,i;return this.then(o=>{i=!1,r=o,t&&t()},o=>{i=!0,r=o,t&&t()}).then(()=>{if(i){n(r);return}a(r)})})}_resolve=t=>{this._setResult(1,t)};_reject=t=>{this._setResult(2,t)};_setResult=(t,a)=>{if(this._state===0){if(jn(a)){a.then(this._resolve,this._reject);return}this._state=t,this._value=a,this._executeHandlers()}};_executeHandlers=()=>{if(this._state===0)return;let t=this._handlers.slice();this._handlers=[],t.forEach(a=>{a[0]||(this._state===1&&a[1](this._value),this._state===2&&a[2](this._value),a[0]=!0)})}};function _0(e){let t=[];function a(){return e===void 0||t.length<e}function n(o){return t.splice(t.indexOf(o),1)[0]}function r(o){if(!a())return Pr(new Le("Not adding Promise due to buffer limit reached."));let s=o();return t.indexOf(s)===-1&&t.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s}function i(o){return new Qt((s,u)=>{let c=t.length;if(!c)return s(!0);let l=setTimeout(()=>{o&&o>0&&s(!1)},o);t.forEach(d=>{ht(d).then(()=>{--c||(clearTimeout(l),s(!0))},u)})})}return{$:t,add:r,drain:i}}var Q3=["fatal","error","warning","log","info","debug"];function j0(e){return e==="warn"?"warning":Q3.includes(e)?e:"log"}var yu={nowSeconds:()=>Date.now()/1e3};function Y3(){if(!performance||!performance.now)return;let e=Date.now()-performance.now();return{now:()=>performance.now(),timeOrigin:e}}var bu=Y3(),L0=bu===void 0?yu:{nowSeconds:()=>(bu.timeOrigin+bu.now())/1e3},Fa=yu.nowSeconds.bind(yu),vu=L0.nowSeconds.bind(L0);var Ki,qP=(()=>{if(!performance||!performance.now){Ki="none";return}let e=3600*1e3,t=performance.now(),a=Date.now(),n=performance.timeOrigin?Math.abs(performance.timeOrigin+t-a):e,r=n<e,i=performance.timeOrigin,s=typeof i=="number"?Math.abs(i+t-a):e,u=s<e;return r||u?n<=s?(Ki="timeOrigin",performance.timeOrigin):(Ki="navigationStart",i):(Ki="dateNow",a)})();var KP=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function $a(e,t=[]){return[e,t]}function N0(e,t){let[a,n]=e;return[a,[...n,t]]}function wu(e,t){e[1].forEach(n=>{let r=n[0].type;t(n,r)})}function xu(e,t){return(t||new TextEncoder).encode(e)}function Wi(e,t){let[a,n]=e,r=JSON.stringify(a);function i(o){typeof r=="string"?r=typeof o=="string"?r+o:[xu(r,t),o]:r.push(typeof o=="string"?xu(o,t):o)}for(let o of n){let[s,u]=o;i(`
${JSON.stringify(s)}
`),i(typeof u=="string"||u instanceof Uint8Array?u:JSON.stringify(u))}return typeof r=="string"?r:J3(r)}function J3(e){let t=e.reduce((r,i)=>r+i.length,0),a=new Uint8Array(t),n=0;for(let r of e)a.set(r,n),n+=r.length;return a}function O0(e,t){let a=typeof e.data=="string"?xu(e.data,t):e.data;return[Xa({type:"attachment",length:a.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),a]}var Z3={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default"};function Au(e){return Z3[e]}function z0(e,t,a){let n=[{type:"client_report"},{timestamp:a||Fa(),discarded_events:e}];return $a(t?{dsn:t}:{},[n])}var X3=60*1e3;function $3(e,t=Date.now()){let a=parseInt(`${e}`,10);if(!isNaN(a))return a*1e3;let n=Date.parse(`${e}`);return isNaN(n)?X3:n-t}function ev(e,t){return e[t]||e.all||0}function U0(e,t,a=Date.now()){return ev(e,t)>a}function q0(e,{statusCode:t,headers:a},n=Date.now()){let r={...e},i=a&&a["x-sentry-rate-limits"],o=a&&a["retry-after"];if(i)for(let s of i.trim().split(",")){let[u,c]=s.split(":",2),l=parseInt(u,10),d=(isNaN(l)?60:l)*1e3;if(!c)r.all=n+d;else for(let m of c.split(";"))r[m]=n+d}else o?r.all=n+$3(o,n):t===429&&(r.all=n+60*1e3);return r}function G0(e){return e[0]}function ku(e){let t=vu(),a={sid:da(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>tv(a)};return e&&Yt(a,e),a}function Yt(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||vu(),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:da()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{let a=e.timestamp-e.started;e.duration=a>=0?a:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function Eu(e,t){let a={};t?a={status:t}:e.status==="ok"&&(a={status:"exited"}),Yt(e,a)}function tv(e){return Xa({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}var H0=100,Lt=class e{_notifyingListeners;_scopeListeners;_eventProcessors;_breadcrumbs;_user;_tags;_extra;_contexts;_attachments;_sdkProcessingMetadata;_fingerprint;_level;_transactionName;_span;_session;_requestSession;constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={}}static clone(t){let a=new e;return t&&(a._breadcrumbs=[...t._breadcrumbs],a._tags={...t._tags},a._extra={...t._extra},a._contexts={...t._contexts},a._user=t._user,a._level=t._level,a._span=t._span,a._session=t._session,a._transactionName=t._transactionName,a._fingerprint=t._fingerprint,a._eventProcessors=[...t._eventProcessors],a._requestSession=t._requestSession,a._attachments=[...t._attachments]),a}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{},this._session&&Yt(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,a){return this._tags={...this._tags,[t]:a},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,a){return this._extra={...this._extra,[t]:a},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,a){return a===null?delete this._contexts[t]:this._contexts={...this._contexts,[t]:a},this._notifyScopeListeners(),this}setSpan(t){return this._span=t,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let t=this.getSpan();return t&&t.transaction}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;if(typeof t=="function"){let a=t(this);return a instanceof e?a:this}return t instanceof e?(this._tags={...this._tags,...t._tags},this._extra={...this._extra,...t._extra},this._contexts={...this._contexts,...t._contexts},t._user&&Object.keys(t._user).length&&(this._user=t._user),t._level&&(this._level=t._level),t._fingerprint&&(this._fingerprint=t._fingerprint),t._requestSession&&(this._requestSession=t._requestSession)):Bt(t)&&(t=t,this._tags={...this._tags,...t.tags},this._extra={...this._extra,...t.extra},this._contexts={...this._contexts,...t.contexts},t.user&&(this._user=t.user),t.level&&(this._level=t.level),t.fingerprint&&(this._fingerprint=t.fingerprint),t.requestSession&&(this._requestSession=t.requestSession)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this}addBreadcrumb(t,a){let n=typeof a=="number"?Math.min(a,H0):H0;if(n<=0)return this;let r={timestamp:Fa(),...t};return this._breadcrumbs=[...this._breadcrumbs,r].slice(-n),this._notifyScopeListeners(),this}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(t,a={}){if(this._extra&&Object.keys(this._extra).length&&(t.extra={...this._extra,...t.extra}),this._tags&&Object.keys(this._tags).length&&(t.tags={...this._tags,...t.tags}),this._user&&Object.keys(this._user).length&&(t.user={...this._user,...t.user}),this._contexts&&Object.keys(this._contexts).length&&(t.contexts={...this._contexts,...t.contexts}),this._level&&(t.level=this._level),this._transactionName&&(t.transaction=this._transactionName),this._span){t.contexts={trace:this._span.getTraceContext(),...t.contexts};let n=this._span.transaction&&this._span.transaction.name;n&&(t.tags={transaction:n,...t.tags})}return this._applyFingerprint(t),t.breadcrumbs=[...t.breadcrumbs||[],...this._breadcrumbs],t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...this._sdkProcessingMetadata},this._notifyEventProcessors([...K0(),...this._eventProcessors],t,a)}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}_notifyEventProcessors(t,a,n,r=0){return new Qt((i,o)=>{let s=t[r];if(a===null||typeof s!="function")i(a);else{let u=s({...a},n);!1&&s.id&&u===null&&$.log(`Event processor "${s.id}" dropped event`),jn(u)?u.then(c=>this._notifyEventProcessors(t,c,n,r+1).then(i)).then(null,o):this._notifyEventProcessors(t,u,n,r+1).then(i).then(null,o)}})}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}_applyFingerprint(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this._fingerprint&&(t.fingerprint=t.fingerprint.concat(this._fingerprint)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}};function K0(){return Nn("globalEventProcessors",()=>[])}function ga(e){K0().push(e)}var Su=4,av=100,Ba=class{constructor(t,a=new Lt,n=Su){this._version=n;this.getStackTop().scope=a,t&&this.bindClient(t)}_stack=[{}];_lastEventId;isOlderThan(t){return this._version<t}bindClient(t){let a=this.getStackTop();a.client=t,t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){let t=Lt.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return this.getStack().length<=1?!1:!!this.getStack().pop()}withScope(t){let a=this.pushScope();try{t(a)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(t,a){let n=this._lastEventId=a&&a.event_id?a.event_id:da(),r=new Error("Sentry syntheticException");return this._withClient((i,o)=>{i.captureException(t,{originalException:t,syntheticException:r,...a,event_id:n},o)}),n}captureMessage(t,a,n){let r=this._lastEventId=n&&n.event_id?n.event_id:da(),i=new Error(t);return this._withClient((o,s)=>{o.captureMessage(t,a,{originalException:t,syntheticException:i,...n,event_id:r},s)}),r}captureEvent(t,a){let n=a&&a.event_id?a.event_id:da();return t.type!=="transaction"&&(this._lastEventId=n),this._withClient((r,i)=>{r.captureEvent(t,{...a,event_id:n},i)}),n}lastEventId(){return this._lastEventId}addBreadcrumb(t,a){let{scope:n,client:r}=this.getStackTop();if(!n||!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:o=av}=r.getOptions&&r.getOptions()||{};if(o<=0)return;let u={timestamp:Fa(),...t},c=i?iu(()=>i(u,a)):u;c!==null&&n.addBreadcrumb(c,o)}setUser(t){let a=this.getScope();a&&a.setUser(t)}setTags(t){let a=this.getScope();a&&a.setTags(t)}setExtras(t){let a=this.getScope();a&&a.setExtras(t)}setTag(t,a){let n=this.getScope();n&&n.setTag(t,a)}setExtra(t,a){let n=this.getScope();n&&n.setExtra(t,a)}setContext(t,a){let n=this.getScope();n&&n.setContext(t,a)}configureScope(t){let{scope:a,client:n}=this.getStackTop();a&&n&&t(a)}run(t){let a=Fr(this);try{t(this)}finally{Fr(a)}}getIntegration(t){let a=this.getClient();if(!a)return null;try{return a.getIntegration(t)}catch{return!1&&$.warn(`Cannot retrieve integration ${t.id} from the current Hub`),null}}startTransaction(t,a){return this._callExtensionMethod("startTransaction",t,a)}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this._sendSessionUpdate()}endSession(){let t=this.getStackTop(),a=t&&t.scope,n=a&&a.getSession();n&&Eu(n),this._sendSessionUpdate(),a&&a.setSession()}startSession(t){let{scope:a,client:n}=this.getStackTop(),{release:r,environment:i}=n&&n.getOptions()||{},o=Ce(),{userAgent:s}=o.navigator||{},u=ku({release:r,environment:i,...a&&{user:a.getUser()},...s&&{userAgent:s},...t});if(a){let c=a.getSession&&a.getSession();c&&c.status==="ok"&&Yt(c,{status:"exited"}),this.endSession(),a.setSession(u)}return u}shouldSendDefaultPii(){let t=this.getClient(),a=t&&t.getOptions();return!!(a&&a.sendDefaultPii)}_sendSessionUpdate(){let{scope:t,client:a}=this.getStackTop();if(!t)return;let n=t.getSession();n&&a&&a.captureSession&&a.captureSession(n)}_withClient(t){let{scope:a,client:n}=this.getStackTop();n&&t(n,a)}_callExtensionMethod(t,...a){let r=Br().__SENTRY__;if(r&&r.extensions&&typeof r.extensions[t]=="function")return r.extensions[t].apply(this,a);!1&&$.warn(`Extension method ${t} couldn't be found, doing nothing.`)}};function Br(){let e=Ce();return e.__SENTRY__=e.__SENTRY__||{extensions:{},hub:void 0},e}function Fr(e){let t=Br(),a=Nt(t);return Vi(t,e),a}function be(){let e=Br();return(!W0(e)||Nt(e).isOlderThan(Su))&&Vi(e,new Ba),R0()?nv(e):Nt(e)}function nv(e){try{let t=Br().__SENTRY__,a=t&&t.extensions&&t.extensions.domain&&t.extensions.domain.active;if(!a)return Nt(e);if(!W0(a)||Nt(a).isOlderThan(Su)){let n=Nt(e).getStackTop();Vi(a,new Ba(n.client,Lt.clone(n.scope)))}return Nt(a)}catch{return Nt(e)}}function W0(e){return!!(e&&e.__SENTRY__&&e.__SENTRY__.hub)}function Nt(e){return Nn("hub",()=>new Ba,e)}function Vi(e,t){if(!e)return!1;let a=e.__SENTRY__=e.__SENTRY__||{};return a.hub=t,!0}function en(e,t){return be().captureException(e,{captureContext:t})}function Rr(e){be().withScope(e)}var rv="7";function iv(e){let t=e.protocol?`${e.protocol}:`:"",a=e.port?`:${e.port}`:"";return`${t}//${e.host}${a}${e.path?`/${e.path}`:""}/api/`}function ov(e){return`${iv(e)}${e.projectId}/envelope/`}function sv(e,t){return S0({sentry_key:e.publicKey,sentry_version:rv,...t&&{sentry_client:`${t.name}/${t.version}`}})}function _r(e,t={}){let a=typeof t=="string"?t:t.tunnel,n=typeof t=="string"||!t._metadata?void 0:t._metadata.sdk;return a||`${ov(e)}?${sv(e,n)}`}function rp(e){if(!e||!e.sdk)return;let{name:t,version:a}=e.sdk;return{name:t,version:a}}function uv(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function ip(e,t,a,n){let r=rp(a),i={sent_at:new Date().toISOString(),...r&&{sdk:r},...!!n&&{dsn:Ln(t)}},o="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e];return $a(i,[o])}function op(e,t,a,n){let r=rp(a),i=e.type||"event",{transactionSampling:o}=e.sdkProcessingMetadata||{},{method:s,rate:u}=o||{};uv(e,a&&a.sdk);let c=lv(e,r,n,t);return delete e.sdkProcessingMetadata,$a(c,[[{type:i,sample_rates:[{id:s,rate:u}]},e]])}function lv(e,t,a,n){let r=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.baggage,i=r&&G0(r);return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!a&&{dsn:Ln(n)},...e.type==="transaction"&&i&&{trace:Xa({...i})}}}var sp=[];function up(e){return e.reduce((t,a)=>(t.every(n=>a.name!==n.name)&&t.push(a),t),[])}function Tu(e){let t=e.defaultIntegrations&&[...e.defaultIntegrations]||[],a=e.integrations,n=[...up(t)];Array.isArray(a)?n=[...n.filter(o=>a.every(s=>s.name!==o.name)),...up(a)]:typeof a=="function"&&(n=a(n),n=Array.isArray(n)?n:[n]);let r=n.map(o=>o.name),i="Debug";return r.indexOf(i)!==-1&&n.push(...n.splice(r.indexOf(i),1)),n}function lp(e){let t={};return e.forEach(a=>{t[a.name]=a,sp.indexOf(a.name)===-1&&(a.setupOnce(ga,be),sp.push(a.name),!1&&$.log(`Integration installed: ${a.name}`))}),t}var cp="Not capturing exception because it's already been captured.",jr=class{_options;_dsn;_transport;_integrations={};_integrationsInitialized=!1;_numProcessing=0;_outcomes={};constructor(t){if(this._options=t,t.dsn){this._dsn=ru(t.dsn);let a=_r(this._dsn,t);this._transport=t.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:a})}else!1&&$.warn("No DSN provided, client will not do anything.")}captureException(t,a,n){if(gu(t)){!1&&$.log(cp);return}let r=a&&a.event_id;return this._process(this.eventFromException(t,a).then(i=>this._captureEvent(i,a,n)).then(i=>{r=i})),r}captureMessage(t,a,n,r){let i=n&&n.event_id,o=Er(t)?this.eventFromMessage(String(t),a,n):this.eventFromException(t,n);return this._process(o.then(s=>this._captureEvent(s,n,r)).then(s=>{i=s})),i}captureEvent(t,a,n){if(a&&a.originalException&&gu(a.originalException)){!1&&$.log(cp);return}let r=a&&a.event_id;return this._process(this._captureEvent(t,a,n).then(i=>{r=i})),r}captureSession(t){if(!this._isEnabled()){!1&&$.warn("SDK not enabled, will not capture session.");return}typeof t.release!="string"?!1&&$.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),Yt(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getTransport(){return this._transport}flush(t){let a=this._transport;return a?this._isClientDoneProcessing(t).then(n=>a.flush(t).then(r=>n&&r)):ht(!0)}close(t){return this.flush(t).then(a=>(this.getOptions().enabled=!1,a))}setupIntegrations(){this._isEnabled()&&!this._integrationsInitialized&&(this._integrations=lp(this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch{return!1&&$.warn(`Cannot retrieve integration ${t.id} from the current Client`),null}}sendEvent(t,a={}){if(this._dsn){let n=op(t,this._dsn,this._options._metadata,this._options.tunnel);for(let r of a.attachments||[])n=N0(n,O0(r,this._options.transportOptions&&this._options.transportOptions.textEncoder));this._sendEnvelope(n)}}sendSession(t){if(this._dsn){let a=ip(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(a)}}recordDroppedEvent(t,a){if(this._options.sendClientReports){let n=`${t}:${a}`;!1&&$.log(`Adding outcome: "${n}"`),this._outcomes[n]=this._outcomes[n]+1||1}}_updateSessionFromEvent(t,a){let n=!1,r=!1,i=a.exception&&a.exception.values;if(i){r=!0;for(let u of i){let c=u.mechanism;if(c&&c.handled===!1){n=!0;break}}}let o=t.status==="ok";(o&&t.errors===0||o&&n)&&(Yt(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new Qt(a=>{let n=0,r=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),a(!0)):(n+=r,t&&n>=t&&(clearInterval(i),a(!1)))},r)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._dsn!==void 0}_prepareEvent(t,a,n){let{normalizeDepth:r=3,normalizeMaxBreadth:i=1e3}=this.getOptions(),o={...t,event_id:t.event_id||a.event_id||da(),timestamp:t.timestamp||Fa()};this._applyClientOptions(o),this._applyIntegrationsMetadata(o);let s=n;a.captureContext&&(s=Lt.clone(s).update(a.captureContext));let u=ht(o);if(s){let c=[...a.attachments||[],...s.getAttachments()];c.length&&(a.attachments=c),u=s.applyToEvent(o,a)}return u.then(c=>typeof r=="number"&&r>0?this._normalizeEvent(c,r,i):c)}_normalizeEvent(t,a,n){if(!t)return null;let r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(i=>({...i,...i.data&&{data:pa(i.data,a,n)}}))},...t.user&&{user:pa(t.user,a,n)},...t.contexts&&{contexts:pa(t.contexts,a,n)},...t.extra&&{extra:pa(t.extra,a,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=pa(t.contexts.trace.data,a,n))),t.spans&&(r.spans=t.spans.map(i=>(i.data&&(i.data=pa(i.data,a,n)),i))),r}_applyClientOptions(t){let a=this.getOptions(),{environment:n,release:r,dist:i,maxValueLength:o=250}=a;"environment"in t||(t.environment="environment"in a?n:"production"),t.release===void 0&&r!==void 0&&(t.release=r),t.dist===void 0&&i!==void 0&&(t.dist=i),t.message&&(t.message=Za(t.message,o));let s=t.exception&&t.exception.values&&t.exception.values[0];s&&s.value&&(s.value=Za(s.value,o));let u=t.request;u&&u.url&&(u.url=Za(u.url,o))}_applyIntegrationsMetadata(t){let a=Object.keys(this._integrations);a.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...a])}_captureEvent(t,a={},n){return this._processEvent(t,a,n).then(r=>r.event_id,r=>{!1&&$.warn(r)})}_processEvent(t,a,n){let{beforeSend:r,sampleRate:i}=this.getOptions();if(!this._isEnabled())return Pr(new Le("SDK not enabled, will not capture event."));let o=t.type==="transaction";return!o&&typeof i=="number"&&Math.random()>i?(this.recordDroppedEvent("sample_rate","error"),Pr(new Le(`Discarding event because it's not included in the random sample (sampling rate = ${i})`))):this._prepareEvent(t,a,n).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new Le("An event processor returned null, will not send event.");if(a.data&&a.data.__sentry__===!0||o||!r)return s;let c=r(s,a);return cv(c)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new Le("`beforeSend` returned `null`, will not send event.");let u=n&&n.getSession();return!o&&u&&this._updateSessionFromEvent(u,s),this.sendEvent(s,a),s}).then(null,s=>{throw s instanceof Le?s:(this.captureException(s,{data:{__sentry__:!0},originalException:s}),new Le(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${s}`))})}_process(t){this._numProcessing+=1,t.then(a=>(this._numProcessing-=1,a),a=>(this._numProcessing-=1,a))}_sendEnvelope(t){this._transport&&this._dsn?this._transport.send(t).then(null,a=>{!1&&$.error("Error while sending event:",a)}):!1&&$.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(a=>{let[n,r]=a.split(":");return{reason:n,category:r,quantity:t[a]}})}};function cv(e){let t="`beforeSend` method has to return `null` or a valid event.";if(jn(e))return e.then(a=>{if(!(Bt(a)||a===null))throw new Le(t);return a},a=>{throw new Le(`beforeSend rejected with ${a}`)});if(!(Bt(e)||e===null))throw new Le(t);return e}function Du(e,t){t.debug===!0&&!1&&$.enable();let a=be(),n=a.getScope();n&&n.update(t.initialScope);let r=new e(t);a.bindClient(r)}var dv=30;function Qi(e,t,a=_0(e.bufferSize||dv)){let n={},r=o=>a.drain(o);function i(o){let s=[];if(wu(o,(d,m)=>{let p=Au(m);U0(n,p)?e.recordDroppedEvent("ratelimit_backoff",p):s.push(d)}),s.length===0)return ht();let u=$a(o[0],s),c=d=>{wu(u,(m,p)=>{e.recordDroppedEvent(d,Au(p))})},l=()=>t({body:Wi(u,e.textEncoder)}).then(d=>{d.statusCode!==void 0&&(d.statusCode<200||d.statusCode>=300)&&!1&&$.warn(`Sentry responded with status code ${d.statusCode} to sent event.`),n=q0(n,d)},d=>{!1&&$.error("Failed while sending event:",d),c("network_error")});return a.add(l).then(d=>d,d=>{if(d instanceof Le)return!1&&$.error("Skipped sending event due to full buffer"),c("queue_overflow"),ht();throw d})}return{send:i,flush:r}}var Lr="7.7.0";var tn={};ml(tn,{FunctionToString:()=>Un,InboundFilters:()=>qn});var dp,Un=class e{static id="FunctionToString";name=e.id;setupOnce(){dp=Function.prototype.toString,Function.prototype.toString=function(...t){let a=On(this)||this;return dp.apply(a,t)}}};var mv=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],qn=class e{constructor(t={}){this._options=t}static id="InboundFilters";name=e.id;setupOnce(t,a){let n=r=>{let i=a();if(i){let o=i.getIntegration(e);if(o){let s=i.getClient(),u=s?s.getOptions():{},c=pv(o._options,u);return gv(r,c)?null:r}}return r};n.id=this.name,t(n)}};function pv(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...mv],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function gv(e,t){return t.ignoreInternal&&vv(e)?(!1&&$.warn(`Event dropped due to being internal Sentry Error.
Event: ${ma(e)}`),!0):hv(e,t.ignoreErrors)?(!1&&$.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${ma(e)}`),!0):fv(e,t.denyUrls)?(!1&&$.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${ma(e)}.
Url: ${Yi(e)}`),!0):bv(e,t.allowUrls)?!1:(!1&&$.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${ma(e)}.
Url: ${Yi(e)}`),!0)}function hv(e,t){return!t||!t.length?!1:yv(e).some(a=>t.some(n=>Gi(a,n)))}function fv(e,t){if(!t||!t.length)return!1;let a=Yi(e);return a?t.some(n=>Gi(a,n)):!1}function bv(e,t){if(!t||!t.length)return!0;let a=Yi(e);return a?t.some(n=>Gi(a,n)):!0}function yv(e){if(e.message)return[e.message];if(e.exception)try{let{type:t="",value:a=""}=e.exception.values&&e.exception.values[0]||{};return[`${a}`,`${t}: ${a}`]}catch{return!1&&$.error(`Cannot extract message for event ${ma(e)}`),[]}return[]}function vv(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function xv(e=[]){for(let t=e.length-1;t>=0;t--){let a=e[t];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}function Yi(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?xv(t):null}catch{return!1&&$.error(`Cannot extract url for event ${ma(e)}`),null}}function Iu(e,t){let a=Pu(e,t),n={type:t&&t.name,value:Sv(t)};return a.length&&(n.stacktrace={frames:a}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function Av(e,t,a,n){let r={exception:{values:[{type:Sr(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:`Non-Error ${n?"promise rejection":"exception"} captured with keys: ${T0(t)}`}]},extra:{__serialized__:fu(t)}};if(a){let i=Pu(e,a);i.length&&(r.exception.values[0].stacktrace={frames:i})}return r}function Cu(e,t){return{exception:{values:[Iu(e,t)]}}}function Pu(e,t){let a=t.stacktrace||t.stack||"",n=Ev(t);try{return e(a,n)}catch{}return[]}var kv=/Minified React error #\d+;/i;function Ev(e){if(e){if(typeof e.framesToPop=="number")return e.framesToPop;if(kv.test(e.message))return 1}return 0}function Sv(e){let t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function mp(e,t,a,n){let r=a&&a.syntheticException||void 0,i=Ji(e,t,r,n);return Pa(i),i.level="error",a&&a.event_id&&(i.event_id=a.event_id),ht(i)}function pp(e,t,a="info",n,r){let i=n&&n.syntheticException||void 0,o=Mu(e,t,i,r);return o.level=a,n&&n.event_id&&(o.event_id=n.event_id),ht(o)}function Ji(e,t,a,n,r){let i;if(qi(t)&&t.error)return Cu(e,t.error);if(h0(t)){let o=t;if("stack"in t)i=Cu(e,t);else{let s=o.name||"DOMException",u=o.message?`${s}: ${o.message}`:s;i=Mu(e,u,a,n),Ir(i,u)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return Ui(t)?Cu(e,t):Bt(t)||Sr(t)?(i=Av(e,t,a,r),Pa(i,{synthetic:!0}),i):(i=Mu(e,t,a,n),Ir(i,`${t}`,void 0),Pa(i,{synthetic:!0}),i)}function Mu(e,t,a,n){let r={message:t};if(n&&a){let i=Pu(e,a);i.length&&(r.exception={values:[{value:t,stacktrace:{frames:i}}]})}return r}var Fu="Breadcrumbs",an=class e{static id=Fu;name=e.id;options;constructor(t){this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t}}setupOnce(){this.options.console&&jt("console",Dv),this.options.dom&&jt("dom",Tv(this.options.dom)),this.options.xhr&&jt("xhr",Cv),this.options.fetch&&jt("fetch",Mv),this.options.history&&jt("history",Iv)}};function Tv(e){function t(a){let n,r=typeof e=="object"?e.serializeAttribute:void 0;typeof r=="string"&&(r=[r]);try{n=a.event.target?Tr(a.event.target,r):Tr(a.event,r)}catch{n="<unknown>"}n.length!==0&&be().addBreadcrumb({category:`ui.${a.name}`,message:n},{event:a.event,name:a.name,global:a.global})}return t}function Dv(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:j0(e.level),message:ou(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${ou(e.args.slice(1)," ")||"console.assert"}`,t.data.arguments=e.args.slice(1);else return;be().addBreadcrumb(t,{input:e.args,level:e.level})}function Cv(e){if(e.endTimestamp){if(e.xhr.__sentry_own_request__)return;let{method:t,url:a,status_code:n,body:r}=e.xhr.__sentry_xhr__||{};be().addBreadcrumb({category:"xhr",data:{method:t,url:a,status_code:n},type:"http"},{xhr:e.xhr,input:r});return}}function Mv(e){e.endTimestamp&&(e.fetchData.url.match(/sentry_key/)&&e.fetchData.method==="POST"||(e.error?be().addBreadcrumb({category:"fetch",data:e.fetchData,level:"error",type:"http"},{data:e.error,input:e.args}):be().addBreadcrumb({category:"fetch",data:{...e.fetchData,status_code:e.response.status},type:"http"},{input:e.args,response:e.response})))}function Iv(e){let t=Ce(),a=e.from,n=e.to,r=Hi(t.location.href),i=Hi(a),o=Hi(n);i.path||(i=r),r.protocol===o.protocol&&r.host===o.host&&(n=o.relative),r.protocol===i.protocol&&r.host===i.host&&(a=i.relative),be().addBreadcrumb({category:"navigation",data:{from:a,to:n}})}var Xi=Ce(),Zi;function Bu(){if(Zi)return Zi;if(M0(fetch))return Zi=fetch.bind(Xi);let e=Xi.document,t=fetch;if(e&&typeof e.createElement=="function")try{let a=e.createElement("iframe");a.hidden=!0,e.head.appendChild(a);let n=a.contentWindow;n&&n.fetch&&(t=n.fetch),e.head.removeChild(a)}catch(a){!1&&$.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",a)}return Zi=t.bind(Xi)}function gp(e,t){Object.prototype.toString.call(Xi&&navigator)==="[object Navigator]"&&typeof navigator.sendBeacon=="function"?navigator.sendBeacon.bind(navigator)(e,t):C0()&&Bu()(e,{body:t,method:"POST",credentials:"omit",keepalive:!0}).then(null,i=>{!1&&$.error(i)})}var Ru=Ce(),Nr=class extends jr{constructor(t){t._metadata=t._metadata||{},t._metadata.sdk=t._metadata.sdk||{name:"sentry.javascript.browser",packages:[{name:"npm:@sentry/browser",version:Lr}],version:Lr},super(t),t.sendClientReports&&Ru.document&&Ru.document.addEventListener("visibilitychange",()=>{Ru.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,a){return mp(this._options.stackParser,t,a,this._options.attachStacktrace)}eventFromMessage(t,a="info",n){return pp(this._options.stackParser,t,a,n,this._options.attachStacktrace)}sendEvent(t,a){let n=this.getIntegrationById(Fu);n&&n.options&&n.options.sentry&&be().addBreadcrumb({category:`sentry.${t.type==="transaction"?"transaction":"event"}`,event_id:t.event_id,level:t.level,message:ma(t)},{event:t}),super.sendEvent(t,a)}_prepareEvent(t,a,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,a,n)}_flushOutcomes(){let t=this._clearOutcomes();if(t.length===0){!1&&$.log("No outcomes to send");return}if(!this._dsn){!1&&$.log("No dsn provided, will not send outcomes");return}!1&&$.log("Sending outcomes:",t);let a=_r(this._dsn,this._options),n=z0(t,this._options.tunnel&&Ln(this._dsn));try{gp(a,Wi(n))}catch(r){!1&&$.error(r)}}};function $i(e,t=Bu()){function a(n){let r={body:n.body,method:"POST",referrerPolicy:"origin",headers:e.headers,...e.fetchOptions};return t(e.url,r).then(i=>({statusCode:i.status,headers:{"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits"),"retry-after":i.headers.get("Retry-After")}}))}return Qi(e,a)}var eo="?";var Pv=30,Fv=40,Bv=50;function _u(e,t,a,n){let r={filename:e,function:t,in_app:!0};return a!==void 0&&(r.lineno=a),n!==void 0&&(r.colno=n),r}var Rv=/^\s*at (?:(.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,_v=/\((\S*)(?::(\d+))(?::(\d+))\)/,jv=e=>{let t=Rv.exec(e);if(t){if(t[2]&&t[2].indexOf("eval")===0){let i=_v.exec(t[2]);i&&(t[2]=i[1],t[3]=i[2],t[4]=i[3])}let[n,r]=vp(t[1]||eo,t[2]);return _u(r,n,t[3]?+t[3]:void 0,t[4]?+t[4]:void 0)}},hp=[Pv,jv],Lv=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|capacitor).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Nv=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Ov=e=>{let t=Lv.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let i=Nv.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}let n=t[3],r=t[1]||eo;return[r,n]=vp(r,n),_u(n,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},fp=[Bv,Ov],zv=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Uv=e=>{let t=zv.exec(e);return t?_u(t[2],t[1]||eo,+t[3],t[4]?+t[4]:void 0):void 0},bp=[Fv,Uv];var yp=[hp,fp,bp],ju=du(...yp),vp=(e,t)=>{let a=e.indexOf("safari-extension")!==-1,n=e.indexOf("safari-web-extension")!==-1;return a||n?[e.indexOf("@")!==-1?e.split("@")[0]:eo,a?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]};var Lu=0;function Nu(){return Lu>0}function qv(){Lu+=1,setTimeout(()=>{Lu-=1})}function Ra(e,t={},a){if(typeof e!="function")return e;try{let r=e.__sentry_wrapped__;if(r)return r;if(On(e))return e}catch{return e}let n=function(){let r=Array.prototype.slice.call(arguments);try{a&&typeof a=="function"&&a.apply(this,arguments);let i=r.map(o=>Ra(o,t));return e.apply(this,i)}catch(i){throw qv(),Rr(o=>{o.addEventProcessor(s=>(t.mechanism&&(Ir(s,void 0,void 0),Pa(s,t.mechanism)),s.extra={...s.extra,arguments:r},s)),en(i)}),i}};try{for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}catch{}uu(n,e),Cr(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get(){return e.name}})}catch{}return n}var Ou={};ml(Ou,{Breadcrumbs:()=>an,Dedupe:()=>sn,GlobalHandlers:()=>ha,HttpContext:()=>on,LinkedErrors:()=>rn,TryCatch:()=>nn});var ha=class e{static id="GlobalHandlers";name=e.id;_options;_installFunc={onerror:Gv,onunhandledrejection:Hv};constructor(t){this._options={onerror:!0,onunhandledrejection:!0,...t}}setupOnce(){let t=this._options;for(let a in t){let n=this._installFunc[a];n&&t[a]&&(Vv(a),n(),this._installFunc[a]=void 0)}}};function Gv(){jt("error",e=>{let[t,a,n]=Ap();if(!t.getIntegration(ha))return;let{msg:r,url:i,line:o,column:s,error:u}=e;if(Nu()||u&&u.__sentry_own_request__)return;let c=u===void 0&&la(r)?Wv(r,i,o,s):xp(Ji(a,u||r,void 0,n,!1),i,o,s);c.level="error",wp(t,u,c,"onerror")})}function Hv(){jt("unhandledrejection",e=>{let[t,a,n]=Ap();if(!t.getIntegration(ha))return;let r=e;try{"reason"in e?r=e.reason:"detail"in e&&"reason"in e.detail&&(r=e.detail.reason)}catch{}if(Nu()||r&&r.__sentry_own_request__)return!0;let i=Er(r)?Kv(r):Ji(a,r,void 0,n,!0);i.level="error",wp(t,r,i,"onunhandledrejection")})}function Kv(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function Wv(e,t,a,n){let r=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i,i=qi(e)?e.message:e,o="Error",s=i.match(r);return s&&(o=s[1],i=s[2]),xp({exception:{values:[{type:o,value:i}]}},t,a,n)}function xp(e,t,a,n){let r=e.exception=e.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{},s=o.stacktrace=o.stacktrace||{},u=s.frames=s.frames||[],c=isNaN(parseInt(n,10))?void 0:n,l=isNaN(parseInt(a,10))?void 0:a,d=la(t)&&t.length>0?t:x0();return u.length===0&&u.push({colno:c,filename:d,function:"?",in_app:!0,lineno:l}),e}function Vv(e){!1&&$.log(`Global Handler attached: ${e}`)}function wp(e,t,a,n){Pa(a,{handled:!1,type:n}),e.captureEvent(a,{originalException:t})}function Ap(){let e=be(),t=e.getClient(),a=t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[e,a.stackParser,a.attachStacktrace]}var Qv=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],nn=class e{static id="TryCatch";name=e.id;_options;constructor(t){this._options={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t}}setupOnce(){let t=Ce();this._options.setTimeout&&Rt(t,"setTimeout",kp),this._options.setInterval&&Rt(t,"setInterval",kp),this._options.requestAnimationFrame&&Rt(t,"requestAnimationFrame",Yv),this._options.XMLHttpRequest&&"XMLHttpRequest"in t&&Rt(XMLHttpRequest.prototype,"send",Jv);let a=this._options.eventTarget;a&&(Array.isArray(a)?a:Qv).forEach(Zv)}};function kp(e){return function(...t){let a=t[0];return t[0]=Ra(a,{mechanism:{data:{function:_t(e)},handled:!0,type:"instrument"}}),e.apply(this,t)}}function Yv(e){return function(t){return e.apply(this,[Ra(t,{mechanism:{data:{function:"requestAnimationFrame",handler:_t(e)},handled:!0,type:"instrument"}})])}}function Jv(e){return function(...t){let a=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in a&&typeof a[r]=="function"&&Rt(a,r,function(i){let o={mechanism:{data:{function:r,handler:_t(i)},handled:!0,type:"instrument"}},s=On(i);return s&&(o.mechanism.data.handler=_t(s)),Ra(i,o)})}),e.apply(this,t)}}function Zv(e){let t=Ce(),a=t[e]&&t[e].prototype;!a||!a.hasOwnProperty||!a.hasOwnProperty("addEventListener")||(Rt(a,"addEventListener",function(n){return function(r,i,o){try{typeof i.handleEvent=="function"&&(i.handleEvent=Ra(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:_t(i),target:e},handled:!0,type:"instrument"}}))}catch{}let s=Ra(i,{mechanism:{data:{function:"addEventListener",handler:_t(i),target:e},handled:!0,type:"instrument"}}),u=[r,s,o];return n.apply(this,u)}}),Rt(a,"removeEventListener",function(n){return function(r,i,o){let s=i;try{let u=s&&s.__sentry_wrapped__;u&&n.call(this,r,u,o)}catch{}return n.call(this,r,s,o)}}))}var Xv="cause",$v=5,rn=class e{static id="LinkedErrors";name=e.id;_key;_limit;constructor(t={}){this._key=t.key||Xv,this._limit=t.limit||$v}setupOnce(){let t=be().getClient();t&&ga((a,n)=>{let r=be().getIntegration(e);return r?ex(t.getOptions().stackParser,r._key,r._limit,a,n):a})}};function ex(e,t,a,n,r){if(!n.exception||!n.exception.values||!r||!Vt(r.originalException,Error))return n;let i=Ep(e,a,r.originalException,t);return n.exception.values=[...i,...n.exception.values],n}function Ep(e,t,a,n,r=[]){if(!Vt(a[n],Error)||r.length+1>=t)return r;let i=Iu(e,a[n]);return Ep(e,t,a[n],n,[i,...r])}var Sp=Ce(),on=class e{static id="HttpContext";name=e.id;setupOnce(){ga(t=>{if(be().getIntegration(e)){if(!navigator&&!location&&!Sp.document)return t;let a=t.request&&t.request.url||location&&location.href,{referrer:n}=Sp.document||{},{userAgent:r}=navigator||{},i={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},o={...a&&{url:a},headers:i};return{...t,request:o}}return t})}};var sn=class e{static id="Dedupe";name=e.id;_previousEvent;setupOnce(t,a){let n=r=>{let i=a().getIntegration(e);if(i){try{if(tx(r,i._previousEvent))return!1&&$.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{return i._previousEvent=r}return i._previousEvent=r}return r};n.id=this.name,t(n)}};function tx(e,t){return t?!!(ax(e,t)||nx(e,t)):!1}function ax(e,t){let a=e.message,n=t.message;return!(!a&&!n||a&&!n||!a&&n||a!==n||!Mp(e,t)||!Cp(e,t))}function nx(e,t){let a=Tp(t),n=Tp(e);return!(!a||!n||a.type!==n.type||a.value!==n.value||!Mp(e,t)||!Cp(e,t))}function Cp(e,t){let a=Dp(e),n=Dp(t);if(!a&&!n)return!0;if(a&&!n||!a&&n||(a=a,n=n,n.length!==a.length))return!1;for(let r=0;r<n.length;r++){let i=n[r],o=a[r];if(i.filename!==o.filename||i.lineno!==o.lineno||i.colno!==o.colno||i.function!==o.function)return!1}return!0}function Mp(e,t){let a=e.fingerprint,n=t.fingerprint;if(!a&&!n)return!0;if(a&&!n||!a&&n)return!1;a=a,n=n;try{return a.join("")===n.join("")}catch{return!1}}function Tp(e){return e.exception&&e.exception.values&&e.exception.values[0]}function Dp(e){let t=e.exception;if(t)try{return t.values[0].stacktrace.frames}catch{return}}var Pp=[new tn.InboundFilters,new tn.FunctionToString,new nn,new an,new ha,new rn,new sn,new on];function zu(e={}){if(e.defaultIntegrations===void 0&&(e.defaultIntegrations=Pp),e.release===void 0){let a=Ce();a.SENTRY_RELEASE&&a.SENTRY_RELEASE.id&&(e.release=a.SENTRY_RELEASE.id)}e.autoSessionTracking===void 0&&(e.autoSessionTracking=!0),e.sendClientReports===void 0&&(e.sendClientReports=!0);let t={...e,stackParser:D0(e.stackParser||ju),integrations:Tu(e),transport:e.transport||$i};Du(Nr,t),e.autoSessionTracking&&rx()}function Uu(e){let t=be().getClient();return t?t.flush(e):(!1&&$.warn("Cannot flush events. No client defined."),ht(!1))}function Ip(e){e.startSession({ignoreDuration:!0}),e.captureSession()}function rx(){if(typeof Ce().document>"u"){!1&&$.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}let a=be();a.captureSession&&(Ip(a),jt("history",({from:n,to:r})=>{n===void 0||n===r||Ip(be())}))}var Fp={},qu=Ce();qu.Sentry&&qu.Sentry.Integrations&&(Fp=qu.Sentry.Integrations);var r_={...Fp,...tn,...Ou};var un=null;async function Bp(){un||(un=await Ue())}function Rp(e){if(!e||!e.enable||qa())return;let t=rt(),a=qo();if(e.ignoreVersions?.find(s=>s===t))return!1;if(e.ignoreBuildPlatforms?.find(s=>s===a))return;let i=globalThis.navigator.userAgent;if(!e.ignoreUserAgents?.find(s=>i.match(new RegExp(s))))return!0}function ox(){try{let e=be();if(!e)return!1;let t=e.getClient();if(!t)return!1;let a=t.getOptions();return!(!a||!a.dsn)}catch{return!1}}async function sx(){try{if(await Bp(),!un?.sentryCaptureConfig||!Rp(un.sentryCaptureConfig)||ox())return;let e=rt(),t=qo(),{initOptions:a}=un.sentryCaptureConfig,n={dsn:ql,environment:ue().PROD==="1"?"production":"develop",defaultIntegrations:!1,beforeSend(r){return r.tags?.trigger!=="user_report"?null:r},...a};a?.ignoreErrors&&(n.ignoreErrors=Gu(a.ignoreErrors)),a?.denyUrls&&(n.denyUrls=Gu(a.denyUrls)),a?.allowUrls&&(n.allowUrls=Gu(a.allowUrls)),n.release=`${t}@${e}`,zu(n)}catch(e){I.debug("init sentry error:",e)}}async function _p(e,t){try{if(await Bp(),!un?.sentryCaptureConfig||!Rp(un.sentryCaptureConfig)||!t.startsWith(za))return;await sx(),await new Promise(a=>setTimeout(a,100)),en(e,{tags:{trigger:"user_report",type:"http_error",error_url:t},extra:{url:t}}),await Uu(2e3)}catch{}}function Gu(e){return e.map(t=>new RegExp(t))}async function Ca(e){let t;if(e&&e.retry&&e.retry>0)try{t=await d0(jp.bind(null,e),{multiplier:2,maxAttempts:e.retry})}catch(a){throw a&&a.name==="RetryError"&&a.cause?a.cause:a}else t=await jp(e);return t}async function jp(e){e.body;let{url:t,responseType:a,requestType:n,...r}=e;a||(a="json"),n||(n="json"),r={mode:"cors",...r},n=="formData"?r.body=m0(e.body):n=="urlSearchParams"?r.body=p0(e.body):n=="blob"&&e.body&&(r.body=nu(e.body));let i=!0;e.fetchPolyfill&&(i=!1);let o=e.fetchPolyfill||fetch,s=3e4,u;if(e.timeout&&(s=e.timeout),i&&!e.signal){let l=new AbortController,d=l.signal;u=setTimeout(()=>{l.abort()},s),r.signal=d}let c;try{c=await o(t,r)}catch(l){I.debug("fetch error",t,l),_p(l,t);let d=l.message||"Unknown Error";throw l.name==="AbortError"&&!e.signal&&(d=`Request timeout after ${s}ms`),new It("fetchError",d).initNetWork(-999).initStack(l.stack)}finally{u&&clearTimeout(u)}if(c.ok&&c.status>=200&&c.status<400){if(a=="HEAD"){if(c.url!==t)throw new It("fetchError","redirect url:"+c.url);return c.statusText}if(a==="json")return await c.json();if(a==="text")return await c.text();if(a==="raw"){let l=await c.text(),d=Object.fromEntries([...c.headers.entries()]),m=c.url;return m||(c.headers.get("X-Final-URL")?m=c.headers.get("X-Final-URL"):m=t),{body:l,headers:d,status:c.status,statusText:c.statusText,url:m}}else if(a==="base64"){let l=await c.blob(),d=new FileReader,m=new Promise((p,v)=>{d.onload=function(){let w=d.result;p(w)},d.onerror=function(){v(d.error)}});return d.readAsDataURL(l),m}else if(a==="stream"){let l=bs({onEvent:m=>{e.onMessage?.(m)},onError:m=>{I.debug("sseParser error",m),e.onError?.(m)}}),d=c?.body?.getReader();if(!d){e.onError?.(new It("fetchError","stream reader not found"));return}try{for(;;){let{done:m,value:p}=await d.read();if(m)break;let v=new TextDecoder().decode(p);l.feed(v)}}catch(m){if(m.name==="AbortError"){e.onFinish?.("request aborted");return}e.onError?.(m)}finally{e.onFinish?.("stream finished"),d.releaseLock()}}}else{let l;try{l=await c.text()}catch(w){I.error("parse response failed",w)}l&&I.error(`fail response: ${t} `,l);let d="";l&&(d=l.slice(0,500));let m=d,v=new URL(t).hostname.endsWith(`.${De}`);throw t.endsWith("edge.microsoft.com/translate/auth")&&(m="bingAuth:"+d),v&&/translation quota.*reached/.test(d)&&(m="NewProQuota:"+d),new It("fetchError",m).initNetWork(c.status)}}var to;function ux(){return to||(to=new sa("content_script",!1).getConnection("pure_main",()=>{}),to)}async function Lp(e,t){try{let a=ue(),n=a.INSTALL_FROM==="firefox_store";if(Wa()?.name?.startsWith("ImtFx")&&(n=!1),n)return;let i=fe(),o=Ea(),s=a.PROD==="1",u=rt(),c=new Date,{fakeUserId:l,installedAt:d}=await oa(),m=await Gt(),p=await mr(),v=await Qa(),w=new Date(d),S=Wt(w),g=Wt(c),M=S===g,f=24*60*60*1e3,T=c.getTime()-w.getTime()<7*f,L=c.getTime()-w.getTime()<30*f,E=c.getTime()-w.getTime()<365*f,x=ii(),k="";typeof navigator<"u"&&(k=window.navigator.userAgent);let y=ka.parse(k),j=e.map(_=>{let P=_.params||{};if(y.os&&(P.os_name=y.os.name||"unknown",P.os_version=y.os.version||"unknown",P.os_version_name=y.os.versionName||"unknown"),y.browser){P.browser_name=y.browser.name||"unknown",P.browser_version=y.browser.version||"unknown";let N=Wa();N&&(P.browser_name=N.name,P.browser_version=N.version)}if(y.platform&&(P.platform_type=y.platform.type||"unknown"),y.engine&&(P.engine_name=y.engine.name||"unknown",P.engine_version=y.engine.version||"unknown"),u&&(P.version=u),a.INSTALL_FROM&&(P.install_from=a.INSTALL_FROM),S){P.install_day=Bn(w);let N=ji(w);P.install_week=`${N.year}${N.week}`}return P.userscript=i.toString(),M?P.is_new_user_today="1":P.is_new_user_today="0",P.is_new_user_this_week=T?"1":"0",P.is_new_user_this_month=L?"1":"0",P.is_new_user_this_year=E?"1":"0",o?P.main_frame=0:P.main_frame=1,m&&(P.ab_tag=m),p&&(P.ab_group=p),P.campaign=v||"none",{..._,params:P}});x.forEach(async _=>{let P=await Np({responseType:"text",url:_,method:"POST",body:JSON.stringify({client_id:l,user_id:l,events:j})})}),t&&cx(l,j)}catch{}}async function lx(e){return await ux().sendMessage("background:main",e)}function Np(e){return fe()||Yr()?(e.fetchPolyfill=globalThis.GM_fetch,Ca(e)):lx({method:"fetch",data:e})}function cx(e,t){try{if(ya())return;t.forEach(a=>{let n={...a.params,event_name:a.name,device_id:e};xr(n);let r=Date.now()+(Math.random()*100).toFixed(0);Np({url:za,method:"POST",responseType:"text",body:JSON.stringify({nonce:r,subject:"user_behaviour",logs:[JSON.stringify(n)]})})})}catch(a){I.debug("report self service error",a)}}var Hu={releaseVersion:"1.13.5",immediateTranslationTextCount:4999,immediateTranslationScrollLimitScreens:1,translationStartMode:"dynamic",domReadyDetectTimeout:3e3,translationService:"bing",clientImageTranslationService:"inherit",inputTranslationService:"inherit",userTranslationServices:{},m/* MULTIPLE_REMOVED_BLOCKS */]","body.notranslate"],"additionalExcludeSelectors.remove":[".notranslate","[translate=no]"]},{id:"otherMathSites",selectorMatches:["math","mjx-container","[class*='MathJax']","[class*='math-']"],enableRichTranslate:!1,advanceMergeConfig:[{condition:"translationService==zhipu",advanceConfig:{"rich.stayOriginalTags.remove":["SUP","SUB"]}}],"additionalExcludeSelectors.remove":[".notranslate","[translate=no]"]},{id:"htmlLangFirst",selectorMatches:["[lang=he-IL]","[lang=nl-NL]","[lang=ar-SA]","[lang=fa-IR]","[lang=fi]","[lang=fi-FI]"],pageLangDetectWeight:{html:2,body:1,tab:1},_comment:"\u4E3A\u4E86\u5904\u7406 js \u5E93\u9519\u8BEF\u68C0\u6D4B\u5E0C\u4F2F\u6765\u8BED\u6210\u5176\u4ED6\u8BED\u8A00\u7684\u95EE\u9898 "},{id:"deepFrameTranslate",matches:["online.vitalsource.com","anarchothaumaturgist.itch.io","darkpetal16.itch.io","registry.khronos.org","achieve.macmillanlearning.com","mail.shanghai.*","help.autodesk.com"],enableDeepFrameTranslatePage:!0},{id:"common.pdfWebPage",waitForSelectorsTimeout:1,selectorMatches:["embed[type='application/pdf']"]},{id:"finalCommon.pdfWebPage",matches:["https://obgyn.onlinelibrary.wiley.com/doi/pdf/*","https://docs.amd.com/v/u/*/*"],selectorMatches:["embed[type='application/pdf']","[id=myPdfIframe][src*=pdf]","#article [type='application/pdf'][src*=pdf]",".textFrame [type='application/pdf'][src*=pdf]",".ggPdf","[id=pdfCanvasContainer] > iframe[src*=pdf]",".viewercontent-container  iframe[src*=documents]"],pdfUrlExtractRule:{selector:"iframe[src*=pdf]",attribute:"src",selectors:["embed[type='application/pdf']","iframe[src*=pdf]","iframe[src*=documents]","#statements-pdf"],attributes:["src"],queries:["file"]}},{id:"common2.pdfWebPage",matches:["https://obgyn.onlinelibrary.wiley.com/doi/pdf/*"],selectorMatches:["[id=myPdfIframe][src*=pdf]","#article [type='application/pdf'][src*=pdf]",".textFrame [type='application/pdf'][src*=pdf]",".ggPdf"],pdfUrlExtractRule:{selector:"iframe[src*=pdf]",attribute:"src"}},{id:"common4.pdfWebPage",selectorMatches:["#statements-pdf"],pdfUrlExtractRule:{selector:"#statements-pdf",attribute:"src"}},{id:"common-query.pdfWebPage",selectorMatches:["[id=pdfCanvasContainer] > iframe[src*=pdf]"],pdfUrlExtractRule:{selector:"iframe[src*=pdf]",attribute:"src",query:"file"}},{id:"fix-nav2header",matches:["www.acea.auto","news.cgtn.com"],"preTranslateLimiter.add_v.[1.12.1]":{"side.selectors":["aside","[class*='Sidebar']","#sidenav"],"header.selectors":["nav","header","[class^='header-v3']"]}},{id:"strict-fix-nav2header",matches:["www.talkclassical.com"],preTranslateLimiter:{"side.selectors":["aside","#sidenav"],"header.selectors":["nav","header"]},_comment:"\u4E25\u683C\u6A21\u5F0F\u4E0B\uFF0C\u53EA\u7FFB\u8BD1 header \u548C side \u6807\u7B7E"},{id:"fix-header",selectorMatches:["article header","header h1","header h2","header h3","header p","header nav"],"preTranslateLimiter.add_v.[1.12.1]":{"header.enableTranslate":!0},"excludeSelectors.add":[".site-header"],"initialSelectorGlobalAttributes.remove":{header:{}},"extraBlockSelectors.add":[".btn"],_comment:"\u7ED9\u90E8\u5206\u7F51\u9875\u5F00\u540E\u95E8\u7FFB\u8BD1 header\u6807\u7B7E"}]};function ao(e){return Array.isArray(e)?e:e?[e]:[]}function Up(e,t){let a={...e};return Gp(a,t),mx(a,t),a}function qp(e,t,a){let n={...e};return lt({rule:t,valueIsArray:r=>Array.isArray(e[r]),getMergedValue:r=>e[r],onMergedResult:(r,i)=>n[r]=i}),lt({rule:a,valueIsArray:r=>Array.isArray(e[r]),getMergedValue:r=>n[r],onMergedResult:(r,i)=>n[r]=i}),n}function mx(e,t){if(!t.condition)return;let a=t.condition.enableSubtitle?.true||{},n=t.condition.enableSubtitle?.false||{},r=e.enableSubtitle?a:n;Gp(e,r)}function px(e){return Object.keys(e).sort((t,a)=>{let[n,r,i]=no(t),[o,s,u]=no(a);return n!==o?t.localeCompare(a):i&&u?Ht(i,u)?1:-1:i?1:u?-1:t.localeCompare(a)})}function lt({rule:e,getMergedValue:t,valueIsArray:a,onMergedResult:n},r){px(e).forEach(o=>{let[s,u,c]=no(o);if(s=="rich"){if(!r)return n(o,e[o]);let[p,v,w,S]=no(o);s=p+"."+v,u=w,c=S}if(!s||e[o]===void 0)return;let l=e[o];a(s)&&(l=ao(e[o]));let d=t(s);if(d==null){n(s,l);return}let m;if(u=="add_v"){if(!Op(c))return;m=Ku(d,l)}else if(u=="remove_v"){if(!Op(c))return;m=zp(d,l)}else u==="add"?m=Ku(d,l):u=="remove"&&(m=zp(d,l));if(m||u=="remove_v"||u=="remove"){n(s,m);return}Array.isArray(e[s])&&s.startsWith("additional")?m=Ku(d,l):m=l,n(s,m)})}function Gp(e,t){return lt({rule:t,valueIsArray:a=>Array.isArray(e[a]),getMergedValue:a=>e[a],onMergedResult:(a,n)=>{e[a]=n}}),e}function Ku(e,t){let a;if(Array.isArray(e)){let n=ao(t);a=[...e,...n],a=Array.from(new Set(a))}else typeof e=="object"&&typeof t=="object"?a={...e,...t}:a=t;return a}function no(e){let t=e.lastIndexOf("["),a="",n=e;return t>0&&(a=e.slice(t+1,e.length-1),n=e.slice(0,t-1)),[...n.split("."),a]}function Op(e){let t=rt();return e&&Ht(t,e)}function zp(e,t){if(Array.isArray(e)){let a=ao(t);return e=e.filter(n=>!a.includes(n)),Array.from(new Set(e))}else if(typeof e=="object"&&typeof t=="object")Object.keys(t).forEach(a=>{delete e[a]});else return;return e}var gx="userConfig",hx="userPromptPool",fx="userObjectPool";async function Hp(){let e=await Fn(jo)||{};if(Object.keys(e).length>0)return e;let t=await Wp(gx)||{},a=await Fn(hx)||{},n=await Fn(fx)||{};return bx(t,a,n)}async function Kp(e){await vr(jo,e)}function bx(e,t,a){return yx(e,(n,r,i)=>{(n[r]||"")==i&&(t[i]?n[r]=t[i]:delete n[r])}),vx(e,(n,r,i,o)=>{i==o&&(a[o]?n[r]=a[o]:delete n[r])}),e}function yx(e,t){Object.entries(e.translationServices||{}).forEach(([a,n])=>{["systemPrompt","prompt","multiplePrompt","subtitlePrompt"].forEach(r=>{let i=`@imt_${a}.${r}`;t(n,r,i)})})}function vx(e,t){(e.independentSyncKeys||["generalRule.glossaries","generalRule.injectedCss","aiAssistantsMatches","customAiAssistants"]).forEach(n=>{let r=n.split("."),i=e,o="",s=i;r.forEach(u=>{i&&(o=u,s=i,i=i[u])}),i&&t(s,o,i,`@imt_${n}`)})}function Vp(e,t){return Qp(e,a=>{let n=[],r,i;function o(){r=t[n[0]],i=n[1]?.trim(),typeof r=="boolean"&&(i=i=="true")}if(n=a.split(/\s*==\s*/),o(),n.length>1)return r==i;if(n=a.split(/\s*!=\s*/),o(),n.length>1)return r!=i;if(n=a.split(/\s*>\s*/),o(),n.length>1){let s=t[n[0]];return typeof s=="number"?s>Number(i):s>i}if(n=a.split(/\s*<\s*/),o(),n.length>1){let s=t[n[0]];return typeof s=="number"?s<Number(i):s<i}return a=="true"})}function Qp(e,t){let a=!0;e=e.replace(/\((.+?)\)/g,(u,c)=>Qp(c,t)+"");let n=[],r=[],i,o=0,s=/ (&&|\|\|) /g;for(;(i=s.exec(e))!==null;)r.push(e.substring(o,i.index)),n.push(i[0]),o=i.index+i[0].length;if(n.length>0){r.length==n.length&&r.push(e.substring(o));for(let u=0;u<r.length;u++){let c=(n[u-1]||" && ").trim();c=="&&"?a=a&&t(r[u]):c=="||"&&(a=a||t(r[u]))}return a}return t(e)}function Wu(){return navigator.userAgent.indexOf("Mac")!==-1}var xx="";function wx(){return xx||globalThis.navigator.userAgent}function Jp(){let e=wx();if(/iPhone/.test(e))return!1;let t=e.indexOf("Macintosh")!==-1||e.indexOf("Mac OS X")!==-1,a=e.indexOf("Safari")!==-1;return t&&a}var Yp={Alt:"\u2325"};function Vu(e=""){return Wu()?Object.keys(Yp).reduce((t,a)=>t.replace(a,Yp[a]),e):e}var Zp=!1;async function Ue(){let e={...Hu,buildinConfigUpdatedAt:re.BUILD_TIME};e=await Px(e);let t=await Tx(e);e.targetLanguage=t;let a=await Ut.get(bt,null),n=Sc(),r=Ax(),i=await ua(),o=Pm();Cm(i,e,o),i?.DEBUG&&!Zp&&(Zp=!0,kl(!0));let s=globalThis.IMMERSIVE_TRANSLATE_CONFIG||{},u=await Ja(),c=Object.assign({},s,r,JSON.parse(JSON.stringify(i)));if(!c.interfaceLanguage){let d=await Ex();c.interfaceLanguage=d}await Ix(c,i,e),Fx(u,c),e=await _x(e);let l=Object.assign(n,e);return lt({rule:e,valueIsArray:d=>Array.isArray(n[d]),getMergedValue:d=>n[d],onMergedResult:(d,m)=>{d!="generalRule"&&(l[d]=m)}}),lt({rule:e.generalRule,valueIsArray:d=>Array.isArray(n.generalRule[d]),getMergedValue:d=>n.generalRule[d],onMergedResult:(d,m)=>{l.generalRule[d]=m}}),await Mx(l),Dx(a,c,l),l=await Bx(l,c),l.donateUrl=e.donateUrl,l.minVersion=e.minVersion,l.feedbackUrl=e.feedbackUrl,l.rawUserConfig=i,Mm(l,o),l}function Ax(){if(re.PROD==="1")return{};let e={};if(re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_ID&&re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_KEY){let a={secretId:re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_ID,secretKey:re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_KEY};e.translationServices={},e.translationServices.tencent=a}if(re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_APPID&&re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_KEY){let a={appid:re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_APPID,key:re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_KEY};e.translationServices||(e.translationServices={}),e.translationServices.baidu=a}if(re.IMMERSIVE_TRANSLATE_SECRET_CAIYUN_TOKEN){let a={token:re.IMMERSIVE_TRANSLATE_SECRET_CAIYUN_TOKEN};e.translationServices||(e.translationServices={}),e.translationServices.caiyun=a}if(re.IMMERSIVE_TRANSLATE_SECRET_OPENL_APIKEY){let a={apikey:re.IMMERSIVE_TRANSLATE_SECRET_OPENL_APIKEY};e.translationServices||(e.translationServices={}),e.translationServices.openl=a}if(re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_ID&&re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_SECRET){let a={appId:re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_ID,appSecret:re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_SECRET};e.translationServices||(e.translationServices={}),e.translationServices.youdao=a}if(re.IMMERSIVE_TRANSLATE_SECRET_VOLC_ACCESS_KEY_ID&&re.IMMERSIVE_TRANSLATE_SECRET_VOLC_SECRET_ACCESS_KEY){let a={accessKeyId:re.IMMERSIVE_TRANSLATE_SECRET_VOLC_ACCESS_KEY_ID,secretAccessKey:re.IMMERSIVE_TRANSLATE_SECRET_VOLC_SECRET_ACCESS_KEY};e.translationServices||(e.translationServices={}),e.translationServices.volc=a}if(re.IMMERSIVE_TRANSLATE_SECRET_DEEPL_AUTH_KEY){let a={authKey:re.IMMERSIVE_TRANSLATE_SECRET_DEEPL_AUTH_KEY};e.translationServices||(e.translationServices={}),e.translationServices.deepl=a}if(re.DEEPL_PROXY_ENDPOINT&&(e.translationServices||(e.translationServices={}),e.translationServices.deepl||(e.translationServices.deepl={}),e.translationServices.deepl.immersiveTranslateApiUrl=re.DEEPL_PROXY_ENDPOINT),re.IMMERSIVE_TRANSLATE_DEEPL_ENDPOINT&&(e.translationServices||(e.translationServices={}),e.translationServices.deepl||(e.translationServices.deepl={}),e.translationServices.deepl.immersiveTranslateDeeplTokenUrl=re.IMMERSIVE_TRANSLATE_DEEPL_ENDPOINT),re.IMMERSIVE_TRANSLATE_SECRET_OPENAI_API_KEY){let a={APIKEY:re.IMMERSIVE_TRANSLATE_SECRET_OPENAI_API_KEY};e.translationServices||(e.translationServices={}),e.translationServices.openai=a}re.IMMERSIVE_TRANSLATE_SERVICE&&(e.translationService=re.IMMERSIVE_TRANSLATE_SERVICE);let t={};return re.DEBUG==="1"&&(t.debug=!0,t.cache=!1),re.MOCK==="1"&&(t.translationService="mock"),t}async function Ja(){let e=await A.storage.local.get(Vn);if(e[Vn]){let t=e[Vn],a=t.tempTranslationUrlMatches||[],n=a.filter(o=>o.expiredAt>Date.now()),r=!1;n.length!==a.length&&(a=n,r=!0);let i={...t,tempTranslationUrlMatches:[...a]};return r&&await Ma(i),i}else return{}}async function Ma(e){await A.storage.local.set({[Vn]:e})}async function kx(){return re.DEBUG==="1"?void 0:(await A.storage.local.get(Co))[Co]}function ua(){return Hp()}function Ft(e){return Kp(e)}async function Wp(e){return(await A.storage.sync.get(e))[e]}async function vr(e,t){await A.storage.local.set({[e]:t})}async function Fn(e){return(await A.storage.local.get(e))[e]}var Ex=async()=>{let e=["zh-CN"];try{e=await A.i18n.getAcceptLanguages()}catch(n){I.warn("get browser language error:",n)}let a=e.map(n=>Ua(n)).find(n=>o0.find(r=>r===n));return a||"en"},Sc=()=>{let e={...Hu,buildinConfigUpdatedAt:re.BUILD_TIME};return{...e,targetLanguage:ta,interfaceLanguage:"en",translationMode:"dual",inputTranslationMode:"translation",debug:!1,alpha:!1,translationUrlPattern:{matches:[],excludeMatches:[]},translationLanguagePattern:{matches:[],excludeMatches:[]},translationThemePatterns:{},translationParagraphLanguagePattern:{matches:[],excludeMatches:[],selectorMatches:[],excludeSelectorMatches:[]},translationBodyAreaPattern:{matches:[],excludeMatches:[],selectorMatches:[],excludeSelectorMatches:[]},translationTheme:"none",translationService:"bing",inputTranslationService:"inherit",mouseHoverTranslationService:"inherit",subtitleTranslateService:"inherit",translationArea:"main",translationStartMode:"dynamic",translationServices:{},monkeyH5FloatBall:{...e.monkeyH5FloatBall},pcFloatBall:{...e.pcFloatBall},generalRule:{...e.generalRule},translationGeneralConfig:{engine:"bing",_systemExcludeWordRegex:"\\b({word})\\b"},rules:[]}};function Sx(e,t){return e=e||[],t=t||[],e.length!==t.length?!0:t.filter(n=>!e.includes(n)).length>0}async function Tx(e){try{let t=e?.autoSelectTargetLanguageAfterInstalledAt;if(!t)return ta;let a=await Ne("installedAt","");if(a&&new Date(a)<new Date(t))return ta;let n=[ta];if(n=await A.i18n.getAcceptLanguages(),!n?.length)return ta;let r=Ua(n[0]);if(!r||r=="auto")return ta;let i=e?.autoSelectAllTargetLanguageAfterInstalledAt;return r=="en"&&i&&a&&new Date(a)<new Date(i)?ta:r}catch(t){return I.warn("get browser language error:",t),ta}}function Dx(e,t,a){let n=fn(e),r={};t.translationServices?.deepl&&(r=t.translationServices.deepl);let i={};t.translationServices?.openai&&(i=t.translationServices.openai),!n&&r.authKey&&!r.provider?(a.translationServices?.deepl||(a.translationServices.deepl={}),a.translationServices.deepl.provider="custom"):r?.provider||(a.translationServices?.deepl||(a.translationServices.deepl={}),a.translationServices.deepl.provider="pro"),!n&&i.APIKEY&&!i.provider?(a.translationServices?.openai||(a.translationServices.openai={}),a.translationServices.openai.provider="custom"):i?.provider||(a.translationServices?.openai||(a.translationServices.openai={}),a.translationServices.openai.provider="pro");let o={};t.translationServices?.deepseek&&(o=t.translationServices.deepseek),!n&&o.APIKEY&&!o.provider?(a.translationServices?.deepseek||(a.translationServices.deepseek={}),a.translationServices.deepseek.provider="custom"):o?.provider||(a.translationServices?.deepseek||(a.translationServices.deepseek={}),a.translationServices.deepseek.provider="pro");let s={};t.translationServices?.gemini&&(s=t.translationServices.gemini),!n&&s.APIKEY&&!s.provider?(a.translationServices?.gemini||(a.translationServices.gemini={}),a.translationServices.gemini.provider="custom"):s&&s.provider||(a.translationServices?.gemini||(a.translationServices.gemini={}),a.translationServices.gemini.provider="pro");let u={};t.translationServices?.claude&&(u=t.translationServices.claude),!n&&u.APIKEY&&!u.provider?(a.translationServices?.claude||(a.translationServices.claude={}),a.translationServices.claude.provider="custom"):u.provider||(a.translationServices?.claude||(a.translationServices.claude={}),a.translationServices.claude.provider="pro")}function Cx(e,t){let a=e.translationServices||{},n={...t.translationServices};function r(l){l.hasMerged||(lt({rule:l,valueIsArray:d=>Array.isArray(l[d]),getMergedValue:d=>l[d],onMergedResult:(d,m)=>{l[d]=m}}),l.hasMerged=!0)}let i=Object.keys(n).reduce((l,d)=>(n[d].type||(l[d]=n[d]),l),{});Object.keys(i).forEach(l=>{let d=i[l],m=i[d.extends];!m||d.extends==l||(r(m),r(i[l]),i[l]=Qu(m,i[l]))}),Object.keys(a).forEach(l=>{let d=a[l];d?.systemPrompt!=null&&d?.multipleSystemPrompt===void 0&&(d.multipleSystemPrompt="")}),lt({rule:{...i},valueIsArray:l=>Array.isArray(i[l]),getMergedValue:l=>i[l],onMergedResult:(l,d)=>{i[l]={...i[l],...d}}});let o=Object.keys(n).reduce((l,d)=>(n[d].type&&(l[d]=n[d]),l),{});Object.keys(o).forEach(l=>{let d=o[l],m=i[d.extends];!m||d.extends==l||(r(o[l]),o[l]=Qu(m,o[l]))}),lt({rule:{...o},valueIsArray:l=>Array.isArray(o[l]),getMergedValue:l=>o[l],onMergedResult:(l,d)=>{o[l]={...o[l],...d}}}),n={...i,...o},Object.keys(a).forEach(l=>{let d=a[l];if(!d.extends)return;let m=n[d.extends];!m||d.extends==l||(a[l]=Qu(m,a[l]))}),lt({rule:{...a},valueIsArray:l=>Array.isArray(n[l]),getMergedValue:l=>n[l],onMergedResult:(l,d)=>{let m=n[l]?.env||{},p=d.env||{};m={...m,...p},n[l]={...n[l],...d,env:m}}}),new Date(e.updatedAt)<=new Date("2024.4.2")&&Object.keys(a).forEach(l=>{["openai","gemini"].includes(l)&&a[l].prompt!=null&&(a[l].maxTextGroupLengthPerRequest==null&&(n[l].maxTextGroupLengthPerRequest=1),a[l].multiplePrompt==null&&(n[l].multiplePrompt=a[l].prompt))});let u=["html_only","content_type","imt_source_field","imt_trans_field","imt_sub_source_field","imt_sub_trans_field","summary_prompt","terms_prompt","sub_summary_prompt","sub_terms_prompt"],c=(l,d)=>{!l||!l[d]||!l?.env||(l[d]=l[d].replace(/{{(.+?)}}/g,(m,p)=>u.includes(p)?m:l?.env[p]||m))};Object.values(n).forEach(l=>{c(l,"prompt"),c(l,"multiplePrompt"),c(l,"subtitlePrompt")}),t.translationServices=n}async function Mx(e){try{let t=await Ne("installedAt",""),a=e.generalRule.subtitleRule.youtubeAutoEnableSubtitleAfterInstalledAt;if(!a||new Date(a)>new Date(t))return;e.generalRule.subtitleRule.youtubeAutoEnableSubtitle=!0}catch(t){I.error("updateYoutubeAutoEnableSubtitleWithAfterInstallAt error",t)}}function Qu(e,t){let a={...e};["provider","visible"].forEach(i=>{a[i]&&delete a[i]});let r={...a,...t};return lt({rule:t,valueIsArray:i=>Array.isArray(a[i]),getMergedValue:i=>a[i],onMergedResult:(i,o)=>{r[i]=o}}),r}async function Ix(e,t,a){let n=e.interfaceLanguage==="en",r=e.targetLanguage==="en",i=a&&a.translationLanguagePattern&&a.translationLanguagePattern.matches&&a.translationLanguagePattern.matches.length===0,o=["bing","google","transmart","mock"].includes(e.translationService)||!e.translationService;!n&&!r&&!i&&o?a.translationLanguagePattern||(a.translationLanguagePattern={matches:["en"],excludeMatches:[]}):a.translationLanguagePattern={matches:[],excludeMatches:[]};let s=a&&a.enableDefaultAlwaysTranslatedUrls;e.enableDefaultAlwaysTranslatedUrls===!1&&(s=!1);let u=e&&e.isChangedAlwaysTranslatedUrls,c=[];e.translationUrlPattern&&e.translationUrlPattern.matches&&(c=e.translationUrlPattern.matches||[]);let l=!1;u===void 0&&(c.length>0?u=!0:u=!1,e.isChangedAlwaysTranslatedUrls=u,t.isChangedAlwaysTranslatedUrls=u,l=!0);let d=(e?.translationLanguagePattern?.matches?.length||0)>0,m=await Ne("installedAt",""),p=Number(await Ha("translage_page_daily",0)),v=Number(await Ne(ft,0)),w;v>0&&(Date.now()-v<a.inactiveDays*24*60*60*1e3?w=!0:w=!1);let S;p>0&&(Date.now()-p<a.inactiveDays*24*60*60*1e3?S=!0:S=!1);let g;if(m){let f=new Date(m);Date.now()-f.getTime()<24*60*60*1e3?g=!0:g=!1}let M=e.modifiedBySystem;s&&!u&&!n&&!r&&o&&!d&&(M===!0||g||S===!1||w===!1||S===void 0&&w===void 0)&&Sx(c,a.defaultAlwaysTranslatedUrls)&&(e.translationUrlPattern||(e.translationUrlPattern={}),e.translationUrlPattern.matches||(e.translationUrlPattern.matches=[]),e.translationUrlPattern.excludeMatches||(e.translationUrlPattern.excludeMatches=[]),e.translationUrlPattern.matches=[...a.defaultAlwaysTranslatedUrls],t.translationUrlPattern=e.translationUrlPattern,t.modifiedBySystem=!0,l=!0,Lp([{name:"modifyAlwaysTranslatedUrls"}],a.enableSelfServiceReport)),l&&(I.debug("isChangedUserConfig",l),await Ft(t))}async function Px(e){let t=await kx();if(t&&t.buildinConfigUpdatedAt){let a=new Date(t.buildinConfigUpdatedAt),n=new Date(e.buildinConfigUpdatedAt);a>n&&(e=t)}return e}function Fx(e,t){let a=new Date;if(e&&e.tempTranslationUrlMatches&&e.tempTranslationUrlMatches.length>0){let n=e.tempTranslationUrlMatches.filter(r=>new Date(r.expiredAt)>a);if(n.length>0){let r=t.translationUrlPattern?t.translationUrlPattern?.matches||[]:[],i=Array.isArray(r)?r:[r],o=Array.from(new Set(i.concat(n.map(s=>s.match))));t.translationUrlPattern={...t.translationUrlPattern,matches:o}}}}async function Bx(e,t){let a=await Rx(),n=Object.keys(e),r=["translationUrlPattern","translationLanguagePattern","immediateTranslationPattern","translationBodyAreaPattern","translationParagraphLanguagePattern","translationThemePatterns","translationGeneralConfig","shortcuts","inputTranslationUrlPattern","inputLanguageCodeAlias","tokenUsageTips"];for(let i of n){let o=i;if(o==="generalRule")typeof t[o]=="object"&&(e[o]=Up(e[o],t[o]));else if(o==="translationServices")Cx(t,e);else if(typeof t[o]!="string"&&typeof t[o]!="boolean"&&typeof t[o]!="number"&&r.includes(o))t[o]&&(e[o]=Object.assign(e[o],t[o])),o==="shortcuts"&&(fe()||Ze()?e[o]={...e[o],...a}:e[o]={...a});else if(o==="rules"){if(Array.isArray(t[o])){let s=e.rules||[],u={};for(let l of s)l.id&&(u[l.id]=l);let c=t[o].map(l=>l.id&&u[l.id]?qp(e.generalRule,u[l.id],l):l);e[o]=[...c,...e[o]]}if(re.PROD==="0"&&re.DEV_RULES){let s=JSON.parse(re.DEV_RULES);e[o]=[...s,...e[o]]}}else t[o]!==void 0&&(e[o]=t[o])}return e}async function Rx(){let e={};if(!fe()&&A.commands&&A.commands.getAll){let t=await A.commands.getAll();for(let a of t)a.name&&a.shortcut&&(e[a.name]=a.shortcut)}return e}async function _x(e){try{if(!e.advanceMergeConfig)return e;let t=e,a=await jx();return e.advanceMergeConfig.forEach(({condition:n,advanceConfig:r})=>{if(!n||!r||!Vp(n,{...e,...a}))return;let{generalRule:o,...s}=r;lt({rule:s,valueIsArray:u=>Array.isArray(t[u]),getMergedValue:u=>t[u],onMergedResult:(u,c)=>{t[u]=c}}),o&&lt({rule:o,valueIsArray:u=>Array.isArray(t.generalRule[u]),getMergedValue:u=>t.generalRule[u],onMergedResult:(u,c)=>{t.generalRule[u]=c}})}),t}catch(t){return I.error("mergeAdvanceConfig error",t),e}}var Gn=null;async function jx(){try{let e=er()||"0",t=Wo()||"0";if(Gn&&Gn.imtAndroidVersion===e&&Gn.imtIOSVersion===t)return Gn;let n=ue().INSTALL_FROM,r=rt(),i=pr(),o=Vo()||"",s=Cc(),u=Mc()||"",c=await Gt()||"a",l=c.charCodeAt(0)-"a".charCodeAt(0),d=new Date(await Tm()).getTime();return Gn={abTag:c,abTagNumber:l,version:r,imtAndroidVersion:e,imtIOSVersion:t,installFrom:n,platform:i,iosSafariVersion:o,iosSafariVersionNumber:s,iosSystemVersion:u,isMobile:!!kt().any,isMacOS:Wu(),isMacSafari:Jp(),versionNumber:Mn(r),imtAndroidVersionNumber:Mn(e),imtIOSVersionNumber:Mn(t),installedAtTimestamp:d},Gn}catch(e){return I.error("getAdvanceConditionInfo error",e),null}}function ro(e){let t=A.runtime.getURL(wl),a=new URL(t);return e&&(e.startsWith("http")||!qa())&&a.searchParams.set("file",e),a.href}function Xp(){return ue().PDF_VIEWER_URL}function $p(e){return new URL(e)?.pathname.toLowerCase().endsWith(".pdf")}var io=class{accessToken;constructor(t){this.accessToken=t}async listAll(){let t=[],a="";do{let{nextPageToken:n,files:r}=await this.list(a).catch(i=>{throw i});t.push(...r),a=n||""}while(a);return t}async getConfig(t){try{return await(await fetch(`https://www.googleapis.com/drive/v3/files/${t}?alt=media`,{headers:{Authorization:`Bearer ${this.accessToken}`}})).json()}catch(a){return I.error("get config error, use default",a),{}}}async delete(t){await fetch(`https://www.googleapis.com/drive/v3/files/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${this.accessToken}`}})}findByName(t){return this.list(void 0,`name = '${t}'`)}uploadConfig(t,a=ai){let n=new Blob([JSON.stringify(t,null,2)],{type:"application/json"});return this.upload({name:a,parents:["appDataFolder"],mimeType:"application/json"},n)}updateConfig(t,a){let n=new Blob([JSON.stringify(a,null,2)],{type:"application/json"});return this.updateContent(t,n)}async upload(t,a){let n=new FormData;n.append("metadata",new Blob([JSON.stringify(t)],{type:"application/json; charset=UTF-8"})),n.append("file",a);let r=await fetch("https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart",{method:"POST",headers:{Authorization:`Bearer ${this.accessToken}`},body:n});return r.ok?await r.json():Promise.reject(r.text())}async list(t,a){let n=new URL("https://www.googleapis.com/drive/v3/files");t&&n.searchParams.append("pageToken",t),a&&n.searchParams.append("q",a),n.searchParams.append("spaces","appDataFolder"),n.searchParams.append("fields","files(id,name,createdTime,modifiedTime,size)"),n.searchParams.append("pageSize","100"),n.searchParams.append("orderBy","createdTime desc");try{return I.debug("list api:",n.toString(),this.accessToken),await(await fetch(n.toString(),{headers:{Authorization:`Bearer ${this.accessToken}`}})).json()}catch(r){throw I.error("fetch google ip error",r),r}}async updateContent(t,a){return await(await fetch(`https://www.googleapis.com/upload/drive/v3/files/${t}?uploadType=media`,{method:"PATCH",headers:{Authorization:`Bearer ${this.accessToken}`},body:a})).text()}};function eg(e,t){let a=["https://www.googleapis.com/auth/drive.appdata"];return`https://accounts.google.com/o/oauth2/v2/auth?client_id=${ti}&response_type=token&redirect_uri=${encodeURIComponent(t)}&scope=${encodeURIComponent(a.join(" "))}&state=${encodeURIComponent(JSON.stringify(e))}`}function tg(e){let t=e.match(/[#?](.*)/);return!t||t.length<1?null:{access_token:new URLSearchParams(t[1].split("#")[0]).get("access_token")}}async function ag(e,t,a,n,r,i,o){if(I.debug(`autoSyncStrategy accessToken: ${e}`),t===null){I.debug("autoSyncStrategy settings is null");return}let s=new io(e);try{let u=(await s.findByName(ai)).files;I.debug("files",u);let c=u[0]?.id,l=null;if(c&&(l=await s.getConfig(c).then(d=>({fileId:c,config:d}))),l){let{config:d,fileId:m}=l,p=d.updatedAt?new Date(d.updatedAt):new Date(0),v=t.updatedAt?new Date(t.updatedAt):new Date(0);if(I.debug("remoteUpdatedAt",p,"localUpdatedAt",v),p>v)I.debug("remote is newer, update local config",d),a(d),i&&i(!0);else if(p.getTime()===v.getTime())I.debug("remote and local are the same, do nothing"),i&&i(!1);else if(p<v){let w=new Date().getTime(),S=await Ne("installedAt",""),g=w-new Date(S).getTime()<60*60*1e3*24,M=Object.keys(d||{}).length,f=Object.keys(t||{}).length;g&&M>f?(I.debug("It's very recent install, and remote config has more fields, use remote config",d),a(d)):(I.debug("local is newer, update remote config",t),await s.updateConfig(m,t)),i&&i(!0)}else{o&&o(": unknown error");return}n(new Date().toISOString())}else if(l===null)if(t){if(!t.updatedAt){let d=new Date().toISOString();r(d),t.updatedAt=d}await s.uploadConfig(t),n(new Date().toISOString()),i&&i(!0)}else o&&o(": Local Config is empty");else o&&o(": latestConfig is "+l)}catch(u){I.error("syncLatestWithDrive error",u),o&&o(": "+u.message)}}var Lx="https://oauth2.googleapis.com/revoke",oo=class e{CLASSNAME="GoogleAuth";_state;_redirectUrl;constructor(t,a){this._state=t,this._redirectUrl=a}static revoke(t){let a=`${Lx}?token=${t}`;return fetch(a,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"}}).then(async n=>(await e.removeAuthInfo(),Eo()&&globalThis.location.reload(),n))}static async getAuthInfo(){let t=await A.storage.local.get(pn);if(t[pn])return t[pn]}static async removeAuthInfo(){return await A.storage.local.remove(pn)}static setAuthInfo(t){return A.storage.local.set({[pn]:t})}async auth(t=!1){let a=await e.getAuthInfo();if(I.debug(this.CLASSNAME,"token from cache:",a),a&&a.access_token&&await ng(a.access_token).then(i=>!0).catch(i=>!1))return Promise.resolve(a);let n=eg(this._state,this._redirectUrl);return I.debug(this.CLASSNAME,"auth url: "+n),Eo()?this.userscriptAuthWorkflow(n,t):this.extensionAuthWorkflow(n).then(r=>(e.setAuthInfo(r),r))}async userscriptAuthWorkflow(t,a){return a&&await A.storage.local.set({[Sl]:!0}),globalThis.open(t,"_self"),Promise.resolve({})}extensionAuthWorkflow(t){let a=typeof A<"u"&&!!A?.runtime?.id,n=a&&typeof A.windows?.create=="function",r,i=!1;return new Promise((o,s)=>{let u=()=>{a&&(A.tabs.onUpdated.removeListener(c),A.tabs.onRemoved.removeListener(l))},c=(p,v,w)=>{if(I.debug(this.CLASSNAME,"create tab onUpdated: "+w.url),r===p){let S=new URL(w.url||""),g=tg(w.url);S.pathname.startsWith("/auth-done")&&g?.access_token&&(I.debug(this.CLASSNAME,"auth done: "+w.url),o({access_token:g.access_token}),i=!0,A.tabs.remove(p),u())}},l=p=>{I.debug(this.CLASSNAME,"create tab onRemoved: "+p),(p===r||!i)&&(u(),s(new Error("auth failed")))},d=()=>{let p=globalThis.open(t,"google_oauth",`width=500,height=650,left=${screen.width/2-250},top=${screen.height/2-325}`);if(!p)throw new Error("Popup blocked. Please allow popups for this site.");return new Promise((v,w)=>{let S=setInterval(()=>{if(p.location.href.includes("google-auth-success")){p.close(),clearInterval(S);try{let g=localStorage.getItem("immersiveTranslateAuthState")||"",M=JSON.parse(g);o(M)}catch(g){w(g)}return}p.closed&&(clearInterval(S),w(new Error("User closed the window")))},100)})},m=()=>{if(n&&!kt().any){let p=globalThis.screen,v=p.availLeft??p.left,w=p.availTop??p.top,S=p.availWidth,g=p.availHeight,M=Math.max(400,Math.min(500,S)),f=Math.max(500,Math.min(650,g)),T=Math.max(v,Math.min(Math.round(v+(S-M)/2),v+S-M)),L=Math.max(w,Math.min(Math.round(w+(g-f)/2),w+g-f));return A.windows.create({url:t,type:"popup",width:M,height:f,left:T,top:L}).then(E=>{r=E.tabs?.[0]?.id})}else return A.tabs.create({url:t}).then(p=>{r=p.id})};try{(a?m():d()).then(()=>{a&&(A.tabs.onUpdated.addListener(c),A.tabs.onRemoved.addListener(l))}).catch(s)}catch(p){s(p)}})}};var Nx="https://www.googleapis.com/oauth2/v3/tokeninfo",Ox=ti,zx=ue().REDIRECT_URL;function ng(e){if(!e)throw"Authorization failure";let t=`${Nx}?access_token=${e}`,a=new Request(t,{method:"GET"});function n(r){return new Promise((i,o)=>{r.status!=200&&o("Token validation error"),r.json().then(s=>{s.aud&&s.aud===Ox?i(e):o("Token validation error")})})}return fetch(a).then(n)}function rg(e,t=!1){let a=zx;if(typeof window<"u"&&window.location.protocol.startsWith("http")){let r=window.location.hostname,i=window.location.port;a=`${window.location.protocol}//${r}${i?`:${i}`:""}/auth-done/`}return new oo(e,a).auth(t)}async function ig(){}function og(e,t){let a=new Map;for(let i of t)a.set(i.header.toLowerCase(),i);let n=[],r=e.filter(i=>{let o=a.get(i.name.toLowerCase());if(o){if(o.operation==="remove")return!1;if(o.operation==="set")return!1}return!0});for(let i of t)i.operation==="set"&&n.push({name:i.header,value:i.value||""});return r.concat(n)}var so=[{id:1,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"Referer",operation:"set",value:"https://httpstat.us/429"},{header:"origin",operation:"set",value:"https://httpstat.us/429"},{header:"DNT",operation:"set",value:"1"}]},condition:{urlFilter:"https://httpstat.us/429",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:2,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"Referer",operation:"set",value:"https://www.deepl.com/"},{header:"origin",operation:"set",value:"https://www.deepl.com"},{header:"DNT",operation:"set",value:"1"},{header:"cookie",operation:"remove"}]},condition:{urlFilter:"https://www2.deepl.com/jsonrpc*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:200,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"Referer",operation:"set",value:"https://www.deepl.com/"},{header:"origin",operation:"set",value:"chrome-extension://cofdbpoegempjloogbagkncekinflcnj"},{header:"DNT",operation:"set",value:"1"}]},condition:{urlFilter:"https://api.deepl.com/jsonrpc*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:201,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"Referer",operation:"set",value:"https://www.deepl.com/"},{header:"origin",operation:"set",value:"chrome-extension://cofdbpoegempjloogbagkncekinflcnj"}]},condition:{urlFilter:"https://w.deepl.com/oidc/token",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:3,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"origin",operation:"set",value:"chrome-extension://lkjkfecdnfjopaeaibboihfkmhdjmanm"}]},condition:{urlFilter:"https://transmart.qq.com/api/imt",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:4,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"origin",operation:"set",value:"chrome-extension://lkjkfecdnfjopaeaibboihfkmhdjmanm"}]},condition:{urlFilter:"https://translate.volcengine.com/crx/translate/v1/",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:5,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"User-Agent",operation:"set",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"}]},condition:{urlFilter:"https://edge.microsoft.com/translate/auth",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:6,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"User-Agent",operation:"set",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"}]},condition:{urlFilter:"https://api-edge.cognitive.microsofttranslator.com/translate",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:301,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://www.pixiv.net/"}]},condition:{urlFilter:"https://i.pximg.net/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:302,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://newtoki341.com/"}]},condition:{urlFilter:"https://img1.newtoki21*.org/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:303,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://newtoki341.com/"}]},condition:{urlFilter:"https://img1.newtoki21.org/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:304,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://2.bp.blogspot.com"}]},condition:{urlFilter:"https://2.bp.blogspot.com/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:305,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://japanreader.com"}]},condition:{urlFilter:"https://japanreader.com/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:307,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://sl.mangafuna.xyz/"}]},condition:{urlFilter:"https://sl.mangafuna.xyz/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:308,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://toonily.me"}]},condition:{urlFilter:"https://s*.toonilycdnv2.xyz/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:309,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://readcomiconline.li"}]},condition:{urlFilter:"https://*.whatsnew*.net/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:310,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://yymanhua.com"}]},condition:{urlFilter:"https://image.yymanhua.com/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:311,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://klz9.com"}]},condition:{urlFilter:"https://*.(klimv1|jfimv2).xyz/images*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:312,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://manhwato.com"}]},condition:{urlFilter:"https://stcdn.manhwato.com/images/manga/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:313,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://www.comemh8.com"}]},condition:{urlFilter:"https://*.kingwar.cn/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:314,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://weibo.com/"}]},condition:{urlFilter:"https://*.sinaimg.cn/",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:315,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"origin",operation:"set",value:"http://127.0.0.1:11434"}]},condition:{urlFilter:"http://*:11434",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:316,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"origin",operation:"set",value:"http://127.0.0.1:1234"}]},condition:{urlFilter:"http://*:1234",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:317,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://jestful.net"}]},condition:{urlFilter:"https://*.jfimv2.xyz/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:318,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://viewer.championcross.jp"}]},condition:{urlFilter:"https://viewer.championcross.jp",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}},{id:319,priority:1,action:{type:"modifyHeaders",requestHeaders:[{header:"referer",operation:"set",value:"https://viewer.comic-growl.com/"}]},condition:{urlFilter:"https://viewer.comic-growl.com/*",resourceTypes:["xmlhttprequest"],domainType:"thirdParty",initiatorDomains:["cfhamdkdjgoelclgllcoikbckcfpaklj","bpoadfkcbjbfhfodiogcnhhhpibjhbnh","amkbmndfnliijdhojkpoglbnaaahippg"]}}];var qx="https://www.google-analytics.com/mp/collect",Gx=30,Hx=100;async function Kx(){let{sessionData:e}=await A.storage.session.get("sessionData"),t=Date.now();return e&&e.timestamp&&((t-e.timestamp)/6e4>Gx?e=null:(e.timestamp=t,await A.storage.session.set({sessionData:e}))),e||(e={session_id:t.toString(),timestamp:t.toString()},await A.storage.session.set({sessionData:e})),e.session_id}function sg(e){Nl().forEach(({measurementId:t,apiSecret:a})=>{ug({measurement_id:t,api_secret:a,page_title:e.pageTitle,page_location:e.pageLocation,time:e.time})})}function Hn(e,t,a){return ug({measurement_id:jl,api_secret:Ll,page_title:e,page_location:t,time:a})}async function ug(e){try{let t=ue(),a=Wa(),n=t.INSTALL_FROM==="firefox_store";if(a?.name?.startsWith("ImtFx")&&(n=!1),n)return;let{fakeUserId:r}=await oa(),i=`${qx}?measurement_id=${e.measurement_id}&api_secret=${e.api_secret}`,o={method:"POST",body:JSON.stringify({client_id:r,events:[{name:"page_view",params:{session_id:await Kx(),engagement_time_msec:e.time||Hx,page_title:e.page_title||document.title,page_location:e.page_location||document.location.href}}]})};fetch(i,o)}catch(t){I.error(t)}}var fa=new Map;function lg(e){if(!e)return null;try{return new URL(e).hostname}catch{return null}}async function lo(e){let t=await dg(),a=Date.now();if(t){if(fa.has(e))return;let n=await A.tabs.get(e),r=lg(n.url);if(!r)return;fa.set(e,{duration:0,lastActiveTime:a,hostname:r});return}Yu(e)}async function cg(e){let t=await dg(),a=Date.now();for(let[n,r]of fa)!(await A.tabs.get(n)).active&&r.lastActiveTime&&(r.duration+=a-r.lastActiveTime,r.lastActiveTime=null);if(t){if(fa.has(e)){let i=fa.get(e);i.lastActiveTime=a;return}let n=await A.tabs.get(e),r=lg(n.url);if(!r)return;fa.set(e,{duration:0,lastActiveTime:a,hostname:r});return}}function Yu(e){if(fa.has(e)){let t=fa.get(e),a=t.duration;t.lastActiveTime&&(a+=Date.now()-t.lastActiveTime),fa.delete(e),Vx(t.hostname,a)}}async function Wx(e){let a=(await A.tabs.query({currentWindow:!0,active:!0}))[0].id;return _a().sendMessage(`content_script:main:${a}`,e)}function Vx(e,t){Hn(`${e}(video)`,e,t),Qx(t)}var uo="videoWatchTimeDay";async function Qx(e){try{let t=new Date,n=new Date(t.getFullYear(),t.getMonth(),t.getDate()).getTime(),r=await Ne(uo,null);if(!r){await Se(uo,{day:n,time:e});return}if(r.day!==n){Hn("day-video."+De,"day-video."+De,r.time),await Se(uo,{day:n,time:e});return}let i={day:n,time:r.time+e};await Se(uo,i)}catch(t){I.error(t)}}async function dg(){try{return await Wx({method:"getIsDulSubtitle",data:{trigger:"shortcut"}})}catch{return null}}var Ju=(e,t,a,n)=>{let r=Hl[e]||e,i=Gl[e]||e,o=Kl[e],s=Wl[e],u={"zh-CN":o,"zh-TW":s,en:i};if(a)return u[e]?u[e]:i;let c=e!==t&&r!=="All Languages";if(u[t]){let l=u[t];return n||e==="auto"||e==="placeholder"?l:c?`${l} (${r})`:`${l}`}else return c?`${i} (${r})`:i};function Yx(e,t,a){let n=e.generalRule.imageRule,r=Dm(a),o=n.supportPlatform?.[r]||n.enableImageTranslation,s=e.beta;return o=="all"||o=="beta"&&s||o=="pro"&&t||o=="pro_beta"&&s&&t?!0:o?!1:s&&!!t}async function Zu(){if(fe())return!1;let e=await Ue(),t=await cc();return Yx(e,t)}function Jx(){return na()&&!Ze()}var Or=Jx()?["action"]:["browser_action","page_action"],zr="",mg=[{id:"toggleTranslatePage",contexts:["page","frame","selection",...Or]},{id:Oa,contexts:["image"]},{id:Mo,contexts:Or},{id:Po,contexts:Or},{id:Fo,contexts:Or},{id:Io,contexts:Or}];async function Xu(e){I.debug("createContextMenu",e.isShowContextMenu,mg);for(let t of mg){let a=!0;try{let n=Re(e.interfaceLanguage,`browser.${t.id}`);if(t.id===Oa&&(a=await Zu()),t.id==="toggleTranslatePage"){n=Re(e.interfaceLanguage,"browser.toggleTranslatePage",{targetLanguage:Ju(e.targetLanguage,e.interfaceLanguage,!1,!0)});let r=Vu(e.shortcuts.toggleTranslatePage);r&&(n+=`(${r})`)}A.contextMenus.create({id:t.id,title:n,contexts:t.contexts,visible:a&&e.isShowContextMenu},()=>A.runtime.lastError)}catch(n){I.debug("create context menu error, it's ok!!",n,`menu id: ${t.id}`)}}}async function Ur({targetLanguage:e,text:t}){let a=await Ue(),n="toggleTranslatePage",r;if(t)zr=t,r=Re(a.interfaceLanguage,"browser.translateText",{text:t});else{let i=await co(),o=i&&i!=="Original";r=Re(a.interfaceLanguage,"browser.toggleTranslatePage",{targetLanguage:Ju(e??a.targetLanguage,a.interfaceLanguage,!1,!0)}),o&&(r=`${Re(a.interfaceLanguage,"show-original")}`);let s=Vu(a.shortcuts.toggleTranslatePage);s&&(r+=`(${s})`),zr=""}A.contextMenus.update(n,{title:r,visible:a.isShowContextMenu})}function pg(){A.contextMenus.onClicked.addListener(async e=>{if(e.menuItemId===Mo)A.runtime.openOptionsPage();else if(e.menuItemId===Io){let t=Xp();A.tabs.create({url:t})}else if(e.menuItemId===Fo){let a=ue().EBOOK_BUILDER_URL;A.tabs.create({url:a})}else if(e.menuItemId===Po){let a=ue().EBOOK_VIEWER_URL;A.tabs.create({url:a})}else if(e.menuItemId===Ml){let a=ue().SUBTITLE_BUILDER_URL;A.tabs.create({url:a})}else if(e.menuItemId===Oa)ln({method:e.menuItemId,data:{srcUrl:e.srcUrl,trigger:"right_menu"}});else{if(e.selectionText&&e.editable){ln({method:"inputSelectedTextTranslate",data:{text:zr,trigger:"right_menu"}});return}if(e.selectionText&&zr){ln({method:"selectionTranslate",data:{text:zr,trigger:"right_menu"}});return}ln({method:e.menuItemId,data:{trigger:"right_menu"}})}})}async function gg(){try{let e=await Ue(),t=await Zu();A.contextMenus.update(Oa,{visible:t&&e.isShowContextMenu})}catch{}}function hg(){A.tabs.onActivated.addListener(function(e){Ur({}),Zx(e.tabId),cg(e.tabId)}),A.tabs.onRemoved.addListener(function(e){bg(e),Yu(e)}),A.tabs.onUpdated.addListener(function(e){$u(e),lo(e)})}var ba=new Map;async function $u(e){let t=await co(),a=Date.now();if(t=="Translated"){if(ba.has(e))return;let n=await A.tabs.get(e),r=fg(n.url);if(!r)return;ba.set(e,{duration:0,lastActiveTime:a,hostname:r});return}bg(e)}function fg(e){if(!e)return null;try{return new URL(e).hostname}catch{return null}}async function Zx(e){let t=await co(),a=Date.now();for(let[n,r]of ba)!(await A.tabs.get(n)).active&&r.lastActiveTime&&(r.duration+=a-r.lastActiveTime,r.lastActiveTime=null);if(t=="Translated"){if(ba.has(e)){let i=ba.get(e);i.lastActiveTime=a;return}let n=await A.tabs.get(e),r=fg(n.url);if(!r)return;ba.set(e,{duration:0,lastActiveTime:a,hostname:r});return}}function bg(e){if(ba.has(e)){let t=ba.get(e),a=t.duration;t.lastActiveTime&&(a+=Date.now()-t.lastActiveTime),ba.delete(e),$x(t.hostname,a)}}async function Xx(e){let a=(await A.tabs.query({currentWindow:!0,active:!0}))[0].id;return _a().sendMessage(`content_script:main:${a}`,e)}function $x(e,t){Hn(e,e,t),ew(t)}async function co(){try{return await Xx({method:"getPageStatus",data:{trigger:"shortcut"}})}catch{return null}}var mo="readTimeDay";async function ew(e){try{let t=new Date,n=new Date(t.getFullYear(),t.getMonth(),t.getDate()).getTime(),r=await Ne(mo,null);if(!r){await Se(mo,{day:n,time:e});return}if(r.day!==n){Hn("day."+De,"day."+De,r.time),await Se(mo,{day:n,time:e});return}let i={day:n,time:r.time+e};await Se(mo,i)}catch(t){I.error(t)}}async function yg(e,t,a,n){if(!chrome?.offscreen)return!1;try{(await chrome.runtime.getContexts({})).find(o=>o.contextType==="OFFSCREEN_DOCUMENT")||await chrome.offscreen.createDocument({url:"offscreen.html",reasons:["WORKERS"],justification:"ocr translate img"}),chrome.runtime.sendMessage({target:"offscreen",data:{context:e,type:"trigger",urlHash:t,mimeType:a,imgBuffer:n}})}catch{return!1}return!0}function po(){Ue().then(e=>{el(e),tl(e)}).catch(e=>{I.error("create menu error",e)})}async function el(e){try{let t=await tw(e);I.debug(`updateUninstallUrl: ${t}`),t&&A.runtime.setUninstallURL&&A.runtime.setUninstallURL(t)}catch(t){I.error("setUninstallUrl error",t)}}async function tw(e){try{if(e||(e=await Ue()),!e.uninstallUrl)return;let t=await Ut.get(bt,null),{installedAt:a,fakeUserId:n}=await oa(),r=new URL(e.uninstallUrl);e.interfaceLanguage&&r.searchParams.set("interface_language",e.interfaceLanguage),e.targetLanguage&&r.searchParams.set("target_language",e.targetLanguage);let i=ka.parse(globalThis.navigator.userAgent),o=Hm(a);r.searchParams.set("is_new_user_today",o?"1":"0"),r.searchParams.set("install_day",_i(Bn(new Date(a)))),r.searchParams.set("version",rt()),i.browser&&(r.searchParams.set("browser_name",i.browser.name||"unknown"),r.searchParams.set("browser_version",i.browser.version||"unknown"));let s=li(t);r.searchParams.set("user_type",s?.user_type||"anonymous"),r.searchParams.set("device_id",n||"unknown");let u=await Gt();r.searchParams.set("t",u),r.searchParams.set("ab_tag",u);let c=await Qa();return r.searchParams.set("campaign",c||"none"),r.toString()}catch(t){return I.error("getUninstallUrl error",t),e?.uninstallUrl}}function tl(e){e?Xu(e):Ue().then(t=>{Xu(t)}).catch(t=>{I.error("create menu error",t)})}function vg(){A.runtime.onInstalled.addListener(e=>{I.debug(`onInstalled reason: ${e.reason}`),I.debug(e);let t=A.runtime.getManifest().version;e.reason=="install"?(async()=>{let a=Wn,r=A.runtime.getURL("").startsWith("safari"),i=!1,o=!1;try{let d=await A.runtime.getPlatformInfo();if(r)d.os==="mac"?i=!0:d.os==="ios"&&(o=!0);else if(d.os==="android"){if($n()){let m=er();if(m&&Ht(m,"1.0.2")){po();return}}a=Wn+"mobile/"}}catch{}i?a=Wn+"safari/step-1/":o&&(a=Wn+"ios/step-2/");let s=await Ne("onboardingDisplayTime",""),u=await Gt(),c=await rt(),l=new URL(a);l.searchParams.set("t",u),l.searchParams.set("v",c),r?s||(await Se("onboardingDisplayTime",new Date().toISOString()),A.tabs.create({url:l.toString()})):A.tabs.create({url:l.toString()}),po()})():(e.reason=="update"&&e.previousVersion&&t!=e.previousVersion,po())})}var go,aw=async function(e,t){let{method:a,data:n}=e;if(I.debug("background received message",a,n||" "),a==="mock")await xa(150);else{if(a==="queryParagraphCache")return zs(n);if(a==="setParagraphCache")return Os(n);if(a==="queryAllTermsMeta")return Bi(!1);if(a==="queryTerms")return hr(n,!1);if(a==="saveTerms")return Pn(n,!1);if(a==="deleteTerms")return gr(n,!1);if(a==="calculateSize")return Fm();if(a==="fetch")return Ca(n);if(a==="getConfig")return n?.userAgent&&Dc(n.userAgent),Ue();if(a==="getLocalConfig")return Ja();if(a==="openOptionsPage"){let r="";n&&n.pageRoute&&(r=n.pageRoute);let i=A.runtime.getURL("options.html");A.tabs.create({url:i+r})}else if(a==="openAboutPage")A.tabs.create({url:A.runtime.getURL("options.html#about")});else if(a==="openInTab")n&&A.tabs.create({url:n});else if(a==="openEbookViewerPage"){let i=ue().EBOOK_VIEWER_URL;A.tabs.create({url:i})}else if(a==="openSubtitleBuilderPage"){let i=ue().SUBTITLE_BUILDER_URL;A.tabs.create({url:i})}else if(a==="openEbookBuilderPage"){let i=ue().EBOOK_BUILDER_URL;A.tabs.create({url:i})}else if(a==="openHtmlViewerPage"){let i=ue().HTML_VIEWER_URL;A.tabs.create({url:i})}else if(a==="openPdfViewerPage"){let r=n?.url,i=ro(r);r||(i=ue().PDF_VIEWER_URL),A.tabs.create({url:i})}else{if(a==="setLocalConfig")return Ma(n);if(a=="getUserConfig")return ua();if(a=="setUserConfig")return Ft(n);if(a==="detectLanguage"){let{text:r}=n;if(A.i18n&&A.i18n.detectLanguage)try{let i=await xg(A.i18n.detectLanguage(r),1500,{isReliable:!1,languages:[]});return i.languages.length>0?Ua(i.languages[0].language):"auto"}catch(i){return I.debug("detect language error",i),"auto"}else return"auto"}else if(a==="detectTabLanguage")try{let r=await xg(A.tabs.detectLanguage(t.id),1500,"auto");return Ua(r)}catch(r){return I.debug("detect tab language error, use auto ",r),"auto"}else if(a==="autoSyncLatestConfig"){try{await ig()}catch(r){I.debug("auto sync latest config error",r)}return""}else if(a!=="updateCommands")if(a==="setBadge"){let r=t.id,i=n&&n.text?n.text:"";r&&(Ur({}),nw(r,i)),r&&$u(r)}else if(a=="getDelay"){let{key:r,options:i}=n||{};return yr.getDelay(r,i,!0)}else if(a==="getIsSupportIsOnToolbar")try{return A?.action?.getUserSettings?(await A.action.getUserSettings(),!0):!1}catch{return!1}else if(a==="getIsOnToolbar")try{return(await A.action.getUserSettings()).isOnToolbar}catch{return!1}else if(a=="reportOptionsPageView")sg(n);else if(a==="updateVideoSubtitleStatus"){let r=t.id;r&&lo(r)}else if(a==="updateToggleTranslateContextMenu")Ur(n);else if(a==="updateImageMenu")gg();else if(a=="triggerClientTranslateImage"){let{mimeType:r,imgBuffer:i,urlHash:o,context:s}=n;return yg(s,o,r,i)}else if(a==="updateUninstallUrl")el();else if(a==="toggleTranslatePage"){let{trigger:r,currentPageStatus:i}=n;await iw({method:"toggleTranslatePage",data:{trigger:r,currentPageStatus:i}})}else{if(a=="isOpenSidePanel")return $s();if(a==="toggleSidePanel"){let r=t.id;zi({isOpen:n?.isOpen,tabId:r})}else if(a==="queryInDB")try{return Us(n.dbName,n.key)}catch{return null}else if(a==="addInDB")return qs(n.dbName,n.value)}}}};function wg(){_a();let e=A.runtime.getManifest();if(e.manifest_version>2,e.manifest_version===2&&A.webRequest&&A.webRequest.onBeforeSendHeaders){let t=so.map(n=>n.condition.urlFilter),a=so.reduce((n,r)=>(r.condition.resourceTypes.forEach(i=>{n.includes(i)||n.push(i)}),n),[]);A.webRequest.onBeforeSendHeaders.addListener(function(n){if(!(n.originUrl&&n.originUrl.startsWith("http"))&&n.originUrl&&n.requestHeaders)for(let r=0;r<t.length;r++){let i=so[r];if(i.condition.urlFilter&&Sn(n.url,i.condition.urlFilter))return{requestHeaders:og(n.requestHeaders,i.action.requestHeaders)}}},{urls:t,types:a},["blocking","requestHeaders"])}}function _a(){return go||(go=new sa("background",!1).getConnection("main",aw),go)}function ho(e,t){return t==="dark"?{32:`${e}/dark-32.png`,48:`${e}/dark-48.png`,64:`${e}/dark-64.png`,128:`${e}/dark-128.png`,256:`${e}/dark-256.png`}:{32:`${e}/32.png`,48:`${e}/48.png`,64:`${e}/64.png`,128:`${e}/128.png`,256:`${e}/256.png`}}async function nw(e,t){if(Ze())return;let a=await rw();a!==null&&(t?A.browserAction&&A.browserAction.setIcon?A.browserAction.setIcon({tabId:e,path:ho("badge-icons",a)}):A.action&&A.action.setIcon&&A.action.setIcon({tabId:e,path:ho("badge-icons",a)}):A.browserAction&&A.browserAction.setIcon?A.browserAction.setIcon({tabId:e,path:ho("icons",a)}):A.action&&A.action.setIcon&&A.action.setIcon({tabId:e,path:ho("icons",a)}))}async function rw(){if(A.theme&&A.theme.getCurrent){let e=await A.theme.getCurrent();if(e.properties&&e.properties.color_scheme)return e.properties.color_scheme;if(e.properties&&e.properties.color_scheme===null)return null}return"light"}function xg(e,t,a){return new Promise((n,r)=>{let i=setTimeout(()=>{n(a)},t);e.then(o=>{clearTimeout(i),n(o)}).catch(o=>{clearTimeout(i),r(o)})})}async function iw(e){let a=(await A.tabs.query({currentWindow:!0,active:!0}))[0].id;_a().sendMessage(`content_script:main:${a}`,e).catch(r=>{I.error("send content message request failed",e,r)})}function Ag(){typeof A.commands<"u"&&A.commands.onCommand.addListener(async(e,t)=>{if(I.debug(`received command: ${e}`),["toggleTranslatePage"].includes(e)){let a=await A.tabs.query({active:!0,currentWindow:!0});if(a.length===0||typeof a[0].id>"u")return;let r=a[0].url;if($p(r)){A.tabs.create({url:ro(r)});return}}if(["toggleSidePanel"].includes(e)){let a=e0();a||zi({isOpen:!1,tabId:t?.id}),await ln({method:"toggleSidePanel",data:{trigger:"shortcut",isOpen:a}});return}await ln({method:e,data:{trigger:"shortcut"}})})}async function ln(e){let a=(await A.tabs.query({currentWindow:!0,active:!0}))[0].id;_a().sendMessage(`content_script:main:${a}`,e).catch(r=>{I.error("send content message request failed",e,r)})}async function ow(e,t,a){let n=s=>{e.postMessage(JSON.stringify({data:s}))},r=s=>{e.postMessage(JSON.stringify({error:{name:s.name,status:s.status,message:s.message,stack:s.stack}})),e.disconnect()},o={...t,onMessage:n,onError:r,onFinish:s=>{e.postMessage(JSON.stringify({finish:!0,reason:s})),e.disconnect()},signal:a};try{await Ca(o)}catch(s){if(s?.message.includes("aborted"))return;r(s)}}function kg(){A.runtime.onConnect.addListener(e=>{switch(I.debug(`received connect: ${e.name}`),e.name){case"fetchStream":let t=new AbortController,{signal:a}=t;e.onMessage.addListener(function(n){let{type:r,options:i}=JSON.parse(n);switch(r){case"abort":t.abort();break;case"start":ow(e,i,a);break}});return}})}var sw=As(null);async function lw(e,t,a){a=a||await tu();let n=a.aiAssistants||[],r=!1;if(e=="edit"&&Eg(t))for(let o=n.length-1;o>=0;o--)n[o].id===t.id&&(n[o]=t,r=!0);else if(e==="add"&&Eg(t)){for(let o=n.length-1;o>=0;o--)n[o].id===t.id&&n.splice(o,1);n.push(t),r=!0}else{for(let o=n.length-1;o>=0;o--)n[o].id===t.id&&n.splice(o,1);r=!0}a.aiAssistants=n.sort((o,s)=>o.priority-s.priority);let i=await ua();i.aiAssistantIds=[...new Set(n.map(o=>o.id))];try{await a0(a),await Ft(i)}catch{return!1}return r}async function cw(e,t){(await Promise.allSettled(e.map(n=>wt({url:`${To}api/plugins/${n}.json`})))).forEach(n=>{if(n.status==="fulfilled"){let r=n.value;r&&lw("add",r,t)}})}async function dw(e,t,a=!0){e||(e=await Ue()),t||(t=await tu());let n=t.aiAssistants||[],r=(e.aiAssistantIds||[]).filter(s=>!n.find(u=>u.id===s)),i=[];if(a){let s=await hw();i=await gw(t,s)}let o=[...new Set([...r,...i])].filter(s=>!s.startsWith("custom"));o.length!==0&&cw(o,t)}var mw=ar(dw,1500);function Eg(e){return Ht(Xn(),e.extensionVersion)}function pw(e,t){return t?!Ht(e.version,t):!1}function gw(e,t){let a=[];return(e.aiAssistants||[]).forEach(n=>{pw(n,t[n.id]?.version)&&a.push(n.id)}),[...new Set(a)]}async function hw(){return(await wt({url:`${To}api/plugins/meta.json`}))?.plugins||{}}var fw=1e3*3600*24;async function Sg(e){try{let t=e?.cacheCleanIntervalDay??1,a=e?.cacheMaxAgeDay??30,n=new Date,r=await A.storage.local.get(null),i=r[ni],o=r[Bo];if(o||(o=n.getTime(),await A.storage.local.set({[Bo]:o})),i||(i=n.getTime(),await A.storage.local.set({[ni]:i})),n.getTime()-i>=t*fw){let s=await Bm(a,o);await A.storage.local.set({[ni]:n.getTime()})}}catch{}}async function Tg(){try{if(fe())return;let e=A.runtime.getURL("locales.json"),a=await(await fetch(e)).json();Object.assign(Ia,a)}catch{}}wg();vg();Ag();kg();A.contextMenus&&pg();async function bw(){await Tg(),A.contextMenus&&tl();let e=await Ue();if(Sg(e),e.joinJobs.length){let t=Ul.replace("{jobs}",e.joinJobs.map(a=>`    \u2022 ${a}`).join(`
`))}hg(),e.debug&&I.setLevel("debug")}bw().catch(e=>{});})();
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
