#!/usr/bin/env python3
"""
智能去重工具 - 针对Prettier格式化后的代码
专门处理变量值级别的重复内容，保持JavaScript语法完整性
"""

import re
import os
import json
from typing import List, Dict, Set, Tuple

class SmartDeduplicator:
    def __init__(self):
        self.files_content = {}
        self.common_values = []
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def extract_string_values(self, content: str) -> Set[str]:
        """提取所有字符串值（包括单引号、双引号、模板字符串）"""
        patterns = [
            r'"([^"]{200,})"',  # 双引号字符串，至少200字符
            r"'([^']{200,})'",  # 单引号字符串，至少200字符
            r'`([^`]{200,})`',  # 模板字符串，至少200字符
        ]
        
        values = set()
        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                # 过滤掉主要是空白字符的内容
                if len(match.strip()) > len(match) * 0.3:
                    values.add(match)
        
        return values
    
    def find_common_string_values(self, min_files: int = None) -> List[str]:
        """找出在多个文件中都出现的字符串值"""
        if not min_files:
            min_files = len(self.files_content)  # 默认要求在所有文件中都出现
        
        print(f"\n查找在至少 {min_files} 个文件中出现的字符串值...")
        
        # 收集每个文件的字符串值
        file_values = {}
        for file_path, content in self.files_content.items():
            values = self.extract_string_values(content)
            file_values[file_path] = values
            print(f"  {os.path.basename(file_path)}: {len(values)} 个长字符串")
        
        # 找出公共值
        if not file_values:
            return []
        
        # 计算每个值在多少个文件中出现
        value_counts = {}
        for file_path, values in file_values.items():
            for value in values:
                value_counts[value] = value_counts.get(value, 0) + 1
        
        # 筛选出现次数足够的值
        common_values = []
        for value, count in value_counts.items():
            if count >= min_files:
                common_values.append(value)
                print(f"  发现公共值 (出现在{count}个文件中, 长度:{len(value)}): {value[:100].replace(chr(10), ' ')[:100]}...")
        
        # 按长度排序，优先处理大的值
        common_values.sort(key=len, reverse=True)
        return common_values
    
    def identify_css_and_language_values(self) -> Tuple[List[str], List[str]]:
        """专门识别CSS和语言相关的值"""
        print("\n专门识别CSS和语言内容...")
        
        css_values = []
        lang_values = []
        
        # 获取所有公共字符串值
        all_common_values = self.find_common_string_values(min_files=len(self.files_content))
        
        for value in all_common_values:
            # 判断是否为CSS内容
            css_indicators = [
                '.immersive-translate',
                'background-color:',
                'font-family:',
                'position:',
                'display:',
                'color:',
                'margin:',
                'padding:',
                'border:',
                'width:',
                'height:',
                '@media',
                'rgba(',
                'px;',
                'rem;',
                'em;',
                '%;}',
            ]
            
            if any(indicator in value for indicator in css_indicators):
                css_values.append(value)
                print(f"  CSS内容 (长度:{len(value)}): {value[:80].replace(chr(10), ' ')[:80]}...")
                continue
            
            # 判断是否为语言内容
            lang_indicators = [
                '简体中文',
                'English',
                'Français',
                'Deutsch',
                'Español',
                'Italiano',
                'Português',
                'Русский',
                '日本語',
                '한국어',
                'العربية',
                'placeholder',
                'Auto Detect',
                '"zh-CN"',
                '"en"',
                '"fr"',
                '"de"',
                '"es"',
                '"it"',
                '"pt"',
                '"ru"',
                '"ja"',
                '"ko"',
                '"ar"',
            ]
            
            if any(indicator in value for indicator in lang_indicators):
                lang_values.append(value)
                print(f"  语言内容 (长度:{len(value)}): {value[:80].replace(chr(10), ' ')[:80]}...")
        
        print(f"\n识别结果:")
        print(f"  CSS值: {len(css_values)} 个")
        print(f"  语言值: {len(lang_values)} 个")
        print(f"  其他公共值: {len(all_common_values) - len(css_values) - len(lang_values)} 个")
        
        return css_values, lang_values
    
    def safe_remove_values(self, content: str, values_to_remove: List[str], replacement_type: str = "empty") -> Tuple[str, int]:
        """安全地移除字符串值，保持语法完整性"""
        cleaned_content = content
        total_removed = 0

        for i, value in enumerate(values_to_remove):
            if value not in cleaned_content:
                continue

            original_size = len(cleaned_content)

            # 为不同类型的内容选择合适的替换策略
            if replacement_type == "empty":
                replacement = ""
            elif replacement_type == "comment":
                replacement = f"/* REMOVED_{replacement_type.upper()}_{i} */"
            else:
                replacement = ""

            # 更安全的替换策略：寻找完整的字符串赋值模式
            patterns_to_try = [
                # 模式1: 变量赋值 - 双引号
                (rf'(\w+\s*:\s*)"{re.escape(value)}"', rf'\1"{replacement}"'),
                # 模式2: 变量赋值 - 单引号
                (rf"(\w+\s*:\s*)'{re.escape(value)}'", rf"\1'{replacement}'"),
                # 模式3: 变量赋值 - 模板字符串
                (rf'(\w+\s*:\s*)`{re.escape(value)}`', rf'\1`{replacement}`'),
                # 模式4: 直接赋值 - 双引号
                (rf'(=\s*)"{re.escape(value)}"', rf'\1"{replacement}"'),
                # 模式5: 直接赋值 - 单引号
                (rf"(=\s*)'{re.escape(value)}'", rf"\1'{replacement}'"),
                # 模式6: 直接赋值 - 模板字符串
                (rf'(=\s*)`{re.escape(value)}`', rf'\1`{replacement}`'),
            ]

            replaced = False
            for pattern, repl in patterns_to_try:
                try:
                    if re.search(pattern, cleaned_content, re.DOTALL):
                        new_content = re.sub(pattern, repl, cleaned_content, flags=re.DOTALL)
                        # 验证替换后的内容长度变化是否合理
                        if len(new_content) < len(cleaned_content):
                            cleaned_content = new_content
                            replaced = True
                            break
                except re.error:
                    # 如果正则表达式有问题，跳过这个模式
                    continue

            if replaced:
                removed_size = original_size - len(cleaned_content)
                total_removed += removed_size
                if removed_size > 0:
                    print(f"    移除 {replacement_type} 值 {i}: {removed_size:,} 字符")

        return cleaned_content, total_removed
    
    def validate_javascript_syntax(self, content: str) -> bool:
        """简单验证JavaScript语法（检查括号匹配等）"""
        try:
            # 检查基本的括号匹配
            brackets = {'(': ')', '[': ']', '{': '}'}
            stack = []

            in_string = False
            string_char = None
            escape_next = False
            in_comment = False
            comment_type = None

            i = 0
            while i < len(content):
                char = content[i]

                if escape_next:
                    escape_next = False
                    i += 1
                    continue

                # 处理注释
                if not in_string and not in_comment:
                    if i < len(content) - 1:
                        if content[i:i+2] == '/*':
                            in_comment = True
                            comment_type = 'block'
                            i += 2
                            continue
                        elif content[i:i+2] == '//':
                            in_comment = True
                            comment_type = 'line'
                            i += 2
                            continue

                if in_comment:
                    if comment_type == 'block' and i < len(content) - 1 and content[i:i+2] == '*/':
                        in_comment = False
                        comment_type = None
                        i += 2
                        continue
                    elif comment_type == 'line' and char == '\n':
                        in_comment = False
                        comment_type = None
                    i += 1
                    continue

                if char == '\\':
                    escape_next = True
                    i += 1
                    continue

                if not in_string:
                    if char in ['"', "'", '`']:
                        in_string = True
                        string_char = char
                    elif char in brackets:
                        stack.append(char)
                    elif char in brackets.values():
                        if not stack:
                            return False
                        if brackets[stack.pop()] != char:
                            return False
                else:
                    if char == string_char:
                        in_string = False
                        string_char = None

                i += 1

            # 检查是否有未闭合的括号或字符串
            return len(stack) == 0 and not in_string and not in_comment

        except Exception:
            # 如果验证过程中出现异常，返回False
            return False
    
    def process_files(self, output_dir: str = "smart_dedup") -> Dict:
        """处理所有文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 识别CSS和语言内容
        css_values, lang_values = self.identify_css_and_language_values()
        
        # 获取其他公共值
        all_common_values = self.find_common_string_values()
        other_values = [v for v in all_common_values if v not in css_values and v not in lang_values]
        
        results = {}
        
        print("\n" + "=" * 60)
        print("开始处理文件...")
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            
            # 移除CSS值
            if css_values:
                print("  移除CSS内容...")
                cleaned_content, css_removed = self.safe_remove_values(
                    cleaned_content, css_values, "css"
                )
                total_removed += css_removed
            
            # 移除语言值
            if lang_values:
                print("  移除语言内容...")
                cleaned_content, lang_removed = self.safe_remove_values(
                    cleaned_content, lang_values, "lang"
                )
                total_removed += lang_removed
            
            # 移除其他大型值
            if other_values:
                print("  移除其他大型值...")
                cleaned_content, other_removed = self.safe_remove_values(
                    cleaned_content, other_values[:50], "other"  # 限制数量避免过度移除
                )
                total_removed += other_removed
            
            # 验证语法
            if self.validate_javascript_syntax(cleaned_content):
                print("  ✓ JavaScript语法验证通过")
            else:
                print("  ⚠ JavaScript语法可能有问题，建议检查")
            
            # 保存文件
            output_path = os.path.join(output_dir, f"smart_{filename}")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计结果
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'output_path': output_path,
                'syntax_valid': self.validate_javascript_syntax(cleaned_content)
            }
            
            print(f"  原始: {original_size:,} 字符 ({original_size/1024:.1f} KB)")
            print(f"  清理后: {cleaned_size:,} 字符 ({cleaned_size/1024:.1f} KB)")
            print(f"  压缩比例: {compression_ratio:.1f}%")
        
        return results

def main():
    deduplicator = SmartDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("智能去重工具 - 针对Prettier格式化后的代码")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("需要至少2个文件进行比较")
        return
    
    # 处理文件
    results = deduplicator.process_files()
    
    # 保存结果
    with open('smart_dedup_report.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("处理完成！总结:")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"总原始大小: {total_original:,} 字符 ({total_original/1024:.1f} KB)")
    print(f"总清理后大小: {total_cleaned:,} 字符 ({total_cleaned/1024:.1f} KB)")
    print(f"总体压缩比例: {overall_compression:.1f}%")
    
    # 语法验证总结
    valid_files = sum(1 for r in results.values() if r['syntax_valid'])
    print(f"语法验证: {valid_files}/{len(results)} 个文件通过")
    
    print(f"\n清理后的文件保存在: smart_dedup/ 目录")
    print(f"详细报告保存在: smart_dedup_report.json")

if __name__ == "__main__":
    main()
