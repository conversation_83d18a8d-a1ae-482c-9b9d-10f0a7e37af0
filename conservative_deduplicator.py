#!/usr/bin/env python3
"""
保守的去重工具 - 只移除完整的变量赋值
确保JavaScript语法完整性
"""

import re
import os
import json
from typing import List, Dict, Set, Tuple

class ConservativeDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_variable_assignments(self, content: str) -> List[Dict]:
        """查找变量赋值语句"""
        # 匹配各种变量赋值模式
        patterns = [
            # CSS相关的赋值
            r'(\w+)\s*:\s*`([^`]{500,})`',  # 模板字符串
            r'(\w+)\s*:\s*"([^"]{500,})"',  # 双引号字符串
            r"(\w+)\s*:\s*'([^']{500,})'",  # 单引号字符串
        ]
        
        assignments = []
        
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.DOTALL)
            for match in matches:
                var_name = match.group(1)
                value = match.group(2)
                full_assignment = match.group(0)
                
                # 判断内容类型
                content_type = "unknown"
                if any(indicator in value for indicator in [
                    '.immersive-translate', 'background-color:', 'font-family:', 
                    'position:', 'display:', 'color:', 'margin:', 'padding:',
                    'border:', 'width:', 'height:', '@media', 'rgba(', 'px;'
                ]):
                    content_type = "css"
                elif any(indicator in value for indicator in [
                    '简体中文', 'English', 'Français', 'Deutsch', 'Español',
                    'Auto Detect', '"zh-CN"', '"en"', '"fr"', '"de"'
                ]):
                    content_type = "language"
                elif len(value) > 2000:
                    content_type = "large_content"
                
                assignments.append({
                    'var_name': var_name,
                    'value': value,
                    'full_assignment': full_assignment,
                    'content_type': content_type,
                    'start': match.start(),
                    'end': match.end(),
                    'size': len(value)
                })
        
        return assignments
    
    def find_common_assignments(self) -> List[Dict]:
        """找出在所有文件中都存在的相同赋值"""
        print("\n查找公共变量赋值...")
        
        # 收集每个文件的赋值
        file_assignments = {}
        for file_path, content in self.files_content.items():
            assignments = self.find_variable_assignments(content)
            file_assignments[file_path] = assignments
            print(f"  {os.path.basename(file_path)}: {len(assignments)} 个赋值")
        
        # 按值内容分组，找出公共的
        value_to_assignments = {}
        for file_path, assignments in file_assignments.items():
            for assignment in assignments:
                value = assignment['value']
                if value not in value_to_assignments:
                    value_to_assignments[value] = []
                value_to_assignments[value].append({
                    'file': file_path,
                    'assignment': assignment
                })
        
        # 筛选出在所有文件中都存在的值
        common_assignments = []
        required_files = len(self.files_content)
        
        for value, file_assignments_list in value_to_assignments.items():
            if len(file_assignments_list) >= required_files:
                # 检查是否在所有文件中都存在
                files_with_value = set(item['file'] for item in file_assignments_list)
                if len(files_with_value) >= required_files:
                    assignment_info = file_assignments_list[0]['assignment']
                    common_assignments.append({
                        'value': value,
                        'content_type': assignment_info['content_type'],
                        'size': assignment_info['size'],
                        'files_count': len(files_with_value)
                    })
                    print(f"  发现公共赋值 ({assignment_info['content_type']}, {len(value)} 字符): {value[:80].replace(chr(10), ' ')[:80]}...")
        
        # 按大小排序
        common_assignments.sort(key=lambda x: x['size'], reverse=True)
        return common_assignments
    
    def safe_remove_assignment(self, content: str, value: str) -> Tuple[str, int]:
        """安全地移除变量赋值"""
        original_size = len(content)
        
        # 尝试不同的替换模式
        patterns = [
            # 模式1: 完整的属性赋值（模板字符串）
            rf'(\w+)\s*:\s*`{re.escape(value)}`',
            # 模式2: 完整的属性赋值（双引号）
            rf'(\w+)\s*:\s*"{re.escape(value)}"',
            # 模式3: 完整的属性赋值（单引号）
            rf"(\w+)\s*:\s*'{re.escape(value)}'",
        ]
        
        for pattern in patterns:
            try:
                matches = list(re.finditer(pattern, content, re.DOTALL))
                if matches:
                    # 从后往前替换，避免位置偏移
                    for match in reversed(matches):
                        var_name = match.group(1)
                        # 替换为空字符串赋值
                        if '`' in match.group(0):
                            replacement = f'{var_name}: ``'
                        elif '"' in match.group(0):
                            replacement = f'{var_name}: ""'
                        else:
                            replacement = f'{var_name}: \'\''
                        
                        content = content[:match.start()] + replacement + content[match.end():]
                    break
            except re.error:
                continue
        
        removed_size = original_size - len(content)
        return content, removed_size
    
    def process_files(self, output_dir: str = "conservative_dedup") -> Dict:
        """处理所有文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 找出公共赋值
        common_assignments = self.find_common_assignments()
        
        # 按类型分组
        css_assignments = [a for a in common_assignments if a['content_type'] == 'css']
        lang_assignments = [a for a in common_assignments if a['content_type'] == 'language']
        large_assignments = [a for a in common_assignments if a['content_type'] == 'large_content']
        
        print(f"\n找到公共赋值:")
        print(f"  CSS: {len(css_assignments)} 个")
        print(f"  语言: {lang_assignments} 个")
        print(f"  大型内容: {len(large_assignments)} 个")
        
        results = {}
        
        print("\n" + "=" * 60)
        print("开始处理文件...")
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            
            # 移除CSS赋值
            for assignment in css_assignments:
                content_before = len(cleaned_content)
                cleaned_content, removed = self.safe_remove_assignment(cleaned_content, assignment['value'])
                if removed > 0:
                    total_removed += removed
                    print(f"  移除CSS赋值: {removed:,} 字符")
            
            # 移除语言赋值
            for assignment in lang_assignments:
                content_before = len(cleaned_content)
                cleaned_content, removed = self.safe_remove_assignment(cleaned_content, assignment['value'])
                if removed > 0:
                    total_removed += removed
                    print(f"  移除语言赋值: {removed:,} 字符")
            
            # 移除大型内容赋值（限制数量）
            for assignment in large_assignments[:10]:  # 只处理前10个最大的
                content_before = len(cleaned_content)
                cleaned_content, removed = self.safe_remove_assignment(cleaned_content, assignment['value'])
                if removed > 0:
                    total_removed += removed
                    print(f"  移除大型赋值: {removed:,} 字符")
            
            # 保存文件
            output_path = os.path.join(output_dir, f"clean_{filename}")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计结果
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'output_path': output_path
            }
            
            print(f"  原始: {original_size:,} 字符 ({original_size/1024:.1f} KB)")
            print(f"  清理后: {cleaned_size:,} 字符 ({cleaned_size/1024:.1f} KB)")
            print(f"  压缩比例: {compression_ratio:.1f}%")
        
        return results

def main():
    deduplicator = ConservativeDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("保守去重工具 - 只移除完整的变量赋值")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("需要至少2个文件进行比较")
        return
    
    # 处理文件
    results = deduplicator.process_files()
    
    # 保存结果
    with open('conservative_dedup_report.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("处理完成！总结:")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"总原始大小: {total_original:,} 字符 ({total_original/1024:.1f} KB)")
    print(f"总清理后大小: {total_cleaned:,} 字符 ({total_cleaned/1024:.1f} KB)")
    print(f"总体压缩比例: {overall_compression:.1f}%")
    print(f"\n清理后的文件保存在: conservative_dedup/ 目录")

if __name__ == "__main__":
    main()
