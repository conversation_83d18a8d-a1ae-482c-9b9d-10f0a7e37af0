#!/usr/bin/env python3
"""
改进的去重工具
基于第一次运行的结果，调整策略以获得更好的压缩效果
"""

import os
import json
from typing import List, Dict, Set, Tuple

class ImprovedDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"✓ 加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_large_duplicates(self) -> List[Dict]:
        """查找大型重复块 - 更激进的策略"""
        print(f"\n🔍 查找大型重复块...")
        
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        duplicates = []
        
        # 更大的块大小，更激进的查找
        sizes_to_check = [50000, 30000, 20000, 15000, 10000, 5000]
        
        base_path, base_content = files[0]
        base_name = file_names[0]
        
        for size in sizes_to_check:
            print(f"  检查大小: {size}")
            found_count = 0
            
            # 更小的步长，更仔细的查找
            step = max(500, size // 10)
            
            for start in range(0, len(base_content) - size + 1, step):
                if start % 100000 == 0:
                    print(f"    进度: {start/len(base_content)*100:.1f}%")
                
                block = base_content[start:start + size]
                
                # 更宽松的过滤条件
                if len(block.strip()) < size * 0.2:  # 降低空白字符阈值
                    continue
                
                # 检查在其他文件中的存在
                found_in = [base_name]
                for i, (_, other_content) in enumerate(files[1:], 1):
                    if block in other_content:
                        found_in.append(file_names[i])
                
                # 如果在至少2个文件中找到
                if len(found_in) >= 2:
                    duplicates.append({
                        'block': block,
                        'size': size,
                        'files': found_in,
                        'start_pos': start
                    })
                    found_count += 1
                    print(f"    ✓ 找到: {len(found_in)}个文件, {size}字符")
                    
                    # 每个大小级别找更多块
                    if found_count >= 5:
                        break
        
        # 去重处理
        filtered_duplicates = []
        for dup in sorted(duplicates, key=lambda x: x['size'], reverse=True):
            is_contained = any(
                dup['block'] in existing['block'] and dup['size'] < existing['size']
                for existing in filtered_duplicates
            )
            if not is_contained:
                # 移除被当前块包含的小块
                filtered_duplicates = [
                    existing for existing in filtered_duplicates
                    if existing['block'] not in dup['block']
                ]
                filtered_duplicates.append(dup)
        
        print(f"  📊 总共找到 {len(filtered_duplicates)} 个有效重复块")
        return filtered_duplicates
    
    def find_css_blocks_specifically(self) -> List[Dict]:
        """专门查找CSS块"""
        print(f"\n🎨 专门查找CSS块...")
        
        css_blocks = []
        
        # 查找CSS相关的大块内容
        for file_path, content in self.files_content.items():
            filename = os.path.basename(file_path)
            
            # 查找CSS变量赋值
            lines = content.split('\n')
            i = 0
            
            while i < len(lines):
                line = lines[i]
                
                # 查找CSS变量开始
                if ('IMMERSIVE_TRANSLATE_' in line and 
                    ('_CSS:' in line or '_INJECT:' in line) and 
                    ('`' in line or '"' in line)):
                    
                    css_start = i
                    css_lines = [line]
                    
                    # 查找CSS结束
                    quote_char = '`' if '`' in line else '"'
                    quote_count = line.count(quote_char)
                    
                    if quote_count >= 2:
                        # 单行CSS
                        start_idx = line.find(quote_char)
                        end_idx = line.rfind(quote_char)
                        css_content = line[start_idx:end_idx+1]
                    else:
                        # 多行CSS
                        i += 1
                        while i < len(lines):
                            css_lines.append(lines[i])
                            if quote_char in lines[i]:
                                break
                            i += 1
                        css_content = '\n'.join(css_lines)
                    
                    if len(css_content) > 1000:  # 只处理大的CSS块
                        css_blocks.append({
                            'content': css_content,
                            'size': len(css_content),
                            'file': filename,
                            'start_line': css_start
                        })
                        print(f"    ✓ 找到CSS块: {filename}, {len(css_content)}字符")
                
                i += 1
        
        # 查找公共CSS块
        common_css = []
        for css_block in css_blocks:
            content = css_block['content']
            found_in = [css_block['file']]
            
            # 检查在其他文件中是否存在
            for file_path, file_content in self.files_content.items():
                other_filename = os.path.basename(file_path)
                if other_filename != css_block['file'] and content in file_content:
                    found_in.append(other_filename)
            
            if len(found_in) >= 2:
                common_css.append({
                    'block': content,
                    'size': len(content),
                    'files': found_in
                })
        
        print(f"  📊 找到 {len(common_css)} 个公共CSS块")
        return common_css
    
    def apply_aggressive_removal(self, duplicates: List[Dict], css_blocks: List[Dict]) -> Dict:
        """更激进的移除策略"""
        print(f"\n🛠️  应用激进移除策略...")
        
        os.makedirs("improved_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n  处理: {filename}")
            
            cleaned_content = original_content
            total_removed = 0
            operations = []
            
            # 移除CSS块（优先级最高）
            for i, css_block in enumerate(css_blocks):
                if filename in css_block['files'] and css_block['block'] in cleaned_content:
                    old_size = len(cleaned_content)
                    # 完全移除CSS内容，只保留变量名
                    css_content = css_block['block']
                    
                    # 尝试找到变量名
                    lines_before = cleaned_content[:cleaned_content.find(css_content)].split('\n')
                    var_line = lines_before[-1] if lines_before else ""
                    
                    if ':' in var_line:
                        var_name = var_line.split(':')[0].strip()
                        replacement = f'{var_name}: "",'
                    else:
                        replacement = f'/* REMOVED_CSS_BLOCK_{i} */'
                    
                    cleaned_content = cleaned_content.replace(css_content, replacement, 1)
                    removed = old_size - len(cleaned_content)
                    total_removed += removed
                    operations.append(f"CSS块{i}: -{removed:,}字符")
            
            # 移除其他重复块
            for i, dup in enumerate(duplicates):
                if filename in dup['files'] and dup['block'] in cleaned_content:
                    old_size = len(cleaned_content)
                    replacement = f'/* REMOVED_DUPLICATE_{i} */'
                    cleaned_content = cleaned_content.replace(dup['block'], replacement, 1)
                    removed = old_size - len(cleaned_content)
                    total_removed += removed
                    operations.append(f"重复块{i}: -{removed:,}字符")
            
            # 保存文件
            output_path = f"improved_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行改进的去重"""
        print("🚀 改进的去重工具")
        print("=" * 50)
        
        # 步骤1: 查找大型重复块
        duplicates = self.find_large_duplicates()
        
        # 步骤2: 专门查找CSS块
        css_blocks = self.find_css_blocks_specifically()
        
        # 步骤3: 激进移除
        results = self.apply_aggressive_removal(duplicates, css_blocks)
        
        return results, duplicates, css_blocks

def main():
    deduplicator = ImprovedDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 改进的去重工具")
    print("💡 基于第一次运行结果的改进策略")
    print("=" * 50)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行去重
    results, duplicates, css_blocks = deduplicator.run()
    
    # 保存报告
    summary = {
        'duplicates_found': len(duplicates),
        'css_blocks_found': len(css_blocks),
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('improved_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("✅ 改进去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 总体效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    print(f"   找到重复块: {len(duplicates)} 个")
    print(f"   找到CSS块: {len(css_blocks)} 个")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: improved_dedup/clean_*.js")
    print(f"   详细报告: improved_dedup_summary.json")

if __name__ == "__main__":
    main()
