(()=>{var Mp=Object.defineProperty;var Iu=(e,t)=>{for(var a in t)Mp(e,a,{get:t[a],enumerable:!0})};var b={BUILD_TIME:"2025-07-25T09:38:29.000Z",VERSION:"1.19.6",PROD:"1",REDIRECT_URL:"https://dash.immersivetranslate.com/auth-done/",PROD_API:"1",BETA:"0",userscript_domains:'["google.com","translate.googleapis.com","api-edge.cognitive.microsofttranslator.com","edge.microsoft.com","transmart.qq.com","translate.yandex.net","tmt.tencentcloudapi.com","www2.deepl.com","w.deepl.com","immersive-translate/* MULTIPLE_REMOVED_BLOCKS */}

/* \u5DF2\u5B8C\u6210\u6309\u94AE\u6837\u5F0F */
.reward-task-item .reward-task-button.completed {
  background: #E8E8E8;
  color: #999;
  cursor: default;
}

/* \u5DF2\u5B8C\u6210\u4EFB\u52A1\u5206\u9694\u7B26\u6837\u5F0F */
.completed-tasks-divider {
  color: #ccc;
  font-size: 12px;
  padding: 8px 0;
  border-top: 1px solid #ECF0F7;
  position: relative;
  gap: 4px;
  display: flex;
  align-items: center;
}

.completed-tasks-divider.clickable {
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
}

.completed-tasks-divider.clickable:hover {
  color: #666;
}

.completed-tasks-text {
  font-weight: 500;
}

.completed-tasks-arrow {
  transition: transform 0.2s ease;
}

.completed-tasks-arrow.expanded {
  transform: rotate(-180deg);
}

.reward-center-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: ShadowRolling 1.5s linear infinite;
}

@keyframes ShadowRolling {
  0%, 100% {
    box-shadow: 0 0 rgba(255, 255, 255, 0);
  }
  12% {
    box-shadow: 100px 0 var(--loading-color);
  }
  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }
  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }
  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }
  62% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }
  75% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }
  87% {
    box-shadow: 130px 0 var(--loading-color);
  }
}

.reward-center-error {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 16px;
  word-break: break-all;
  color: #EA4C89;
}

.reward-center-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-center-footer-text {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  cursor: pointer;
}

/* \u4EFB\u52A1 Loading \u8F6C\u5708\u52A8\u753B */
.reward-task-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-task-loading::before {
  content: '';
  width: 8px;
  height: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: reward-task-spin 1s linear infinite;
}

@keyframes reward-task-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
`,OPTIONS_URL:"https://dash.immersivetranslate.com/",SHARE_DRAFT_URL:"https://immersivetranslate.com/preview",ASSETS_BASE_URL:"https://s.immersivetranslate.com/static/extension/",EBOOK_VIEWER_URL:"https://app.immersivetranslate.com/ebook/",EBOOK_BUILDER_URL:"https://app.immersivetranslate.com/ebook/make/",SUBTITLE_BUILDER_URL:"https://app.immersivetranslate.com/subtitle/",HTML_VIEWER_URL:"https://app.immersivetranslate.com/html/",MARKDOWN_VIEWER_URL:"https://app.immersivetranslate.com/markdown/",BABELDOC_URL:"https://app.immersivetranslate.com/babel-doc/",PDF_VIEWER_URL:"https://app.immersivetranslate.com/pdf/",PDF_PRO_URL:"https://app.immersivetranslate.com/pdf-pro/",TEXT_TRANSLATE_URL:"https://app.immersivetranslate.com/text/",TRANSLATE_FILE_URL:"https://app.immersivetranslate.com/"};var Pp=Object.create,Ui=Object.defineProperty,Fp=Object.getOwnPropertyDescriptor,Fu=Object.getOwnPropertyNames,Bp=Object.getPrototypeOf,Rp=Object.prototype.hasOwnProperty,_p=(e,t)=>function(){return t||(0,e[Fu(e)[0]])((t={exports:{}}).exports,t),t.exports},jp=(e,t)=>{for(var a in t)Ui(e,a,{get:t[a],enumerable:!0})},zi=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Fu(t))!Rp.call(e,r)&&r!==a&&Ui(e,r,{get:()=>t[r],enumerable:!(n=Fp(t,r))||n.enumerable});return e},Lp=(e,t,a)=>(zi(e,t,"default"),a&&zi(a,t,"default")),Bu=(e,t,a)=>(a=e!=null?Pp(Bp(e)):{},zi(t||!e||!e.__esModule?Ui(a,"default",{value:e,enumerable:!0}):a,e)),Ru=_p({"../esmd/npm/webextension-polyfill@0.10.0/node_modules/.pnpm/webextension-polyfill@0.10.0/node_modules/webextension-polyfill/dist/browser-polyfill.js"(e,t){(function(a,n){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],n);else if(typeof e<"u")n(t);else{var r={exports:{}};n(r),a.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:e,function(a){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let n="The message port closed before a response was received.",r=i=>{let o={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:/* REMOVED_COMMON_BLOCK_377 */equest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(o).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class s extends WeakMap{constructor(v,w=void 0){super(w),this.createItem=v}get(v){return this.has(v)||this.set(v,this.createItem(v)),super.get(v)}}let u=E=>E&&typeof E=="object"&&typeof E.then=="function",c=(E,v)=>(...w)=>{i.runtime.lastError?E.reject(new Error(i.runtime.lastError.message)):v.singleCallbackArg||w.length<=1&&v.singleCallbackArg!==!1?E.resolve(w[0]):E.resolve(w)},l=E=>E==1?"argument":"arguments",d=(E,v)=>function(y,...R){if(R.length<v.minArgs)throw new Error(`Expected at least ${v.minArgs} ${l(v.minArgs)} for ${E}(), got ${R.length}`);if(R.length>v.maxArgs)throw new Error(`Expected at most ${v.maxArgs} ${l(v.maxArgs)} for ${E}(), got ${R.length}`);return new Promise((F,C)=>{if(v.fallbackToNoCallback)try{y[E](...R,c({resolve:F,reject:C},v))}catch{y[E](...R),v.fallbackToNoCallback=!1,v.noCallback=!0,F()}else v.noCallback?(y[E](...R),F()):y[E](...R,c({resolve:F,reject:C},v))})},m=(E,v,w)=>new Proxy(v,{apply(y,R,F){return w.call(R,E,...F)}}),p=Function.call.bind(Object.prototype.hasOwnProperty),x=(E,v={},w={})=>{let y=Object.create(null),R={has(C,j){return j in E||j in y},get(C,j,Y){if(j in y)return y[j];if(!(j in E))return;let Z=E[j];if(typeof Z=="function")if(typeof v[j]=="function")Z=m(E,E[j],v[j]);else if(p(w,j)){let ve=d(j,w[j]);Z=m(E,E[j],ve)}else Z=Z.bind(E);else if(typeof Z=="object"&&Z!==null&&(p(v,j)||p(w,j)))Z=x(Z,v[j],w[j]);else if(p(w,"*"))Z=x(Z,v[j],w["*"]);else return Object.defineProperty(y,j,{configurable:!0,enumerable:!0,get(){return E[j]},set(ve){E[j]=ve}}),Z;return y[j]=Z,Z},set(C,j,Y,Z){return j in y?y[j]=Y:E[j]=Y,!0},defineProperty(C,j,Y){return Reflect.defineProperty(y,j,Y)},deleteProperty(C,j){return Reflect.deleteProperty(y,j)}},F=Object.create(E);return new Proxy(F,R)},A=E=>({addListener(v,w,...y){v.addListener(E.get(w),...y)},hasListener(v,w){return v.hasListener(E.get(w))},removeListener(v,w){v.removeListener(E.get(w))}}),S=new s(E=>typeof E!="function"?E:function(w){let y=x(w,{},{getContent:{minArgs:0,maxArgs:0}});E(y)}),g=new s(E=>typeof E!="function"?E:function(w,y,R){let F=!1,C,j=new Promise(Se=>{C=function(H){F=!0,Se(H)}}),Y;try{Y=E(w,y,C)}catch(Se){Y=Promise.reject(Se)}let Z=Y!==!0&&u(Y);if(Y!==!0&&!Z&&!F)return!1;let ve=Se=>{Se.then(H=>{R(H)},H=>{let oe;H&&(H instanceof Error||typeof H.message=="string")?oe=H.message:oe="An unexpected error occurred",R({__mozWebExtensionPolyfillReject__:!0,message:oe})}).catch(H=>{})};return ve(Z?Y:j),!0}),M=({reject:E,resolve:v},w)=>{i.runtime.lastError?i.runtime.lastError.message===n?v():E(new Error(i.runtime.lastError.message)):w&&w.__mozWebExtensionPolyfillReject__?E(new Error(w.message)):v(w)},h=(E,v,w,...y)=>{if(y.length<v.minArgs)throw new Error(`Expected at least ${v.minArgs} ${l(v.minArgs)} for ${E}(), got ${y.length}`);if(y.length>v.maxArgs)throw new Error(`Expected at most ${v.maxArgs} ${l(v.maxArgs)} for ${E}(), got ${y.length}`);return new Promise((R,F)=>{let C=M.bind(null,{resolve:R,reject:F});y.push(C),w.sendMessage(...y)})},k={devtools:{network:{onRequestFinished:A(S)}},runtime:{onMessage:A(g),onMessageExternal:A(g),sendMessage:h.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:h.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},L={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return o.privacy={network:{"*":L},services:{"*":L},websites:{"*":L}},x(i,k,o)};a.exports=r(chrome)}else a.exports=globalThis.browser})}}),_u={};jp(_u,{default:()=>Dr});var Np=Bu(Ru());Lp(_u,Bu(Ru()));var{default:Pu,...Op}=Np,Dr=Pu!==void 0?Pu:Op;globalThis.immersiveTranslateBrowserAPI=Dr;function ke(){return typeof process>"u"&&typeof Deno<"u"?Deno.env.toObject():b}var re=ke();function zp(){return typeof location>"u"?!1:location.href.includes("side-panel")&&location.href.includes("extension://")}function pe(e,t){return!t&&zp()?!0:e&&globalThis?.document?.querySelector("meta[name=immersive-translate-options]")?!!globalThis.document?.getElementById("immersive-translate-manifest")?.value?.includes("_isUserscript"):re.IMMERSIVE_TRANSLATE_USERSCRIPT==="1"}function Wt(){return re.PROD==="1"}function Cr(){return re.PROD_API==="1"}function pt(){if(re.IMMERSIVE_TRANSLATE_SAFARI==="1")return!0;if(typeof globalThis.immersiveTranslateBrowserAPI<"u"&&globalThis.immersiveTranslateBrowserAPI.runtime&&globalThis.immersiveTranslateBrowserAPI.runtime.getManifest){let t=globalThis.immersiveTranslateBrowserAPI.runtime.getManifest();return!!(t&&t._isSafari)}else return!1}function Mr(){return typeof Deno<"u"}var nx=ke().PROD==="1",rx=ke().PROD!=="1";function ka(){return re.IMMERSIVE_TRANSLATE_JSSDK==="1"}var qi=/iPhone/i,ju=/iPod/i,Lu=/iPad/i,Nu=/\biOS-universal(?:.+)Mac\b/i,Gi=/\bAndroid(?:.+)Mobile\b/i,Ou=/Android/i,Za=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,Ir=/Silk/i,Qt=/Windows Phone/i,zu=/\bWindows(?:.+)ARM\b/i,Uu=/BlackBerry/i,qu=/BB10/i,Gu=/Opera Mini/i,Hu=/\b(CriOS|Chrome)(?:.+)Mobile/i,Ku=/Mobile(?:.+)Firefox\b/i,Wu=e=>typeof e<"u"&&e.platform==="MacIntel"&&typeof e.maxTouchPoints=="number"&&e.maxTouchPoints>1&&typeof globalThis.MSStream>"u";function Up(e){return t=>t.test(e)}function Ta(e){let t={userAgent:"",platform:"",maxTouchPoints:0};!e&&typeof navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let a=t.userAgent,n=a.split("[FBAN");typeof n[1]<"u"&&(a=n[0]),n=a.split("Twitter"),typeof n[1]<"u"&&(a=n[0]);let r=Up(a),i={apple:{phone:r(qi)&&!r(Qt),ipod:r(ju),tablet:!r(qi)&&(r(Lu)||Wu(t))&&!r(Qt),universal:r(Nu),device:(r(qi)||r(ju)||r(Lu)||r(Nu)||Wu(t))&&!r(Qt)},amazon:{phone:r(Za),tablet:!r(Za)&&r(Ir),device:r(Za)||r(Ir)},android:{phone:!r(Qt)&&r(Za)||!r(Qt)&&r(Gi),tablet:!r(Qt)&&!r(Za)&&!r(Gi)&&(r(Ir)||r(Ou)),device:!r(Qt)&&(r(Za)||r(Ir)||r(Gi)||r(Ou))||r(/\bokhttp\b/i)},windows:{phone:r(Qt),tablet:r(zu),device:r(Qt)||r(zu)},other:{blackberry:r(Uu),blackberry10:r(qu),opera:r(Gu),firefox:r(Ku),chrome:r(Hu),device:r(Uu)||r(qu)||r(Gu)||r(Ku)||r(Hu)},any:!1,phone:!1,tablet:!1};return i.any=i.apple.device||i.android.device||i.windows.device||i.other.device,i.phone=i.apple.phone||i.android.phone||i.windows.phone,i.tablet=i.apple.tablet||i.android.tablet||i.windows.tablet,i}var Hi="DENO",Pr="CHROME",Fr="FIREFOX";function Qu(e){let t;try{let a=navigator?.userAgent||"";/firefox/i.test(a)||typeof InstallTrigger<"u"?t=Fr:/deno/i.test(a)?t=Hi:/chrome/i.test(a)&&(t=Pr)}catch{}return e===Pr&&t===Pr||e===Fr&&t===Fr||e===Hi&&t===Hi}function Xa(){return Qu(Pr)}function Vu(){return typeof Deno<"u"}function Rn(){return ke().IMMERSIVE_TRANSLATE_FIREFOX==="1"?!0:Qu(Fr)}function Ki(){return pe(!1,!0)?"monkey":Xa()?"chrome":Rn()?"firefox":pt()?"safari":null}var Yu={addListener:()=>{},removeListener:()=>{},hasListener:()=>{}},Ju={permissions:{contains:()=>{},request:()=>{}},runtime:{onMessage:Yu,openOptionsPage:()=>{},lastError:{message:""}},storage:{sync:{get:()=>{},set:()=>{}},local:{map:new Map,async get(e){return new Promise(t=>{setTimeout(()=>{let a=this.map.get(e);t({[e]:a})},100)})},async set(e,t){return new Promise((a,n)=>{setTimeout(()=>{this.map.set(e,t),a("")},100)})}}},tabs:{onUpdated:Yu,query:()=>{},sendMessage:()=>{}}};var ie;Vu()?ie=Ju:ie=globalThis.immersiveTranslateBrowserAPI;var{Deno:Zu}=globalThis,qp=typeof Zu?.noColor=="boolean"?Zu.noColor:!0,Gp=!qp;function Br(e,t){return{open:`\x1B[${e.join(";")}m`,close:`\x1B[${t}m`,regexp:new RegExp(`\\x1b\\[${t}m`,"g")}}function Rr(e,t){return Gp?`${t.open}${e.replace(t.regexp,t.open)}${t.close}`:e}function Wi(e){return Rr(e,Br([2],22))}function _r(e){return Rr(e,Br([31],39))}function Qi(e){return Rr(e,Br([32],39))}function Vi(e){return Rr(e,Br([33],39))}var wx=new RegExp(["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|"),"g");var $u=["translationService","inputTranslationService","mouseHoverTranslationService","clientImageTranslationService","subtitleTranslateService"];var z="immersiveTranslate",Da="Immersive Translate",Ae="immersive-translate",rl="imt",Hp="immersivetranslate";var Le="immersivetranslate.com",Kp=`https://config.${Le}/`,Tx=`https://app.${Le}/`,P=Wt()||Cr()?`https://${Le}/`:`https://test.${Le}/`,jr=`https://dash.${Le}/`,$a=Wt()||Cr()?`https://api2.${Le}/`:`https://test-api2.${Le}/`,Yi=Wt()||Cr()?`https://ai.${Le}/`:`https://test-ai.${Le}/`,Dx=`https://assets.${Hp}.cn/`,il=P+"accounts/login?from=plugin",Ji=P+"profile/",Ne=P+"auth/pricing/",Vt=P+"pricing/";pt()&&(Ne=P+"accounts/safari-iap/",Vt=P+"accounts/safari-iap/");function ol(e){e&&(P=`https://test.${Le}/`,$a=`https://test-api2.${Le}/`,il=P+"accounts/login?from=plugin",Ji=P+"profile/",Ne=P+"auth/pricing/",Vt=P+"pricing/",pt()&&(Ne=P+"accounts/safari-iap/",Vt=P+"accounts/safari-iap/"))}var Cx=Wt()?`https://onboarding.${Le}/`:`https://test-onboarding.${Le}/`,sl=`https://github.com/${Ae}/${Ae}/`,Mx=`https://s.${Le}/`;var Ix=z+"DeeplGlobalState",Px=z+"BingGlobalState",Fx=z+"YandexGlobalState",Bx=z+"BaiduQianfanGlobalConfigStorageKey",Rx=z+"SiliconCloudGlobalConfigStorageKey",_x=z+"ZhipuGlobalConfigStorageKey";var jx=z+"GoogleAccessToken",Lx=z+"AuthFlow",Nx=Ae+"-config-latest.json",Ox=z+"AuthState",zx=z+"IframeMessage",Ux=z+"WaitForRateLimit",qx=z+"DocumentMessageAsk",Lr=z+"DocumentMessageTellThirdParty",Gx=z+"showError",ul=z+"showModal",Hx=z+"showToast",Kx=z+"tokenUsageChange",ll=z+"DocumentMessageThirdPartyTell",Wx=z+"DocumentMessageEventUpload",Qx=z+"DocumentMessageTypeStopJsSDK",Vx=z+"DocumentMessageHandler",Yx=z+"DocumentSetFloatBallActive",cl=`${z}Share`,Jx=`${z}ShowFloatBallGuide`,Zx=`${z}ShowPopupModalGuide`,Xx=z+"DocumentMessageTempEnableSubtitleChanged",$x=z+"DocumentMessageUpdateQuickButtonAiSubtitle",dl=`${z}ToggleMouseHoverTranslateDirectly`,ew=`${z}ReqDraft`,tw=`${z}ResDraft`,Wp=`${z}Container`,Qp=`${z}SpecifiedContainer`,Zi="buildinConfig",_n="localConfig";var ml="translateMangaMenuId";var Vp=`${z}PageTranslatedStatus`,Yp=`${z}MangaTranslatedStatus`,aw=`${z}PageUrlChanged`,nw=`${z}ReceiveCommand`,rw=z+"LastUseMouseHoverTime",iw=z+"LastUseInputTime",gt=z+"LastUseManualTranslatePageTime",Jp=`${z}PopupReceiveMessage`,ow=z+"DocumentMessageEventTogglePopup",sw=`${Kp}default_config.json`,uw=`${z}Mark`,Zp=`${z}Root`,lw=`${z}Walked`,cw=`data-${Ae}-walked`,dw=`${z}Paragraph`,mw=`data-${Ae}-paragraph`,pw=`data-${Ae}-translation-element-mark`,gw=`${z}TranslationElementMark`,hw=`${z}TranslatedMark`,Xp=`${Ae}-input-injected-css`,$p=`${z}LoadingId`,eg=`data-${Ae}-loading-id`,fw=`${z}ErrorId`,bw=`data-${Ae}-error-id`,tg=`${z}AtomicBlockMark`,ag=`${z}ExcludeMark`,yw=`data-${Ae}-exclude-mark`,ng=`${z}StayOriginalMark`,vw=`${z}PreWhitespaceMark`,rg=`${z}InlineMark`,ig=`${z}BlockMark`,xw=`${z}Left`,ww=`${z}Right`,Aw=`${z}Width`,Ew=`${z}Height`,Sw=`${z}Top`,kw=`${z}FontSize`;var Tw=`${z}GlobalStyleMark`,Xi=["@","#"];var og=`${Ae}-target-wrapper`,Dw=`${Ae}-pdf-target-container`,Cw=`${Ae}-target-inner`,Mw=`${Ae}-source-wrapper`,Iw=`${Ae}-target-translation-block-wrapper`,Pw=`${Ae}-root-translation-theme`,Fw=`${z}RootTranslationTheme`,Bw=`${Ae}-target-translation-vertical-block-wrapper`,Rw=`${Ae}-target-translation-pdf-block-wrapper`,_w=`${Ae}-target-translation-pre-whitespace`,jw=`${Ae}-target-translation-inline-wrapper`;var pl=["https://immersive-translate.owenyoung.com/options/","https://immersive-translate.owenyoung.com/auth-done/",jr,jr+"auth-done/","http://localhost:8000/dist/userscript/options/","http://localhost:8000/auth-done/","http://************:8000/dist/userscript/options/","http://*************:8000/dist/userscript/options/","http://************:8000/dist/userscript/options/","https://www.deepl.com/translator","translate.google.com","http://localhost:8000/options/","http://************:8000/options/","http://*************:8000/options/","http://************:8000/options/"],Yt="zh-CN",Lw=P+"docs/communities/",Nw=sl+"issues/1809",Ow=sl+"issues/1179",zw={type:z+"ChildFrameToRootFrameIdentifier"};var Nr=Wt()?jr+"#general":"http://localhost:8000/dist/userscript/options/#general";var Xe="user_info",gl=jr+"#general",Uw=P+"accounts/login?from=plugin&return_url="+encodeURIComponent(gl),qw=il+"&utm_source=extension&utm_medium=extension&utm_campaign=error_modal",sg=P+"download/",ug=P+"topup?type=open_ai&",lg=P+"topup?type=deepl&",hl=P+"topup?type=comics&",Gw=Vt+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_more",Hw=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_guide",Kw=sg+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",Ww=Vt+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_footer",Qw=Vt+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",fl=Vt+"?utm_source=extension&utm_medium=extension&utm_campaign=max_",Vw=Vt+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",Yw=Ji+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Jw=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=subtitle_download",Zw=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal_ai_subtitle",Xw=ug+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",$w=lg+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",$i=P+"topup?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",eA=Vt+"?utm_source=extension&utm_medium=extension&utm_campaign=option_sync_config",bl=Ji+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal&upgradeFromTrial=true",tA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_intro",aA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=image_intro",nA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=image_client",rA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=yt_ai_asr",iA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=",oA=P+"accounts/usage",sA=P+"docs/usage/",uA=P+"docs/communities/",jn=ke().TRANSLATE_FILE_URL,lA=jn+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",cA=jn+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",dA=`${jn}download-subtitle/`,mA=`${jn}pdf-pro/`,pA=`${jn}text/`;var gA=P+"docs/usage/";var cg="G-BHWL0KMJB8",dg="7pr-olTJR6GKAjIW48UD0Q",el="G-MKMD9LWFTR",tl="sitc4WmvShWYwfU0dANM3Q",al="G-V5H2F5MJFJ",nl="UBjpGOLISEaY5LVXNj3WvQ";function Or(){return ka()?[`https://www.google-analytics.com/mp/collect?measurement_id=${cg}&api_secret=${dg}`]:Wt?[`https://www.google-analytics.com/mp/collect?measurement_id=${el}&api_secret=${tl}`,`https://www.google-analytics.com/mp/collect?measurement_id=${al}&api_secret=${nl}`]:[`https://www.google-analytics.com/debug/mp/collect?measurement_id=${el}&api_secret=${tl}`,`https://www.google-analytics.com/debug/mp/collect?measurement_id=${al}&api_secret=${nl}`]}var Ca=`https://analytics.${Le}/collect`,hA=`https://analytics.${Le}/internal`,fA=`${P}activities/components/image-pro`;var yl="LdgzvqcdlDvNLdxrJVtZqxMTKaIgExlL",vl="0VmM83i2D1ICuYBf",bA=50*1e4,yA=`[${rl}-ctx-divider]`,mg=`${rl}_context_preview`;var eo="fullLocalUserConfig";var xl="https://<EMAIL>/4506813369548800",vA=`${z}_selection_update_params`,xA=`data-${Ae}-subtitle-type`,wA=`data-${Ae}-ai-subtitle-url`,AA=`data-${Ae}-has-subtitle`;var to=["translationService","inputTranslationService","mouseHoverTranslationService","subtitleTranslateService","clientImageTranslationService"];var EA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=freeImageError";var pg=Ae+"-large-cache";var SA=Ne+"?utm_source=extension&utm_medium=extension&utm_campaign=live_subtitle_btn";var en=console,ao=class{#e=performance.now();reset(){this.#e=performance.now()}stop(t){let a=performance.now(),n=Math.round(a-this.#e),r=Qi;n>1e4?r=_r:n>1e3&&(r=Vi),en.debug(Wi(Da+" TIMING:"),t,"in",r(n+"ms")),this.#e=a}},Ln=class{#e=1;get level(){return this.#e}setLevel(t){switch(t){case"debug":this.#e=0;break;case"info":this.#e=1;break;case"warn":this.#e=2;break;case"error":this.#e=3;break;case"fatal":this.#e=4;break}}debug(...t){this.#e<=0&&en.log(Wi(Da+" DEBUG:"),...t)}v(...t){this.#e<=0}info(...t){this.#e<=1&&en.log(Qi(Da+" INFO:"),...t)}l(...t){this.#e<=1}warn(...t){this.#e<=2&&en.warn(Vi(Da+" WARN:"),...t)}error(...t){this.#e<=3&&en.error(_r(Da+" ERROR:"),...t)}fatal(...t){this.#e<=4&&en.error(_r(Da+" FATAL:"),...t)}timing(){return this.level===0?new ao:{reset:()=>{},stop:()=>{}}}},X=new Ln;var zr=/* REMOVED_PATTERN_71 */;function Ur(e){if(typeof e!="string")return"auto";let t=e.toLowerCase();if(t==="und")return"auto";if(t==="zh"||t.startsWith("zh-hans"))return"zh-CN";if(t.startsWith("zh-hant")||t.startsWith("zh-hk")||t.startsWith("zh-tw")||t.startsWith("yue"))return"zh-TW";if(t.startsWith("zh-"))return"zh-CN";if(t==="iw")return"he";if(t==="jv")return"jw";let a=zr.map(r=>r.toLowerCase()),n=a.indexOf(t);if(n===-1)if(t.indexOf("-")>=0){t=t.split("-")[0];let r=a.indexOf(t);return r===-1?"auto":zr[r]}else return"auto";else return zr[n]}var Ma=z+"CacheKey_";function wl(e,t){let a=Ma+e;return ie.storage.local.get(a).then(n=>n[a]===void 0?t:n[a])}function gg(){let e=ie.storage.local.refresh;e&&e()}function hg(e,t){let a=Ma+e;return ie.storage.local.get(a).then(n=>{if(n[a]===void 0)return t;let{value:r,expired:i}=n[a];return i&&i<Date.now()?t:r})}function fg(e,t,a){let n=Ma+e,r=Date.now()+a;return ie.storage.local.set({[n]:{value:t,expired:r}})}function Al(e,t){let a=Ma+e;return ie.storage.local.set({[a]:t})}function bg(e){let t=Ma+e;return ie.storage.local.remove(t)}async function yg(){let e=await ie.storage.local.get(null);if(e){let a=Object.keys(e).filter(n=>n.startsWith(Ma)).filter(n=>n!==Ma+Xe);if(a.length>0)return ie.storage.local.remove(a)}}var st={get:wl,set:Al,getExpired:hg,setExpired:fg,remove:bg,clear:yg,refresh:gg};function Jt(e){return!!(e&&e.subscription&&e.subscription.subscriptionStatus==="active")}function El(e){return e?.subscription?.memberShip==="max"}function Sl(e){if(e){let t=new Date(e.createTime),a=vg(t),n="free",r="unknown";return e.subscription&&e.subscription.subscriptionStatus==="active"&&(n=e.subscription.subscriptionType),e.subscription&&e.subscription.subscriptionId&&(e.subscription.subscriptionId.startsWith("sub_")?r="stripe":r="admin"),e.subscription?.isTrial&&(n="trial"),{user_type:n,user_register_day:a,subscription_from:r}}else return null}function vg(e){try{let a=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];a.endsWith(",")&&(a=a.slice(0,-1));let[n,r,i]=a.split("/");return a=`${i}-${n}-${r}`,a}catch{return"unknown"}}var kl=z+"SyncStoreKey_";function ut(e,t){let a=kl+e;return ie.storage.sync.get(a).then(n=>n[a]===void 0?t:n[a])}function Ke(e,t){let a=kl+e;return ie.storage.sync.set({[a]:t})}var Tl=z+"StoreKey_";function Ia(e,t){let a=Tl+e;return ie.storage.local.get(a).then(n=>n[a]===void 0?t:n[a])}function Dl(e,t){let a=Tl+e;return ie.storage.local.set({[a]:t})}var eE=Cl(2),xg=Cl(3);function Cl(e){if(typeof e!="number"||Number.isNaN(e)||e<1||e===Number.POSITIVE_INFINITY)throw new Error("`"+e+"` is not a valid argument for `n-gram`");return t;function t(a){let n=[];if(a==null)return n;let r=typeof a.slice=="function"?a:String(a),i=r.length-e+1;if(i<1)return n;for(;i--;)n[i]=r.slice(i,i+e);return n}}var uE={}.hasOwnProperty;var no={Latin:{spa:" de|de |os | la| a |la | y |\xF3n |i\xF3n|es |ere|rec|ien|o a|der|ci\xF3|a p|cho|ech|en |ent|a l|aci|e d|el |ona|na | co|as |al |da | to|ene|e l| en| el| pe|nte|tod|ho | su|per|ad | ti|a t|ers|tie| se|rso| pr|son|e s|te |oda|cia|n d|o d|dad|ida| in|ne | es|ion|cio|s d|con|est|a e| po|men| li|res|nci|su |to |tra| re|n e| lo|tad| na|los|a s| o |ia |que| pa|r\xE1 |pro| un|s y|ual|s e|lib|nac|do |ra |er |nal|ue | qu|e e|a d|ar |nes|ica|a c|sta|ser|/* MULTIPLE_REMOVED_BLOCKS */u0935\u0924\u0902|\u094D\u0935\u0924|\u093E \u092E|\u0935 \u0915|\u0947 \u0935|\u093E\u0925 |\u0938\u093E\u0925| \u0926\u094B|\u0939\u094B\u092C| \u092A\u093E|\u094B \u0915|\u0947 \u092C|\u094B\u0917 | \u0909\u092A|\u0938\u094D\u0924|\u092A\u0930\u093F|\u0928 \u092A|\u0947 \u0924|\u094D\u0924\u0930|\u0932\u0947\u0932|\u0947 \u0913|\u091A\u093E\u0939| \u091A\u093E|\u092F \u0915|\u0935\u093E |\u0947\u0936 |\u092F \u0938|\u0928 \u0939|\u0937\u0923 |\u093E \u092C|\u0964 \u0924|\u090F\u0915 |\u090F\u0932 |\u0940\u092F |\u0915\u0947\u0915|\u0947 \u0939|\u0930 \u0906|\u093F \u0915|\u0938\u094D\u0925|\u091C\u093F\u0915|\u093E\u091C\u093F|\u093E\u092E\u093E|\u0930\u0940\u092F|\u094D\u0930\u0940|\u0924\u093F\u0915|\u093E\u0924\u093F| \u092C\u093F|\u091A\u093E\u0930|\u0947 \u0906|\u093E\u0938 | \u0909\u091A|\u093E \u0924|\u092F\u0915\u094D|\u094D\u092F\u0915|\u093F\u0932 |\u092E\u092F |\u0938\u092E\u092F|\u0936\u093E\u0926|\u092A\u092F\u094B|\u0909\u092A\u092F|\u0947 \u0916|\u0930\u093F\u0935| \u092A\u0942|\u0947 \u0932|\u0947 \u091A|\u094C\u0928\u094B|\u0915\u094C\u0928| \u0915\u094C|\u0902 \u0915|\u0938\u0902\u0917|\u0928 \u0926|\u0902 \u0938|\u0923 \u092A|\u094D\u0937\u0923|\u0930 \u0928|\u0947 \u0928|\u094B \u092D|\u0915\u0930\u094B|\u093E \u0914|\u0930\u0924\u093E|\u093E\u0935 |\u092D\u093E\u0935|\u0915 \u0914|\u0930\u094D\u092E|\u094B\u0938\u0930|\u0926\u094B\u0938|\u0923 \u0915|\u0947 \u092A|\u0928 \u0914|\u092C \u0939|\u093F\u0915\u094D|\u0936\u093F\u0915| \u0936\u093F|\u093E\u092C\u0947|\u0928\u093F\u092F|\u091A\u093F\u0924|\u0909\u091A\u093F|\u093F\u0924\u094D|\u0917 \u0915|\u0947\u0964 |\u0924 \u0938|\u0940 \u0936|\u0902 \u0936|\u090F\u0915\u0930|\u0964 \u090F|\u0924\u0928 | \u0913 |\u0930\u0940 |\u094D\u0930 |\u091C\u0947 |\u0915 \u0915| \u0938\u0940|\u0938\u0928 |\u093F\u0935\u093E| \u0905\u0928|\u0942\u0930\u093E| \u092C\u091A|\u090F\u0964 | \u092C\u0947|\u0924 \u0939| \u0924\u0915| \u092E\u093F|\u0927\u093E\u0930|\u0925\u0935\u093E|\u0905\u0925\u0935| \u0905\u0925|\u093F\u0932\u093E|\u094D\u0935\u093E|\u093F \u092E| \u0906\u0926|\u0928\u0947 |\u0915\u090F\u0932| \u0915\u090F|\u094D\u092F\u093E"}};var Ml={}.hasOwnProperty,Nn,Il={};for(Nn in no)if(Ml.call(no,Nn)){let e=no[Nn],t;Il[Nn]={};for(t in e)if(Ml.call(e,t)){let a=e[t].split("|"),n={},r=a.length;for(;r--;)n[a[r]]=r;Il[Nn][t]=n}}var Pl=[["afr","af"],["amh","am"],["arb","ar"],["azj","az"],["bel","be"],["bul","bg"],["ben","bn"],["bos","bs"],["cat","ca"],["ceb","ceb"],["ces","cs"],["dan","da"],["deu","de"],["ell","el"],["eng","en"],["epo","eo"],["spa","es"],["est","et"],["fas","fa"],["fin","fi"],["fra","fr"],["gax","ga"],["glg","gl"],["guj","gu"],["hau","ha"],["heb","he"],["hin","hi"],["hrv","hr"],["hun","hu"],["hye","hy"],["ind","id"],["ibo","ig"],["ita","it"],["jpn","ja"],["jav","jw"],["kat","ka"],["kaz","kk"],["khm","km"],["kan","kn"],["kor","ko"],["ckb","ku"],["lao","lo"],["lit","lt"],["lav","lv"],["min","mi"],["mkd","mk"],["mal","ml"],["mar","mr"],["mya","my"],["nep","ne"],["nld","nl"],["nob","no"],["nya","ny"],["pan","pa"],["pol","pl"],["pbu","ps"],["por","pt"],["ron","ro"],["rus","ru"],["sin","si"],["slk","sk"],["slv","sl"],["sna","sn"],["som","so"],["als","sq"],["srp","sr"],["sun","su"],["swe","sv"],["swh","sw"],["tam","ta"],["tel","te"],["tgk","tg"],["tha","th"],["toi","to"],["tur","tr"],["ukr","uk"],["urd","ur"],["uzn","uz"],["vie","vi"],["xho","xh"],["ydd","yi"],["yor","yo"],["cmn","zh-CN"],["zul","zu"]],AE=new Map(Pl),EE=new Map(Pl.map(([e,t])=>[t,e]));var Fl=[{type:"select",name:"codename",labelKey:"field.translationEngine",default:"youdao",required:!1,options:[{label:"translationServices.google",value:"google"},{label:"translationServices.deepl",value:"deepl"},{label:"translationServices.youdao",value:"youdao"},{label:"translationServices.tencent",value:"tencent"},{label:"translationServices.aliyun",value:"aliyun"},{label:"translationServices.baidu",value:"baidu"},{label:"translationServices.caiyun",value:"caiyun"},{label:"translationServices.wechat",value:"wechat"},{label:"translationServices.ibm",value:"ibm"},{label:"translationServices.azure",value:"azure"},{label:"translationServices.aws",value:"aws"}]}],tn={ai:!0,name:"Custom AI",homepage:"https://openai.com/api/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,direction:"column",type:"text"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/ai/",default:"https://api.openai.com/v1/chat/completions"},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description/* REMOVED_COMMON_BLOCK_2451 */type:"number",default:"0",optional:!0}]},an={bing:{name:"\u5FAE\u8F6F\u7FFB\u8BD1",homepage:"https://www.bing.com/translator"},google:{name:"Google",homepage:"https://translate.google.com/"},zhipu:{ai:!0,name:"zhipu",homepage:"https://open.bigmodel.cn/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"glm-4-flash (free)",value:"glm-4-flash"},{label:"glm-4-air",value:"glm-4-air"},{label:"glm-4-airx",value:"glm-4-airx"},{label:"glm-4",value:"glm-4"},{label:"glm-4-plus",value:"glm-4-plus"},{label:"glm-4-0520",value:"glm-4-0520"},{label:"glm-4-long",value:"glm-4-long"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column",descriptionKey:"description.zhipuCustomAPIKey",descriptionLink1:"https://open.bigmodel.cn/",descriptionLink2:P+"zh-Hans/docs/services/zhipu/"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",placeholder:"ep-20240512123030-kv128",descriptionKey:"description.generalLimitPerSecond",type:"number",default:10},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerReques/* REMOVED_COMMON_BLOCK_2465 */re",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},deepl:{name:"DeepL",homepage:"https://www.deepl.com/translator",docUrl:P+"docs/services/deepL/",link1:"https://www.deepl.com/blog/next-gen-language-model",link2:P+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_deepl",providers:[{name:"pro",nameKey:"deepLProName",descriptionKey:"deepLProDescription",descriptionKeyForNormal:"deeplProDescriptionForNormal",descriptionLink1:Ne+"?utm_campaign=services"},{name:"custom",nameKey:"deepLCustomName",descriptionKey:"deepLCustomDescription",descriptionLink1:"https://www.deepl.com/translator",descriptionLink2:P+"docs/services/deepL/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"authKey",providers:["custom"],label:"Auth Key",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!1}]},deepseek:{ai:!0,name:"deepseek",homepage:"https://www.deepseek.com/",docUrl:P+"docs/services/deepseek/",link1:"",link2:"",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"serviceProDescriptionForNormal",descriptionLink1:Ne+"?utm_campaign=services"},{name:"custom",nameKey:"openaiCustomName",descriptionKey:"deepseekCustomDescription",descriptionLink1:"https://www.deepseek.com/",descriptionLink2:P+"docs/services/deepseek/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column",providers:["custom"]},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!1,type:"model-select",providers:["custom"],default:"deepseek-chat",options:[{label:"deepseek-chat",value:"deepseek-chat"},{label:"deepseek-reasoner",value:"deepseek-reasoner"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:1,providers:["custom"]},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.deepseek.com/chat/completions",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/deepseek/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:`Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}`,optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},openai:{ai:!0,name:"Open AI",homepage:"https://openai.com/api/",docUrl:P+"docs/services/openai/",link1:"https://readit.plus/a/GKQas/understanding-chatgpt",link2:P+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_openai",providers:[{name:"pro",nameKey:"openaiProName",descriptionKey:"openaiProDescription",descriptionKeyForNormal:"openaiProDescriptionForNormal",descriptionLink1:Ne+"?utm_campaign=services"},{name:"custom",nameKey:"openaiCustomName",descriptionKey:"openaiCustomDescription",descriptionLink1:P+"docs/services/openai/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlacehold/* REMOVED_COMMON_BLOCK_2468 */label:"gpt-4",value:"gpt-4"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.limitPerSecond",descriptionLink1:P+"docs/services/openai/",type:"number",default:10,providers:["custom"]},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.openai.com/v1/chat/completions",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/openai/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:`Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}`,optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},gemini:{ai:!0,name:"Gemini",homepage:"https://ai.google.dev/aistudio/",docUrl:P+"docs/services/gemini/",link2:P+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_gemini",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"serviceProDescriptionForNormal",descriptionLink1:Ne+"?utm_campaign=services"},{name:"custom",nameKey:"serviceCustomName",descriptionKey:"geminiCustomDescription",descriptionLink1:"https://ai.google.dev/aistudio/",descriptionLink2:P+"docs/services/gemini/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",providers:["custom"],required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!1,type:"model-select",providers:["custom"],options:[{label:"gemini-1.5-pro-latest",value:"gemini-1.5-pro-latest"},{label:"gemini-1.5-flash",value:"gemini-1.5-flash"},{label:"gemini-2.0-flash",value:"gemini-2.0-flash"},{label:"gemini-2.0-flash-lite",value:"gemini-2.0-flash-lite"},{label:"gemini-2.0-flash-lite-preview-02-05",value:"gemini-2.0-flash-lite-preview-02-05"},{label:"gemini-2.5-pro-preview-05-06",value:"gemini-2.5-pro-preview-05-06"},{label:"gemini-2.5-flash-preview-05-20",value:"gemini-2.5-flash-preview-05-20"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:P+"docs/services/openai/",type:"number",providers:["custom"],default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:3,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={key}",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/gemini/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:/* REMOVED_COMMON_BLOCK_2469 */hropic.com/",docUrl:P+"docs/services/claude/",link2:P+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_claude",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"serviceProDescriptionForNormal",descriptionLink1:Ne+"?utm_campaign=services"},{name:"custom",nameKey:"serviceCustomName",descriptionKey:"claudeCustomDescription",descriptionLink1:"https://www.anthropic.com/",descriptionLink2:P+"docs/services/claude/"}],allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",providers:["custom"],required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!1,type:"model-select",default:"claude-3-haiku-20240307",providers:["custom"],options:[{label:"claude-3-haiku-20240307",value:"claude-3-haiku-20240307"},{label:"claude-3-sonnet-20240229",value:"claude-3-sonnet-20240229"},{label:"claude-3-5-sonnet-20240620",value:"claude-3-5-sonnet-20240620"},{label:"claude-3-opus-20240229",value:"claude-3-opus-20240229"},{label:"claude-2.1",value:"claude-2.1"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",providers:["custom"],descriptionKey:"description.generalLimitPerSecond",descriptionLink1:P+"docs/services/openai/",type:"number",default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:10,optional:!0},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.anthropic.com/v1/messages",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/claude/",optional:!0,providers:["custom"]},{name:"enableAIContext",labelKey:"field.enableAIContext",descriptionKey:"description.enableAIContext",required:!1,type:"boolean",default:!1,optional:!1},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:"",optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},"zhipu-pro":{ai:!0,name:"Gemini",homepage:"https://open.bigmodel.cn/",docUrl:"",link2:P+"pricing/?utm_source=extension&utm_medium=settings&utm_campaign=service_zhipu_pro",providers:[{name:"pro",nameKey:"serviceProName",descriptionKey:"serviceProDescription",descriptionKeyForNormal:"translationServices.proOnly",descriptionLink1:Ne+"?utm_campaign=services"}],allProps:[{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"fiel/* REMOVED_COMMON_BLOCK_2452 */9b-chat",value:"THUDM/glm-4-9b-chat"},{label:"google/gemma-2-9b-it",value:"google/gemma-2-9b-it"},{label:"01-ai/Yi-1.5-9B-Chat-16K",value:"01-ai/Yi-1.5-9B-Chat-16K"}]},{name:"APIKEY",descriptionKey:"description.siliconcloudCustomAPIKey",descriptionLink1:"https://siliconflow.cn/",providers:["custom"],required:!0,type:"password",sensitive:!0,direction:"column"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:P+"docs/serv/* REMOVED_COMMON_BLOCK_2466 */required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},yandex:{name:"Yandex",homepage:"https://translate.yandex.com/"},transmart:{name:"Transmart",homepage:"https://transmart.qq.com/"},grok:{ai:!0,name:"grok",homepage:"https://x.ai/",docUrl:P+"docs/services/grok/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/ai/",default:"https://api.x.ai/v1/chat/completions"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",provider/* REMOVED_COMMON_BLOCK_2453 */extarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},ollama:{...tn,name:"ollama",homepage:"https://ollama.com/",runningDocUrl:P+"docs/services/ollama/"},"azure-openai":{...tn,name:"azure-openai",homepage:"https://learn.microsoft.com/zh-cn/azure/cognitive-services/openai/chatgpt-quickstart?tabs=command-line&pivots=rest-api",docUrl:P+"docs/services/azure-openai/"},doubao:{ai:!0,name:"doubao",homepage:"https://www.volcengine.com/product/doubao",docUrl:P+"docs/services/doubao/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.accessPoint",descriptionKey:"description.accessPoint",placeholder:" ",required:!1,type:"text",default:""},{name:"limit",required:!1,labelKey:"field.limitPerSecond",placeholder:"ep-20240512123030-kv128",descriptionKey:"description.generalLimitPerSecond",type:"number",default:10},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"systemPrompt",label:"System Prompt",required:!1,descriptionKey:"description.systemPrompt",placeholderKey:"description.systemPromptPlaceholder",type:"textarea",optional:!0,default:"You are a translation engine, you can only translate text and cannot interpret it, and do not explain."},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:`Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}`,optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},"aliyun-bailian":{ai:!0,name:"aliyun-bailian",homepage:"https://bailian.console.aliyun.com/",docUrl:P+"docs/services/aliyun-bailian/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field./* REMOVED_COMMON_BLOCK_2454 */le Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},"qwen-mt":{name:"QwenMT",homepage:"https://help.aliyun.com/zh/model-studio/machine-translation",docUrl:P+"docs/services/qwen-mt/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"apiKey",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"qwen-mt-turbo",value:"qwen-mt-turbo"},{label:"qwen-mt-plus",value:"qwen-mt-plus"}]},{name:"domains",labelKey:"labelKey.domains",required:!1,placeholderKey:"description.qwenMtDomains",type:"textarea",optional:!0,default:""},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:10,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:10,optional:!0}]},qianfan:{ai:!0,name:"baidu-qianfan",homepage:"https://console.bce.baidu.com/qianfan/overview",docUrl:P+"docs/services/baidu-qianfan/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{label:"API Key",name:"AccessKeyID",placeholder:"API Key",required:!0,type:"text",sensitive:!0,direction:"column"},{label:"Secret Key",name:"AccessKeySecret",placeholder:"Secret Key",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"ERNIE-Speed-8K",value:"ernie_speed"},{label:"ERNIE-Speed-128K",value:"ernie-speed-128k"},{label:"ERNIE-4.0-8K",value:"completions_pro"},{label:"ERNIE-4.0-8K-Preview",value:"ernie-4.0-8k-preview"},{label:"ERNIE-3.5-8K",value:"completions"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:P+"docs/services/openai/",type:"number",providers:["custom"],default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:/* REMOVED_COMMON_BLOCK_2470 */extarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},qianfan2:{...tn,name:"baidu-qianfan2",homepage:"https://console.bce.baidu.com/qianfan/overview",docUrl:P+"docs/services/baidu-qianfan/"},hunyuan:{ai:!0,name:"hunyuan",homepage:"https://hunyuan.tencent.com/",docUrl:P+"docs/services/tencent-hunyuan/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{label:"Secret ID",name:"secret_id",placeholder:"Secret ID",required:!0,type:"text",sensitive:!0,direction:"column"},{label:"Secret Key",name:"secret_key",placeholder:"Secret Key",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"hunyuan-lite",value:"hunyuan-lite"},{label:"hunyuan-standard",value:"hunyuan-standard"},{label:"hunyuan-standard-256K",value:"hunyuan-standard-256K"},{label:"hunyuan-pro",value:"hunyuan-pro"},{providers:["custom"],label:"more-models",value:"more-models"}]},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",descriptionLink1:P+"docs/services/openai/",type:"number",providers:["custom"],default:1},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:3,optional:!0},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0},{name:"prompt",label:"Prompt",required:!1,descriptionKey:"description.prompt",type:"textarea",default:"",optional:!0},{name:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},lingyiwanwu:{ai:!0,name:"lingyiwanwu",homepage:"https://platform.lingyiwanwu.com/docs",docUrl:P/* REMOVED_COMMON_BLOCK_2456 */:"multiplePrompt",label:"Multiple Prompt",required:!1,descriptionKey:"description.multiplePrompt",type:"textarea",default:"",optional:!0},{name:"maxTextGroupLengthPerRequestForSubtitle",required:!1,labelKey:"field.maxTextGroupLengthPerRequestForSubtitle",default:5,type:"number",optional:!0},{name:"subtitlePrompt",label:"Subtitle Prompt",required:!1,descriptionKey:"description.subtitlePrompt",type:"textarea",default:"",optional:!0},{name:"temperature",descriptionKey:"description.temperature",label:"Temperature",required:!1,type:"number",default:"0",optional:!0}]},groq:{ai:!0,name:"groq",homepage:"https://groq.com/",docUrl:P+"docs/services/groq/",link1:"",link2:"",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",descriptionKey:"description.apiUrl",descriptionLink1:P+"docs/services/ai/",default:"https://api.groq.com/openai/v1/chat/completions"},{name:"model",labelKey:"field.model",descriptionKey:"description.model",required:!0,type:"model-select",providers:["custom"],options:[{label:"gemma2-9b-it",value:"gemma2-9b-it"},{label:"llama-3.3-70b-versatile",value:"llama-3.3-70b-versatile"},{label:"llama-3.1-8b-instant",value:"llama-3.1-8b-instant"},{label:"llama-guard-3-8b",value:"llama-guard-3-8b"},{label:"llama3-70b-8192",value:"/* REMOVED_COMMON_BLOCK_2458 */re/cognitive-services/translator/text-translation-overview",docUrl:P+"docs/services/azure/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"region",required:!1,default:"eastasia",type:"text"},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"apiUrl",direction:"column",labelKey:"field.apiUrl",required:!1,type:"text",default:"https://api.cognitive.microsofttranslator.com/",descriptionKey:"description.azureApiUrl",optional:!0},{name:"enableRichTranslate",labelKey:"field.enableRichTranslate",descriptionKey:"description.enableRichTranslate",required:!1,type:"boolean",default:!1,optional:!0}]},volc:{name:"Volc",homepage:"https://www.volcengine.com/",docUrl:P+"docs/services/volcano/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"accessKeyId",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"secretAccessKey",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:5,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1800,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:8,optional:!0}]},aliyun:{name:"Aliyun",homepage:"https://translate.alibaba.com/",docUrl:P+"docs/services/aliyun/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"AccessKeyID",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"AccessKeySecret",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"scene",labelKey:"field.scene",descriptionKey:"description.scene",descriptionLink1:"https://help.aliyun.com/document_detail/158267.html",required:!1,optional:!0,type:"text",default:"general"}]},baidu:{name:"Baidu",homepage:"https://fanyi.baidu.com/",docUrl:P+"docs/services/baidu/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{/* REMOVED_COMMON_BLOCK_2471 */xt",default:"",optional:!0}]},tencent:{name:"Tencent",homepage:"https://fanyi.qq.com/translateapi",docUrl:P+"docs/services/tencent/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"secretId",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"secretKey",required:!0,type:"password",sensitive:!0,direction:"column"}]},"youdao-ziyue":{name:"YoudaoZiyue",homepage:"https://fanyi.youdao.com/#/AITranslate",docUrl:P+"docs/services/youdao-ziyue/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"appKey",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"appSecret",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"prompt",labelKey:"Prompt",required:!1,placeholderKey:"description.ziyuePromptMaxLength",type:"textarea",optional:!0,default:""},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:1,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:10,optional:!0}]},youdao:{name:"Youdao",homepage:"https://youdao.com/",docUrl:P+"docs/services/youdao/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"appId",required:!0,type:"text",sensitive:!0,direction:"column"},{name:"appSecret",required:!0,type:"password",sensitive:!0,direction:"column"},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:5,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:200,optional:!0},{name:"domain",required:!1,labelKey:"field.domain",descriptionKey:"description.domain",descriptionLink1:"https://fanyi.youdao.com/openapi/",type:"text",default:"general",optional:!0},{name:"vocabId",required:!1,labelKey:"field.vocabId",descriptionKey:"description.vocabId",type:"text",default:"",optional:!0}]},openrouter:{...tn,name:"OpenRouter",homepage:"https://openrouter.ai/",docUrl:P+"docs/services/openrouter/"},caiyun:{name:"Caiyun",homepage:"https://fanyi.caiyunapp.com/",docUrl:P+"docs/services/caiyun/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"token",required:!0,type:"password",sensitive:!0,direction:"column"}]},"custom-ai":tn,openl:{name:"Openl",homepage:"https://openl.club/",docUrl:P+"docs/services/openL/",beta:!0,allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},...Fl,{type:"password",name:"apikey",required:!0,sensitive:!0,direction:"column"}],props:Fl/* REMOVED_COMMON_BLOCK_2472 */ps://niutrans.com/",docUrl:P+"docs/services/niu/",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"APIKEY",required:!0,type:"password",sensitive:!0,direction:"column"}]},custom:{name:"Custom",beta:!0,homepage:P+"docs/services/custom/",docUrl:P+"docs/services/custom/",titleKey:"description.custom",allProps:[{name:"name",labelKey:"field.name",placeholderKey:"field.namePlaceholder",required:!0,type:"text",direction:"column",providers:["custom"]},{name:"url",label:"API URL",required:!0,type:"text"},{name:"langs",required:!1,labelKey:"field.langs",type:"textarea",default:"zh-CN,en",optional:!0},{name:"placeholderDelimiters",required:!1,labelKey:"field.placeholderDelimiters",type:"text",default:Xi,optional:!0},{name:"limit",required:!1,labelKey:"field.limitPerSecond",descriptionKey:"description.generalLimitPerSecond",type:"number",default:5,optional:!0},{name:"maxTextLengthPerRequest",required:!1,labelKey:"field.maxTextLengthPerRequest",descriptionKey:"description.maxTextLengthPerRequest",type:"number",default:1200,optional:!0},{name:"maxTextGroupLengthPerRequest",required:!1,labelKey:"field.maxTextGroupLengthPerRequest",descriptionKey:"description.maxTextGroupLengthPerRequest",type:"number",default:1,optional:!0}]},tenAlpha:{name:"TenAlpha",homepage:"https://fanyi.qq.com/",alpha:!0},cai:{name:"Cai",homepage:"https://fanyi.caiyunapp.com/",alpha:!0},you:{name:"You",alpha:!0,homepage:"https://youdao.com/"},volcAlpha:{name:"Volc Alpha",alpha:!0,homepage:"https://www.volcengine.com/"},d:{name:"D () ",canary:!0,homepage:"https://www.deepl.com/translator"},dpro:{name:"DPro (Canary) ",canary:!0,homepage:"https://www.deepl.com/translator"},papago:{name:"Papago",homepage:"https://translate.google.com/",canary:!0},mock:{name:"Mock",homepage:"https://www.google.com"},mock2:{name:"Mock2",homepage:"https://www.google.com"}};var Bl={manifest_version:3,name:"__MSG_brandName__",desc/* REMOVED_COMMON_BLOCK_2445 */.png",light:"icons/dark-48.png",size:48},{dark:"icons/64.png",light:"icons/dark-64.png",size:64},{dark:"icons/128.png",light:"icons/dark-128.png",size:128},{dark:"icons/256.png",light:"icons/dark-256.png",size:256}]},icons:{"32":"icons/32.png","48":"icons/48.png","64":"icons/64.png","128":"icons/128.png","256":"icons/256.png"},browser_specific_settings:{gecko:{id:"{5efceaa7-f3a2-4e59-a54b-85319448e305}",strict_min_version:"63.0"},gecko_android:{strict_min_version:"113.0"}},key:"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7JPn78UfqI3xIIOPPLPS74UTzLfJL1gQM8hlk/deKWvFP/WqUBnPJPdhQeF45sFpI1OjO70nFqdATT4/RwYAiZK7G/E6m27MDVnhHjszfzReOuoAEn9J3RnE2xEx5pFhRFcelhnwTTLrrn90aaPcaMtNsgXtZA1Ggz/SnX9I4ZygqpJYjx3Ql2t6SyNK222oRQiKMT93Rrjgyc8RFA7FKXsWglG0TvseRjbmG5Jk5gDx+2/YTcWGqCDotQnWnkPj/dBO23UAX7IpyJK3FGYdkvWFih6OVClHIIWY8mfCjjwSGbXNQNesaa9F2hrzBZ5MRTj4m7yj76mGxuPHPIE8mwIDAQAB"};var Ag="";function ht(){return Ag||Bl.version}function On(){return ie.runtime.getManifest().version}var Eg="";function Pa(){return Eg||globalThis.navigator.userAgent}function Zt(){return Pa().includes("ImtFxiOS")}function io(){let t=Pa().match(/ImtFxiOS\/(\d+\.\d+\.\d+)/);return t?t[1]:null}function Xt(){let e=Pa();return e.includes("ImtFxAndroid")||e.includes("ImtFxAOS")}function oo(){let e=Pa();if(e.includes("ImtFxAndroid")){let t=e.match(/ImtFxAndroid\/(\d+\.\d+\.\d+)/);return t?t[1]:null}if(e.includes("ImtFxAOS")){let t=e.match(/ImtFxAOS\/(\d+\.\d+\.\d+)/);return t?t[1]:null}return null}function zn(){let e=Pa();if(Zt()||Xt()){let t=e.match(/Imt[\w/.]+/);if(t){let a=t[0].split("/"),n=a[0];return a[2]&&(n+="_"+a[2]),{name:n,version:a[1]}}}return null}function so(){let e=Pa();if(!e.includes("iPhone"))return null;let t=e.match(/Version\/(\d+(?:\.\d+)?)/);return t?t[1]:null}function Rl(){try{let e=so();return e?parseFloat(e):0}catch{return 0}}function _l(){let e=Pa();if(!e.includes("iPhone"))return null;let t=e.match(/iPhone OS (\d+(?:_\d+){1,2})/);return t&&t?t[1]:null}function Un(){let e,t="pending",a=new Promise((n,r)=>{e={async resolve(i){await i,t="fulfilled",n(i)},reject(i){t="rejected",r(i)}}});return Object.defineProperty(a,"state",{get:()=>t}),Object.assign(a,e)}function qn(e,t={}){let{signal:a,persistent:n}=t;return a?.aborted?Promise.reject(new DOMException("Delay was aborted.","AbortError")):new Promise((r,i)=>{let o=()=>{clearTimeout(u),i(new DOMException("Delay was aborted.","AbortError"))},u=setTimeout(()=>{a?.removeEventListener("abort",o),r()},e);if(a?.addEventListener("abort",o,{once:!0}),n===!1)try{Deno.unrefTimer(u)}catch(c){if(!(c instanceof ReferenceError))throw c}})}var jl=class{#e=0;#t=[];#a=[];#n=Un();add(t){++this.#e,this.#r(t[Symbol.asyncIterator]())}async#r(t){try{let{value:a,done:n}=await t.next();n?--this.#e:this.#t.push({iterator:t,value:a})}catch(a){this.#a.push(a)}this.#n.resolve()}async*iterate(){for(;this.#e>0;){await this.#n;for(let t=0;t<this.#t.length;t++){let{iterator:a,value:n}=this.#t[t];yield n,this.#r(a)}if(this.#a.length){for(let t of this.#a)throw t;this.#a.length=0}this.#t.length=0,this.#n=Un()}}[Symbol.asyncIterator](){return this.iterate()}};var qr=globalThis||(typeof window<"u"?window:self),kg=Object.create,lo=Object.defineProperty,Tg=Object.getOwnPropertyDescriptor,Dg=Object.getOwnPropertyNames,Cg=Object.getPrototypeOf,Mg=Object.prototype.hasOwnProperty,Ig=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Pg=(e,t)=>{for(var a in t)lo(e,a,{get:t[a],enumerable:!0})},uo=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Dg(t))!Mg.call(e,r)&&r!==a&&lo(e,r,{get:()=>t[r],enumerable:!(n=Tg(t,r))||n.enumerable});return e},Fg=(e,t,a)=>(uo(e,t,"default"),a&&uo(a,t,"default")),Nl=(e,t,a)=>(a=e!=null?kg(Cg(e)):{},uo(t||!e||!e.__esModule?lo(a,"default",{value:e,enumerable:!0}):a,e)),Ol=Ig((e,t)=>{var a="Expected a function",n=NaN,r="[object Symbol]",i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt,l=typeof qr=="object"&&qr&&qr.Object===Object&&qr,d=typeof self=="object"&&self&&self.Object===Object&&self,m=l||d||Function("return this")(),p=Object.prototype,x=p.toString,A=Math.max,S=Math.min,g=function(){return m.Date.now()};function M(w,y,R){var F,C,j,Y,Z,ve,Se=0,H=!1,oe=!1,Fe=!0;if(typeof w!="function")throw new TypeError(a);y=v(y)||0,k(R)&&(H=!!R.leading,oe="maxWait"in R,j=oe?A(v(R.maxWait)||0,y):j,Fe="trailing"in R?!!R.trailing:Fe);function he(N){var V=F,ae=C;return F=C=void 0,Se=N,Y=w.apply(ae,V),Y}function T(N){return Se=N,Z=setTimeout(ne,y),H?he(N):Y}function I(N){var V=N-ve,ae=N-Se,$=y-V;return oe?S($,j-ae):$}function U(N){var V=N-ve,ae=N-Se;return ve===void 0||V>=y||V<0||oe&&ae>=j}function ne(){var N=g();if(U(N))return Q(N);Z=setTimeout(ne,I(N))}function Q(N){return Z=void 0,Fe&&F?he(N):(F=C=void 0,Y)}function ce(){Z!==void 0&&clearTimeout(Z),Se=0,F=ve=C=Z=void 0}function fe(){return Z===void 0?Y:Q(g())}function _(){var N=g(),V=U(N);if(F=arguments,C=this,ve=N,V){if(Z===void 0)return T(ve);if(oe)return Z=setTimeout(ne,y),he(ve)}return Z===void 0&&(Z=setTimeout(ne,y)),Y}return _.cancel=ce,_.flush=fe,_}function h(w,y,R){var F=!0,C=!0;if(typeof w!="function")throw new TypeError(a);return k(R)&&(F="leading"in R?!!R.leading:F,C="trailing"in R?!!R.trailing:C),M(w,y,{leading:F,maxWait:y,trailing:C})}function k(w){var y=typeof w;return!!w&&(y=="object"||y=="function")}function L(w){return!!w&&typeof w=="object"}function E(w){return typeof w=="symbol"||L(w)&&x.call(w)==r}function v(w){if(typeof w=="number")return w;if(E(w))return n;if(k(w)){var y=typeof w.valueOf=="function"?w.valueOf():w;w=k(y)?y+"":y}if(typeof w!="string")return w===0?w:+w;w=w.replace(i,"");var R=s.test(w);return R||u.test(w)?c(w.slice(2),R?2:8):o.test(w)?n:+w}t.exports=h}),zl={};Pg(zl,{default:()=>co});var Bg=Nl(Ol());Fg(zl,Nl(Ol()));var{default:Ll,...Rg}=Bg,co=Ll!==void 0?Ll:Rg;var Gr=globalThis||(typeof window<"u"?window:self),_g=Object.create,po=Object.defineProperty,jg=Object.getOwnPropertyDescriptor,Lg=Object.getOwnPropertyNames,Ng=Object.getPrototypeOf,Og=Object.prototype.hasOwnProperty,zg=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ug=(e,t)=>{for(var a in t)po(e,a,{get:t[a],enumerable:!0})},mo=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Lg(t))!Og.call(e,r)&&r!==a&&po(e,r,{get:()=>t[r],enumerable:!(n=jg(t,r))||n.enumerable});return e},qg=(e,t,a)=>(mo(e,t,"default"),a&&mo(a,t,"default")),ql=(e,t,a)=>(a=e!=null?_g(Ng(e)):{},mo(t||!e||!e.__esModule?po(a,"default",{value:e,enumerable:!0}):a,e)),Gl=zg((e,t)=>{var a="Expected a function",n=NaN,r="[object Symbol]",i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt,l=typeof Gr=="object"&&Gr&&Gr.Object===Object&&Gr,d=typeof self=="object"&&self&&self.Object===Object&&self,m=l||d||Function("return this")(),p=Object.prototype,x=p.toString,A=Math.max,S=Math.min,g=function(){return m.Date.now()};function M(v,w,y){var R,F,C,j,Y,Z,ve=0,Se=!1,H=!1,oe=!0;if(typeof v!="function")throw new TypeError(a);w=E(w)||0,h(y)&&(Se=!!y.leading,H="maxWait"in y,C=H?A(E(y.maxWait)||0,w):C,oe="trailing"in y?!!y.trailing:oe);function Fe(_){var N=R,V=F;return R=F=void 0,ve=_,j=v.apply(V,N),j}function he(_){return ve=_,Y=setTimeout(U,w),Se?Fe(_):j}function T(_){var N=_-Z,V=_-ve,ae=w-N;return H?S(ae,C-V):ae}function I(_){var N=_-Z,V=_-ve;return Z===void 0||N>=w||N<0||H&&V>=C}function U(){var _=g();if(I(_))return ne(_);Y=setTimeout(U,T(_))}function ne(_){return Y=void 0,oe&&R?Fe(_):(R=F=void 0,j)}function Q(){Y!==void 0&&clearTimeout(Y),ve=0,R=Z=F=Y=void 0}function ce(){return Y===void 0?j:ne(g())}function fe(){var _=g(),N=I(_);if(R=arguments,F=this,Z=_,N){if(Y===void 0)return he(Z);if(H)return Y=setTimeout(U,w),Fe(Z)}return Y===void 0&&(Y=setTimeout(U,w)),j}return fe.cancel=Q,fe.flush=ce,fe}function h(v){var w=typeof v;return!!v&&(w=="object"||w=="function")}function k(v){return!!v&&typeof v=="object"}function L(v){return typeof v=="symbol"||k(v)&&x.call(v)==r}function E(v){if(typeof v=="number")return v;if(L(v))return n;if(h(v)){var w=typeof v.valueOf=="function"?v.valueOf():v;v=h(w)?w+"":w}if(typeof v!="string")return v===0?v:+v;v=v.replace(i,"");var y=s.test(v);return y||u.test(v)?c(v.slice(2),y?2:8):o.test(v)?n:+v}t.exports=M}),Hl={};Ug(Hl,{default:()=>Fa});var Gg=ql(Gl());qg(Hl,ql(Gl()));var{default:Ul,...Hg}=Gg,Fa=Ul!==void 0?Ul:Hg;var Kg=Object.create,ho=Object.defineProperty,Wg=Object.getOwnPropertyDescriptor,Qg=Object.getOwnPropertyNames,Vg=Object.getPrototypeOf,Yg=Object.prototype.hasOwnProperty,Jg=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Zg=(e,t)=>{for(var a in t)ho(e,a,{get:t[a],enumerable:!0})},go=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Qg(t))!Yg.call(e,r)&&r!==a&&ho(e,r,{get:()=>t[r],enumerable:!(n=Wg(t,r))||n.enumerable});return e},Xg=(e,t,a)=>(go(e,t,"default"),a&&go(a,t,"default")),Wl=(e,t,a)=>(a=e!=null?Kg(Vg(e)):{},go(t||!e||!e.__esModule?ho(a,"default",{value:e,enumerable:!0}):a,e)),Ql=Jg((e,t)=>{(function(a,n){typeof e=="object"&&typeof t=="object"?t.exports=n():typeof define=="function"&&define.amd?define([],n):typeof e=="object"?e.notie=n():a.notie=n()})(e,function(){return function(a){function n(i){if(r[i])return r[i].exports;var o=r[i]={i,l:!1,exports:{}};return a[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}var r={};return n.m=a,n.c=r,n.i=function(i){return i},n.d=function(i,o,s){n.o(i,o)||Object.defineProperty(i,o,{configurable:!1,enumerable:!0,get:s})},n.n=function(i){var o=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(o,"a",o),o},n.o=function(i,o){return Object.prototype.hasOwnProperty.call(i,o)},n.p="",n(n.s=1)}([function(a,n){a.exports=function(r){return r.webpackPolyfill||(r.deprecate=function(){},r.paths=[],r.children||(r.children=[]),Object.defineProperty(r,"loaded",{enumerable:!0,get:function(){return r.l}}),Object.defineProperty(r,"id",{enumerable:!0,get:function(){return r.i}}),r.webpackPolyfill=1),r}},function(a,n,r){"use strict";(function(i){var o,s,u,c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(l){return typeof l}:function(l){return l&&typeof Symbol=="function"&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l};(function(l,d){c(n)==="object"&&c(i)==="object"?i.exports=d():(s=[],o=d,u=typeof o=="function"?o.apply(n,s):o,u!==void 0&&(i.exports=u))})(void 0,function(){return function(l){function d(p){if(m[p])return m[p].exports;var x=m[p]={i:p,l:!1,exports:{}};return l[p].call(x.exports,x,x.exports,d),x.l=!0,x.exports}var m={};return d.m=l,d.c=m,d.i=function(p){return p},d.d=function(p,x,A){d.o(p,x)||Object.defineProperty(p,x,{configurable:!1,enumerable:!0,get:A})},d.n=function(p){var x=p&&p.__esModule?function(){return p.default}:function(){return p};return d.d(x,"a",x),x},d.o=function(p,x){return Object.prototype.hasOwnProperty.call(p,x)},d.p="",d(d.s=0)}([function(l,d,m){function p(T,I){var U={};for(var ne in T)I.indexOf(ne)>=0||Object.prototype.hasOwnProperty.call(T,ne)&&(U[ne]=T[ne]);return U}Object.defineProperty(d,"__esModule",{value:!0});var x=typeof Symbol=="function"&&c(Symbol.iterator)==="symbol"?function(T){return typeof T>"u"?"undefined":c(T)}:function(T){return T&&typeof Symbol=="function"&&T.constructor===Symbol&&T!==Symbol.prototype?"symbol":typeof T>"u"?"undefined":c(T)},A=Object.assign||function(T){for(var I=1;I<arguments.length;I++){var U=arguments[I];for(var ne in U)Object.prototype.hasOwnProperty.call(U,ne)&&(T[ne]=U[ne])}return T},S={top:"top",bottom:"bottom"},g={alertTime:3,dateMonths:["January","February","March","April","May","June","July","August","September","October","November","December"],overlayClickDismiss:!0,overlayOpacity:.75,transitionCurve:"ease",transitionDuration:.3,transitionSelector:"all",classes:{container:"notie-container",textbox:"notie-textbox",textboxInner:"notie-textbox-inner",button:"notie-button",element:"notie-element",elementHalf:"notie-element-half",elementThird:"notie-element-third",overlay:"notie-overlay",backgroundSuccess:"notie-background-success",backgroundWarning:"notie-background-warning",backgroundError:"notie-background-error",backgroundInfo:"notie-background-info",backgroundNeutral:"notie-background-neutral",backgroundOverlay:"notie-background-overlay",alert:"notie-alert",inputField:"notie-input-field",selectChoiceRepeated:"notie-select-choice-repeated",dateSelectorInner:"notie-date-selector-inner",dateSelectorUp:"notie-date-selector-up"},ids:{overlay:"notie-overlay"},positions:{alert:S.top,force:S.top,confirm:S.top,input:S.top,select:S.bottom,date:S.top}},M=d.setOptions=function(T){g=A({},g,T,{classes:A({},g.classes,T.classes),ids:A({},g.ids,T.ids),positions:A({},g.positions,T.positions)})},h=function(){return new Promise(function(T){return setTimeout(T,0)})},k=function(T){return new Promise(function(I){return setTimeout(I,1e3*T)})},L=function(){document.activeElement&&document.activeElement.blur()},E=function(){var T="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(I){var U=16*Math.random()|0,ne=I==="x"?U:3&U|8;return ne.toString(16)});return"notie-"+T},v={1:g.classes.backgroundSuccess,success:g.classes.backgroundSuccess,2:g.classes.backgroundWarning,warning:g.classes.backgroundWarning,3:g.classes.backgroundError,error:g.classes.backgroundError,4:g.classes.backgroundInfo,info:g.classes.backgroundInfo,5:g.classes.backgroundNeutral,neutral:g.classes.backgroundNeutral},w=function(){return g.transitionSelector+" "+g.transitionDuration+"s "+g.transitionCurve},y=function(T){return T.keyCode===13},R=function(T){return T.keyCode===27},F=function(T,I){T.classList.add(g.classes.container),T.style[I]="-10000px",document.body.appendChild(T),T.style[I]="-"+T.offsetHeight+"px",T.listener&&window.addEventListener("keydown",T.listener),h().then(function(){T.style.transition=w(),T.style[I]=0})},C=function(T,I){var U=document.getElementById(T);U&&(U.style[I]="-"+U.offsetHeight+"px",U.listener&&window.removeEventListener("keydown",U.listener),k(g.transitionDuration).then(function(){U.parentNode&&U.parentNode.removeChild(U)}))},j=function(T,I){var U=document.createElement("div");U.id=g.ids.overlay,U.classList.add(g.classes.overlay),U.classList.add(g.classes.backgroundOverlay),U.style.opacity=0,T&&g.overlayClickDismiss&&(U.onclick=function(){C(T.id,I),Y()}),document.body.appendChild(U),h().then(function(){U.style.transition=w(),U.style.opacity=g.overlayOpacity})},Y=function(){var T=document.getElementById(g.ids.overlay);T.style.opacity=0,k(g.transitionDuration).then(function(){T.parentNode&&T.parentNode.removeChild(T)})},Z=d.hideAlerts=function(T){var I=document.getElementsByClassName(g.classes.alert);if(I.length){for(var U=0;U<I.length;U++){var ne=I[U];C(ne.id,ne.position)}T&&k(g.transitionDuration).then(function(){return T()})}},ve=d.alert=function(T){var I=T.type,U=I===void 0?4:I,ne=T.text,Q=T.time,ce=Q===void 0?g.alertTime:Q,fe=T.stay,_=fe!==void 0&&fe,N=T.position,V=N===void 0?g.positions.alert||V.top:N;L(),Z();var ae=document.createElement("div"),$=E();ae.id=$,ae.position=V,ae.classList.add(g.classes.textbox),ae.classList.add(v[U]),ae.classList.add(g.classes.alert),ae.innerHTML='<div class="'+g.classes.textboxInner+'">'+ne+"</div>",ae.onclick=function(){return C($,V)},ae.listener=function(q){(y(q)||R(q))&&Z()},F(ae,V),ce&&ce<1&&(ce=1),!_&&ce&&k(ce).then(function(){return C($,V)})},Se=d.force=function(T,I){var U=T.type,ne=U===void 0?5:U,Q=T.text,ce=T.buttonText,fe=ce===void 0?"OK":ce,_=T.callback,N=T.position,V=N===void 0?g.positions.force||V.top:N;L(),Z();var ae=document.createElement("div"),$=E();ae.id=$;var q=document.createElement("div");q.classList.add(g.classes.textbox),q.classList.add(g.classes.backgroundInfo),q.innerHTML='<div class="'+g.classes.textboxInner+'">'+Q+"</div>";var O=document.createElement("div");O.classList.add(g.classes.button),O.classList.add(v[ne]),O.innerHTML=fe,O.onclick=function(){C($,V),Y(),_?_():I&&I()},ae.appendChild(q),ae.appendChild(O),ae.listener=function(ue){y(ue)&&O.click()},F(ae,V),j()},H=d.confirm=function(T,I,U){var ne=T.text,Q=T.submitText,ce=Q===void 0?"Yes":Q,fe=T.cancelText,_=fe===void 0?"Cancel":fe,N=T.submitCallback,V=T.cancelCallback,ae=T.position,$=ae===void 0?g.positions.confirm||$.top:ae;L(),Z();var q=document.createElement("div"),O=E();q.id=O;var ue=document.createElement("div");ue.classList.add(g.classes.textbox),ue.classList.add(g.classes.backgroundInfo),ue.innerHTML='<div class="'+g.classes.textboxInner+'">'+ne+"</div>";var me=document.createElement("div");me.classList.add(g.classes.button),me.classList.add(g.classes.elementHalf),me.classList.add(g.classes.backgroundSuccess),me.innerHTML=ce,me.onclick=function(){C(O,$),Y(),N?N():I&&I()};var J=document.createElement("div");J.classList.add(g.classes.button),J.classList.add(g.classes.elementHalf),J.classList.add(g.classes.backgroundError),J.innerHTML=_,J.onclick=function(){C(O,$),Y(),V?V():U&&U()},q.appendChild(ue),q.appendChild(me),q.appendChild(J),q.listener=function(Ee){y(Ee)?me.click():R(Ee)&&J.click()},F(q,$),j(q,$)},oe=function(T,I,U){var ne=T.text,Q=T.submitText,ce=Q===void 0?"Submit":Q,fe=T.cancelText,_=fe===void 0?"Cancel":fe,N=T.submitCallback,V=T.cancelCallback,ae=T.position,$=ae===void 0?g.positions.input||$.top:ae,q=p(T,["text","submitText","cancelText","submitCallback","cancelCallback","position"]);L(),Z();var O=document.createElement("div"),ue=E();O.id=ue;var me=document.createElement("div");me.classList.add(g.classes.textbox),me.classList.add(g.classes.backgroundInfo),me.innerHTML='<div class="'+g.classes.textboxInner+'">'+ne+"</div>";var J=document.createElement("input");J.classList.add(g.classes.inputField),J.setAttribute("autocapitalize",q.autocapitalize||"none"),J.setAttribute("autocomplete",q.autocomplete||"off"),J.setAttribute("autocorrect",q.autocorrect||"off"),J.setAttribute("autofocus",q.autofocus||"true"),J.setAttribute("inputmode",q.inputmode||"verbatim"),J.setAttribute("max",q.max||""),J.setAttribute("maxlength",q.maxlength||""),J.setAttribute("min",q.min||""),J.setAttribute("minlength",q.minlength||""),J.setAttribute("placeholder",q.placeholder||""),J.setAttribute("spellcheck",q.spellcheck||"default"),J.setAttribute("step",q.step||"any"),J.setAttribute("type",q.type||"text"),J.value=q.value||"",q.allowed&&(J.oninput=function(){var se=void 0;if(Array.isArray(q.allowed)){for(var Ce="",W=q.allowed,He=0;He<W.length;He++)W[He]==="an"?Ce+="0-9a-zA-Z":W[He]==="a"?Ce+="a-zA-Z":W[He]==="n"&&(Ce+="0-9"),W[He]==="s"&&(Ce+=" ");se=new RegExp("[^"+Ce+"]","g")}else x(q.allowed)==="object"&&(se=q.allowed);J.value=J.value.replace(se,"")});var Ee=document.createElement("div");Ee.classList.add(g.classes.button),Ee.classList.add(g.classes.elementHalf),Ee.classList.add(g.classes.backgroundSuccess),Ee.innerHTML=ce,Ee.onclick=function(){C(ue,$),Y(),N?N(J.value):I&&I(J.value)};var Re=document.createElement("div");Re.classList.add(g.classes.button),Re.classList.add(g.classes.elementHalf),Re.classList.add(g.classes.backgroundError),Re.innerHTML=_,Re.onclick=function(){C(ue,$),Y(),V?V(J.value):U&&U(J.value)},O.appendChild(me),O.appendChild(J),O.appendChild(Ee),O.appendChild(Re),O.listener=function(se){y(se)?Ee.click():R(se)&&Re.click()},F(O,$),J.focus(),j(O,$)};d.input=oe;var Fe=d.select=function(T,I){var U=T.text,ne=T.cancelText,Q=ne===void 0?"Cancel":ne,ce=T.cancelCallback,fe=T.choices,_=T.position,N=_===void 0?g.positions.select||N.top:_;L(),Z();var V=document.createElement("div"),ae=E();V.id=ae;var $=document.createElement("div");$.classList.add(g.classes.textbox),$.classList.add(g.classes.backgroundInfo),$.innerHTML='<div class="'+g.classes.textboxInner+'">'+U+"</div>",V.appendChild($),fe.forEach(function(O,ue){var me=O.type,J=me===void 0?1:me,Ee=O.text,Re=O.handler,se=document.createElement("div");se.classList.add(v[J]),se.classList.add(g.classes.button),se.classList.add(g.classes.selectChoice);var Ce=fe[ue+1];Ce&&!Ce.type&&(Ce.type=1),Ce&&Ce.type===J&&se.classList.add(g.classes.selectChoiceRepeated),se.innerHTML=Ee,se.onclick=function(){C(ae,N),Y(),Re()},V.appendChild(se)});var q=document.createElement("div");q.classList.add(g.classes.backgroundNeutral),q.classList.add(g.classes.button),q.innerHTML=Q,q.onclick=function(){C(ae,N),Y(),ce?ce():I&&I()},V.appendChild(q),V.listener=function(O){R(O)&&q.click()},F(V,N),j(V,N)},he=d.date=function(T,I,U){var ne=T.value,Q=ne===void 0?new Date:ne,ce=T.submitText,fe=ce===void 0?"OK":ce,_=T.cancelText,N=_===void 0?"Cancel":_,V=T.submitCallback,ae=T.cancelCallback,$=T.position,q=$===void 0?g.positions.date||q.top:$;L(),Z();var O="&#9662",ue=document.createElement("div"),me=document.createElement("div"),J=document.createElement("div"),Ee=function(Be){ue.innerHTML=g.dateMonths[Be.getMonth()],me.innerHTML=Be.getDate(),J.innerHTML=Be.getFullYear()},Re=function(Be){var we=new Date(Q.getFullYear(),Q.getMonth()+1,0).getDate(),ot=Be.target.textContent.replace(/^0+/,"").replace(/[^\d]/g,"").slice(0,2);Number(ot)>we&&(ot=we.toString()),Be.target.textContent=ot,Number(ot)<1&&(ot="1"),Q.setDate(Number(ot))},se=function(Be){var we=Be.target.textContent.replace(/^0+/,"").replace(/[^\d]/g,"").slice(0,4);Be.target.textContent=we,Q.setFullYear(Number(we))},Ce=function(Be){Ee(Q)},W=function(Be){var we=new Date(Q.getFullYear(),Q.getMonth()+Be+1,0).getDate();Q.getDate()>we&&Q.setDate(we),Q.setMonth(Q.getMonth()+Be),Ee(Q)},He=function(Be){Q.setDate(Q.getDate()+Be),Ee(Q)},Bn=function(Be){var we=Q.getFullYear()+Be;we<0?Q.setFullYear(0):Q.setFullYear(Q.getFullYear()+Be),Ee(Q)},Lt=document.createElement("div"),Ea=E();Lt.id=Ea;var Va=document.createElement("div");Va.classList.add(g.classes.backgroundInfo);var Qe=document.createElement("div");Qe.classList.add(g.classes.dateSelectorInner);var xt=document.createElement("div");xt.classList.add(g.classes.button),xt.classList.add(g.classes.elementThird),xt.classList.add(g.classes.dateSelectorUp),xt.innerHTML=O;var Ve=document.createElement("div");Ve.classList.add(g.classes.button),Ve.classList.add(g.classes.elementThird),Ve.classList.add(g.classes.dateSelectorUp),Ve.innerHTML=O;var it=document.createElement("div");it.classList.add(g.classes.button),it.classList.add(g.classes.elementThird),it.classList.add(g.classes.dateSelectorUp),it.innerHTML=O,ue.classList.add(g.classes.element),ue.classList.add(g.classes.elementThird),ue.innerHTML=g.dateMonths[Q.getMonth()],me.classList.add(g.classes.element),me.classList.add(g.classes.elementThird),me.setAttribute("contentEditable",!0),me.addEventListener("input",Re),me.addEventListener("blur",Ce),me.innerHTML=Q.getDate(),J.classList.add(g.classes.element),J.classList.add(g.classes.elementThird),J.setAttribute("contentEditable",!0),J.addEventListener("input",se),J.addEventListener("blur",Ce),J.innerHTML=Q.getFullYear();var qt=document.createElement("div");qt.classList.add(g.classes.button),qt.classList.add(g.classes.elementThird),qt.innerHTML=O;var Gt=document.createElement("div");Gt.classList.add(g.classes.button),Gt.classList.add(g.classes.elementThird),Gt.innerHTML=O;var Sa=document.createElement("div");Sa.classList.add(g.classes.button),Sa.classList.add(g.classes.elementThird),Sa.innerHTML=O,xt.onclick=function(){return W(1)},Ve.onclick=function(){return He(1)},it.onclick=function(){return Bn(1)},qt.onclick=function(){return W(-1)},Gt.onclick=function(){return He(-1)},Sa.onclick=function(){return Bn(-1)};var Je=document.createElement("div");Je.classList.add(g.classes.button),Je.classList.add(g.classes.elementHalf),Je.classList.add(g.classes.backgroundSuccess),Je.innerHTML=fe,Je.onclick=function(){C(Ea,q),Y(),V?V(Q):I&&I(Q)};var Ht=document.createElement("div");Ht.classList.add(g.classes.button),Ht.classList.add(g.classes.elementHalf),Ht.classList.add(g.classes.backgroundError),Ht.innerHTML=N,Ht.onclick=function(){C(Ea,q),Y(),ae?ae(Q):U&&U(Q)},Qe.appendChild(xt),Qe.appendChild(Ve),Qe.appendChild(it),Qe.appendChild(ue),Qe.appendChild(me),Qe.appendChild(J),Qe.appendChild(qt),Qe.appendChild(Gt),Qe.appendChild(Sa),Va.appendChild(Qe),Lt.appendChild(Va),Lt.appendChild(Je),Lt.appendChild(Ht),Lt.listener=function(Be){y(Be)?Je.click():R(Be)&&Ht.click()},F(Lt,q),j(Lt,q)};d.default={alert:ve,force:Se,confirm:H,input:oe,select:Fe,date:he,setOptions:M,hideAlerts:Z}}])})}).call(n,r(0)(a))}])})}),Vl={};Zg(Vl,{default:()=>Hr});var $g=Wl(Ql());Xg(Vl,Wl(Ql()));var{default:Kl,...eh}=$g,Hr=Kl!==void 0?Kl:eh;var Kr=typeof navigator<"u"?navigator.userAgent.toLowerCase().indexOf("firefox")>0:!1;function Wr(e,t,a,n){e.addEventListener?e.addEventListener(t,a,n):e.attachEvent&&e.attachEvent(`on${t}`,a)}function nn(e,t,a,n){e.removeEventListener?e.removeEventListener(t,a,n):e.detachEvent&&e.detachEvent(`on${t}`,a)}function fo(e,t){let a=t.slice(0,t.length-1);for(let n=0;n<a.length;n++)a[n]=e[a[n].toLowerCase()];return a}function bo(e){typeof e!="string"&&(e=""),e=e.replace(/\s/g,"");let t=e.split(","),a=t.lastIndexOf("");for(;a>=0;)t[a-1]+=",",t.splice(a,1),a=t.lastIndexOf("");return t}function Yl(e,t){let a=e.length>=t.length?e:t,n=e.length>=t.length?t:e,r=!0;for(let i=0;i<a.length;i++)n.indexOf(a[i])===-1&&(r=!1);return r}var rn={backspace:8,"\u232B":8,tab:9,clear:12,enter:13,"\u21A9":13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,ins:45,insert:45,home:36,end:35,pageup:33,pagedown:34,capslock:20,num_0:96,num_1:97,num_2:98,num_3:99,num_4:100,num_5:101,num_6:102,num_7:103,num_8:104,num_9:105,num_multiply:106,num_add:107,num_enter:108,num_subtract:109,num_decimal:110,num_divide:111,"\u21EA":20,",":188,".":190,"/":191,"`":192,"-":Kr?173:189,"=":Kr?61:187,";":Kr?59:186,"'":222,"[":219,"]":221,"\\":220},ft={"\u21E7":16,shift:16,"\u2325":18,alt:18,option:18,"\u2303":17,ctrl:17,control:17,"\u2318":91,cmd:91,command:91},on={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey",shiftKey:16,ctrlKey:17,altKey:18,metaKey:91},Ue={16:!1,18:!1,17:!1,91:!1},Me={};for(let e=1;e<20;e++)rn[`f${e}`]=111+e;var Ie=[],Gn=null,Xl="all",$t=new Map,Kn=e=>rn[e.toLowerCase()]||ft[e.toLowerCase()]||e.toUpperCase().charCodeAt(0),th=e=>Object.keys(rn).find(t=>rn[t]===e),ah=e=>Object.keys(ft).find(t=>ft[t]===e);function $l(e){Xl=e||"all"}function Hn(){return Xl||"all"}function nh(){return Ie.slice(0)}function rh(){return Ie.map(e=>th(e)||ah(e)||String.fromCharCode(e))}function ih(){let e=[];return Object.keys(Me).forEach(t=>{Me[t].forEach(({key:a,scope:n,mods:r,shortcut:i})=>{e.push({scope:n,shortcut:i,mods:r,keys:a.split("+").map(o=>Kn(o))})})}),e}function oh(e){let t=e.target||e.srcElement,{tagName:a}=t,n=!0,r=a==="INPUT"&&!["checkbox","radio","range","button","file","reset","submit","color"].includes(t.type);return(t.isContentEditable||(r||a==="TEXTAREA"||a==="SELECT")&&!t.readOnly)&&(n=!1),n}function sh(e){return typeof e=="string"&&(e=Kn(e)),Ie.indexOf(e)!==-1}function uh(e,t){let a,n;e||(e=Hn());for(let r in Me)if(Object.prototype.hasOwnProperty.call(Me,r))for(a=Me[r],n=0;n<a.length;)a[n].scope===e?a.splice(n,1).forEach(({element:o})=>vo(o)):n++;Hn()===e&&$l(t||"all")}function lh(e){let t=e.keyCode||e.which||e.charCode,a=Ie.indexOf(t);if(a>=0&&Ie.splice(a,1),e.key&&e.key.toLowerCase()==="meta"&&Ie.splice(0,Ie.length),(t===93||t===224)&&(t=91),t in Ue){Ue[t]=!1;for(let n in ft)ft[n]===t&&(da[n]=!1)}}function ec(e,...t){if(typeof e>"u")Object.keys(Me).forEach(a=>{Array.isArray(Me[a])&&Me[a].forEach(n=>Qr(n)),delete Me[a]}),vo(null);else if(Array.isArray(e))e.forEach(a=>{a.key&&Qr(a)});else if(typeof e=="object")e.key&&Qr(e);else if(typeof e=="string"){let[a,n]=t;typeof a=="function"&&(n=a,a=""),Qr({key:e,scope:a,method:n,splitKey:"+"})}}var Qr=({key:e,scope:t,method:a,splitKey:n="+"})=>{bo(e).forEach(i=>{let o=i.split(n),s=o.length,u=o[s-1],c=u==="*"?"*":Kn(u);if(!Me[c])return;t||(t=Hn());let l=s>1?fo(ft,o):[],d=[];Me[c]=Me[c].filter(m=>{let x=(a?m.method===a:!0)&&m.scope===t&&Yl(m.mods,l);return x&&d.push(m.element),!x}),d.forEach(m=>vo(m))})};function Jl(e,t,a,n){if(t.element!==n)return;let r;if(t.scope===a||t.scope==="all"){r=t.mods.length>0;for(let i in Ue)Object.prototype.hasOwnProperty.call(Ue,i)&&(!Ue[i]&&t.mods.indexOf(+i)>-1||Ue[i]&&t.mods.indexOf(+i)===-1)&&(r=!1);(t.mods.length===0&&!Ue[16]&&!Ue[18]&&!Ue[17]&&!Ue[91]||r||t.shortcut==="*")&&(t.keys=[],t.keys=t.keys.concat(Ie),t.method(e,t)===!1&&(e.preventDefault?e.preventDefault():e.returnValue=!1,e.stopPropagation&&e.stopPropagation(),e.cancelBubble&&(e.cancelBubble=!0)))}}function Zl(e,t){let a=Me["*"],n=e.keyCode||e.which||e.charCode;if(!da.filter.call(this,e))return;if((n===93||n===224)&&(n=91),Ie.indexOf(n)===-1&&n!==229&&Ie.push(n),["metaKey","ctrlKey","altKey","shiftKey"].forEach(s=>{let u=on[s];e[s]&&Ie.indexOf(u)===-1?Ie.push(u):!e[s]&&Ie.indexOf(u)>-1?Ie.splice(Ie.indexOf(u),1):s==="metaKey"&&e[s]&&(Ie=Ie.filter(c=>c in on||c===n))}),n in Ue){Ue[n]=!0;for(let s in ft)if(Object.prototype.hasOwnProperty.call(ft,s)){let u=on[ft[s]];da[s]=e[u]}if(!a)return}for(let s in Ue)Object.prototype.hasOwnProperty.call(Ue,s)&&(Ue[s]=e[on[s]]);e.getModifierState&&!(e.altKey&&!e.ctrlKey)&&e.getModifierState("AltGraph")&&(Ie.indexOf(17)===-1&&Ie.push(17),Ie.indexOf(18)===-1&&Ie.push(18),Ue[17]=!0,Ue[18]=!0);let r=Hn();if(a)for(let s=0;s<a.length;s++)a[s].scope===r&&(e.type==="keydown"&&a[s].keydown||e.type==="keyup"&&a[s].keyup)&&Jl(e,a[s],r,t);if(!(n in Me))return;let i=Me[n],o=i.length;for(let s=0;s<o;s++)if((e.type==="keydown"&&i[s].keydown||e.type==="keyup"&&i[s].keyup)&&i[s].key){let u=i[s],{splitKey:c}=u,l=u.key.split(c),d=[];for(let m=0;m<l.length;m++)d.push(Kn(l[m]));d.sort().join("")===Ie.sort().join("")&&Jl(e,u,r,t)}}function da(e,t,a){Ie=[];let n=bo(e),r=[],i="all",o=document,s=0,u=!1,c=!0,l="+",d=!1,m=!1;for(a===void 0&&typeof t=="function"&&(a=t),Object.prototype.toString.call(t)==="[object Object]"&&(t.scope&&(i=t.scope),t.element&&(o=t.element),t.keyup&&(u=t.keyup),t.keydown!==void 0&&(c=t.keydown),t.capture!==void 0&&(d=t.capture),typeof t.splitKey=="string"&&(l=t.splitKey),t.single===!0&&(m=!0)),typeof t=="string"&&(i=t),m&&ec(e,i);s<n.length;s++)e=n[s].split(l),r=[],e.length>1&&(r=fo(ft,e)),e=e[e.length-1],e=e==="*"?"*":Kn(e),e in Me||(Me[e]=[]),Me[e].push({keyup:u,keydown:c,scope:i,mods:r,shortcut:n[s],method:a,key:n[s],splitKey:l,element:o});if(typeof o<"u"&&window){if(!$t.has(o)){let p=(A=window.event)=>Zl(A,o),x=(A=window.event)=>{Zl(A,o),lh(A)};$t.set(o,{keydownListener:p,keyupListenr:x,capture:d}),Wr(o,"keydown",p,d),Wr(o,"keyup",x,d)}if(!Gn){let p=()=>{Ie=[]};Gn={listener:p,capture:d},Wr(window,"focus",p,d)}}}function ch(e,t="all"){Object.keys(Me).forEach(a=>{Me[a].filter(r=>r.scope===t&&r.shortcut===e).forEach(r=>{r&&r.method&&r.method()})})}function vo(e){let t=Object.values(Me).flat();if(t.findIndex(({element:n})=>n===e)<0){let{keydownListener:n,keyupListenr:r,capture:i}=$t.get(e)||{};n&&r&&(nn(e,"keyup",r,i),nn(e,"keydown",n,i),$t.delete(e))}if((t.length<=0||$t.size<=0)&&(Object.keys($t).forEach(r=>{let{keydownListener:i,keyupListenr:o,capture:s}=$t.get(r)||{};i&&o&&(nn(r,"keyup",o,s),nn(r,"keydown",i,s),$t.delete(r))}),$t.clear(),Object.keys(Me).forEach(r=>delete Me[r]),Gn)){let{listener:r,capture:i}=Gn;nn(window,"focus",r,i),Gn=null}}var yo={getPressedKeyString:rh,setScope:$l,getScope:Hn,deleteScope:uh,getPressedKeyCodes:nh,getAllKeyCodes:ih,isPressed:sh,filter:oh,trigger:ch,unbind:ec,keyMap:rn,modifier:ft,modifierMap:on};for(let e in yo)Object.prototype.hasOwnProperty.call(yo,e)&&(da[e]=yo[e]);if(typeof window<"u"){let e=window.hotkeys;da.noConflict=t=>(t&&window.hotkeys===da&&(window.hotkeys=e),da),window.hotkeys=da}var dh={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},ac={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},je={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},$e={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},ma={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"},D=class sn{static getFirstMatch(t,a){let n=a.match(t);return n&&n.length>0&&n[1]||""}static getSecondMatch(t,a){let n=a.match(t);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(t,a,n){if(t.test(a))return n}static getWindowsVersionName(t){switch(t){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(t){let a=t.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(a.push(0),a[0]===10)switch(a[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(t){let a=t.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(a.push(0),!(a[0]===1&&a[1]<5)){if(a[0]===1&&a[1]<6)return"Cupcake";if(a[0]===1&&a[1]>=6)return"Donut";if(a[0]===2&&a[1]<2)return"Eclair";if(a[0]===2&&a[1]===2)return"Froyo";if(a[0]===2&&a[1]>2)return"Gingerbread";if(a[0]===3)return"Honeycomb";if(a[0]===4&&a[1]<1)return"Ice Cream Sandwich";if(a[0]===4&&a[1]<4)return"Jelly Bean";if(a[0]===4&&a[1]>=4)return"KitKat";if(a[0]===5)return"Lollipop";if(a[0]===6)return"Marshmallow";if(a[0]===7)return"Nougat";if(a[0]===8)return"Oreo";if(a[0]===9)return"Pie"}}static getVersionPrecision(t){return t.split(".").length}static compareVersions(t,a,n=!1){let r=sn.getVersionPrecision(t),i=sn.getVersionPrecision(a),o=Math.max(r,i),s=0,u=sn.map([t,a],c=>{let l=o-sn.getVersionPrecision(c),d=c+new Array(l+1).join(".0");return sn.map(d.split("."),m=>new Array(20-m.length).join("0")+m).reverse()});for(n&&(s=o-Math.min(r,i)),o-=1;o>=s;){if(u[0][o]>u[1][o])return 1;if(u[0][o]===u[1][o]){if(o===s)return 0;o-=1}else if(u[0][o]<u[1][o])return-1}}static map(t,a){let n=[],r;if(Array.prototype.map)return Array.prototype.map.call(t,a);for(r=0;r<t.length;r+=1)n.push(a(t[r]));return n}static find(t,a){let n,r;if(Array.prototype.find)return Array.prototype.find.call(t,a);for(n=0,r=t.length;n<r;n+=1){let i=t[n];if(a(i,n))return i}}static assign(t,...a){let n=t,r,i;if(Object.assign)return Object.assign(t,...a);for(r=0,i=a.length;r<i;r+=1){let o=a[r];typeof o=="object"&&o!==null&&Object.keys(o).forEach(s=>{n[s]=o[s]})}return t}static getBrowserAlias(t){return dh[t]}static getBrowserTypeByAlias(t){return ac[t]||""}},Te=/version\/(\d+(\.?_?\d+)+)/i,mh=[{test:[/googlebot/i],describe(e){let t={name:"Googlebot"},a=D.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/opera/i],describe(e){let t={name:"Opera"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/opr\/|opios/i],describe(e){let t={name:"Opera"},a=D.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/SamsungBrowser/i],describe(e){let t={name:"Samsung Internet for Android"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/Whale/i],describe(e){let t={name:"NAVER Whale Browser"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/MZBrowser/i],describe(e){let t={name:"MZ Browser"},a=D.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/focus/i],describe(e){let t={name:"Focus"},a=D.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/swing/i],describe(e){let t={name:"Swing"},a=D.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/coast/i],describe(e){let t={name:"Opera Coast"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){let t={name:"Opera Touch"},a=D.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/yabrowser/i],describe(e){let t={name:"Yandex Browser"},a=D.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/ucbrowser/i],describe(e){let t={name:"UC Browser"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/Maxthon|mxios/i],describe(e){let t={name:"Maxthon"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/epiphany/i],describe(e){let t={name:"Epiphany"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/puffin/i],describe(e){let t={name:"Puffin"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/sleipnir/i],describe(e){let t={name:"Sleipnir"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/k-meleon/i],describe(e){let t={name:"K-Meleon"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/micromessenger/i],describe(e){let t={name:"WeChat"},a=D.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/qqbrowser/i],describe(e){let t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},a=D.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/msie|trident/i],describe(e){let t={name:"Internet Explorer"},a=D.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/\sedg\//i],describe(e){let t={name:"Microsoft Edge"},a=D.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/edg([ea]|ios)/i],describe(e){let t={name:"Microsoft Edge"},a=D.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/vivaldi/i],describe(e){let t={name:"Vivaldi"},a=D.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/seamonkey/i],describe(e){let t={name:"SeaMonkey"},a=D.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/sailfish/i],describe(e){let t={name:"Sailfish"},a=D.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return a&&(t.version=a),t}},{test:[/silk/i],describe(e){let t={name:"Amazon Silk"},a=D.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/phantom/i],describe(e){let t={name:"PhantomJS"},a=D.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/slimerjs/i],describe(e){let t={name:"SlimerJS"},a=D.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t={name:"BlackBerry"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/(web|hpw)[o0]s/i],describe(e){let t={name:"WebOS Browser"},a=D.getFirstMatch(Te,e)||D.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/bada/i],describe(e){let t={name:"Bada"},a=D.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/tizen/i],describe(e){let t={name:"Tizen"},a=D.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/qupzilla/i],describe(e){let t={name:"QupZilla"},a=D.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){let t={name:"Firefox"},a=D.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/electron/i],describe(e){let t={name:"Electron"},a=D.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/MiuiBrowser/i],describe(e){let t={name:"Miui"},a=D.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/chromium/i],describe(e){let t={name:"Chromium"},a=D.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/chrome|crios|crmo/i],describe(e){let t={name:"Chrome"},a=D.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/GSA/i],describe(e){let t={name:"Google Search"},a=D.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test(e){let t=!e.test(/like android/i),a=e.test(/android/i);return t&&a},describe(e){let t={name:"Android Browser"},a=D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/playstation 4/i],describe(e){let t={name:"PlayStation 4"},a=D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/safari|applewebkit/i],describe(e){let t={name:"Safari"},a=D.getFirstMatch(Te,e);return a&&(t.version=a),t}},{test:[/.*/i],describe(e){let t=/^(.*)\/(.*) /,a=/^(.*)\/(.*)[ \t]\((.*)/,n=e.search("\\(")!==-1?a:t;return{name:D.getFirstMatch(n,e),version:D.getSecondMatch(n,e)}}}],ph=mh,gh=[{test:[/Roku\/DVP/],describe(e){let t=D.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:$e.Roku,version:t}}},{test:[/windows phone/i],describe(e){let t=D.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:$e.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){let t=D.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),a=D.getWindowsVersionName(t);return{name:$e.Windows,version:t,versionName:a}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){let t={name:$e.iOS},a=D.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return a&&(t.version=a),t}},{test:[/macintosh/i],describe(e){let t=D.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),a=D.getMacOSVersionName(t),n={name:$e.MacOS,version:t};return a&&(n.versionName=a),n}},{test:[/(ipod|iphone|ipad)/i],describe(e){let t=D.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:$e.iOS,version:t}}},{test(e){let t=!e.test(/like android/i),a=e.test(/android/i);return t&&a},describe(e){let t=D.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),a=D.getAndroidVersionName(t),n={name:$e.Android,version:t};return a&&(n.versionName=a),n}},{test:[/(web|hpw)[o0]s/i],describe(e){let t=D.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),a={name:$e.WebOS};return t&&t.length&&(a.version=t),a}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t=D.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||D.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||D.getFirstMatch(/\bbb(\d+)/i,e);return{name:$e.BlackBerry,version:t}}},{test:[/bada/i],describe(e){let t=D.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:$e.Bada,version:t}}},{test:[/tizen/i],describe(e){let t=D.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:$e.Tizen,version:t}}},{test:[/linux/i],describe(){return{name:$e.Linux}}},{test:[/CrOS/],describe(){return{name:$e.ChromeOS}}},{test:[/PlayStation 4/],describe(e){let t=D.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:$e.PlayStation4,version:t}}}],hh=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(e){let t=D.getFirstMatch(/(can-l01)/i,e)&&"Nova",a={type:je.mobile,vendor:"Huawei"};return t&&(a.model=t),a}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:je.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:je.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:je.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:je.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:je.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:je.tablet}}},{test(e){let t=e.test(/ipod|iphone/i),a=e.test(/like (ipod|iphone)/i);return t&&!a},describe(e){let t=D.getFirstMatch(/(ipod|iphone)/i,e);return{type:je.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:je.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:je.mobile}}},{test(e){return e.getBrowserName(!0)==="blackberry"},describe(){return{type:je.mobile,vendor:"BlackBerry"}}},{test(e){return e.getBrowserName(!0)==="bada"},describe(){return{type:je.mobile}}},{test(e){return e.getBrowserName()==="windows phone"},describe(){return{type:je.mobile,vendor:"Microsoft"}}},{test(e){let t=Number(String(e.getOSVersion()).split(".")[0]);return e.getOSName(!0)==="android"&&t>=3},describe(){return{type:je.tablet}}},{test(e){return e.getOSName(!0)==="android"},describe(){return{type:je.mobile}}},{test(e){return e.getOSName(!0)==="macos"},describe(){return{type:je.desktop,vendor:"Apple"}}},{test(e){return e.getOSName(!0)==="windows"},describe(){return{type:je.desktop}}},{test(e){return e.getOSName(!0)==="linux"},describe(){return{type:je.desktop}}},{test(e){return e.getOSName(!0)==="playstation 4"},describe(){return{type:je.tv}}},{test(e){return e.getOSName(!0)==="roku"},describe(){return{type:je.tv}}}],fh=[{test(e){return e.getBrowserName(!0)==="microsoft edge"},describe(e){if(/\sedg\//i.test(e))return{name:ma.Blink};let t=D.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:ma.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){let t={name:ma.Trident},a=D.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test(e){return e.test(/presto/i)},describe(e){let t={name:ma.Presto},a=D.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test(e){let t=e.test(/gecko/i),a=e.test(/like gecko/i);return t&&!a},describe(e){let t={name:ma.Gecko},a=D.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:ma.Blink}}},{test:[/(apple)?webkit/i],describe(e){let t={name:ma.WebKit},a=D.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return a&&(t.version=a),t}}],bh=class{constructor(e,t=!1){if(e==null||e==="")throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},t!==!0&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};let e=D.find(ph,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};let e=D.find(gh,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){let{name:t}=this.getOS();return e?String(t).toLowerCase()||"":t||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){let{type:t}=this.getPlatform();return e?String(t).toLowerCase()||"":t||""}parsePlatform(){this.parsedResult.platform={};let e=D.find(hh,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};let e=D.find(fh,t=>{if(typeof t.test=="function")return t.test(this);if(t.test instanceof Array)return t.test.some(a=>this.test(a));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return D.assign({},this.parsedResult)}satisfies(e){let t={},a=0,n={},r=0;if(Object.keys(e).forEach(i=>{let o=e[i];typeof o=="string"?(n[i]=o,r+=1):typeof o=="object"&&(t[i]=o,a+=1)}),a>0){let i=Object.keys(t),o=D.find(i,u=>this.isOS(u));if(o){let u=this.satisfies(t[o]);if(u!==void 0)return u}let s=D.find(i,u=>this.isPlatform(u));if(s){let u=this.satisfies(t[s]);if(u!==void 0)return u}}if(r>0){let i=Object.keys(n),o=D.find(i,s=>this.isBrowser(s,!0));if(o!==void 0)return this.compareVersion(n[o])}}isBrowser(e,t=!1){let a=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),r=D.getBrowserTypeByAlias(n);return t&&r&&(n=r.toLowerCase()),n===a}compareVersion(e){let t=[0],a=e,n=!1,r=this.getBrowserVersion();if(typeof r=="string")return e[0]===">"||e[0]==="<"?(a=e.substr(1),e[1]==="="?(n=!0,a=e.substr(2)):t=[],e[0]===">"?t.push(1):t.push(-1)):e[0]==="="?a=e.substr(1):e[0]==="~"&&(n=!0,a=e.substr(1)),t.indexOf(D.compareVersions(r,a,n))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,t=!1){return this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(t=>this.is(t))}},tc=bh,yh=class{static getParser(e,t=!1){if(typeof e!="string")throw new Error("UserAgent should be a string");return new tc(e,t)}static parse(e){return new tc(e).getResult()}static get BROWSER_MAP(){return ac}static get ENGINE_MAP(){return ma}static get OS_MAP(){return $e}static get PLATFORMS_MAP(){return je}},un=yh;var{entries:cc,setPrototypeOf:nc,isFrozen:vh,getPrototypeOf:xh,getOwnPropertyDescriptor:wh}=Object,{freeze:et,seal:At,create:Ah}=Object,{apply:ko,construct:To}=typeof Reflect<"u"&&Reflect;ko||(ko=function(e,t,a){return e.apply(t,a)});et||(et=function(e){return e});At||(At=function(e){return e});To||(To=function(e,t){return new e(...t)});var Eh=bt(Array.prototype.forEach),rc=bt(Array.prototype.pop),Wn=bt(Array.prototype.push),Jr=bt(String.prototype.toLowerCase),xo=bt(String.prototype.toString),Sh=bt(String.prototype.match),wt=bt(String.prototype.replace),kh=bt(String.prototype.indexOf),Th=bt(String.prototype.trim),lt=bt(RegExp.prototype.test),Qn=Dh(TypeError);function bt(e){return function(t){for(var a=arguments.length,n=new Array(a>1?a-1:0),r=1;r<a;r++)n[r-1]=arguments[r];return ko(e,t,n)}}function Dh(e){return function(){for(var t=arguments.length,a=new Array(t),n=0;n<t;n++)a[n]=arguments[n];return To(e,a)}}function le(e,t,a){var n;a=(n=a)!==null&&n!==void 0?n:Jr,nc&&nc(e,null);let r=t.length;for(;r--;){let i=t[r];if(typeof i=="string"){let o=a(i);o!==i&&(vh(t)||(t[r]=o),i=o)}e[i]=!0}return e}function ln(e){let t=Ah(null);for(let[a,n]of cc(e))t[a]=n;return t}function Vr(e,t){for(;e!==null;){let n=wh(e,t);if(n){if(n.get)return bt(n.get);if(typeof n.value=="function")return bt(n.value)}e=xh(e)}function a(n){return null}return a}var ic=et(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),wo=et(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ao=et(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ch=et(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Eo=et(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Mh=et(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),oc=et(["#text"]),sc=et(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),So=et(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direc/* REMOVED_COMMON_BLOCK_2467 */length","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),uc=et(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Yr=et(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Ih=At(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ph=At(/<%[\w\W]*|[\w\W]*%>/gm),Fh=At(/\${[\w\W]*}/gm),Bh=At(/^data-[\-\w.\u00B7-\uFFFF]/),Rh=At(/^aria-[\-\w]+$/),dc=At(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),_h=At(/^(?:\w+script|data):/i),jh=At(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),mc=At(/^html$/i),lc=Object.freeze({__proto__:null,MUSTACHE_EXPR:Ih,ERB_EXPR:Ph,TMPLIT_EXPR:Fh,DATA_ATTR:Bh,ARIA_ATTR:Rh,IS_ALLOWED_URI:dc,IS_SCRIPT_OR_DATA:_h,ATTR_WHITESPACE:jh,DOCTYPE_NAME:mc}),Lh=()=>typeof window>"u"?null:window,Nh=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let a=null,n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(a=t.getAttribute(n));let r="dompurify"+(a?"#"+a:"");try{return e.createPolicy(r,{createHTML(i){return i},createScriptURL(i){return i}})}catch{return null}};function pc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Lh(),t=f=>pc(f);if(t.version="3.0.3",t.removed=[],!e||!e.document||e.document.nodeType!==9)return t.isSupported=!1,t;let a=e.document,n=a.currentScript,{document:r}=e,{DocumentFragment:i,HTMLTemplateElement:o,Node:s,Element:u,NodeFilter:c,NamedNodeMap:l=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:d,DOMParser:m,trustedTypes:p}=e,x=u.prototype,A=Vr(x,"cloneNode"),S=Vr(x,"nextSibling"),g=Vr(x,"childNodes"),M=Vr(x,"parentNode");if(typeof o=="function"){let f=r.createElement("template");f.content&&f.content.ownerDocument&&(r=f.content.ownerDocument)}let h,k="",{implementation:L,createNodeIterator:E,createDocumentFragment:v,getElementsByTagName:w}=r,{importNode:y}=a,R={};t.isSupported=typeof cc=="function"&&typeof M=="function"&&L&&L.createHTMLDocument!==void 0;let{MUSTACHE_EXPR:F,ERB_EXPR:C,TMPLIT_EXPR:j,DATA_ATTR:Y,ARIA_ATTR:Z,IS_SCRIPT_OR_DATA:ve,ATTR_WHITESPACE:Se}=lc,{IS_ALLOWED_URI:H}=lc,oe=null,Fe=le({},[...ic,...wo,...Ao,...Eo,...oc]),he=null,T=le({},[...sc,...So,...uc,...Yr]),I=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),U=null,ne=null,Q=!0,ce=!0,fe=!1,_=!0,N=!1,V=!1,ae=!1,$=!1,q=!1,O=!1,ue=!1,me=!0,J=!1,Ee="user-content-",Re=!0,se=!1,Ce={},W=null,He=le({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Bn=null,Lt=le({},["audio","video","img","source","image","track"]),Ea=null,Va=le({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Qe="http://www.w3.org/1998/Math/MathML",xt="http://www.w3.org/2000/svg",Ve="http://www.w3.org/1999/xhtml",it=Ve,qt=!1,Gt=null,Sa=le({},[Qe,xt,Ve],xo),Je,Ht=["application/xhtml+xml","text/html"],Be="text/html",we,ot=null,Sp=r.createElement("form"),xu=function(f){return f instanceof RegExp||f instanceof Function},Li=function(f){if(!(ot&&ot===f)){if((!f||typeof f!="object")&&(f={}),f=ln(f),Je=Ht.indexOf(f.PARSER_MEDIA_TYPE)===-1?Je=Be:Je=f.PARSER_MEDIA_TYPE,we=Je==="application/xhtml+xml"?xo:Jr,oe="ALLOWED_TAGS"in f?le({},f.ALLOWED_TAGS,we):Fe,he="ALLOWED_ATTR"in f?le({},f.ALLOWED_ATTR,we):T,Gt="ALLOWED_NAMESPACES"in f?le({},f.ALLOWED_NAMESPACES,xo):Sa,Ea="ADD_URI_SAFE_ATTR"in f?le(ln(Va),f.ADD_URI_SAFE_ATTR,we):Va,Bn="ADD_DATA_URI_TAGS"in f?le(ln(Lt),f.ADD_DATA_URI_TAGS,we):Lt,W="FORBID_CONTENTS"in f?le({},f.FORBID_CONTENTS,we):He,U="FORBID_TAGS"in f?le({},f.FORBID_TAGS,we):{},ne="FORBID_ATTR"in f?le({},f.FORBID_ATTR,we):{},Ce="USE_PROFILES"in f?f.USE_PROFILES:!1,Q=f.ALLOW_ARIA_ATTR!==!1,ce=f.ALLOW_DATA_ATTR!==!1,fe=f.ALLOW_UNKNOWN_PROTOCOLS||!1,_=f.ALLOW_SELF_CLOSE_IN_ATTR!==!1,N=f.SAFE_FOR_TEMPLATES||!1,V=f.WHOLE_DOCUMENT||!1,q=f.RETURN_DOM||!1,O=f.RETURN_DOM_FRAGMENT||!1,ue=f.RETURN_TRUSTED_TYPE||!1,$=f.FORCE_BODY||!1,me=f.SANITIZE_DOM!==!1,J=f.SANITIZE_NAMED_PROPS||!1,Re=f.KEEP_CONTENT!==!1,se=f.IN_PLACE||!1,H=f.ALLOWED_URI_REGEXP||dc,it=f.NAMESPACE||Ve,I=f.CUSTOM_ELEMENT_HANDLING||{},f.CUSTOM_ELEMENT_HANDLING&&xu(f.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(I.tagNameCheck=f.CUSTOM_ELEMENT_HANDLING.tagNameCheck),f.CUSTOM_ELEMENT_HANDLING&&xu(f.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(I.attributeNameCheck=f.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),f.CUSTOM_ELEMENT_HANDLING&&typeof f.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(I.allowCustomizedBuiltInElements=f.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),N&&(ce=!1),O&&(q=!0),Ce&&(oe=le({},[...oc]),he=[],Ce.html===!0&&(le(oe,ic),le(he,sc)),Ce.svg===!0&&(le(oe,wo),le(he,So),le(he,Yr)),Ce.svgFilters===!0&&(le(oe,Ao),le(he,So),le(he,Yr)),Ce.mathMl===!0&&(le(oe,Eo),le(he,uc),le(he,Yr))),f.ADD_TAGS&&(oe===Fe&&(oe=ln(oe)),le(oe,f.ADD_TAGS,we)),f.ADD_ATTR&&(he===T&&(he=ln(he)),le(he,f.ADD_ATTR,we)),f.ADD_URI_SAFE_ATTR&&le(Ea,f.ADD_URI_SAFE_ATTR,we),f.FORBID_CONTENTS&&(W===He&&(W=ln(W)),le(W,f.FORBID_CONTENTS,we)),Re&&(oe["#text"]=!0),V&&le(oe,["html","head","body"]),oe.table&&(le(oe,["tbody"]),delete U.tbody),f.TRUSTED_TYPES_POLICY){if(typeof f.TRUSTED_TYPES_POLICY.createHTML!="function")throw Qn('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof f.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Qn('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');h=f.TRUSTED_TYPES_POLICY,k=h.createHTML("")}else h===void 0&&(h=Nh(p,n)),h!==null&&typeof k=="string"&&(k=h.createHTML(""));et&&et(f),ot=f}},wu=le({},["mi","mo","mn","ms","mtext"]),Au=le({},["foreignobject","desc","title","annotation-xml"]),kp=le({},["title","style","font","a","script"]),kr=le({},wo);le(kr,Ao),le(kr,Ch);let Ni=le({},Eo);le(Ni,Mh);let Tp=function(f){let G=M(f);(!G||!G.tagName)&&(G={namespaceURI:it,tagName:"template"});let B=Jr(f.tagName),ye=Jr(G.tagName);return Gt[f.namespaceURI]?f.namespaceURI===xt?G.namespaceURI===Ve?B==="svg":G.namespaceURI===Qe?B==="svg"&&(ye==="annotation-xml"||wu[ye]):!!kr[B]:f.namespaceURI===Qe?G.namespaceURI===Ve?B==="math":G.namespaceURI===xt?B==="math"&&Au[ye]:!!Ni[B]:f.namespaceURI===Ve?G.namespaceURI===xt&&!Au[ye]||G.namespaceURI===Qe&&!wu[ye]?!1:!Ni[B]&&(kp[B]||!kr[B]):!!(Je==="application/xhtml+xml"&&Gt[f.namespaceURI]):!1},Ya=function(f){Wn(t.removed,{element:f});try{f.parentNode.removeChild(f)}catch{f.remove()}},Oi=function(f,G){try{Wn(t.removed,{attribute:G.getAttributeNode(f),from:G})}catch{Wn(t.removed,{attribute:null,from:G})}if(G.removeAttribute(f),f==="is"&&!he[f])if(q||O)try{Ya(G)}catch{}else try{G.setAttribute(f,"")}catch{}},Eu=function(f){let G,B;if($)f="<remove></remove>"+f;else{let Ze=Sh(f,/^[\r\n\t ]+/);B=Ze&&Ze[0]}Je==="application/xhtml+xml"&&it===Ve&&(f='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+f+"</body></html>");let ye=h?h.createHTML(f):f;if(it===Ve)try{G=new m().parseFromString(ye,Je)}catch{}if(!G||!G.documentElement){G=L.createDocument(it,"template",null);try{G.documentElement.innerHTML=qt?k:ye}catch{}}let Oe=G.body||G.documentElement;return f&&B&&Oe.insertBefore(r.createTextNode(B),Oe.childNodes[0]||null),it===Ve?w.call(G,V?"html":"body")[0]:V?G.documentElement:Oe},Su=function(f){return E.call(f.ownerDocument||f,f,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Dp=function(f){return f instanceof d&&(typeof f.nodeName!="string"||typeof f.textContent!="string"||typeof f.removeChild!="function"||!(f.attributes instanceof l)||typeof f.removeAttribute!="function"||typeof f.setAttribute!="function"||typeof f.namespaceURI!="string"||typeof f.insertBefore!="function"||typeof f.hasChildNodes!="function")},Tr=function(f){return typeof s=="object"?f instanceof s:f&&typeof f=="object"&&typeof f.nodeType=="number"&&typeof f.nodeName=="string"},Kt=function(f,G,B){R[f]&&Eh(R[f],ye=>{ye.call(t,G,B,ot)})},ku=function(f){let G;if(Kt("beforeSanitizeElements",f,null),Dp(f))return Ya(f),!0;let B=we(f.nodeName);if(Kt("uponSanitizeElement",f,{tagName:B,allowedTags:oe}),f.hasChildNodes()&&!Tr(f.firstElementChild)&&(!Tr(f.content)||!Tr(f.content.firstElementChild))&&lt(/<[/\w]/g,f.innerHTML)&&lt(/<[/\w]/g,f.textContent))return Ya(f),!0;if(!oe[B]||U[B]){if(!U[B]&&Du(B)&&(I.tagNameCheck instanceof RegExp&&lt(I.tagNameCheck,B)||I.tagNameCheck instanceof Function&&I.tagNameCheck(B)))return!1;if(Re&&!W[B]){let ye=M(f)||f.parentNode,Oe=g(f)||f.childNodes;if(Oe&&ye){let Ze=Oe.length;for(let nt=Ze-1;nt>=0;--nt)ye.insertBefore(A(Oe[nt],!0),S(f))}}return Ya(f),!0}return f instanceof u&&!Tp(f)||(B==="noscript"||B==="noembed")&&lt(/<\/no(script|embed)/i,f.innerHTML)?(Ya(f),!0):(N&&f.nodeType===3&&(G=f.textContent,G=wt(G,F," "),G=wt(G,C," "),G=wt(G,j," "),f.textContent!==G&&(Wn(t.removed,{element:f.cloneNode()}),f.textContent=G)),Kt("afterSanitizeElements",f,null),!1)},Tu=function(f,G,B){if(me&&(G==="id"||G==="name")&&(B in r||B in Sp))return!1;if(!(ce&&!ne[G]&&lt(Y,G))&&!(Q&&lt(Z,G))){if(!he[G]||ne[G]){if(!(Du(f)&&(I.tagNameCheck instanceof RegExp&&lt(I.tagNameCheck,f)||I.tagNameCheck instanceof Function&&I.tagNameCheck(f))&&(I.attributeNameCheck instanceof RegExp&&lt(I.attributeNameCheck,G)||I.attributeNameCheck instanceof Function&&I.attributeNameCheck(G))||G==="is"&&I.allowCustomizedBuiltInElements&&(I.tagNameCheck instanceof RegExp&&lt(I.tagNameCheck,B)||I.tagNameCheck instanceof Function&&I.tagNameCheck(B))))return!1}else if(!Ea[G]&&!lt(H,wt(B,Se,""))&&!((G==="src"||G==="xlink:href"||G==="href")&&f!=="script"&&kh(B,"data:")===0&&Bn[f])&&!(fe&&!lt(ve,wt(B,Se,"")))&&B)return!1}return!0},Du=function(f){return f.indexOf("-")>0},Cu=function(f){let G,B,ye,Oe;Kt("beforeSanitizeAttributes",f,null);let{attributes:Ze}=f;if(!Ze)return;let nt={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:he};for(Oe=Ze.length;Oe--;){G=Ze[Oe];let{name:ze,namespaceURI:Ja}=G;if(B=ze==="value"?G.value:Th(G.value),ye=we(ze),nt.attrName=ye,nt.attrValue=B,nt.keepAttr=!0,nt.forceKeepAttr=void 0,Kt("uponSanitizeAttribute",f,nt),B=nt.attrValue,nt.forceKeepAttr||(Oi(ze,f),!nt.keepAttr))continue;if(!_&&lt(/\/>/i,B)){Oi(ze,f);continue}N&&(B=wt(B,F," "),B=wt(B,C," "),B=wt(B,j," "));let Mu=we(f.nodeName);if(Tu(Mu,ye,B)){if(J&&(ye==="id"||ye==="name")&&(Oi(ze,f),B=Ee+B),h&&typeof p=="object"&&typeof p.getAttributeType=="function"&&!Ja)switch(p.getAttributeType(Mu,ye)){case"TrustedHTML":{B=h.createHTML(B);break}case"TrustedScriptURL":{B=h.createScriptURL(B);break}}try{Ja?f.setAttributeNS(Ja,ze,B):f.setAttribute(ze,B),rc(t.removed)}catch{}}}Kt("afterSanitizeAttributes",f,null)},Cp=function f(G){let B,ye=Su(G);for(Kt("beforeSanitizeShadowDOM",G,null);B=ye.nextNode();)Kt("uponSanitizeShadowNode",B,null),!ku(B)&&(B.content instanceof i&&f(B.content),Cu(B));Kt("afterSanitizeShadowDOM",G,null)};return t.sanitize=function(f){let G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B,ye,Oe,Ze;if(qt=!f,qt&&(f="<!-->"),typeof f!="string"&&!Tr(f))if(typeof f.toString=="function"){if(f=f.toString(),typeof f!="string")throw Qn("dirty is not a string, aborting")}else throw Qn("toString is not a function");if(!t.isSupported)return f;if(ae||Li(G),t.removed=[],typeof f=="string"&&(se=!1),se){if(f.nodeName){let Ja=we(f.nodeName);if(!oe[Ja]||U[Ja])throw Qn("root node is forbidden and cannot be sanitized in-place")}}else if(f instanceof s)B=Eu("<!---->"),ye=B.ownerDocument.importNode(f,!0),ye.nodeType===1&&ye.nodeName==="BODY"||ye.nodeName==="HTML"?B=ye:B.appendChild(ye);else{if(!q&&!N&&!V&&f.indexOf("<")===-1)return h&&ue?h.createHTML(f):f;if(B=Eu(f),!B)return q?null:ue?k:""}B&&$&&Ya(B.firstChild);let nt=Su(se?f:B);for(;Oe=nt.nextNode();)ku(Oe)||(Oe.content instanceof i&&Cp(Oe.content),Cu(Oe));if(se)return f;if(q){if(O)for(Ze=v.call(B.ownerDocument);B.firstChild;)Ze.appendChild(B.firstChild);else Ze=B;return(he.shadowroot||he.shadowrootmod)&&(Ze=y.call(a,Ze,!0)),Ze}let ze=V?B.outerHTML:B.innerHTML;return V&&oe["!doctype"]&&B.ownerDocument&&B.ownerDocument.doctype&&B.ownerDocument.doctype.name&&lt(mc,B.ownerDocument.doctype.name)&&(ze="<!DOCTYPE "+B.ownerDocument.doctype.name+`>
`+ze),N&&(ze=wt(ze,F," "),ze=wt(ze,C," "),ze=wt(ze,j," ")),h&&ue?h.createHTML(ze):ze},t.setConfig=function(f){Li(f),ae=!0},t.clearConfig=function(){ot=null,ae=!1},t.isValidAttribute=function(f,G,B){ot||Li({});let ye=we(f),Oe=we(G);return Tu(ye,Oe,B)},t.addHook=function(f,G){typeof G=="function"&&(R[f]=R[f]||[],Wn(R[f],G))},t.removeHook=function(f){if(R[f])return rc(R[f])},t.removeHooks=function(f){R[f]&&(R[f]=[])},t.removeAllHooks=function(){R={}},t}var Do=pc();var Oh=Object.create,Mo=Object.defineProperty,zh=Object.getOwnPropertyDescriptor,Uh=Object.getOwnPropertyNames,qh=Object.getPrototypeOf,Gh=Object.prototype.hasOwnProperty,Io=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Hh=(e,t)=>{for(var a in t)Mo(e,a,{get:t[a],enumerable:!0})},Co=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Uh(t))!Gh.call(e,r)&&r!==a&&Mo(e,r,{get:()=>t[r],enumerable:!(n=zh(t,r))||n.enumerable});return e},Kh=(e,t,a)=>(Co(e,t,"default"),a&&Co(a,t,"default")),hc=(e,t,a)=>(a=e!=null?Oh(qh(e)):{},Co(t||!e||!e.__esModule?Mo(a,"default",{value:e,enumerable:!0}):a,e)),Wh=Io((e,t)=>{function a(n,r){if(r&&r.documentElement)n=r,r=arguments[2];else if(!n||!n.documentElement)throw new Error("First argument to Readability constructor should be a document object.");if(r=r||{},this._doc=n,this._docJSDOMParser=this._doc.firstChild.__JSDOMParser__,this._articleTitle=null,this._articleByline=null,this._articleDir=null,this._articleSiteName=null,this._attempts=[],this._debug=!!r.debug,this._maxElemsToParse=r.maxElemsToParse||this.DEFAULT_MAX_ELEMS_TO_PARSE,this._nbTopCandidates=r.nbTopCandidates||this.DEFAULT_N_TOP_CANDIDATES,this._charThreshold=r.charThreshold||this.DEFAULT_CHAR_THRESHOLD,this._classesToPreserve=this.CLASSES_TO_PRESERVE.concat(r.classesToPreserve||[]),this._keepClasses=!!r.keepClasses,this._serializer=r.serializer||function(i){return i.innerHTML},this._disableJSONLD=!!r.disableJSONLD,this._allowedVideoRegex=r.allowedVideoRegex||this.REGEXPS.videos,this._flags=this.FLAG_STRIP_UNLIKELYS|this.FLAG_WEIGHT_CLASSES|this.FLAG_CLEAN_CONDITIONALLY,this._debug){let i=function(o){if(o.nodeType==o.TEXT_NODE)return`${o.nodeName} ("${o.textContent}")`;let s=Array.from(o.attributes||[],function(u){return`${u.name}="${u.value}"`}).join(" ");return`<${o.localName} ${s}>`};this.log=function(){if(typeof console<"u")Array.from(arguments,u=>u&&u.nodeType==this.ELEMENT_NODE?i(u):u).unshift("Reader: (Readability)");else if(typeof dump<"u"){var o=Array.prototype.map.call(arguments,function(s){return s&&s.nodeName?i(s):s}).join(" ");dump("Reader: (Readability) "+o+`
`)}}}else this.log=function(){}}a.prototype={FLAG_STRIP_UNLIKELYS:1,FLAG_WEIGHT_CLASSES:2,FLAG_CLEAN_CONDITIONALLY:4,ELEMENT_NODE:1,TEXT_NODE:3,DEFAULT_MAX_ELEMS_TO_PARSE:0,DEFAULT_N_TOP_CANDIDATES:5,DEFAULT_TAGS_TO_SCORE:"section,h2,h3,h4,h5,h6,p,td,pre".toUpperCase().split(","),DEFAULT_CHAR_T/* REMOVED_COMMON_BLOCK_2459 */pacing","frame","hspace","rules","style","valign","vspace"],DEPRECATED_SIZE_ATTRIBUTE_ELEMS:["TABLE","TH","TD","HR","PRE"],PHRASING_ELEMS:["ABBR","AUDIO","B","BDO","BR","BUTTON","CITE","CODE","DATA","DATALIST","DFN","EM","EMBED","I","IMG","INPUT","KBD","LABEL","MARK","MATH","METER","NOSCRIPT","OBJECT","OUTPUT","PROGRESS","Q","RUBY","SAMP","SCRIPT","SELECT","SMALL","SPAN","STRONG","SUB","SUP","TEXTAREA","TIME","VAR","WBR"],CLASSES_TO_PRESERVE:["page"],HTML_ESCAPE_MAP:{lt:"<",gt:">",amp:"&",quot:'"',apos:"'"},_postProcessContent:function(n){this._fixRelativeUris(n),this._simplifyNestedElements(n),this._keepClasses||this._cleanClasses(n)},_removeNodes:function(n,r){if(this._docJSDOMParser&&n._isLiveNodeList)throw new Error("Do not pass live node lists to _removeNodes");for(var i=n.length-1;i>=0;i--){var o=n[i],s=o.parentNode;s&&(!r||r.call(this,o,i,n))&&s.removeChild(o)}},_replaceNodeTags:function(n,r){if(this._docJSDOMParser&&n._isLiveNodeList)throw new Error("Do not pass live node lists to _replaceNodeTags");for(let i of n)this._setNodeTag(i,r)},_forEachNode:function(n,r){Array.prototype.forEach.call(n,r,this)},_findNode:function(n,r){return Array.prototype.find.call(n,r,this)},_someNode:function(n,r){return Array.prototype.some.call(n,r,this)},_everyNode:function(n,r){return Array.prototype.every.call(n,r,this)},_concatNodeLists:function(){var n=Array.prototype.slice,r=n.call(arguments),i=r.map(function(o){return n.call(o)});return Array.prototype.concat.apply([],i)},_getAllNodesWithTag:function(n,r){return n.querySelectorAll?n.querySelectorAll(r.join(",")):[].concat.apply([],r.map(function(i){var o=n.getElementsByTagName(i);return Array.isArray(o)?o:Array.from(o)}))},_cleanClasses:function(n){var r=this._classesToPreserve,i=(n.getAttribute("class")||"").split(/\s+/).filter(function(o){return r.indexOf(o)!=-1}).join(" ");for(i?n.setAttribute("class",i):n.removeAttribute("class"),n=n.firstElementChild;n;n=n.nextElementSibling)this._cleanClasses(n)},_fixRelativeUris:function(n){var r=this._doc.baseURI,i=this._doc.documentURI;function o(c){if(r==i&&c.charAt(0)=="#")return c;try{return new URL(c,r).href}catch{}return c}var s=this._getAllNodesWithTag(n,["a"]);this._forEachNode(s,function(c){var l=c.getAttribute("href");if(l)if(l.indexOf("javascript:")===0)if(c.childNodes.length===1&&c.childNodes[0].nodeType===this.TEXT_NODE){var d=this._doc.createTextNode(c.textContent);c.parentNode.replaceChild(d,c)}else{for(var m=this._doc.createElement("span");c.firstChild;)m.appendChild(c.firstChild);c.parentNode.replaceChild(m,c)}else c.setAttribute("href",o(l))});var u=this._getAllNodesWithTag(n,["img","picture","figure","video","audio","source"]);this._forEachNode(u,function(c){var l=c.getAttribute("src"),d=c.getAttribute("poster"),m=c.getAttribute("srcset");if(l&&c.setAttribute("src",o(l)),d&&c.setAttribute("poster",o(d)),m){var p=m.replace(this.REGEXPS.srcsetUrl,function(x,A,S,g){return o(A)+(S||"")+g});c.setAttribute("srcset",p)}})},_simplifyNestedElements:function(n){for(var r=n;r;){if(r.parentNode&&["DIV","SECTION"].includes(r.tagName)&&!(r.id&&r.id.startsWith("readability"))){if(this._isElementWithoutContent(r)){r=this._removeAndGetNext(r);continue}else if(this._hasSingleTagInsideElement(r,"DIV")||this._hasSingleTagInsideElement(r,"SECTION")){for(var i=r.children[0],o=0;o<r.attributes.length;o++)i.setAttribute(r.attributes[o].name,r.attributes[o].value);r.parentNode.replaceChild(i,r),r=i;continue}}r=this._getNextNode(r)}},_getArticleTitle:function(){var n=this._doc,r="",i="";try{r=i=n.title.trim(),typeof r!="string"&&(r=i=this._getInnerText(n.getElementsByTagName("title")[0]))}catch{}var o=!1;function s(p){return p.split(/\s+/).length}if(/ [\|\-\\\/>»] /.test(r))o=/ [\\\/>»] /.test(r),r=i.replace(/(.*)[\|\-\\\/>»] .*/gi,"$1"),s(r)<3&&(r=i.replace(/[^\|\-\\\/>»]*[\|\-\\\/>»](.*)/gi,"$1"));else if(r.indexOf(": ")!==-1){var u=this._concatNodeLists(n.getElementsByTagName("h1"),n.getElementsByTagName("h2")),c=r.trim(),l=this._someNode(u,function(p){return p.textContent.trim()===c});l||(r=i.substring(i.lastIndexOf(":")+1),s(r)<3?r=i.substring(i.indexOf(":")+1):s(i.substr(0,i.indexOf(":")))>5&&(r=i))}else if(r.length>150||r.length<15){var d=n.getElementsByTagName("h1");d.length===1&&(r=this._getInnerText(d[0]))}r=r.trim().replace(this.REGEXPS.normalize," ");var m=s(r);return m<=4&&(!o||m!=s(i.replace(/[\|\-\\\/>»]+/g,""))-1)&&(r=i),r},_prepDocument:function(){var n=this._doc;this._removeNodes(this._getAllNodesWithTag(n,["style"])),n.body&&this._replaceBrs(n.body),this._replaceNodeTags(this._getAllNodesWithTag(n,["font"]),"SPAN")},_nextNode:function(n){for(var r=n;r&&r.nodeType!=this.ELEMENT_NODE&&this.REGEXPS.whitespace.test(r.textContent);)r=r.nextSibling;return r},_replaceBrs:function(n){this._forEachNode(this._getAllNodesWithTag(n,["br"]),function(r){for(var i=r.nextSibling,o=!1;(i=this._nextNode(i))&&i.tagName=="BR";){o=!0;var s=i.nextSibling;i.parentNode.removeChild(i),i=s}if(o){var u=this._doc.createElement("p");for(r.parentNode.replaceChild(u,r),i=u.nextSibling;i;){if(i.tagName=="BR"){var c=this._nextNode(i.nextSibling);if(c&&c.tagName=="BR")break}if(!this._isPhrasingContent(i))break;var l=i.nextSibling;u.appendChild(i),i=l}for(;u.lastChild&&this._isWhitespace(u.lastChild);)u.removeChild(u.lastChild);u.parentNode.tagName==="P"&&this._setNodeTag(u.parentNode,"DIV")}})},_setNodeTag:function(n,r){if(this.log("_setNodeTag",n,r),this._docJSDOMParser)return n.localName=r.toLowerCase(),n.tagName=r.toUpperCase(),n;for(var i=n.ownerDocument.createElement(r);n.firstChild;)i.appendChild(n.firstChild);n.parentNode.replaceChild(i,n),n.readability&&(i.readability=n.readability);for(var o=0;o<n.attributes.length;o++)try{i.setAttribute(n.attributes[o].name,n.attributes[o].value)}catch{}return i},_prepArticle:function(n){this._cleanStyles(n),this._markDataTables(n),this._fixLazyImages(n),this._cleanConditionally(n,"form"),this._cleanConditionally(n,"fieldset"),this._clean(n,"object"),this._clean(n,"embed"),this._clean(n,"footer"),this._clean(n,"link"),this._clean(n,"aside");var r=this.DEFAULT_CHAR_THRESHOLD;this._forEachNode(n.children,function(i){this._cleanMatchedNodes(i,function(o,s){return this.REGEXPS.shareElements.test(s)&&o.textContent.length<r})}),this._clean(n,"iframe"),this._clean(n,"input"),this._clean(n,"textarea"),this._clean(n,"select"),this._clean(n,"button"),this._cleanHeaders(n),this._cleanConditionally(n,"table"),this._cleanConditionally(n,"ul"),this._cleanConditionally(n,"div"),this._replaceNodeTags(this._getAllNodesWithTag(n,["h1"]),"h2"),this._removeNodes(this._getAllNodesWithTag(n,["p"]),function(i){var o=i.getElementsByTagName("img").length,s=i.getElementsByTagName("embed").length,u=i.getElementsByTagName("object").length,c=i.getElementsByTagName("iframe").length,l=o+s+u+c;return l===0&&!this._getInnerText(i,!1)}),this._forEachNode(this._getAllNodesWithTag(n,["br"]),function(i){var o=this._nextNode(i.nextSibling);o&&o.tagName=="P"&&i.parentNode.removeChild(i)}),this._forEachNode(this._getAllNodesWithTag(n,["table"]),function(i){var o=this._hasSingleTagInsideElement(i,"TBODY")?i.firstElementChild:i;if(this._hasSingleTagInsideElement(o,"TR")){var s=o.firstElementChild;if(this._hasSingleTagInsideElement(s,"TD")){var u=s.firstElementChild;u=this._setNodeTag(u,this._everyNode(u.childNodes,this._isPhrasingContent)?"P":"DIV"),i.parentNode.replaceChild(u,i)}}})},_initializeNode:function(n){switch(n.readability={contentScore:0},n.tagName){case"DIV":n.readability.contentScore+=5;break;case"PRE":case"TD":case"BLOCKQUOTE":n.readability.contentScore+=3;break;case"ADDRESS":case"OL":case"UL":case"DL":case"DD":case"DT":case"LI":case"FORM":n.readability.contentScore-=3;break;case"H1":case"H2":case"H3":case"H4":case"H5":case"H6":case"TH":n.readability.contentScore-=5;break}n.readability.contentScore+=this._getClassWeight(n)},_removeAndGetNext:function(n){var r=this._getNextNode(n,!0);return n.parentNode.removeChild(n),r},_getNextNode:function(n,r){if(!r&&n.firstElementChild)return n.firstElementChild;if(n.nextElementSibling)return n.nextElementSibling;do n=n.parentNode;while(n&&!n.nextElementSibling);return n&&n.nextElementSibling},_textSimilarity:function(n,r){var i=n.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean),o=r.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean);if(!i.length||!o.length)return 0;var s=o.filter(c=>!i.includes(c)),u=s.join(" ").length/o.join(" ").length;return 1-u},_checkByline:function(n,r){if(this._articleByline)return!1;if(n.getAttribute!==void 0)var i=n.getAttribute("rel"),o=n.getAttribute("itemprop");return(i==="author"||o&&o.indexOf("author")!==-1||this.REGEXPS.byline.test(r))&&this._isValidByline(n.textContent)?(this._articleByline=n.textContent.trim(),!0):!1},_getNodeAncestors:function(n,r){r=r||0;for(var i=0,o=[];n.parentNode&&(o.push(n.parentNode),!(r&&++i===r));)n=n.parentNode;return o},_grabArticle:function(n){this.log("**** grabArticle ****");var r=this._doc,i=n!==null;if(n=n||this._doc.body,!n)return this.log("No body found in document. Abort."),null;for(var o=n.innerHTML;;){this.log("Starting grabArticle loop");var s=this._flagIsActive(this.FLAG_STRIP_UNLIKELYS),u=[],c=this._doc.documentElement;let $=!0;for(;c;){c.tagName==="HTML"&&(this._articleLang=c.getAttribute("lang"));var l=c.className+" "+c.id;if(!this._isProbablyVisible(c)){this.log("Removing hidden node - "+l),c=this._removeAndGetNext(c);continue}if(c.getAttribute("aria-modal")=="true"&&c.getAttribute("role")=="dialog"){c=this._removeAndGetNext(c);continue}if(this._checkByline(c,l)){c=this._removeAndGetNext(c);continue}if($&&this._headerDuplicatesTitle(c)){this.log("Removing header: ",c.textContent.trim(),this._articleTitle.trim()),$=!1,c=this._removeAndGetNext(c);continue}if(s){if(this.REGEXPS.unlikelyCandidates.test(l)&&!this.REGEXPS.okMaybeItsACandidate.test(l)&&!this._hasAncestorTag(c,"table")&&!this._hasAncestorTag(c,"code")&&c.tagName!=="BODY"&&c.tagName!=="A"){this.log("Removing unlikely candidate - "+l),c=this._removeAndGetNext(c);continue}if(this.UNLIKELY_ROLES.includes(c.getAttribute("role"))){this.log("Removing content with role "+c.getAttribute("role")+" - "+l),c=this._removeAndGetNext(c);continue}}if((c.tagName==="DIV"||c.tagName==="SECTION"||c.tagName==="HEADER"||c.tagName==="H1"||c.tagName==="H2"||c.tagName==="H3"||c.tagName==="H4"||c.tagName==="H5"||c.tagName==="H6")&&this._isElementWithoutContent(c)){c=this._removeAndGetNext(c);continue}if(this.DEFAULT_TAGS_TO_SCORE.indexOf(c.tagName)!==-1&&u.push(c),c.tagName==="DIV"){for(var d=null,m=c.firstChild;m;){var p=m.nextSibling;if(this._isPhrasingContent(m))d!==null?d.appendChild(m):this._isWhitespace(m)||(d=r.createElement("p"),c.replaceChild(d,m),d.appendChild(m));else if(d!==null){for(;d.lastChild&&this._isWhitespace(d.lastChild);)d.removeChild(d.lastChild);d=null}m=p}if(this._hasSingleTagInsideElement(c,"P")&&this._getLinkDensity(c)<.25){var x=c.children[0];c.parentNode.replaceChild(x,c),c=x,u.push(c)}else this._hasChildBlockElement(c)||(c=this._setNodeTag(c,"P"),u.push(c))}c=this._getNextNode(c)}var A=[];this._forEachNode(u,function(q){if(!(!q.parentNode||typeof q.parentNode.tagName>"u")){var O=this._getInnerText(q);if(!(O.length<25)){var ue=this._getNodeAncestors(q,5);if(ue.length!==0){var me=0;me+=1,me+=O.split(",").length,me+=Math.min(Math.floor(O.length/100),3),this._forEachNode(ue,function(J,Ee){if(!(!J.tagName||!J.parentNode||typeof J.parentNode.tagName>"u")){if(typeof J.readability>"u"&&(this._initializeNode(J),A.push(J)),Ee===0)var Re=1;else Ee===1?Re=2:Re=Ee*3;J.readability.contentScore+=me/Re}})}}}});for(var S=[],g=0,M=A.length;g<M;g+=1){var h=A[g],k=h.readability.contentScore*(1-this._getLinkDensity(h));h.readability.contentScore=k,this.log("Candidate:",h,"with score "+k);for(var L=0;L<this._nbTopCandidates;L++){var E=S[L];if(!E||k>E.readability.contentScore){S.splice(L,0,h),S.length>this._nbTopCandidates&&S.pop();break}}}var v=S[0]||null,w=!1,y;if(v===null||v.tagName==="BODY"){for(v=r.createElement("DIV"),w=!0;n.firstChild;)this.log("Moving child out:",n.firstChild),v.appendChild(n.firstChild);n.appendChild(v),this._initializeNode(v)}else if(v){for(var R=[],F=1;F<S.length;F++)S[F].readability.contentScore/v.readability.contentScore>=.75&&R.push(this._getNodeAncestors(S[F]));var C=3;if(R.length>=C)for(y=v.parentNode;y.tagName!=="BODY";){for(var j=0,Y=0;Y<R.length&&j<C;Y++)j+=Number(R[Y].includes(y));if(j>=C){v=y;break}y=y.parentNode}v.readability||this._initializeNode(v),y=v.parentNode;for(var Z=v.readability.contentScore,ve=Z/3;y.tagName!=="BODY";){if(!y.readability){y=y.parentNode;continue}var Se=y.readability.contentScore;if(Se<ve)break;if(Se>Z){v=y;break}Z=y.readability.contentScore,y=y.parentNode}for(y=v.parentNode;y.tagName!="BODY"&&y.children.length==1;)v=y,y=v.parentNode;v.readability||this._initializeNode(v)}var H=r.createElement("DIV");i&&(H.id="readability-content");var oe=Math.max(10,v.readability.contentScore*.2);y=v.parentNode;for(var Fe=y.children,he=0,T=Fe.length;he<T;he++){var I=Fe[he],U=!1;if(this.log("Looking at sibling node:",I,I.readability?"with score "+I.readability.contentScore:""),this.log("Sibling has score",I.readability?I.readability.contentScore:"Unknown"),I===v)U=!0;else{var ne=0;if(I.className===v.className&&v.className!==""&&(ne+=v.readability.contentScore*.2),I.readability&&I.readability.contentScore+ne>=oe)U=!0;else if(I.nodeName==="P"){var Q=this._getLinkDensity(I),ce=this._getInnerText(I),fe=ce.length;(fe>80&&Q<.25||fe<80&&fe>0&&Q===0&&ce.search(/\.( |$)/)!==-1)&&(U=!0)}}U&&(this.log("Appending node:",I),this.ALTER_TO_DIV_EXCEPTIONS.indexOf(I.nodeName)===-1&&(this.log("Altering sibling:",I,"to div."),I=this._setNodeTag(I,"DIV")),H.appendChild(I),Fe=y.children,he-=1,T-=1)}if(this._debug&&this.log("Article content pre-prep: "+H.innerHTML),this._prepArticle(H),this._debug&&this.log("Article content post-prep: "+H.innerHTML),w)v.id="readability-page-1",v.className="page";else{var _=r.createElement("DIV");for(_.id="readability-page-1",_.className="page";H.firstChild;)_.appendChild(H.firstChild);H.appendChild(_)}this._debug&&this.log("Article content after paging: "+H.innerHTML);var N=!0,V=this._getInnerText(H,!0).length;if(V<this._charThreshold)if(N=!1,n.innerHTML=o,this._flagIsActive(this.FLAG_STRIP_UNLIKELYS))this._removeFlag(this.FLAG_STRIP_UNLIKELYS),this._attempts.push({articleContent:H,textLength:V});else if(this._flagIsActive(this.FLAG_WEIGHT_CLASSES))this._removeFlag(this.FLAG_WEIGHT_CLASSES),this._attempts.push({articleContent:H,textLength:V});else if(this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY))this._removeFlag(this.FLAG_CLEAN_CONDITIONALLY),this._attempts.push({articleContent:H,textLength:V});else{if(this._attempts.push({articleContent:H,textLength:V}),this._attempts.sort(function(q,O){return O.textLength-q.textLength}),!this._attempts[0].textLength)return null;H=this._attempts[0].articleContent,N=!0}if(N){var ae=[y,v].concat(this._getNodeAncestors(y));return this._someNode(ae,function(q){if(!q.tagName)return!1;var O=q.getAttribute("dir");return O?(this._articleDir=O,!0):!1}),H}}},_isValidByline:function(n){return typeof n=="string"||n instanceof String?(n=n.trim(),n.length>0&&n.length<100):!1},_unescapeHtmlEntities:function(n){if(!n)return n;var r=this.HTML_ESCAPE_MAP;return n.replace(/&(quot|amp|apos|lt|gt);/g,function(i,o){return r[o]}).replace(/&#(?:x([0-9a-z]{1,4})|([0-9]{1,4}));/gi,function(i,o,s){var u=parseInt(o||s,o?16:10);return String.fromCharCode(u)})},_getJSONLD:function(n){var r=this._getAllNodesWithTag(n,["script"]),i;return this._forEachNode(r,function(o){if(!i&&o.getAttribute("type")==="application/ld+json")try{var s=o.textContent.replace(/^\s*<!\[CDATA\[|\]\]>\s*$/g,""),u=JSON.parse(s);if(!u["@context"]||!u["@context"].match(/^https?\:\/\/schema\.org$/)||(!u["@type"]&&Array.isArray(u["@graph"])&&(u=u["@graph"].find(function(m){return(m["@type"]||"").match(this.REGEXPS.jsonLdArticleTypes)})),!u||!u["@type"]||!u["@type"].match(this.REGEXPS.jsonLdArticleTypes)))return;if(i={},typeof u.name=="string"&&typeof u.headline=="string"&&u.name!==u.headline){var c=this._getArticleTitle(),l=this._textSimilarity(u.name,c)>.75,d=this._textSimilarity(u.headline,c)>.75;d&&!l?i.title=u.headline:i.title=u.name}else typeof u.name=="string"?i.title=u.name.trim():typeof u.headline=="string"&&(i.title=u.headline.trim());u.author&&(typeof u.author.name=="string"?i.byline=u.author.name.trim():Array.isArray(u.author)&&u.author[0]&&typeof u.author[0].name=="string"&&(i.byline=u.author.filter(function(m){return m&&typeof m.name=="string"}).map(function(m){return m.name.trim()}).join(", "))),typeof u.description=="string"&&(i.excerpt=u.description.trim()),u.publisher&&typeof u.publisher.name=="string"&&(i.siteName=u.publisher.name.trim());return}catch(m){this.log(m.message)}}),i||{}},_getArticleMetadata:function(n){var r={},i={},o=this._doc.getElementsByTagName("meta"),s=/\s*(dc|dcterm|og|twitter)\s*:\s*(author|creator|description|title|site_name)\s*/gi,u=/^\s*(?:(dc|dcterm|og|twitter|weibo:(article|webpage))\s*[\.:]\s*)?(author|creator|description|title|site_name)\s*$/i;return this._forEachNode(o,function(c){var l=c.getAttribute("name"),d=c.getAttribute("property"),m=c.getAttribute("content");if(m){var p=null,x=null;d&&(p=d.match(s),p&&(x=p[0].toLowerCase().replace(/\s/g,""),i[x]=m.trim())),!p&&l&&u.test(l)&&(x=l,m&&(x=x.toLowerCase().replace(/\s/g,"").replace(/\./g,":"),i[x]=m.trim()))}}),r.title=n.title||i["dc:title"]||i["dcterm:title"]||i["og:title"]||i["weibo:article:title"]||i["weibo:webpage:title"]||i.title||i["twitter:title"],r.title||(r.title=this._getArticleTitle()),r.byline=n.byline||i["dc:creator"]||i["dcterm:creator"]||i.author,r.excerpt=n.excerpt||i["dc:description"]||i["dcterm:description"]||i["og:description"]||i["weibo:article:description"]||i["weibo:webpage:description"]||i.description||i["twitter:description"],r.siteName=n.siteName||i["og:site_name"],r.title=this._unescapeHtmlEntities(r.title),r.byline=this._unescapeHtmlEntities(r.byline),r.excerpt=this._unescapeHtmlEntities(r.excerpt),r.siteName=this._unescapeHtmlEntities(r.siteName),r},_isSingleImage:function(n){return n.tagName==="IMG"?!0:n.children.length!==1||n.textContent.trim()!==""?!1:this._isSingleImage(n.children[0])},_unwrapNoscriptImages:function(n){var r=Array.from(n.getElementsByTagName("img"));this._forEachNode(r,function(o){for(var s=0;s<o.attributes.length;s++){var u=o.attributes[s];switch(u.name){case"src":case"srcset":case"data-src":case"data-srcset":return}if(/\.(jpg|jpeg|png|webp)/i.test(u.value))return}o.parentNode.removeChild(o)});var i=Array.from(n.getElementsByTagName("noscript"));this._forEachNode(i,function(o){var s=n.createElement("div");if(s.innerHTML=o.innerHTML,!!this._isSingleImage(s)){var u=o.previousElementSibling;if(u&&this._isSingleImage(u)){var c=u;c.tagName!=="IMG"&&(c=u.getElementsByTagName("img")[0]);for(var l=s.getElementsByTagName("img")[0],d=0;d<c.attributes.length;d++){var m=c.attributes[d];if(m.value!==""&&(m.name==="src"||m.name==="srcset"||/\.(jpg|jpeg|png|webp)/i.test(m.value))){if(l.getAttribute(m.name)===m.value)continue;var p=m.name;l.hasAttribute(p)&&(p="data-old-"+p),l.setAttribute(p,m.value)}}o.parentNode.replaceChild(s.firstElementChild,u)}}})},_removeScripts:function(n){this._removeNodes(this._getAllNodesWithTag(n,["script","noscript"]))},_hasSingleTagInsideElement:function(n,r){return n.children.length!=1||n.children[0].tagName!==r?!1:!this._someNode(n.childNodes,function(i){return i.nodeType===this.TEXT_NODE&&this.REGEXPS.hasContent.test(i.textContent)})},_isElementWithoutContent:function(n){return n.nodeType===this.ELEMENT_NODE&&n.textContent.trim().length==0&&(n.children.length==0||n.children.length==n.getElementsByTagName("br").length+n.getElementsByTagName("hr").length)},_hasChildBlockElement:function(n){return this._someNode(n.childNodes,function(r){return this.DIV_TO_P_ELEMS.has(r.tagName)||this._hasChildBlockElement(r)})},_isPhrasingContent:function(n){return n.nodeType===this.TEXT_NODE||this.PHRASING_ELEMS.indexOf(n.tagName)!==-1||(n.tagName==="A"||n.tagName==="DEL"||n.tagName==="INS")&&this._everyNode(n.childNodes,this._isPhrasingContent)},_isWhitespace:function(n){return n.nodeType===this.TEXT_NODE&&n.textContent.trim().length===0||n.nodeType===this.ELEMENT_NODE&&n.tagName==="BR"},_getInnerText:function(n,r){r=typeof r>"u"?!0:r;var i=n.textContent.trim();return r?i.replace(this.REGEXPS.normalize," "):i},_getCharCount:function(n,r){return r=r||",",this._getInnerText(n).split(r).length-1},_cleanStyles:function(n){if(!(!n||n.tagName.toLowerCase()==="svg")){for(var r=0;r<this.PRESENTATIONAL_ATTRIBUTES.length;r++)n.removeAttribute(this.PRESENTATIONAL_ATTRIBUTES[r]);this.DEPRECATED_SIZE_ATTRIBUTE_ELEMS.indexOf(n.tagName)!==-1&&(n.removeAttribute("width"),n.removeAttribute("height"));for(var i=n.firstElementChild;i!==null;)this._cleanStyles(i),i=i.nextElementSibling}},_getLinkDensity:function(n){var r=this._getInnerText(n).length;if(r===0)return 0;var i=0;return this._forEachNode(n.getElementsByTagName("a"),function(o){var s=o.getAttribute("href"),u=s&&this.REGEXPS.hashUrl.test(s)?.3:1;i+=this._getInnerText(o).length*u}),i/r},_getClassWeight:function(n){if(!this._flagIsActive(this.FLAG_WEIGHT_CLASSES))return 0;var r=0;return typeof n.className=="string"&&n.className!==""&&(this.REGEXPS.negative.test(n.className)&&(r-=25),this.REGEXPS.positive.test(n.className)&&(r+=25)),typeof n.id=="string"&&n.id!==""&&(this.REGEXPS.negative.test(n.id)&&(r-=25),this.REGEXPS.positive.test(n.id)&&(r+=25)),r},_clean:function(n,r){var i=["object","embed","iframe"].indexOf(r)!==-1;this._removeNodes(this._getAllNodesWithTag(n,[r]),function(o){if(i){for(var s=0;s<o.attributes.length;s++)if(this._allowedVideoRegex.test(o.attributes[s].value))return!1;if(o.tagName==="object"&&this._allowedVideoRegex.test(o.innerHTML))return!1}return!0})},_hasAncestorTag:function(n,r,i,o){i=i||3,r=r.toUpperCase();for(var s=0;n.parentNode;){if(i>0&&s>i)return!1;if(n.parentNode.tagName===r&&(!o||o(n.parentNode)))return!0;n=n.parentNode,s++}return!1},_getRowAndColumnCount:function(n){for(var r=0,i=0,o=n.getElementsByTagName("tr"),s=0;s<o.length;s++){var u=o[s].getAttribute("rowspan")||0;u&&(u=parseInt(u,10)),r+=u||1;for(var c=0,l=o[s].getElementsByTagName("td"),d=0;d<l.length;d++){var m=l[d].getAttribute("colspan")||0;m&&(m=parseInt(m,10)),c+=m||1}i=Math.max(i,c)}return{rows:r,columns:i}},_markDataTables:function(n){for(var r=n.getElementsByTagName("table"),i=0;i<r.length;i++){var o=r[i],s=o.getAttribute("role");if(s=="presentation"){o._readabilityDataTable=!1;continue}var u=o.getAttribute("datatable");if(u=="0"){o._readabilityDataTable=!1;continue}var c=o.getAttribute("summary");if(c){o._readabilityDataTable=!0;continue}var l=o.getElementsByTagName("caption")[0];if(l&&l.childNodes.length>0){o._readabilityDataTable=!0;continue}var d=["col","colgroup","tfoot","thead","th"],m=function(x){return!!o.getElementsByTagName(x)[0]};if(d.some(m)){this.log("Data table because found data-y descendant"),o._readabilityDataTable=!0;continue}if(o.getElementsByTagName("table")[0]){o._readabilityDataTable=!1;continue}var p=this._getRowAndColumnCount(o);if(p.rows>=10||p.columns>4){o._readabilityDataTable=!0;continue}o._readabilityDataTable=p.rows*p.columns>10}},_fixLazyImages:function(n){this._forEachNode(this._getAllNodesWithTag(n,["img","picture","figure"]),function(r){if(r.src&&this.REGEXPS.b64DataUrl.test(r.src)){var i=this.REGEXPS.b64DataUrl.exec(r.src);if(i[1]==="image/svg+xml")return;for(var o=!1,s=0;s<r.attributes.length;s++){var u=r.attributes[s];if(u.name!=="src"&&/\.(jpg|jpeg|png|webp)/i.test(u.value)){o=!0;break}}if(o){var c=r.src.search(/base64\s*/i)+7,l=r.src.length-c;l<133&&r.removeAttribute("src")}}if(!((r.src||r.srcset&&r.srcset!="null")&&r.className.toLowerCase().indexOf("lazy")===-1)){for(var d=0;d<r.attributes.length;d++)if(u=r.attributes[d],!(u.name==="src"||u.name==="srcset"||u.name==="alt")){var m=null;if(/\.(jpg|jpeg|png|webp)\s+\d/.test(u.value)?m="srcset":/^\s*\S+\.(jpg|jpeg|png|webp)\S*\s*$/.test(u.value)&&(m="src"),m){if(r.tagName==="IMG"||r.tagName==="PICTURE")r.setAttribute(m,u.value);else if(r.tagName==="FIGURE"&&!this._getAllNodesWithTag(r,["img","picture"]).length){var p=this._doc.createElement("img");p.setAttribute(m,u.value),r.appendChild(p)}}}}})},_getTextDensity:function(n,r){var i=this._getInnerText(n,!0).length;if(i===0)return 0;var o=0,s=this._getAllNodesWithTag(n,r);return this._forEachNode(s,u=>o+=this._getInnerText(u,!0).length),o/i},_cleanConditionally:function(n,r){this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)&&this._removeNodes(this._getAllNodesWithTag(n,[r]),function(i){var o=function(y){return y._readabilityDataTable},s=r==="ul"||r==="ol";if(!s){var u=0,c=this._getAllNodesWithTag(i,["ul","ol"]);this._forEachNode(c,y=>u+=this._getInnerText(y).length),s=u/this._getInnerText(i).length>.9}if(r==="table"&&o(i)||this._hasAncestorTag(i,"table",-1,o)||this._hasAncestorTag(i,"code"))return!1;var l=this._getClassWeight(i);this.log("Cleaning Conditionally",i);var d=0;if(l+d<0)return!0;if(this._getCharCount(i,",")<10){for(var m=i.getElementsByTagName("p").length,p=i.getElementsByTagName("img").length,x=i.getElementsByTagName("li").length-100,A=i.getElementsByTagName("input").length,S=this._getTextDensity(i,["h1","h2","h3","h4","h5","h6"]),g=0,M=this._getAllNodesWithTag(i,["object","embed","iframe"]),h=0;h<M.length;h++){for(var k=0;k<M[h].attributes.length;k++)if(this._allowedVideoRegex.test(M[h].attributes[k].value))return!1;if(M[h].tagName==="object"&&this._allowedVideoRegex.test(M[h].innerHTML))return!1;g++}var L=this._getLinkDensity(i),E=this._getInnerText(i).length,v=p>1&&m/p<.5&&!this._hasAncestorTag(i,"figure")||!s&&x>m||A>Math.floor(m/3)||!s&&S<.9&&E<25&&(p===0||p>2)&&!this._hasAncestorTag(i,"figure")||!s&&l<25&&L>.2||l>=25&&L>.5||g===1&&E<75||g>1;if(s&&v){for(var w=0;w<i.children.length;w++)if(i.children[w].children.length>1)return v;let y=i.getElementsByTagName("li").length;if(p==y)return!1}return v}return!1})},_cleanMatchedNodes:function(n,r){for(var i=this._getNextNode(n,!0),o=this._getNextNode(n);o&&o!=i;)r.call(this,o,o.className+" "+o.id)?o=this._removeAndGetNext(o):o=this._getNextNode(o)},_cleanHeaders:function(n){let r=this._getAllNodesWithTag(n,["h1","h2"]);this._removeNodes(r,function(i){let o=this._getClassWeight(i)<0;return o&&this.log("Removing header with low class weight:",i),o})},_headerDuplicatesTitle:function(n){if(n.tagName!="H1"&&n.tagName!="H2")return!1;var r=this._getInnerText(n,!1);return this.log("Evaluating similarity of header:",r,this._articleTitle),this._textSimilarity(this._articleTitle,r)>.75},_flagIsActive:function(n){return(this._flags&n)>0},_removeFlag:function(n){this._flags=this._flags&~n},_isProbablyVisible:function(n){return(!n.style||n.style.display!="none")&&!n.hasAttribute("hidden")&&(!n.hasAttribute("aria-hidden")||n.getAttribute("aria-hidden")!="true"||n.className&&n.className.indexOf&&n.className.indexOf("fallback-image")!==-1)},parse:function(){if(this._maxElemsToParse>0){var n=this._doc.getElementsByTagName("*").length;if(n>this._maxElemsToParse)throw new Error("Aborting parsing document; "+n+" elements found")}this._unwrapNoscriptImages(this._doc);var r=this._disableJSONLD?{}:this._getJSONLD(this._doc);this._removeScripts(this._doc),this._prepDocument();var i=this._getArticleMetadata(r);this._articleTitle=i.title;var o=this._grabArticle();if(!o)return null;if(this.log("Grabbed: "+o.innerHTML),this._postProcessContent(o),!i.excerpt){var s=o.getElementsByTagName("p");s.length>0&&(i.excerpt=s[0].textContent.trim())}var u=o.textContent;return{title:this._articleTitle,byline:i.byline||this._articleByline,dir:this._articleDir,lang:this._articleLang,content:this._serializer(o),textContent:u,length:u.length,excerpt:i.excerpt,siteName:i.siteName||this._articleSiteName}}},typeof t=="object"&&(t.exports=a)}),Qh=Io((e,t)=>{var a={unlikelyCandidates:/-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,okMaybeItsACandidate:/and|article|body|column|content|main|shadow/i};function n(i){return(!i.style||i.style.display!="none")&&!i.hasAttribute("hidden")&&(!i.hasAttribute("aria-hidden")||i.getAttribute("aria-hidden")!="true"||i.className&&i.className.indexOf&&i.className.indexOf("fallback-image")!==-1)}function r(i,o={}){typeof o=="function"&&(o={visibilityChecker:o});var s={minScore:20,minContentLength:140,visibilityChecker:n};o=Object.assign(s,o);var u=i.querySelectorAll("p, pre, article"),c=i.querySelectorAll("div > br");if(c.length){var l=new Set(u);[].forEach.call(c,function(m){l.add(m.parentNode)}),u=Array.from(l)}var d=0;return[].some.call(u,function(m){if(!o.visibilityChecker(m))return!1;var p=m.className+" "+m.id;if(a.unlikelyCandidates.test(p)&&!a.okMaybeItsACandidate.test(p)||m.matches("li p"))return!1;var x=m.textContent.trim().length;return x<o.minContentLength?!1:(d+=Math.sqrt(x-o.minContentLength),d>o.minScore)})}typeof t=="object"&&(t.exports=r)}),fc=Io((e,t)=>{var a=Wh(),n=Qh();t.exports={Readability:a,isProbablyReaderable:n}}),bc={};Hh(bc,{Readability:()=>vc,default:()=>wc,isProbablyReaderable:()=>xc});var yc=hc(fc());Kh(bc,hc(fc()));var{Readability:vc,isProbablyReaderable:xc}=yc,{default:gc,...Vh}=yc,wc=gc!==void 0?gc:Vh;var Ac=class extends Error{constructor(e,t){super(e),this.name="ParseError",this.type=t.type,this.field=t.field,this.value=t.value,this.line=t.line}};function Po(e){}function Fo(e){if(typeof e=="function")throw new TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");let{onEvent:t=Po,onError:a=Po,onRetry:n=Po,onComment:r}=e,i="",o=!0,s,u="",c="";function l(A){let S=o?A.replace(/^\xEF\xBB\xBF/,""):A,[g,M]=Yh(`${i}${S}`);for(let h of g)d(h);i=M,o=!1}function d(A){if(A===""){p();return}if(A.startsWith(":")){r&&r(A.slice(A.startsWith(": ")?2:1));return}let S=A.indexOf(":");if(S!==-1){let g=A.slice(0,S),M=A[S+1]===" "?2:1,h=A.slice(S+M);m(g,h,A);return}m(A,"",A)}function m(A,S,g){switch(A){case"event":c=S;break;case"data":u=`${u}${S}
`;break;case"id":s=S.includes("\0")?void 0:S;break;case"retry":/^\d+$/.test(S)?n(parseInt(S,10)):a(new Ac(`Invalid \`retry\` value: "${S}"`,{type:"invalid-retry",value:S,line:g}));break;default:a(new Ac(`Unknown field "${A.length>20?`${A.slice(0,20)}\u2026`:A}"`,{type:"unknown-field",field:A,value:S,line:g}));break}}function p(){u.length>0&&t({id:s,event:c||void 0,data:u.endsWith(`
`)?u.slice(0,-1):u}),s=void 0,u="",c=""}function x(A={}){i&&A.consume&&d(i),o=!0,s=void 0,u="",c="",i=""}return{feed:l,reset:x}}function Yh(e){let t=[],a="",n=0;for(;n<e.length;){let r=e.indexOf("\r",n),i=e.indexOf(`
`,n),o=-1;if(r!==-1&&i!==-1?o=Math.min(r,i):r!==-1?o=r:i!==-1&&(o=i),o===-1){a=e.slice(n);break}else{let s=e.slice(n,o);t.push(s),n=o+1,e[n-1]==="\r"&&e[n]===`
`&&n++}}return[t,a]}var Jh=Object.create,Ro=Object.defineProperty,Zh=Object.getOwnPropertyDescriptor,Xh=Object.getOwnPropertyNames,$h=Object.getPrototypeOf,ef=Object.prototype.hasOwnProperty,tf=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),af=(e,t)=>{for(var a in t)Ro(e,a,{get:t[a],enumerable:!0})},Bo=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Xh(t))!ef.call(e,r)&&r!==a&&Ro(e,r,{get:()=>t[r],enumerable:!(n=Zh(t,r))||n.enumerable});return e},nf=(e,t,a)=>(Bo(e,t,"default"),a&&Bo(a,t,"default")),Sc=(e,t,a)=>(a=e!=null?Jh($h(e)):{},Bo(t||!e||!e.__esModule?Ro(a,"default",{value:e,enumerable:!0}):a,e)),kc=tf(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parse=void 0;function t(h){if(h!==void 0){if(h===null)return null;if(h==="")return"";h=h.replace(/\\+$/,k=>k.length%2===0?k:k.slice(0,-1));try{return JSON.parse(h)}catch(k){let[L,E]=h.trimLeft()[0]===":"?a(h,k):a(h,k,p);if(t.lastParseReminding=E,t.onExtraToken&&E.length>0){let v=E.trimRight();t.lastParseReminding=v,v.length>0&&t.onExtraToken(h,L,v)}return L}}}e.parse=t,function(h){h.onExtraToken=(k,L,E)=>{}}(t=e.parse||(e.parse={}));function a(h,k,L){let E=r[h[0]]||L;if(!E)throw k;return E(h,k)}function n(h,k,L){return h[0]==='"'?l(h):h[0]==="'"?m(h):p(h,k,L)}var r={};function i(h){return h.trimLeft()}r[" "]=o,r["\r"]=o,r[`
`]=o,r["	"]=o;function o(h,k){return h=i(h),a(h,k)}r["["]=s;function s(h,k){h=h.substr(1);let L=[];for(h=i(h);h.length>0;){if(h[0]==="]"){h=h.substr(1);break}let E=a(h,k,(v,w)=>p(v,w,[",","]"]));L.push(E[0]),h=E[1],h=i(h),h[0]===","&&(h=h.substring(1),h=i(h))}return[L,h]}for(let h of"0123456789.-".slice())r[h]=u;function u(h){for(let k=0;k<h.length;k++){let L=h[k];if(r[L]===u)continue;let E=h.substring(0,k);return h=h.substring(k),[c(E),h]}return[c(h),""]}function c(h){if(h==="-")return-0;let k=+h;return Number.isNaN(k)?h:k}r['"']=l;function l(h){for(let k=1;k<h.length;k++){let L=h[k];if(L==="\\"){k++;continue}if(L==='"'){let E=d(h.substring(0,k+1));return h=h.substring(k+1),[JSON.parse(E),h]}}return[JSON.parse(d(h)+'"'),""]}function d(h){return h.replace(/\n/g,"\\n").replace(/\t/g,"\\t").replace(/\r/g,"\\r")}r["'"]=m;function m(h){for(let k=1;k<h.length;k++){let L=h[k];if(L==="\\"){k++;continue}if(L==="'"){let E=d(h.substring(0,k+1));return h=h.substring(k+1),[JSON.parse('"'+E.slice(1,-1)+'"'),h]}}return[JSON.parse('"'+d(h.slice(1))+'"'),""]}function p(h,k,L=[" "]){let E=Math.min(...L.map(y=>{let R=h.indexOf(y);return R===-1?h.length:R})),v=h.substring(0,E).trim(),w=h.substring(E);return[v,w]}r["{"]=x;function x(h,k){h=h.substr(1);let L={};for(h=i(h);h.length>0;){if(h[0]==="}"){h=h.substr(1);break}let E=n(h,k,[":","}"]),v=E[0];if(h=E[1],h=i(h),h[0]!==":"){L[v]=void 0;break}if(h=h.substr(1),h=i(h),h.length===0){L[v]=void 0;break}let w=a(h,k);L[v]=w[0],h=w[1],h=i(h),h[0]===","&&(h=h.substr(1),h=i(h))}return[L,h]}r.t=A;function A(h,k){return M(h,"true",!0,k)}r.f=S;function S(h,k){return M(h,"false",!1,k)}r.n=g;function g(h,k){return M(h,"null",null,k)}function M(h,k,L,E){for(let v=k.length;v>=1;v--)if(h.startsWith(k.slice(0,v)))return[L,h.slice(v)];{let v=JSON.stringify(h.slice(0,k.length));throw E}}}),Tc={};af(Tc,{__esModule:()=>rf,default:()=>Mc,parse:()=>Cc});var Dc=Sc(kc());nf(Tc,Sc(kc()));var{__esModule:rf,parse:Cc}=Dc,{default:Ec,...of}=Dc,Mc=Ec!==void 0?Ec:of;function sf({allowIcannDomains:e=!0,allowPrivateDomains:t=!1,detectIp:a=!0,extractHostname:n=!0,mixedInputs:r=!0,validHosts:i=null,validateHostname:o=!0}){return{allowIcannDomains:e,allowPrivateDomains:t,detectIp:a,extractHostname:n,mixedInputs:r,validHosts:i,validateHostname:o}}var Ok=sf({});function Ic(){return{domain:null,domainWithoutSuffix:null,hostname:null,isIcann:null,isIp:null,isPrivate:null,publicSuffix:null,subdomain:null}}var Qk=function(){let e=[1,{}],t=[2,{}],a=[0,{city:e}];return[0,{ck:[0,{www:e}],jp:[0,{kawasaki:a,kitakyushu:a,kobe:a,nagoya:a,sapporo:a,sendai:a,yokohama:a}],dev:[0,{hrsn:[0,{psl:[0,{wc:[0,{ignored:t,sub:[0,{ignored:t}]}]}]}]}]}]}(),Vk=function(){let e=[1,{}],t=[2,{}],a=[1,{com:e,edu:e,gov:e,net:e,org:e}],n=[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e}],r=[0,{"*":t}],i=[2,{s:r}],o=[0,{relay:t}],s=[2,{id:t}],u=[1,{gov:e}],c=[0,{"transfer-webapp":t}],l=[0,{notebook:t,studio:t}],d=[0,{labeling:t,notebook:t,studio:t}],m=[0,{notebook:t}],p=[0,{labeling:t,notebook:t,"notebook-fips":t,studio:t}],x=[0,{notebook:t,"notebook-fips":t,studio:t,"studio-fips":t}],A=[0,{"*":e}],S=[1,{co:t}],g=[0,{objects:t}],M=[2,{nodes:t}],h=[0,{my:r}],k=[0,{s3:t,"s3-accesspoint":t,"s3-website":t}],L=[0,{s3:t,"s3-accesspoint":t}],E=[0,{direct:t}],v=[0,{"webview-assets":t}],w=[0,{vfs:t,"webview-assets":t}],y=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":v,cloud9:w}],R=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:L,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":v,cloud9:w}],F=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":v,cloud9:w}],C=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t}],j=[0,{s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-website":t}],Y=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:j,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":v,cloud9:w}],Z=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:j,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-deprecated":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":v,cloud9:w}],ve=[0,{s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t}],Se=[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:ve,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t}],H=[0,{auth:t}],oe=[0,{auth:t,"auth-fips":t}],Fe=[0,{"auth-fips":t}],he=[0,{apps:t}],T=[0,{paas:t}],I=[2,{eu:t}],U=[0,{app:t}],ne=[0,{site:t}],Q=[1,{com:e,edu:e,net:e,org:e}],ce=[0,{j:t}],fe=[0,{dyn:t}],_=[1,{co:e,com:e,edu:e,gov:e,net:e,org:e}],N=[0,{p:t}],V=[0,{user:t}],ae=[0,{shop:t}],$=[0,{cdn:t}],q=[0,{cust:t,reservd:t}],O=[0,{cust:t}],ue=[0,{s3:t}],me=[1,{biz:e,com:e,edu:e,gov:e,info:e,net:e,org:e}],J=[0,{ipfs:t}],Ee=[1,{framer:t}],Re=[0,{forgot:t}],se=[1,{gs:e}],Ce=[0,{nes:e}],W=[1,{k12:e,cc:e,lib:e}],He=[1,{cc:e,lib:e}];return[0,{ac:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,drr:t,feedback:t,forms:t}],ad:e,ae:[1,{ac:e,co:e,gov:e,mil:e,net:e,org:e,sch:e}],aero:[1,{airline:e,airport:e,"accident-investigation":e,"accident-prevention":e,aerobatic:e,aeroclub:e,aerodrome:e,agents:e,"air-surveillance":e,"air-traffic-control":e,aircraft:e,airtraffic:e,ambulance:e,association:e,author:e,ballooning:e,broker:e,caa:e,cargo:e,catering:e,certification:e,championship:e,charter:e,civilaviation:e,club:e,conference:e,consultant:e,consulting:e,control:e,council:e,crew:e,design:e,dgca:e,educator:e,emergency:e,engine:e,engineer:e,entertainment:e,equipment:e,exchange:e,express:e,federation:e,flight:e,freight:e,fuel:e,gliding:e,government:e,groundhandling:e,group:e,hanggliding:e,homebuilt:e,insurance:e,journal:e,journalist:e,leasing:e,logistics:e,magazine:e,maintenance:e,marketplace:e,media:e,microlight:e,modelling:e,navigation:e,parachuting:e,paragliding:e,"passenger-association":e,pilot:e,press:e,production:e,recreation:e,repbody:e,res:e,research:e,rotorcraft:e,safety:e,scientist:e,services:e,show:e,skydiving:e,software:e,student:e,taxi:e,trader:e,trading:e,trainer:e,union:e,workinggroup:e,works:e}],af:a,ag:[1,{co:e,com:e,net:e,nom:e,org:e,obj:t}],ai:[1,{com:e,net:e,off:e,org:e,uwu:t,framer:t}],al:n,am:[1,{co:e,com:e,commune:e,net:e,org:e,radio:t}],ao:[1,{co:e,ed:e,edu:e,gov:e,gv:e,it:e,og:e,org:e,pb:e}],aq:e,ar:[1,{bet:e,com:e,coop:e,edu:e,gob:e,gov:e,int:e,mil:e,musica:e,mutual:e,net:e,org:e,seg:e,senasa:e,tur:e}],arpa:[1,{e164:e,home:e,"in-addr":e,ip6:e,iris:e,uri:e,urn:e}],as:u,asia:[1,{cloudns:t,daemon:t,dix:t}],at:[1,{ac:[1,{sth:e}],co:e,gv:e,or:e,funkfeuer:[0,{wien:t}],futurecms:[0,{"*":t,ex:r,in:r}],futurehosting:t,futuremailing:t,ortsinfo:[0,{ex:r,kunden:r}],biz:t,info:t,"123webseite":t,priv:t,myspreadshop:t,"12hp":t,"2ix":t,"4lima":t,"lima-city":t}],au:[1,{asn:e,com:[1,{cloudlets:[0,{mel:t}],myspreadshop:t}],edu:[1,{act:e,catholic:e,nsw:[1,{schools:e}],nt:e,qld:e,sa:e,tas:e,vic:e,wa:e}],gov:[1,{qld:e,sa:e,tas:e,vic:e,wa:e}],id:e,net:e,org:e,conf:e,oz:e,act:e,nsw:e,nt:e,qld:e,sa:e,tas:e,vic:e,wa:e}],aw:[1,{com:e}],ax:e,az:[1,{biz:e,co:e,com:e,edu:e,gov:e,info:e,int:e,mil:e,name:e,net:e,org:e,pp:e,pro:e}],ba:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,rs:t}],bb:[1,{biz:e,co:e,com:e,edu:e,gov:e,info:e,net:e,org:e,store:e,tv:e}],bd:A,be:[1,{ac:e,cloudns:t,webhosting:t,interhostsolutions:[0,{cloud:t}],kuleuven:[0,{ezproxy:t}],"123website":t,myspreadshop:t,transurl:r}],bf:u,bg:[1,{0:e,1:e,2:e,3:e,4:e,5:e,6:e,7:e,8:e,9:e,a:e,b:e,c:e,d:e,e,f:e,g:e,h:e,i:e,j:e,k:e,l:e,m:e,n:e,o:e,p:e,q:e,r:e,s:e,t:e,u:e,v:e,w:e,x:e,y:e,z:e,barsy:t}],bh:a,bi:[1,{co:e,com:e,edu:e,or:e,org:e}],biz:[1,{activetrail:t,"cloud-ip":t,cloudns:t,jozi:t,dyndns:t,"for-better":t,"for-more":t,"for-some":t,"for-the":t,selfip:t,webhop:t,orx:t,mmafan:t,myftp:t,"no-ip":t,dscloud:t}],bj:[1,{africa:e,agro:e,architectes:e,assur:e,avocats:e,co:e,com:e,eco:e,econo:e,edu:e,info:e,loisirs:e,money:e,net:e,org:e,ote:e,restaurant:e,resto:e,tourism:e,univ:e}],bm:a,bn:[1,{com:e,edu:e,gov:e,net:e,org:e,co:t}],bo:[1,{com:e,edu:e,gob:e,int:e,mil:e,net:e,org:e,tv:e,web:e,academia:e,agro:e,arte:e,blog:e,bolivia:e,ciencia:e,cooperativa:e,democracia:e,deporte:e,ecologia:e,economia:e,empres/* REMOVED_COMMON_BLOCK_2473 */:t,rj:t,rn:t,ro:t,rr:t,rs:t,sc:t,se:t,sp:t,to:t}],leilao:e,lel:e,log:e,londrina:e,macapa:e,maceio:e,manaus:e,maringa:e,mat:e,med:e,mil:e,morena:e,mp:e,mus:e,natal:e,net:e,niteroi:e,nom:A,not:e,ntr:e,odo:e,ong:e,org:e,osasco:e,palmas:e,poa:e,ppg:e,pro:e,psc:e,psi:e,pvh:e,qsl:e,radio:e,rec:e,recife:e,rep:e,ribeirao:e,rio:e,riobranco:e,riopreto:e,salvador:e,sampa:e,santamaria:e,santoandre:e,saobernardo:e,saogonca:e,seg:e,sjc:e,slg:e,slz:e,sorocaba:e,srv:e,taxi:e,tc:e,tec:e,teo:e,the:e,tmp:e,trd:e,tur:e,tv:e,udi:e,vet:e,vix:e,vlog:e,wiki:e,zlg:e}],bs:[1,{com:e,edu:e,gov:e,net:e,org:e,we:t}],bt:a,bv:e,bw:[1,{ac:e,co:e,gov:e,net:e,org:e}],by:[1,{gov:e,mil:e,com:e,of:e,mediatech:t}],bz:[1,{co:e,com:e,edu:e,gov:e,net:e,org:e,za:t,mydns:t,gsj:t}],ca:[1,{ab:e,bc:e,mb:e,nb:e,nf:e,nl:e,ns:e,nt:e,nu:e,on:e,pe:e,qc:e,sk:e,yk:e,gc:e,barsy:t,awdev:r,co:t,"no-ip":t,myspreadshop:t,box:t}],cat:e,cc:[1,{cleverapps:t,cloudns:t,ftpaccess:t,"game-server":t,myphotos:t,scrapping:t,twmail:t,csx:t,fantasyleague:t,spawn:[0,{instances:t}]}],cd:u,cf:e,cg:e,ch:[1,{square7:t,cloudns:t,cloudscale:[0,{cust:t,lpg:g,rma:g}],objectstorage:[0,{lpg:t,rma:t}],flow:[0,{ae:[0,{alp1:t}],appengine:t}],"linkyard-cloud":t,gotdns:t,dnsking:t,"123website":t,myspreadshop:t,firenet:[0,{"*":t,svc:r}],"12hp":t,"2ix":t,"4lima":t,"lima-city":t}],ci:[1,{ac:e,"xn--aroport-bya":e,a\u00E9roport:e,asso:e,co:e,com:e,ed:e,edu:e,go:e,gouv:e,int:e,net:e,or:e,org:e}],ck:A,cl:[1,{co:e,gob:e,gov:e,mil:e,cloudns:t}],cm:[1,{co:e,com:e,gov:e,net:e}],cn:[1,{ac:e,com:[1,{amazonaws:[0,{"cn-north-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-deprecated":t,"s3-object-lambda":t,"s3-website":t}],"cn-northwest-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:L,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t}],compute:r,airflow:[0,{"cn-north-1":r,"cn-northwest-1":r}],eb:[0,{"cn-north-1":t,"cn-northwest-1":t}],elb:r}],sagemaker:[0,{"cn-north-1":l,"cn-northwest-1":l}]}],edu:e,gov:e,mil:e,net:e,org:e,"xn--55qx5d":e,\u516C\u53F8:e,"xn--od0alg":e,\u7DB2\u7D61:e,"xn--io0a7i":e,\u7F51\u7EDC:e,ah:e,bj:e,cq:e,fj:e,gd:e,gs:e,gx:e,gz:e,ha:e,hb:e,he:e,hi:e,hk:e,hl:e,hn:e,jl:e,js:e,jx:e,ln:e,mo:e,nm:e,nx:e,qh:e,sc:e,sd:e,sh:[1,{as:t}],sn:e,sx:e,tj:e,tw:e,xj:e,xz:e,yn:e,zj:e,"canva-apps":t,canvasite:h,myqnapcloud:t,quickconnect:E}],co:[1,{com:e,edu:e,gov:e,mil:e,net:e,nom:e,org:e,carrd:t,crd:t,otap:r,leadpages:t,lpages:t,mypi:t,xmit:r,firewalledreplit:s,repl:s,supabase:t}],com:[1,{a2hosted:t,cpserver:t,adobeaemcloud:[2,{dev:r}],africa:t,airkitapps:t,"airkitapps-au":t,aivencloud:t,alibabacloudcs:t,kasserver:t,amazonaws:[0,{"af-south-1":y,"ap-east-1":R,"ap-northeast-1":F,"ap-northeast-2":F,"ap-northeast-3":y,"ap-south-1":F,"ap-south-2":C,"ap-southeast-1":F,"ap-southeast-2":F,"ap-southeast-3":C,"ap-southeast-4":C,"ap-southeast-5":[0,{"execute-api":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-deprecated":t,"s3-object-lambda":t,"s3-website":t}],"ca-central-1":Y,"ca-west-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:j,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t}],"eu-central-1":F,"eu-central-2":C,"eu-north-1":R,"eu-south-1":y,"eu-south-2":C,"eu-west-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-deprecated":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":v,cloud9:w}],"eu-west-2":R,"eu-west-3":y,"il-central-1":[0,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:k,s3:t,"s3-accesspoint":t,"s3-object-lambda":t,"s3-website":t,"aws-cloud9":v,cloud9:[0,{vfs:t}]}],"me-central-1":C,"me-south-1":R,"sa-east-1":y,"us-east-1":[2,{"execute-api":t,"emrappui-prod":t,"emrnotebooks-prod":t,"emrstudio-prod":t,dualstack:j,s3:t,"s3-accesspoint":t,"s3-accesspoint-fips":t,"s3-deprecated":t,"s3-fips":t,"s3-object-lambda":t,"s3-website":t,"analytics-gateway":t,"aws-cloud9":v,cloud9:w}],"us-east-2":Z,"us-gov-east-1":Se,"us-gov-west-1":Se,"us-west-1":Y,"us-west-2":Z,compute:r,"compute-1":r,airflow:[0,{"af-south-1":r,"ap-east-1":r,"ap-northeast-1":r,"ap-northeast-2":r,"ap-northeast-3":r,"ap-south-1":r,"ap-south-2":r,"ap-southeast-1":r,"ap-southeast-2":r,"ap-southeast-3":r,"ap-southeast-4":r,"ca-central-1":r,"ca-west-1":r,"eu-central-1":r,"eu-central-2":r,"eu-north-1":r,"eu-south-1":r,"eu-south-2":r,"eu-west-1":r,"eu-west-2":r,"eu-west-3":r,"il-central-1":r,"me-central-1":r,"me-south-1":r,"sa-east-1":r,"us-east-1":r,"us-east-2":r,"us-west-1":r,"us-west-2":r}],s3:t,"s3-1":t,"s3-ap-east-1":t,"s3-ap-northeast-1":t,"s3-ap-northeast-2":t,"s3-ap-northeast-3":t,"s3-ap-south-1":t,"s3-ap-southeast-1":t,"s3-ap-southeast-2":t,"s3-ca-central-1":t,"s3-eu-central-1":t,"s3-eu-north-1":t,"s3-eu-west-1":t,"s3-eu-west-2":t,"s3-eu-west-3":t,"s3-external-1":t,"s3-fips-us-gov-east-1":t,"s3-fips-us-gov-west-1":t,"s3-global":[0,{accesspoint:[0,{mrap:t}]}],"s3-me-south-1":t,"s3-sa-east-1":t,"s3-us-east-2":t,"s3-us-gov-east-1":t,"s3-us-gov-west-1":t,"s3-us-west-1":t,"s3-us-west-2":t,"s3-website-ap-northeast-1":t,"s3-website-ap-southeast-1":t,"s3-website-ap-southeast-2":t,"s3-website-eu-west-1":t,"s3-website-sa-east-1":t,"s3-website-us-east-1":t,"s3-website-us-gov-west-1":t,"s3-website-us-west-1":t,"s3-website-us-west-2":t,elb:r}],amazoncognito:[0,{"af-south-1":H,"ap-east-1":H,"ap-northeast-1":H,"ap-northeast-2":H,"ap-northeast-3":H,"ap-south-1":H,"ap-south-2":H,"ap-southeast-1":H,"ap-southeast-2":H,"ap-southeast-3":H,"ap-southeast-4":H,"ap-southeast-5":H,"ca-central-1":H,"ca-west-1":H,"eu-central-1":H,"eu-central-2":H,"eu-north-1":H,"eu-south-1":H,"eu-south-2":H,"eu-west-1":H,"eu-west-2":H,"eu-west-3":H,"il-central-1":H,"me-central-1":H,"me-south-1":H,"sa-east-1":H,"us-east-1":oe,"us-east-2":oe,"us-gov-east-1":Fe,"us-gov-west-1":Fe,"us-west-1":oe,"us-west-2":oe}],amplifyapp:t,awsapprunner:r,awsapps:t,elasticbeanstalk:[2,{"af-south-1":t,"ap-east-1":t,"ap-northeast-1":t,"ap-northeast-2":t,"ap-northeast-3":t,"ap-south-1":t,"ap-southeast-1":t,"ap-southeast-2":t,"ap-southeast-3":t,"ca-central-1":t,"eu-central-1":t,"eu-north-1":t,"eu-south-1":t,"eu-west-1":t,"eu-west-2":t,"eu-west-3":t,"il-central-1":t,"me-south-1":t,"sa-east-1":t,"us-east-1":t,"us-east-2":t,"us-gov-east-1":t,"us-gov-west-1":t,"us-west-1":t,"us-west-2":t}],awsglobalaccelerator:t,siiites:t,appspacehosted:t,appspaceusercontent:t,"on-aptible":t,myasustor:t,"balena-devices":t,boutir:t,bplaced:t,cafjs:t,"canva-apps":t,"cdn77-storage":t,br:t,cn:t,de:t,eu:t,jpn:t,mex:t,ru:t,sa:t,uk:t,us:t,za:t,"clever-cloud":[0,{services:r}],dnsabr:t,"ip-ddns":t,jdevcloud:t,wpdevcloud:t,"cf-ipfs":t,"cloudflare-ipfs":t,trycloudflare:t,co:t,devinapps:r,builtwithdark:t,datadetect:[0,{demo:t,instance:t}],dattolocal:t,dattorelay:t,dattoweb:t,mydatto:t,digitaloceanspaces:r,discordsays:t,discordsez:t,drayddns:t,dreamhosters:t,duru/* REMOVED_COMMON_BLOCK_2461 */t,"is-slick":t,"is-uberleet":t,"is-with-theband":t,"isa-geek":t,"isa-hockeynut":t,issmarterthanyou:t,"likes-pie":t,likescandy:t,"neat-url":t,"saves-the-whales":t,selfip:t,"sells-for-less":t,"sells-for-u":t,servebbs:t,"simple-url":t,"space-to-rent":t,"teaches-yoga":t,writesthisblog:t,ddnsfree:t,ddnsgeek:t,giize:t,gleeze:t,kozow:t,loseyourip:t,ooguy:t,theworkpc:t,mytuleap:t,"tuleap-partners":t,encoreapi:t,evennode:[0,{"eu-1":t,"eu-2":t,"eu-3":t,"eu-4":t,"us-1":t,"us-2":t,"us-3":t,"us-4":t}],onfabrica:t,"fastly-edge":t,"fastly-terrarium":t,"fastvps-server":t,mydobiss:t,firebaseapp:t,fldrv:t,forgeblocks:t,framercanvas:t,"freebox-os":t,freeboxos:t,freemyip:t,aliases121:t,gentapps:t,gentlentapis:t,githubusercontent:t,"0emm":r,appspot:[2,{r}],blogspot:t,codespot:t,googleapis:t,googlecode:t,pagespeedmobilizer:t,withgoogle:t,withyoutube:t,grayjayleagues:t,hatenablog:t,hatenadiary:t,herokuapp:t,gr:t,smushcdn:t,wphostedmail:t,wpmucdn:t,pixolino:t,"apps-1and1":t,"live-website":t,dopaas:t,"hosted-by-previder":T,hosteur:[0,{"rag-cloud":t,"rag-cloud-ch":t}],"ik-server":[0,{jcloud:t,"jcloud-ver-jpc":t}],jelastic:[0,{demo:t}],massivegrid:T,wafaicloud:[0,{jed:t,ryd:t}],webadorsite:t,joyent:[0,{cns:r}],lpusercontent:t,linode:[0,{members:t,nodebalancer:r}],linodeobjects:r,linodeusercontent:[0,{ip:t}],localtonet:t,lovableproject:t,barsycenter:t,barsyonline:t,modelscape:t,mwcloudnonprod:t,polyspace:t,mazeplay:t,miniserver:t,atmeta:t,fbsbx:he,meteorapp:I,routingthecloud:t,mydbserver:t,hostedpi:t,"mythic-beasts":[0,{caracal:t,customer:t,fentiger:t,lynx:t,ocelot:t,oncilla:t,onza:t,sphinx:t,vs:t,x:t,yali:t}],nospamproxy:[0,{cloud:[2,{o365:t}]}],"4u":t,nfshost:t,"3utilities":t,blogsyte:t,ciscofreak:t,damnserver:t,ddnsking:t,ditchyourip:t,dnsiskinky:t,dynns:t,geekgalaxy:t,"health-carereform":t,homesecuritymac:t,homesecuritypc:t,myactivedirectory:t,mysecuritycamera:t,myvnc:t,"net-freaks":t,onthewifi:t,point2this:t,quicksytes:t,securitytactics:t,servebeer:t,servecounterstrike:t,serveexchange:t,serveftp:t,servegame:t,servehalflife:t,servehttp:t,servehumour:t,serveirc:t,servemp3:t,servep2p:t,servepics:t,servequake:t,servesarcasm:t,stufftoread:t,unusualperson:t,workisboring:t,myiphost:t,observableusercontent:[0,{static:t}],simplesite:t,orsites:t,operaunite:t,"customer-oci":[0,{"*":t,oci:r,ocp:r,ocs:r}],oraclecloudapps:r,oraclegovcloudapps:r,"authgear-staging":t,authgearapps:t,skygearapp:t,outsystemscloud:t,ownprovider:t,pgfog:t,pagexl:t,gotpantheon:t,paywhirl:r,upsunapp:t,"postman-echo":t,prgmr:[0,{xen:t}],pythonanywhere:I,qa2:t,"alpha-myqnapcloud":t,"dev-myqnapcloud":t,mycloudnas:t,mynascloud:t,myqnapcloud:t,qualifioapp:t,ladesk:t,qbuser:t,quipelements:r,rackmaze:t,"readthedocs-hosted":t,rhcloud:t,onrender:t,render:U,"subsc-pay":t,"180r":t,dojin:t,sakuratan:t,sakuraweb:t,x0:t,code:[0,{builder:r,"dev-builder":r,"stg-builder":r}],salesforce:[0,{platform:[0,{"code-builder-stg":[0,{test:[0,{"001":r}]}]}]}],logoip:t,scrysec:t,"firewall-gateway":t,myshopblocks:t,myshopify:t,shopitsite:t,"1kapp":t,appchizi:t,applinzi:t,sinaapp:t,vipsinaapp:t,streamlitapp:t,"try-snowplow":t,"playstation-cloud":t,myspreadshop:t,"w-corp-staticblitz":t,"w-credentialless-staticblitz":t,"w-staticblitz":t,"stackhero-network":t,stdlib:[0,{api:t}],strapiapp:[2,{media:t}],"streak-link":t,streaklinks:t,streakusercontent:t,"temp-dns":t,dsmynas:t,familyds:t,mytabit:t,taveusercontent:t,"tb-hosting":ne,reservd:t,thingdustdata:t,"townnews-staging":t,typeform:[0,{pro:t}],hk:t,it:t,"deus-canvas":t,vultrobjects:r,wafflecell:t,hotelwithflight:t,"reserve-online":t,cprapid:t,pleskns:t,remotewd:t,wiardweb:[0,{pages:t}],wixsite:t,wixstudio:t,messwithdns:t,"woltlab-demo":t,wpenginepowered:[2,{js:t}],xnbay:[2,{u2:t,"u2-local":t}],yolasite:t}],coop:e,cr:[1,{ac:e,co:e,ed:e,fi:e,go:e,or:e,sa:e}],cu:[1,{com:e,edu:e,gob:e,inf:e,nat:e,net:e,org:e}],cv:[1,{com:e,edu:e,id:e,int:e,net:e,nome:e,org:e,publ:e}],cw:Q,cx:[1,{gov:e,cloudns:t,ath:t,info:t,assessments:t,calculators:t,funnels:t,paynow:t,quizzes:t,researched:t,tests:t}],cy:[1,{ac:e,biz:e,com:[1,{scaleforce:ce}],ekloges:e,gov:e,ltd:e,mil:e,net:e,org:e,press:e,pro:e,tm:e}],cz:[1,{contentproxy9:[0,{rsc:t}],realm:t,e4:t,co:t,metacentrum:[0,{cloud:r,custom:t}],muni:[0,{cloud:[0,{flt:t,usr:t}]}]}],de:[1,{bplaced:t,square7:t,com:t,cosidns:fe,dnsupdater:t,"dynamisches-dns":t,"internet-dns":t,"l-o-g-i-n":t,ddnss:[2,{dyn:t,dyndns:t}],"dyn-ip24":t,dyndns1:t,"home-webserver":[2,{dyn:t}],"myhome-server":t,dnshome:t,fuettertdasnetz:t,isteingeek:t,istmein:t,lebtimnetz:t,leitungsen:t,traeumtgerade:t,frusky:r,goip:t,"xn--gnstigbestellen-zvb":t,g\u00FCnstigbestellen:t,"xn--gnstigliefern-wob":t,g\u00FCnstigliefern:t,"hs-heilbronn":[0,{it:[0,{pages:t,"pages-research":t}]}],"dyn-berlin":t,"in-berlin":t,"in-brb":t,"in-butter":t,"in-dsl":t,"in-vpn":t,iservschule:t,"mein-iserv":t,schulplattform:t,schulserver:t,"test-iserv":t,keymachine:t,"git-repos":t,"lcube-server":t,"svn-repos":t,barsy:t,webspaceconfig:t,"123webseite":t,rub:t,"ruhr-uni-bochum":[2,{noc:[0,{io:t}]}],logoip:t,"firewall-gateway":t,"my-gateway":t,"my-router":t,spdns:t,speedpartner:[0,{customer:t}],myspreadshop:t,"taifun-dns":t,"12hp":t,"2ix":t,"4lima":t,"lima-city":t,"dd-dns":t,"dray-dns":t,draydns:t,"dyn-vpn":t,dynvpn:t,"mein-vigor":t,"my-vigor":t,"my-wan":t,"syno-ds":t,"synology-diskstation":t,"synology-ds":t,uberspace:r,"virtual-user":t,virtualuser:t,"community-pro":t,diskussionsbereich:t}],dj:e,dk:[1,{biz:t,co:t,firm:t,reg:t,store:t,"123hjemmeside":t,myspreadshop:t}],dm:_,do:[1,{art:e,com:e,edu:e,gob:e,gov:e,mil:e,net:e,org:e,sld:e,web:e}],dz:[1,{art:e,asso:e,com:e,edu:e,gov:e,net:e,org:e,pol:e,soc:e,tm:e}],ec:[1,{com:e,edu:e,fin:e,gob:e,gov:e,info:e,k12:e,med:e,mil:e,net:e,org:e,pro:e,base:t,official:t}],edu:[1,{rit:[0,{"git-pages":t}]}],ee:[1,{aip:e,com:e,edu:e,fie:e,gov:e,lib:e,med:e,org:e,pri:e,riik:e}],eg:[1,{ac:e,com:e,edu:e,eun:e,gov:e,info:e,me:e,mil:e,name:e,net:e,org:e,sci:e,sport:e,tv:e}],er:A,es:[1,{com:e,edu:e,gob:e,nom:e,org:e,"123miweb":t,myspreadshop:t}],et:[1,{biz:e,com:e,edu:e,gov:e,info:e,name:e,net:e,org:e}],eu:[1,{airkitapps:t,cloudns:t,dogado:[0,{jelastic:t}],barsy:t,spdns:t,transurl:r,diskstation:t}],fi:[1,{aland:e,dy:t,"xn--hkkinen-5wa":t,h\u00E4kkinen:t,iki:t,cloudplatform:[0,{fi:t}],datacenter:[0,{demo:t,paas:t}],kapsi:t,"123kotisivu":t,myspreadshop:t}],fj:[1,{ac:e,biz:e,com:e,gov:e,info:e,mil:e,name:e,net:e,org:e,pro:e}],fk:A,fm:[1,{com:e,edu:e,net:e,org:e,radio:t,user:r}],fo:e,fr:[1,{asso:e,com:e,gouv:e,nom:e,prd:e,tm:e,avoues:e,cci:e,greta:e,"huissier-justice":e,"en-root":t,"fbx-os":t,fbxos:t,"freebox-os":t,freeboxos:t,goupile:t,"123siteweb":t,"on-web":t,"chirurgiens-dentistes-en-france":t,dedibox:t,aeroport:t,avocat:t,chambagri:t,"chirurgiens-dentistes":t,"experts-comptables":t,medecin:t,notaires:t,pharmacien:t,port:t,veterinaire:t,myspreadshop:t,ynh:t}],ga:e,gb:e,gd:[1,{edu:e,gov:e}],ge:[1,{com:e,edu:e,gov:e,net:e,org:e,pvt:e,school:e}],gf:e,gg:[1,{co:e,net:e,org:e,botdash:t,kaas:t,stackit:t,panel:[2,{daemon:t}]}],gh:[1,{com:e,edu:e,gov:e,mil:e,org:e}],gi:[1,{com:e,edu:e,gov:e,ltd:e,mod:e,org:e}],gl:[1,{co:e,com:e,edu:e,net:e,org:e,biz:t}],gm:e,gn:[1,{ac:e,com:e,edu:e,gov:e,net:e,org:e}],gov:e,gp:[1,{asso:e,com:e,edu:e,mobi:e,net:e,org:e}],gq:e,gr:[1,{com:e,edu:e,gov:e,net:e,org:e,barsy:t,simplesite:t}],gs:e,gt:[1,{com:e,edu:e,gob:e,ind:e,mil:e,net:e,org:e}],gu:[1,{com:e,edu:e,gov:e,guam:e,info:e,net:e,org:e,web:e}],gw:e,gy:_,hk:[1,{com:e,edu:e,gov:e,idv:e,net:e,org:e,"xn--ciqpn":e,\u4E2A\u4EBA:e,"xn--gmqw5a":e,\u500B\u4EBA:e,"xn--55qx5d":e,\u516C\u53F8:e,"xn--mxtq1m":e,\u653F\u5E9C:e,"xn--lcvr32d":e,\u654E\u80B2:e,"xn--wcvs22d":e,\u6559\u80B2:e,"xn--gmq050i":e,\u7B87\u4EBA:e,"xn--uc0atv":e,\u7D44\u7E54:e,"xn--uc0ay4a":e,\u7D44\u7EC7:e,"xn--od0alg":e,\u7DB2\u7D61:e,"xn--zf0avx":e,\u7DB2\u7EDC:e,"xn--mk0axi":e,\u7EC4\u7E54:e,"xn--tn0ag":e,\u7EC4\u7EC7:e,"xn--od0aq3b":e,\u7F51\u7D61:e,"xn--io0a7i":e,\u7F51\u7EDC:e,inc:t,ltd:t}],hm:e,hn:[1,{com:e,edu:e,gob:e,mil:e,net:e,org:e}],hr:[1,{com:e,from:e,iz:e,name:e,brendly:ae}],ht:[1,{adult:e,art:e,asso:e,com:e,coop:e,edu:e,firm:e,gouv:e,info:e,med:e,net:e,org:e,perso:e,pol:e,pro:e,rel:e,shop:e,rt:t}],hu:[1,{2e3:e,agrar:e,bolt:e,casino:e,city:e,co:e,erotica:e,erotika:e,film:e,forum:e,games:e,hotel:e,info:e,ingatlan:e,jogasz:e,konyvelo:e,lakas:e,media:e,news:e,org:e,priv:e,reklam:e,sex:e,shop:e,sport:e,suli:e,szex:e,tm:e,tozsde:e,utazas:e,video:e}],id:[1,{ac:e,biz:e,co:e,desa:e,go:e,mil:e,my:e,net:e,or:e,ponpes:e,sch:e,web:e,zone:t}],ie:[1,{gov:e,mysprea/* REMOVED_COMMON_BLOCK_2474 */t,dnsupdate:t,"v-info":t}],int:[1,{eu:e}],io:[1,{2038:t,co:e,com:e,edu:e,gov:e,mil:e,net:e,nom:e,org:e,"on-acorn":r,myaddr:t,apigee:t,"b-data":t,beagleboard:t,bitbucket:t,bluebite:t,boxfuse:t,brave:i,browsersafetymark:t,bubble:$,bubbleapps:t,bigv:[0,{uk0:t}],cleverapps:t,cloudbeesusercontent:t,dappnode:[0,{dyndns:t}],darklang:t,definima:t,dedyn:t,"fh-muenster":t,shw:t,forgerock:[0,{id:t}],github:t,gitlab:t,lolipop:t,"hasura-app":t,hostyhosting:t,hypernode:t,moonscale:r,beebyte:T,beebyteapp:[0,{sekd1:t}],jele:t,webthings:t,loginline:t,barsy:t,azurecontainer:r,ngrok:[2,{ap:t,au:t,eu:t,in:t,jp:t,sa:t,us:t}],nodeart:[0,{stage:t}],pantheonsite:t,pstmn:[2,{mock:t}],protonet:t,qcx:[2,{sys:r}],qoto:t,vaporcloud:t,myrdbx:t,"rb-hosting":ne,"on-k3s":r,"on-rio":r,readthedocs:t,resindevice:t,resinstaging:[0,{devices:t}],hzc:t,sandcats:t,scrypted:[0,{client:t}],"mo-siemens":t,lair:he,stolos:r,musician:t,utwente:t,edugit:t,telebit:t,thingdust:[0,{dev:q,disrec:q,prod:O,testing:q}],tickets:t,webflow:t,webflowtest:t,editorx:t,wixstudio:t,basicserver:t,virtualserver:t}],iq:n,ir:[1,{ac:e,co:e,gov:e,id:e,net:e,org:e,sch:e,"xn--mgba3a4f16a":e,\u0627\u06CC\u0631\u0627\u0646:e,"xn--mgba3a4fra":e,\u0627\u064A\u0631\u0627\u0646:e,arvanedge:t}],is:e,it:[1,{edu:e,gov:e,abr:e,abruzzo:e,"aosta-valley":e,aostavalley:e,bas:e,basilicata:e,cal:e,calabria:e,cam:e,campania:e,"emilia-romagna":e,emiliaromagna:e,emr:e,"friuli-v-giulia":e,"friuli-ve-giulia":e,"friuli-vegiulia":e,"friuli-venezia-giulia":e,"friuli-/* REMOVED_COMMON_BLOCK_703 */,"16-b":t,"32-b":t,"64-b":t,myspreadshop:t,syncloud:t}],je:[1,{co:e,net:e,org:e,of:t}],jm:A,jo:[1,{agri:e,ai:e,com:e,edu:e,eng:e,fm:e,gov:e,mil:e,net:e,org:e,per:e,phd:e,sch:e,tv:e}],jobs:e,jp:[1,{ac:e,ad:e,co:e,ed:e,go:e,gr:e,lg:e,ne:[1,{aseinet:V,gehirn:t,ivory:t,"mail-box":t,mints:t,mokuren:t,opal:t,sakura:t,sumomo:t,topaz:t}],or:e,aichi:[1,{aisai:e,ama:e,anjo:e,asuke:e,chiryu:e,chita:e,fuso:e,gamagori:e,handa:e,hazu:e,hekinan:e,higashiura:e,ichinomiya:e,inazawa:e,inuyama:e,isshiki:e,iwakura:/* MULTIPLE_REMOVED_BLOCKS */,hino:e,hinode:e,hinohara:e,inagi:e,itabashi:e,katsushika:e,kita:e,kiyose:e,kodaira:e,koganei:e,kokubunji:e,komae:e,koto:e,kouzushima:e,kunitachi:e,machida:e,meguro:e,minato:e,mitaka:e,mizuho:e,musashimurayama:e,musashino:e,nakano:e,nerima:e,ogasawara:e,okutama:e,ome:e,oshima:e,ota:e,setagaya:e,shibuya:e,shinagawa:e,shinjuku:e,suginami:e,sumida:e,tachikawa:e,taito:e,tama:e,toshima:e}],tottori:[1,{chizu:e,hino:e,kawahara:e,koge:e,kotoura:e,misasa:e,nanbu:e,nichinan:e,sakaiminato:e,tottori:e,wakasa:e,yazu:e,yonago:e}],toyama:[1,{asahi:e,fuchu:e,fukumitsu:e,funahashi:e,himi:e,imizu:e,inami:e,johana:e,kamiichi:e,kurobe:e,nakaniikawa:e,namerikawa:e,nanto:e,nyuzen:e,oyabe:e,taira:e,takaoka:e,tateyama:e,toga:e,tonami:e,toyama:e,unazuki:e,uozu:e,yamada:e}],wakayama:[1,{arida:e,aridagawa:e,gobo:e,hashimoto:e,hidaka:e,hirogawa:e,inami:e,iwade:e,kainan:e,kamitonda:e,katsuragi:e,kimino:e,kinokawa:e,kitayama:e,koya:e,koza:e,kozagawa:e,kudoyama:e,kushimoto:e,mihama:e,misato:e,nachikatsuura:e,shingu:e,shirahama:e,taiji:e,tanabe:e,wakayama:e,yuasa:e,yura:e}],yamagata:[1,{asahi:e,funagata:e,higashine:e,iide:e,kahoku:e,kaminoyama:e,kaneyama:e,kawanishi:e,mamurogawa:e,mikawa:e,murayama:e,nagai:e,nakayama:e,nanyo:e,nishikawa:e,obanazawa:e,oe:e,oguni:e,ohkura:e,oishida:e,sagae:e,sakata:e,sakegawa:e,shinjo:e,shirataka:e,shonai:e,takahata:e,tendo:e,tozawa:e,tsuruoka:e,yamagata:e,yamanobe:e,yonezawa:e,yuza:e}],yamaguchi:[1,{abu:e,hagi:e,hikari:e,hofu:e,iwakuni:e,kudamatsu:e,mitou:e,nagato:e,oshima:e,shimonoseki:e,shunan:e,tabuse:e,tokuyama:e,toyota:e,ube:e,yuu:e}],yamanashi:[1,{chuo:e,doshi:e,fuefuki:e,fujikawa:e,fujikawaguchiko:e,fujiyoshida:e,hayakawa:e,hokuto:e,ichikawamisato:e,kai:e,kofu:e,koshu:e,kosuge:e,"minami-alps":e,minobu:e,nakamichi:e,nanbu:e,narusawa:e,nirasaki:e,nishikatsura:e,oshino:e,otsuki:e,showa:e,tabayama:e,tsuru:e,uenohara:e,yamanakako:e,yamanashi:e}],"xn--ehqz56n":e,\u4E09\u91CD:e,"xn--1lqs03n":e,\u4EAC\u90FD:e,"xn--qqqt11m":e,\u4F50\u8CC0:e,"xn--f6qx53a":e,\u5175\u5EAB:e,"xn--djrs72d6uy":e,\u5317\u6D77\u9053:e,"xn--mkru45i":e,\u5343\u8449:e,"xn--0trq7p7nn":e,\u548C\u6B4C\u5C71:e,"xn--5js045d":e,\u57FC\u7389:e,"xn--kbrq7o":e,\u5927\u5206:e,"xn--pssu33l":e,\u5927\u962A:e,"xn--ntsq17g":e,\u5948\u826F:e,"xn--uisz3g":e,\u5BAE\u57CE:e,"xn--6btw5a":e,\u5BAE\u5D0E:e,"xn--1ctwo":e,\u5BCC\u5C71:e,"xn--6orx2r":e,\u5C71\u53E3:e,"xn--rht61e":e,\u5C71\u5F62:e,"xn--rht27z":e,\u5C71\u68A8:e,"xn--nit225k":e,\u5C90\u961C:e,"xn--rht3d":e,\u5CA1\u5C71:e,"xn--djty4k":e,\u5CA9\u624B:e,"xn--klty5x":e,\u5CF6\u6839:e,"xn--kltx9a":e,\u5E83\u5CF6:e,"xn--kltp7d":e,\u5FB3\u5CF6:e,"xn--c3s14m":e,\u611B\u5A9B:e,"xn--vgu402c":e,\u611B\u77E5:e,"xn--efvn9s":e,\u65B0\u6F5F:e,"xn--1lqs71d":e,\u6771\u4EAC:e,"xn--4pvxs":e,\u6803\u6728:e,"xn--uuwu58a":e,\u6C96\u7E04:e,"xn--zbx025d":e,\u6ECB\u8CC0:e,"xn--8pvr4u":e,\u718A\u672C:e,"xn--5rtp49c":e,\u77F3\u5DDD:e,"xn--ntso0iqx3a":e,\u795E\u5948\u5DDD:e,"xn--elqq16h":e,\u798F\u4E95:e,"xn--4it168d":e,\u798F\u5CA1:e,"xn--klt787d":e,\u798F\u5CF6:e,"xn--rny31h":e,\u79CB\u7530:e,"xn--7t0a264c":e,\u7FA4\u99AC:e,"xn--uist22h":e,\u8328\u57CE:e,"xn--8ltr62k":e,\u9577\u5D0E:e,"xn--2m4a15e":e,\u9577\u91CE:e,"xn--32vp30h":e,\u9752\u68EE:e,"xn--4it797k":e,\u9759\u5CA1:e,"xn--5rtq34k":e,\u9999\u5DDD:e,"xn--k7yn95e":e,\u9AD8\u77E5:e,"xn--tor131o":e,\u9CE5\u53D6:e,"xn--d5qv7z876c":e,\u9E7F\u5150\u5CF6:e,kawasaki:A,kitakyushu:A,kobe:A,nagoya:A,sapporo:A,sendai:A,yokohama:A,buyshop:t,fashionstore:t,handcrafted:t,kawaiishop:t,supersale:t,theshop:t,"0/* REMOVED_COMMON_BLOCK_2475 */,zombie:t,hateblo:t,hatenablog:t,hatenadiary:t,"2-d":t,bona:t,crap:t,daynight:t,eek:t,flop:t,halfmoon:t,jeez:t,matrix:t,mimoza:t,netgamers:t,nyanta:t,o0o0:t,rdy:t,rgr:t,rulez:t,sakurastorage:[0,{isk01:ue,isk02:ue}],saloon:t,sblo:t,skr:t,tank:t,"uh-oh":t,undo:t,webaccel:[0,{rs:t,user:t}],websozai:t,xii:t}],ke:[1,{ac:e,co:e,go:e,info:e,me:e,mobi:e,ne:e,or:e,sc:e}],kg:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,us:t}],kh:A,ki:me,km:[1,{ass:e,com:e,edu:e,gov:e,mil:e,nom:e,org:e,prd:e,tm:e,asso:e,coop:e,gouv:e,medecin:e,notaires:e,pharmaciens:e,presse:e,veterinaire:e}],kn:[1,{edu:e,gov:e,net:e,org:e}],kp:[1,{com:e,edu:e,gov:e,org:e,rep:e,tra:e}],kr:[1,{ac:e,ai:e,co:e,es:e,go:e,hs:e,io:e,it:e,kg:e,me:e,mil:e,ms:e,ne:e,or:e,pe:e,re:e,sc:e,busan:e,chungbuk:e,chungnam:e,daegu:e,daejeon:e,gangwon:e,gwangju:e,gyeongbuk:e,gyeonggi:e,gyeongnam:e,incheon:e,jeju:e,jeonbuk:e,jeonnam:e,seoul:e,ulsan:e,c01:t,"eliv-dns":t}],kw:[1,{com:e,edu:e,emb:e,gov:e,ind:e,net:e,org:e}],ky:Q,kz:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,jcloud:t}],la:[1,{com:e,edu:e,gov:e,info:e,int:e,net:e,org:e,per:e,bnr:t}],lb:a,lc:[1,{co:e,com:e,edu:e,gov:e,net:e,org:e,oy:t}],li:e,lk:[1,{ac:e,assn:e,com:e,edu:e,gov:e,grp:e,hotel:e,int:e,ltd:e,net:e,ngo:e,org:e,sch:e,soc:e,web:e}],lr:a,ls:[1,{ac:e,biz:e,co:e,edu:e,gov:e,info:e,net:e,org:e,sc:e}],lt:u,lu:[1,{"123website":t}],lv:[1,{asn:e,com:e,conf:e,edu:e,gov:e,id:e,mil:e,net:e,org:e}],ly:[1,{com:e,edu:e,gov:e,id:e,med:e,net:e,org:e,plc:e,sch:e}],ma:[1,{ac:e,co:e,gov:e,net:e,org:e,press:e}],mc:[1,{asso:e,tm:e}],md:[1,{ir:t}],me:[1,{ac:e,co:e,edu:e,gov:e,its:e,net:e,org:e,priv:e,c66:t,craft:t,edgestack:t,filegear:t,glitch:t,"filegear-sg":t,lohmus:t,barsy:t,mcdir:t,brasilia:t,ddns:t,dnsfor:t,hopto:t,loginto:t,noip:t,webhop:t,soundcast:t,tcp4:t,vp4:t,diskstation:t,dscloud:t,i234:t,myds:t,synology:t,transip:ne,nohost:t}],mg:[1,{co:e,com:e,edu:e,gov:e,mil:e,nom:e,org:e,prd:e}],mh:e,mil:e,mk:[1,{com:e,edu:e,gov:e,inf:e,name:e,net:e,org:e}],ml:[1,{ac:e,art:e,asso:e,com:e,edu:e,gouv:e,gov:e,info:e,inst:e,net:e,org:e,pr:e,presse:e}],mm:A,mn:[1,{edu:e,gov:e,org:e,nyc:t}],mo:a,mobi:[1,{barsy:t,dscloud:t}],mp:[1,{ju:t}],mq:e,mr:u,ms:[1,{com:e,edu:e,gov:e,net:e,org:e,minisite:t}],mt:Q,mu:[1,{ac:e,co:e,com:e,gov:e,net:e,or:e,org:e}],museum:e,mv:[1,{aero:e,biz:e,com:e,coop:e,edu:e,gov:e,info:e,int:e,mil:e,museum:e,name:e,net:e,org:e,pro:e}],mw:[1,{ac:e,biz:e,co:e,com:e,coop:e,edu:e,gov:e,int:e,net:e,org:e}],mx:[1,{com:e,edu:e,gob:e,net:e,org:e}],my:[1,{biz:e,com:e,edu:e,gov:e,mil:e,name:e,net:e,org:e}],mz:[1,{ac:e,adv:e,co:e,edu:e,gov:e,mil:e,net:e,org:e}],na:[1,{alt:e,co:e,com:e,gov:e,net:e,org:e}],name:[1,{her:Re,his:Re}],nc:[1,{asso:e,nom:e}],ne:e,net:[1,{adobeaemcloud:t,"adobeio-static":t,adobeioruntime:t,akadns:t,akamai:t,"akamai-staging":t,akamaiedge:t,"akamaiedge-staging":t,akamaihd:t,"akamaihd-staging":t,akamaiorigin:t,"akamaiorigin-staging":t,akamaized:t,"akamaized-staging":t,edgekey:t,"edgekey-staging":t,edgesuite:t,"edgesuite-staging":t,alwaysdata:t,myamaze:t,cloudfront:t,appudo:t,"atlassian-dev":[0,{prod:$}],myfritz:t,onavstack:t,shopselect:t,blackbaudcdn:t,boomla:t,bplaced:t,square7:t,cdn77:[0,{r:t}],"cdn77-ssl":t,gb:t,hu:t,jp:t,se:t,uk:t,clickrising:t,"ddns-ip":t,"dns-cloud":t,"dns-dynamic":t,cloudaccess:t,cloudflare:[2,{cdn:t}],cloudflareanycast:$,cloudflarecn:$,cloudflareglobal:$,ctfcloud:t,"feste-ip":t,"knx-server":t,"static-access":t,cryptonomic:r,dattolocal:t,mydatto:t,debian:t,definima:t,deno:t,"at-band-camp":t,blogdns:t,"broke-it":t,buyshouses:t,dnsalias:t,dnsdojo:t,"does-it":t,dontexist:t,dynalias:t,dynathome:t,endofinternet:t,"from-az":t,"from-co":t,"from-la":t,"from-ny":t,"gets-it":t,"ham-radio-op":t,homeftp:t,homeip:t,homelinux:t,homeunix:t,"in-the-band":t,"is-a-chef":t,"is-a-geek":t,"isa-geek":t,"kicks-ass":t,"office-on-the":t,podzone:t,"scrapper-site":t,selfip:t,"sells-it":t,servebbs:t,serveftp:t,thruhere:t,webhop:t,casacam:t,dynu:t,dynv6:t,twmail:t,ru:t,channelsdvr:[2,{u:t}],fastly:[0,{freetls:t,map:t,prod:[0,{a:t,global:t}],ssl:[0,{a:t,b:t,global:t}]}],fastlylb:[2,{map:t}],edgeapp:t,"keyword-on":t,"live-on":t,"server-on":t,"cdn-edges":t,heteml:t,cloudfunctions:t,"grafana-dev":t,iobb:t,moonscale:t,"in-dsl":t,"in-vpn":t,oninferno:t,botdash:t,"apps-1and1":t,ipifony:t,cloudjiffy:[2,{"fra1-de":t,"west1-us":t}],elastx:[0,{"jls-sto1":t,"jls-sto2":t,"jls-sto3":t}],massivegrid:[0,{paas:[0,{"fr-1":t,"lon-1":t,"lon-2":t,"ny-1":t,"ny-2":t,"sg-1":t}]}],saveincloud:[0,{jelastic:t,"nordeste-idc":t}],scaleforce:ce,kinghost:t,uni5:t,krellian:t,ggff:t,localcert:t,localhostcert:t,localto:r,barsy:t,luyani:t,memset:t,"azure-api":t,"azure-mobile":t,azureedge:t,azurefd:t,azurestaticapps:[2,{1:t,2:t,3:t,4:t,5:t,6:t,7:t,centralus:t,eastasia:t,eastus2:t,westeurope:t,westus2:t}],azurewebsites:t,cloudapp:t,trafficmanager:t,windows:[0,{core:[0,{blob:t}],servicebus:t}],mynetname:[0,{sn:t}],routingthecloud:t,bounceme:t,ddns:t,"eating-organic":t,mydissent:t,myeffect:t,mymediapc:t,mypsx:t,mysecuritycamera:t,nhlfan:t,"no-ip":t,pgafan:t,privatizehealthinsurance:t,redirectme:t,serveblog:t,serveminecraft:t,sytes:t,dnsup:t,hicam:t,"now-dns":t,ownip:t,vpndns:t,cloudycluster:t,ovh:[0,{hosting:r,webpaas:r}],rackmaze:t,myradweb:t,in:t,"subsc-pay":t,squares:t,schokokeks:t,"firewall-gateway":t,seidat:t,senseering:t,siteleaf:t,mafelo:t,myspreadshop:t,"vps-host":[2,{jelastic:[0,{atl:t,njs:t,ric:t}]}],srcf:[0,{soc:t,user:t}],supabase:t,dsmynas:t,familyds:t,ts:[2,{c:r}],torproject:[2,{pages:t}],vusercontent:t,"reserve-online":t,"community-pro":t,meinforum:t,yandexcloud:[2,{storage:t,website:t}],za:t}],nf:[1,{arts:e,com:e,firm:e,info:e,net:e,other:e,per:e,rec:e,store:e,web:e}],ng:[1,{com:e,edu:e,gov:e,i:e,mil:e,mobi:e,name:e,net:e,org:e,sch:e,biz:[2,{co:t,dl:t,go:t,lg:t,on:t}],col:t,firm:t,gen:t,ltd:t,ngo:t,plc:t}],ni:[1,{ac:e,biz:e,co:e,com:e,edu:e,gob:e,in:e,info:e,int:e,mil:e,net:e,nom:e,org:e,web:e}],nl:[1,{co:t,"hosting-cluster":t,gov:t,khplay:t,"123website":t,myspreadshop:t,transurl:r,cistron:t,demon:t}],no:[1,{fhs:e,folkebibl:e,fylkesbibl:e,idrett:e,museum:e,priv:e,vgs:e,dep:e,herad:e,kommune:e,mil:e,stat:e,aa:se,ah:se,bu:se,fm:se,hl:se,hm:se,"jan-mayen":se,mr:se,nl:se,nt:se,of:se,ol:se,oslo:se,rl:se,sf:se,st:se,svalbard:se,tm:se,tr:se,va:se,vf:se,akrehamn:e,"xn--krehamn-dxa":e,\u00E5krehamn:e,algard:e,"xn--lgrd-poac":e,\u00E5lg\u00E5rd:e,arna:e,bronnoysund:e,"xn--brnnysund-m8ac":e,br\u00F8nn\u00F8ysund:e,brumunddal:e,bryne:e,drobak:e,"xn--drbak-wua":e,dr\u00F8bak:e,egersund:e,fetsund:e,floro:e,"xn--flor-jra":e,flor\u00F8:e,fredrikstad:e,hokksund:e,honefoss:e,"xn--hnefoss-q1a":e,h\u00F8nefoss:e,jessheim:e,jorpeland:e,"xn--jrpeland-54a":e,j\u00F8rpeland:e,kirkenes:e,kopervik:e,krokstadelva:e,langevag:e,"xn--langevg-jxa":e,langev\u00E5g:e,leirvik:e,mjondalen:e,"xn--mjndalen-64a":e,mj\u00F8ndalen:e,"mo-i-rana":e,mosjoen:e,"xn--mosjen-eya":e,mosj\u00F8en:e,nesoddtangen:e,orkanger:e,osoyro:e,"xn--osyro-wua":e,os\u00F8yro:e,raholt:e,"xn--rholt-mra":e,r\u00E5holt:e,sandnessjoen:e,"xn--sandnessjen-ogb":e,sandnessj\u00F8en:e,skedsmokorset:e,slattum:e,spjelkavik:e,stathelle:e,stavern:e,stjordalshalsen:e,"xn--stjrdalshalsen-sqb":e,stj\u00F8rdalshalsen:e,tananger:e,tranby:e,vossevangen:e,aarborte:e,aejrie:e,afjord:e,"xn--fjord-lra":e,\u00E5fjord:e,agdenes:e,akershus:Ce,aknoluokta:e,"xn--koluokta-7ya57h":e,\u00E1k\u014Boluokta:e,al:e,"xn--l-1fa":e,\u00E5l:e,alaheadju:e,"xn--laheadju-7ya":e,\u00E1laheadju:e,alesund:e,"xn--lesund-hua":e,\u00E5lesund:e,alstahaug:e,alta:e,"xn--lt-liac":e,\u00E1lt\u00/* REMOVED_COMMON_BLOCK_2476 */n--bearalvhki-y4a":e,bearalv\u00E1hki:e,beardu:e,beiarn:e,berg:e,bergen:e,berlevag:e,"xn--berlevg-jxa":e,berlev\u00E5g:e,bievat:e,"xn--bievt-0qa":e,biev\u00E1t:e,bindal:e,birkenes:e,bjarkoy:e,"xn--bjarky-fya":e,bjark\u00F8y:e,bjerkreim:e,bjugn:e,bodo:e,"xn--bod-2na":e,bod\u00F8:e,bokn:e,bomlo:e,"xn--bmlo-gra":e,b\u00F8mlo:e,bremanger:e,bronnoy:e,"xn--brnny-wuac":e,br\u00F8nn\u00F8y:e,budejju:e,buskerud:Ce,bygland:e,bykle:e,cahcesuolo:e,"xn--hcesuolo-7ya35b":e,\u010D\u00E1hcesuolo:e,davvenjarga:e/* REMOVED_COMMON_BLOCK_731 */tfold-9xa":[0,{"xn--vler-qoa":e}],\u00F8stfold:[0,{v\u00E5ler:e}],"ostre-toten":e,"xn--stre-toten-zcb":e,"\xF8stre-toten":e,overhalla:e,"ovre-eiker":e,"xn--vre-eiker-k8a":e,"\xF8vre-eiker":e,oyer:e,"xn--yer-zna":e,\u00F8yer:e,oygarden:e,"xn--ygarden-p1a":e,\u00F8ygarden:e,"oystre-slidre":e,"xn--ystre-slidre-ujb":e,"\xF8ystre-slidre":e,porsanger:e,porsangu:e,"xn--porsgu-sta26f":e,pors\u00E1\u014Bgu:e,porsgrunn:e,rade:e,"xn--rde-ula":e,r\u00E5de:e,radoy:e,"xn--rady-ira":e,rad\u00F8y:e,"xn--rlingen-mxa":e,r\u00E6lingen:e,rahkkeravju:e,"xn--rhkkervju-01af":e,r\u00E1hkker\u00E1vju:e,raisa:e,"xn--risa-5na":e,r\u00E1isa:e,rakkestad:e,ralingen:e,rana:e,randaberg:e,rauma:e,rendalen:e,rennebu:e,rennesoy:e,"xn--rennesy-v1a":e,rennes\u00F8y:e,rindal:e,ringebu:e,ringerike:e,ringsaker:e,risor:e,"xn--risr-ira":e,ris\u00F8r:e,rissa:e,roan:e,rodoy:e,"xn--rdy-0nab":e,r\u00F8d\u00F8y:e,rollag:e,romsa:e,romskog:e,"xn--rmskog-bya":e,r\u00F8mskog:e,roros:e,"xn--rros-gra":e,r\u00F8ros:e,rost:e,"xn--rst-0na":e,r\u00F8st:e,royken:e,"xn--ryken-vua":e,r\u00F8yken:e,royrvik:e,"xn--ryrvik-bya":e,r\u00F8yrvik:e,ruovat:e,rygge:e,salangen:e,salat:e,"xn--slat-5na":e,s\u00E1lat:e,"xn--slt-elab":e,s\u00E1l\u00E1t:e,saltdal:e,samnanger:e,sandefjord:e,sandnes:e,sandoy:e,"xn--sandy-yua":e,sand\u00F8y:e,sarpsborg:e,sauda:e,sauherad:e,sel:e,selbu:e,selje:e,seljord:e,siellak:e,sigdal:e,siljan:e,sirdal:e,skanit:e,"xn--sknit-yqa":e,sk\u00E1nit:e,skanland:e,"xn--sknland-fxa":e,sk\u00E5nland:e,skaun:e,skedsmo:e,ski:e,skien:e,skierva:e,"xn--skierv-uta":e,skierv\u00E1:e,skiptvet:e,skjak:e,"xn--skjk-soa":e,skj\u00E5k:e,skjervoy:e,"xn--skjervy-v1a":e,skjerv\u00F8y:e,skodje:e,smola:e,"xn--smla-hra":e,sm\u00F8la:e,snaase:e,"xn--snase-nra":e,sn\u00E5ase:e,snasa:e,"xn--snsa-roa":e,sn\u00E5sa:e,snillfjord:e,snoasa:e,sogndal:e,sogne:e,"xn--sgne-gra":e,s\u00F8gne:e,sokndal:e,sola:e,solund:e,somna:e,"xn--smna-gra":e,s\u00F8mna:e,"sondre-land":e,"xn--sndre-land-0cb":e,"s\xF8ndre-land":e,songdalen:e,"sor-aurdal":e,"xn--sr-aurdal-l8a":e,"s\xF8r-aurdal":e,"sor-fron":e,"xn--sr-fron-q1a":e,"s\xF8r-fron":e,"sor-odal":e,"xn--sr-odal-q1a":e,"s\xF8r-odal":e,"sor-varanger":e,"xn--sr-varanger-ggb":e,"s\xF8r-varanger":e,sorfold:e,"xn--srfold-bya":e,s\u00F8rfold:e,sorreisa:e,"xn--srreisa-q1a":e,s\u00F8rreisa:e,sortland:e,sorum:e,"xn--srum-gra":e,s\u00F8rum:e,spydeberg:e,stange:e,stavanger:e,steigen:e,steinkjer:e,stjordal:e,"xn--stjrdal-s1a":e,stj\u00F8rdal:e,stokke:e,"stor-elvdal":e,stord:e,stordal:e,storfjord:e,strand:e,stranda:e,stryn:e,sula:e,suldal:e,sund:e,sunndal:e,surnadal:e,sveio:e,svelvik:e,sykkylven:e,tana:e,telemark:[0,{bo:e,"xn--b-5ga":e,b\u00F8:e}],time:e,tingvoll:e,tinn:e,tjeldsund:e,tjome:e,"xn--tjme-hra":e,tj\u00F8me:e,tokke:e,tolga:e,tonsberg:e,"xn--tnsberg-q1a":e,t\u00F8nsberg:e,torsken:e,"xn--trna-woa":e,tr\u00E6na:e,trana:e,tranoy:e,"xn--trany-yua":e,tran\u00F8y:e,troandin:e,trogstad:e,"xn--trgstad-r1a":e,tr\u00F8gstad:e,tromsa:e,tromso:e,"xn--troms-zua":e,troms\u00F8:e,trondheim:e,trysil:e,tvedestrand:e,tydal:e,tynset:e,tysfjord:e,tysnes:e,"xn--tysvr-vra":e,tysv\u00E6r:e,tysvar:e,ullensaker:e,ullensvang:e,ulvik:e,unjarga:e,"xn--unjrga-rta":e,unj\u00E1rga:e,utsira:e,vaapste:e,vadso:e,"xn--vads-jra":e,vads\u00F8:e,"xn--vry-yla5g":e,v\u00E6r\u00F8y:e,vaga:e,"xn--vg-yiab":e,v\u00E5g\u00E5:e,vagan:e,"xn--vgan-qoa":e,v\u00E5gan:e,vagsoy:e,"xn--vgsy-qoa0j":e,v\u00E5gs\u00F8y:e,vaksdal:e,valle:e,vang:e,vanylven:e,vardo:e,"xn--vard-jra":e,vard\u00F8:e,varggat:e,"xn--vrggt-xqad":e,v\u00E1rgg\u00E1t:e,varoy:e,vefsn:e,vega:e,vegarshei:e,"xn--vegrshei-c0a":e,veg\u00E5rshei:e,vennesla:e,verdal:e,verran:e,vestby:e,vestfold:[0,{sande:e}],vestnes:e,"vestre-slidre":e,"vestre-toten":e,vestvagoy:e,"xn--vestvgy-ixa6o":e,vestv\u00E5g\u00F8y:e,vevelstad:e,vik:e,vikna:e,vindafjord:e,voagat:e,volda:e,voss:e,co:t,"123hjemmeside":t,myspreadshop:t}],np:A,nr:me,nu:[1,{merseine:t,mine:t,shacknet:t,enterprisecloud:t}],nz:[1,{ac:e,co:e,cri:e,geek:e,gen:e,govt:e,health:e,iwi:e,kiwi:e,maori:e,"xn--mori-qsa":e/* REMOVED_COMMON_BLOCK_2477 */:t,"stuff-4-sale":t,webhop:t,accesscam:t,camdvr:t,freeddns:t,mywire:t,webredirect:t,twmail:t,eu:[2,{al:t,asso:t,at:t,au:t,be:t,bg:t,ca:t,cd:t,ch:t,cn:t,cy:t,cz:t,de:t,dk:t,edu:t,ee:t,es:t,fi:t,fr:t,gr:t,hr:t,hu:t,ie:t,il:t,in:t,int:t,is:t,it:t,jp:t,kr:t,lt:t,lu:t,lv:t,me:t,mk:t,mt:t,my:t,net:t,ng:t,nl:t,no:t,nz:t,pl:t,pt:t,ro:t,ru:t,se:t,si:t,sk:t,tr:t,uk:t,us:t}],fedorainfracloud:t,fedorapeople:t,fedoraproject:[0,{cloud:t,os:U,stg:[0,{os:U}]}],freedesktop:t,hatenadiary:t,hepforge:t,"in-dsl":t,"in-vpn":t,js:t,barsy:t,mayfirst:t,routingthecloud:t,bmoattachments:t,"cable-modem":t,collegefan:t,couchpotatofries:t,hopto:t,mlbfan:t,myftp:t,mysecuritycamera:t,nflfan:t,"no-ip":t,"read-books":t,ufcfan:t,zapto:t,dynserv:t,"now-dns":t,"is-local":t,httpbin:t,pubtls:t,jpn:t,"my-firewall":t,myfirewall:t,spdns:t,"small-web":t,dsmynas:t,familyds:t,teckids:ue,tuxfamily:t,diskstation:t,hk:t,us:t,toolforge:t,wmcloud:t,wmflabs:t,za:t}],pa:[1,{abo:e,ac:e,com:e,edu:e,gob:e,ing:e,med:e,net:e,nom:e,org:e,sld:e}],pe:[1,{com:e,edu:e,gob:e,mil:e,net:e,nom:e,org:e}],pf:[1,{com:e,edu:e,org:e}],pg:A,ph:[1,{com:e,edu:e,gov:e,i:e,mil:e,net:e,ngo:e,org:e,cloudns:t}],pk:[1,{ac:e,biz:e,com:e,edu:e,fam:e,gkp:e,gob:e,gog:e,gok:e,gop:e,gos:e,gov:e,net:e,org:e,web:e}],pl:[1,{com:e,net:e,org:e,agro:e,aid:e,atm:e,auto:e,biz:e,edu:e,gmina:e,gsm:e,info:e,mail:e,media:e,miasta:e,mil:e,nieruchomosci:e,nom:e,pc:e,powiat:e,priv:e,realestate:e,rel:e,sex:e,shop:e,sklep:e,sos:e,szkola:e,targi:e,tm:e,tourism:e,travel:e,turyst/* REMOVED_COMMON_BLOCK_2463 */,gov:e,net:e,org:e}],post:e,pr:[1,{biz:e,com:e,edu:e,gov:e,info:e,isla:e,name:e,net:e,org:e,pro:e,ac:e,est:e,prof:e}],pro:[1,{aaa:e,aca:e,acct:e,avocat:e,bar:e,cpa:e,eng:e,jur:e,law:e,med:e,recht:e,"12chars":t,cloudns:t,barsy:t,ngrok:t}],ps:[1,{com:e,edu:e,gov:e,net:e,org:e,plo:e,sec:e}],pt:[1,{com:e,edu:e,gov:e,int:e,net:e,nome:e,org:e,publ:e,"123paginaweb":t}],pw:[1,{gov:e,cloudns:t,x443:t}],py:[1,{com:e,coop:e,edu:e,gov:e,mil:e,net:e,org:e}],qa:[1,{com:e,edu:e,gov:e,mil:e,name:e,net:e,org:e,sch:e}],re:[1,{asso:e,com:e,netlib:t,can:t}],ro:[1,{arts:e,com:e,firm:e,info:e,nom:e,nt:e,org:e,rec:e,store:e,tm:e,www:e,co:t,shop:t,barsy:t}],rs:[1,{ac:e,co:e,edu:e,gov:e,in:e,org:e,brendly:ae,barsy:t,ox:t}],ru:[1,{ac:t,edu:t,gov:t,int:t,mil:t,eurodir:t,adygeya:t,bashkiria:t,bir:t,cbg:t,com:t,dagestan:t,grozny:t,kalmykia:t,kustanai:t,marine:t,mordovia:t,msk:t,mytis:t,nalchik:t,nov:t,pyatigorsk:t,spb:t,vladikavkaz:t,vladimir:t,na4u:t,mircloud:t,myjino:[2,{hosting:r,landing:r,spectrum:r,vps:r}],cldmail:[0,{hb:t}],mcdir:[2,{vps:t}],mcpre:t,net:t,org:t,pp:t,lk3:t,ras:t}],rw:[1,{ac:e,co:e,coop:e,gov:e,mil:e,net:e,org:e}],sa:[1,{com:e,edu:e,gov:e,med:e,net:e,org:e,pub:e,sch:e}],sb:a,sc:a,sd:[1,{com:e,edu:e,gov:e,info:e,med:e,net:e,org:e,tv:e}],se:[1,{a:e,ac:e,b:e,bd:e,brand:e,c:e,d:e,e,f:e,fh:e,fhsk:e,fhv:e,g:e,h:e,i:e,k:e,komforb:e,kommunalforbund:e,komvux:e,l:e,lanbib:e,m:e,n:e,naturbruksgymn:e,o:e,org:e,p:e,parti:e,pp:e,press:e,r:e,s:e,t:e,tm:e,u:e,w:e,x:e,y:e,z:e,com:t,iopsys:t,"123minsida":t,itcouldbewor:t,myspreadshop:t}],sg:[1,{com:e,edu:e,gov:e,net:e,org:e,enscaled:t}],sh:[1,{com:e,gov:e,mil:e,net:e,org:e,hashbang:t,botda:t,platform:[0,{ent:t,eu:t,us:t}],now:t}],si:[1,{f5:t,gitapp:t,gitpage:t}],sj:e,sk:e,sl:a,sm:e,sn:[1,{art:e,com:e,edu:e,gouv:e,org:e,perso:e,univ:e}],so:[1,{com:e,edu:e,gov:e,me:e,net:e,org:e,surveys:t}],sr:e,ss:[1,{biz:e,co:e,com:e,edu:e,gov:e,me:e,net:e,org:e,sch:e}],st:[1,{co:e,com:e,consulado:e,edu:e,embaixada:e,mil:e,net:e,org:e,principe:e,saotome:e,store:e,helioho:t,kirara:t,noho:t}],su:[1,{abkhazia:t,adygeya:t,aktyubinsk:t,arkhangelsk:t,armenia:t,ashgabad:t,azerbaijan:t,balashov:t,bashkiria:t,bryansk:t,bukhara:t,chimkent:t,dagestan:t,"east-kazakhstan":t,exnet:t,georgia:t,grozny:t,ivanovo:t,jambyl:t,kalmykia:t,kaluga:t,karacol:t,karaganda:t,karelia:t,khakassia:t,krasnodar:t,kurgan:t,kustanai:t,lenug:t,mangyshlak:t,mordovia:t,msk:t,murmansk:t,nalchik:t,navoi:t,"north-kazakhstan":t,nov:t,obninsk:t,penza:t,pokrovsk:t,sochi:t,spb:t,tashkent:t,termez:t,togliatti:t,troitsk:t,tselinograd:t,tula:t,tuva:t,vladikavkaz:t,vladimir:t,vologda:t}],sv:[1,{com:e,edu:e,gob:e,org:e,red:e}],sx:u,sy:n,sz:[1,{ac:e,co:e,org:e}],tc:e,td:e,tel:e,tf:[1,{sch:t}],tg:e,th:[1,{ac:e,co:e,go:e,in:e,mi:e,net:e,or:e,online:t,shop:t}],tj:[1,{ac:e,biz:e,co:e,com:e,edu:e,go:e,gov:e,int:e,mil:e,name:e,net:e,nic:e,org:e,test:e,web:e}],tk:e,tl:u,tm:[1,{co:e,com:e,edu:e,gov:e,mil:e,net:e,nom:e,org:e}],tn:[1,{com:e,ens:e,fin:e,gov:e,ind:e,info:e,intl:e,mincom:e,nat:e,net:e,org:e,perso:e,tourism:e,orangecloud:t}],to:[1,{611:t,com:e,edu:e,gov:e,mil:e,net:e,org:e,oya:t,x0:t,quickconnect:E,vpnplus:t}],tr:[1,{av:e,bbs:e,bel:e,biz:e,com:e,dr:e,edu:e,gen:e,gov:e,info:e,k12:e,kep:e,mil:e,name:e,net:e,org:e,pol:e,tel:e,tsk:e,tv:e,web:e,nc:u}],tt:[1,{biz:e,co:e,com:e,edu:e,gov:e,info:e,mil:e,name:e,net:e,org:e,pro:e}],tv:[1,{"better-than":t,dyndns:t,"on-the-web":t,"worse-than":t,from:t,sakura:t}],tw:[1,{club:e,com:[1,{mymailer:t}],ebiz:e,edu:e,game:e,gov:e,idv:e,mil:e,net:e,org:e,url:t,mydns:t}],tz:[1,{ac:e,co:e,go:e,hotel:e,info:e,me:e,mil:e,mobi:e,ne:e,or:e,sc:e,tv:e}],ua:[1,{com:e,edu:e,gov:e,in:e,net:e,org:e,cherkassy:e,cherkasy:e,chernigov:e,chernihiv:e,chernivtsi:e,chernovtsy:e,ck:e,cn:e,cr:e,crimea:e,cv:e,dn:e,dnepropetrovsk:e,dnipropetrovsk:e,donetsk:e,dp:e,if:e,"ivano-frankivsk":e,kh:e,kharkiv:e,kharkov:e,kherson:e,khmelnitskiy:e,khmelnytskyi:e,kiev:e,kirovograd:e,km:e,kr:e,kropyvnytskyi:e,krym:e,ks:e,kv:e,kyiv:e,lg:e,lt:e,lugansk:e,luhansk:e,lutsk:e,lv:e,lviv:e,mk:e,mykolaiv:e,nikolaev:e,od:e,odesa:e,odessa:e,pl:e,poltava:e,rivne:e,rovno:e,rv:e,sb:e,sebastopol:e,sevastopol:e,sm:e,sumy:e,te:e,ternopil:e,uz:e,uzhgorod:e,uzhhorod:e,vinnica:e,vinnytsia:e,vn:e,volyn:e,yalta:e,zakarpattia:e,zaporizhzhe:e,zaporizhzhia:e,zhitomir:e,zhytomyr:e,zp:e,zt:e,cc:t,inf:t,ltd:t,cx:t,biz:t,co:t,pp:t,v:t}],ug:[1,{ac:e,co:e,com:e,edu:e,go:e,gov:e,mil:e,ne:e,or:e,org:e,sc:e,us:e}],uk:[1,{ac:e,co:[1,{bytemark:[0,{dh:t,vm:t}],layershift:ce,barsy:t,barsyonline:t,retrosnub:O,"nh-serv":t,"no-ip":t,adimo:t,myspreadshop:t}],gov:[1,{api:t,campaign:t,service:t}],ltd:e,me:e,net:e,nhs:e,org:[1,{glug:t,lug:t,lugs:t,affinitylottery:t,raffleentry:t,weeklylottery:t}],plc:e,police:e,sch:A,conn:t,copro:t,hosp:t,"independent-commission":t,"independent-inquest":t,"independent-inquiry":t,"independent-panel":t,"independent-review":t,"public-inquiry":t,"royal-commission":t,pymnt:t,barsy:t,nimsite:t,oraclegovcloudapps:r}],us:[1,{dni:e,isa:e,nsn:e,ak:W,al:W,ar:W,as:W,az:W,ca:W,co:W,ct:W,dc:W,de:[1,{cc:e,lib:t}],fl:W,ga:W,gu:W,hi:He,ia:W,id:W,il:W,in:W,ks:W,ky:W,la:W,ma:[1,{k12:[1,{chtr:e,paroch:e,pvt:e}],cc:e,lib:e}],md:W,me:W,mi:[1,{k12:e,cc:e,lib:e,"ann-arbor":e,cog:e,dst:e,eaton:e,gen:e,mus:e,tec:e,washtenaw:e}],mn:W,mo:W,ms:W,mt:W,nc:W,nd:He,ne:W,nh:W,nj:W,nm:W,nv:W,ny:W,oh:W,ok:W,or:W,pa:W,pr:W,ri:He,sc:W,sd:He,tn:W,tx:W,ut:W,va:W,vi:W,vt:W,wa:W,wi:W,wv:[1,{cc:e}],wy:W,cloudns:t,"is-by":t,"land-4-sale":t,"stuff-4-sale":t,heliohost:t,enscaled:[0,{phx:t}],mircloud:t,ngo:t,golffan:t,noip:t,pointto:t,freeddns:t,srv:[2,{gh:t,gl:t}],platterp:t,servername:t}],uy:[1,{com:e,edu:e,gub:e,mil:e,net:e,org:e}],uz:[1,{co:e,com:e,net:e,org:e}],va:e,vc:[1,{com:e,edu:e,gov:e,mil:e,net:e,org:e,gv:[2,{d:t}],"0e":r,mydns:t}],ve:[1,{arts:e,bib:e,co:e,com:e,e12:e,edu:e,emprende:e,firm:e,gob:e,gov:e,info:e,int:e,mil:e,net:e,nom:e,org:e,rar:e,rec:e,store:e,tec:e,web:e}],vg:[1,{edu:e}],vi:[1,{co:e,com:e,k12:e,net:e,org:e}],vn:[1,{ac:e,ai:e,biz:e,com:e,edu:e,gov:e,health:e,id:e,info:e,int:e,io:e,name:e,net:e,org:e,pro:e,angiang:e,bacgiang:e,backan:e,baclieu:e,bacninh:e,"baria-vungtau":e,bentre:e,binhdinh:e,binhduong:e,binhphuoc:e,binhthuan:e,camau:e,cantho:e,caobang:e,daklak:e,daknong:e,danang:e,dienbien:e,dongnai:e,dongthap:e,gialai:e,hagiang:e,haiduong:e,haiphong:e,hanam:e,hanoi:e,hatinh:e,haugiang:e,hoabinh:e,hungyen:e,khanhhoa:e,kiengiang:e,kontum:e,laichau:e,lamdong:e,langson:e,laocai:e,longan:e,namdinh:e,nghean:e,ninhbinh:e,ninhthuan:e,phutho:e,phuyen:e,quangbinh:e,quangnam:e,quangngai:e,quangninh:e,quangtri:e,soctrang:e,sonla:e,tayninh:e,thaibinh:e,thainguyen:e,thanhhoa:e,thanhphohochiminh:e,thuathienhue:e,tiengiang:e,travinh:e,tuyenquang:e,vinhlong:e,vinhphuc:e,yenbai:e}],vu:Q,wf:[1,{biz:t,sch:t}],ws:[1,{com:e,edu:e,gov:e,net:e,org:e,advisor:r,cloud66:t,dyndns:t,mypets:t}],yt:[1,{org:t}],"xn--mgbaam7a8h":e,\u0627\u0645\u0627\u0631\u0627\u062A:e,"xn--y9a3aq":e,\u0570\u0561\u0575:e,"xn--54b7fta0cc":e,\u09AC\u09BE\u0982\u09B2\u09BE:e,"xn--90ae":e,\u0431\/* REMOVED_COMMON_BLOCK_2447 */2g2a9gcd":e,\u0B9A\u0BBF\u0B99\u0BCD\u0B95\u0BAA\u0BCD\u0BAA\u0BC2\u0BB0\u0BCD:e,"xn--ogbpf8fl":e,\u0633\u0648\u0631\u064A\u0629:e,"xn--mgbtf8fl":e,\u0633\u0648\u0631\u064A\u0627:e,"xn--o3cw4h":[1,{"xn--o3cyx2a":e,"xn--12co0c3b4eva":e,"xn--m3ch0j3a":e,"xn--h3cuzk1di":e,"xn--12c1fe0br":e,"xn--12cfi8ixb8l":e}],\u0E44\u0E17\u0E22:[1,{\u0E17\u0E2B\u0E32\u0E23:e,\u0E18\u0E38\u0E23\u0E01\u0E34\u0E08:e,\u0E40\u0E19\u0E47\u0E15:e,\u0E23\u0E31\u0E10\u0E1A\u0E32\u0E25:e,\u0E28\u0E36\u0E01\u0E29\u0E32:e,\u0E2D\u0E07\u0E04\u0E4C\u0E01\u0E23:e}],"xn--pgbs0dh":e,\u062A\u0648\u0646\u0633:e,"xn--kpry57d":e,\u53F0\u7063:e,"xn--kprw13d":e,\u53F0\u6E7E:e,"xn--nnx388a":e,\u81FA\u7063:e,"xn--j1amh":e,\u0443\u043A\u0440:e,"xn--mgb2ddes":e,\u0627\u0644\u064A\u0645\u0646:e,xxx:e,ye:n,za:[0,{ac:e,agric:e,alt:e,co:e,edu:e,gov:e,grondar:e,law:e,mil:e,net:e,ngo:e,nic:e,nis:e,nom:e,org:e,school:e,tm:e,web:e}],zm:[1,{ac:e,biz:e,co:e,com:e,edu:e,gov:e,info:e,mil:e,net:e,org:e,sch:e}],zw:[1,{ac:e,co:e,gov:e,mil:e,org:e}],aaa:e,aarp:e,abb:e,abbott:e,abbvie:e,abc:e,able:e,abogado:e,abudhabi:e,academy:[1,{official:t}],accenture:e,accountant:e,accountants:e,aco:e,actor:e,ads:e,adult:e,aeg:e,aetna:e,afl:e,africa:e,agakhan:e,agency:e,aig:e,airbus:e,airforce:e,airtel:e,akdn:e,alibaba:e,alipay:e,allfinanz:e,allstate:e,ally:e,alsace:e,alstom:e,amazon:e,americanexpress:e,americanfamily:e,amex:e,amfam:e,amica:e,amsterdam:e,analytics:e,android:e,anquan:e,anz:e,aol:e,apartments:e,app:[1,{adaptable:t,aiven:t,beget:r,brave:i,clerk:t,clerkstage:t,wnext:t,csb:[2,{preview:t}],convex:t,deta:t,ondigitalocean:t,easypanel:t,encr:t,evervault:o,expo:[2,{staging:t}],edgecompute:t,"on-fleek":t,flutterflow:t,e2b:t,framer:t,hosted:r,run:r,web:t,hasura:t,botdash:t,loginline:t,lovable:t,luyani:t,medusajs:t,messerli:t,netfy:t,netlify:t,ngrok:t,"ngrok-free":t,developer:r,noop:t,northflank:r,upsun:r,replit:s,nyat:t,snowflake:[0,{"*":t,privatelink:r}],streamlit:t,storipress:t,telebit:t,typedream:t,vercel:t,bookonline:t,wdh:t,windsurf:t,zeabur:t,zerops:r}],apple:e,aquarelle:e,arab:e,aramco:e,archi:e,army:e,art:e,arte:e,asda:e,associates:e,athleta:e,attorney:e,auction:e,audi:e,audible:e,audio:e,auspost:e,author:e,auto:e,autos:e,aws:[1,{sagemaker:[0,{"ap-northeast-1":d,"ap-northeast-2":d,"ap-south-1":d,"ap-southeast-1":d,"ap-southeast-2":d,"ca-central-1":p,"eu-central-1":d,"eu-west-1":d,"eu-west-2":d,"us-east-1":p,"us-east-2":p,"us-west-2":p,"af-south-1":l,"ap-east-1":l,"ap-northeast-3":l,"ap-south-2":m,"ap-southeast-3":l,"ap-southeast-4":m,"ca-west-1":[0,{notebook:t,"notebook-fips":t}],"eu-central-2":l,"eu-north-1":l,"eu-south-1":l,"eu-south-2":l,"eu-west-3":l,"il-central-1":l,"me-central-1":l,"me-south-1":l,"sa-east-1":l,"us-gov-east-1":x,"us-gov-west-1":x,"us-west-1":[0,{notebook:t,"notebook-fips":t,studio:t}],experiments:r}],repost:[0,{private:r}],on:[0,{"ap-northeast-1":c,"ap-southeast-1":c,"ap-southeast-2":c,"eu-central-1":c,"eu-north-1":c,"eu-west-1":c,"us-east-1":c,"us-east-2":c,"us-west-2":c}]}],axa:e,azure:e,baby:e,baidu:e,banamex:e,band:e,bank:e,bar:e,barcelona:e,barclaycard:e,barclays:e,barefoot:e,bargains:e,baseball:e,basketball:[1,{aus:t,nz:t}],bauhaus:e,bayern:e,bbc:e,bbt:e,bbva:e,bcg:e,bcn:e,beats:e,beauty:e,beer:e,bentley:e,berlin:e,best:e,bestbuy:e,bet:e,bharti:e,bible:e,bid:e,bike:e,bing:e,bingo:e,bio:e,black:e,blackfriday:e,blockbuster:e,blog:e,bloomberg:e,blue:e,bms:e,bmw:e,bnpparibas:e,boats:e,boehringer:e,bofa:e,bom:e,bond:e,boo:e,book:e,booking:e,bosch:e,bostik:e,boston:e,bot:e,boutique:e,box:e,bradesco:e,bridgestone:e,broadway:e,broker:e,brother:e,brussels:e,build:[1,{v0:t,windsurf:t}],builders:[1,{cloudsite:t}],business:S,buy:e,buzz:e,bzh:e,cab:e,cafe:e,cal:e,call:e,calvinklein:e,cam:e,camera:e,camp:[1,{emf:[0,{at:t}]}],canon:e,capetown:e,capital:e,capitalone:e,car:e,caravan:e,cards:e,care:e,career:e,careers:e,cars:e,casa:[1,{nabu:[0,{ui:t}]}],case:e,cash:e,casino:e,catering:e,catholic:e,cba:e,cbn:e,cbre:e,center:e,ceo:e,cern:e,cfa:e,cfd:e,chanel:e,channel:e,charity:e,chase:e,chat:e,cheap:e,chintai:e,christmas:e,chrome:e,church:e,cipriani:e,circle:e,cisco:e,citadel:e,citi:e,citic:e,city:e,claims:e,cleaning:e,click:e,clinic:e,clinique:e,clothing:e,cloud:[1,{convex:t,elementor:t,encoway:[0,{eu:t}],statics:r,ravendb:t,axarnet:[0,{"es-1":t}],diadem:t,jelastic:[0,{vip:t}],jele:t,"jenv-aruba":[0,{aruba:[0,{eur:[0,{it1:t}]}],it1:t}],keliweb:[2,{cs:t}],oxa:[2,{tn:t,uk:t}],primetel:[2,{uk:t}],reclaim:[0,{ca:t,uk:t,us:t}],trendhosting:[0,{ch:t,de:t}],jotelulu:t,kuleuven:t,laravel:t,linkyard:t,magentosite:r,matlab:t,observablehq:t,perspecta:t,vapor:t,"on-rancher":r,scw:[0,{baremetal:[0,{"fr-par-1":t,"fr-par-2":t,"nl-ams-1":t}],"fr-par":[0,{cockpit:t,fnc:[2,{functions:t}],k8s:M,s3:t,"s3-website":t,whm:t}],instances:[0,{priv:t,pub:t}],k8s:t,"nl-ams":[0,{cockpit:t,k8s:M,s3:t,"s3-website":t,whm:t}],"pl-waw":[0,{cockpit:t,k8s:M,s3:t,"s3-website":t}],scalebook:t,smartlabeling:t}],servebolt:t,onstackit:[0,{runs:t}],trafficplex:t,"unison-services":t,urown:t,voorloper:t,zap:t}],club:[1,{cloudns:t,jele:t,barsy:t}],clubmed:e,coach:e,codes:[1,{owo:r}],coffee:e,college:e,cologne:e,commbank:e,community:[1,{nog:t,ravendb:t,myforum:t}],company:e,compare:e,computer:e,comsec:e,condos:e,construction:e,consulting:e,contact:e,contractors:e,cooking:e,cool:[1,{elementor:t,de:t}],corsica:e,country:e,coupon:e,coupons:e,courses:e,cpa:e,credit:e,creditcard:e,creditunion:e,cricket:e,crown:e,crs:e,cruise:e,cruises:e,cuisinella:e,cymru:e,cyou:e,dad:e,dance:e,data:e,date:e,dating:e,datsun:e,day:e,dclk:e,dds:e,deal:e,dealer:e,deals:e,degree:e,delivery:e,dell:e,deloitte:e,delta:e,democrat:e,dental:e,dentist:e,desi:e,design:[1,{graphic:t,bss:t}],dev:[1,{"12chars":t,myaddr:t,panel:t,lcl:r,lclstage:r,stg:r,stgstage:r,pages:t,r2:t,workers:t,deno:t,"deno-staging":t,deta:t,evervault:o,fly:t,githubpreview:t,gateway:r,hrsn:[2,{psl:[0,{sub:t,wc:[0,{"*":t,sub:r}]}]}],botdash:t,inbrowser:r,"is-a-good":t,"is-a":t,iserv:t,runcontainers:t,localcert:[0,{user:r}],loginline:t,barsy:t,mediatech:t,modx:t,ngrok:t,"ngrok-free":t,"is-a-fullstack":t,"is-cool":t,"is-not-a":t,localplayer:t,xmit:t,"platter-app":t,replit:[2,{archer:t,bones:t,canary:t,global:t,hacker:t,id:t,janeway:t,kim:t,kira:t,kirk:t,odo:t,paris:t,picard:t,pike:t,prerelease:t,reed:t,riker:t,sisko:t,spock:t,staging:t,sulu:t,tarpit:t,teams:t,tucker:t,wesley:t,worf:t}],crm:[0,{d:r,w:r,wa:r,wb:r,wc:r,wd:r,we:r,wf:r}],vercel:t,webhare:r}],dhl:e,diamonds:e,diet:e,digital:[1,{cloudapps:[2,{london:t}]}],direct:[1,{libp2p:t}],directory:e,discount:e,discover:e,dish:e,diy:e,dnp:e,docs:e,doctor:e,dog:e,domains:e,dot:e,download:e,drive:e,dtv:e,dubai:e,dunlop:e,dupont:e,durban:e,dvag:e,dvr:e,earth:e,eat:e,eco:e,edeka:e,education:S,email:[1,{crisp:[0,{on:t}],tawk:N,tawkto:N}],emerck:e,energy:e,engineer:e,engineering:e,enterprises:e,epson:e,equipment:e,ericsson:e,erni:e,esq:e,estate:[1,{compute:r}],eurovision:e,eus:[1,{party:V}],events:[1,{koobin:t,co:t}],exchange:e,expert:e,exposed:e,express:e,extraspace:e,fage:e,fail:e,fairwinds:e,faith:e,family:e,fan:e,fans:e,farm:[1,{storj:t}],farmers:e,fashion:e,fast:e,fedex:e,feedback:e,ferrari:e,ferrero:e,fidelity:e,fido:e,film:e,final:e,finance:e,financial:S,fire:e,firestone:e,firmdale:e,fish:e,fishing:e,fit:e,fitness:e,flickr:e,flights:e,flir:e,florist:e,flowers:e,fly:e,foo:e,food:e,football:e,ford:e,forex:e,forsale:e,forum:e,foundation:e,fox:e,free:e,fresenius:e,frl:e,frogans:e,frontier:e,ftr:e,fujitsu:e,fun:e,fund:e,furniture:e,futbol:e,fyi:e,gal:e,gallery:e,gallo:e,gallup:e,game:e,games:[1,{pley:t,sheezy:t}],gap:e,garden:e,gay:[1,{pages:t}],gbiz:e,gdn:[1,{cnpy:t}],gea:e,gent:e,genting:e,george:e,ggee:e,gift:e,gifts:e,gives:e,giving:e,glass:e,gle:e,global:[1,{appwrite:t}],globo:e,gmail:e,gmbh:e,gmo:e,gmx:e,godaddy:e,gold:e,goldpoint:e,golf:e,goo:e,goodyear:e,goog:[1,{cloud:t,translate:t,usercontent:r}],google:e,gop:e,got:e,grainger:e,graphics:e,gratis:e,green:e,gripe:e,grocery:e,group:[1,{discourse:t}],gucci:e,guge:e,guide:e,guitars:e,guru:e,hair:e,hamburg:e,hang/* REMOVED_COMMON_BLOCK_2478 */edu:t}],kred:e,kuokgroup:e,kyoto:e,lacaixa:e,lamborghini:e,lamer:e,lancaster:e,land:e,landrover:e,lanxess:e,lasalle:e,lat:e,latino:e,latrobe:e,law:e,lawyer:e,lds:e,lease:e,leclerc:e,lefrak:e,legal:e,lego:e,lexus:e,lgbt:e,lidl:e,life:e,lifeinsurance:e,lifestyle:e,lighting:e,like:e,lilly:e,limited:e,limo:e,lincoln:e,link:[1,{myfritz:t,cyon:t,dweb:r,inbrowser:r,nftstorage:J,mypep:t,storacha:J,w3s:J}],live:[1,{aem:t,hlx:t,ewp:r}],living:e,llc:e,llp:e,loan:e,loans:e,locker:e,locus:e,lol:[1,{omg:t}],london:e,lotte:e,lotto:e,love:e,lpl:e,lplfinancial:e,ltd:e,ltda:e,lundbeck:e,luxe:e,luxury:e,madrid:e,maif:e,maison:e,makeup:e,man:e,management:e,mango:e,map:e,market:e,marketing:e,markets:e,marriott:e,marshalls:e,mattel:e,mba:e,mckinsey:e,med:e,media:Ee,meet:e,melbourne:e,meme:e,memorial:e,men:e,menu:[1,{barsy:t,barsyonline:t}],merck:e,merckmsd:e,miami:e,microsoft:e,mini:e,mint:e,mit:e,mitsubishi:e,mlb:e,mls:e,mma:e,mobile:e,moda:e,moe:e,moi:e,mom:[1,{ind:t}],monash:e,money:e,monster:e,mormon:e,mortgage:e,moscow:e,moto:e,motorcycles:e,mov:e,movie:e,msd:e,mtn:e,mtr:e,music:e,nab:e,nagoya:e,navy:e,nba:e,nec:e,netbank:e,netflix:e,network:[1,{alces:r,co:t,arvo:t,azimuth:t,tlon:t}],neustar:e,new:e,news:[1,{noticeable:t}],next:e,nextdirect:e,nexus:e,nfl:e,ngo:e,nhk:e,nico:e,nike:e,nikon:e,ninja:e,nissan:e,nissay:e,nokia:e,norton:e,now:e,nowruz:e,nowtv:e,nra:e,nrw:e,ntt:e,nyc:e,obi:e,observer:e,office:e,okinawa:e,olayan:e,olayangroup:e,ollo:e,omega:e,one:[1,{kin:r,service:t}],ong:[1,{obl:t}],onl:e,online:[1,{eero:t,"eero-stage":t,websitebuilder:t,barsy:t}],ooo:e,open:e,oracle:e,orange:[1,{tech:t}],organic:e,origins:e,osaka:e,otsuka:e,ott:e,ovh:[1,{nerdpol:t}],page:[1,{aem:t,hlx:t,hlx3:t,translated:t,codeberg:t,heyflow:t,prvcy:t,rocky:t,pdns:t,plesk:t}],panasonic:e,paris:e,pars:e,partners:e,parts:e,party:e,pay:e,pccw:e,pet:e,pfizer:e,pharmacy:e,phd:e,philips:e,phone:e,photo:e,photography:e,photos:Ee,physio:e,pics:e,pictet:e,pictures:[1,{1337:t}],pid:e,pin:e,ping:e,pink:e,pioneer:e,pizza:[1,{ngrok:t}],place:S,play:e,playstation:e,plumbing:e,plus:e,pnc:e,pohl:e,poker:e,politie:e,porn:e,pramerica:e,praxi:e,press:e,prime:e,prod:e,productions:e,prof:e,progressive:e,promo:e,properties:e,property:e,protection:e,pru:e,prudential:e,pub:[1,{id:r,kin:r,barsy:t}],pwc:e,qpon:e,quebec:e,quest:e,racing:e,radio:e,read:e,realestate:e,realtor:e,realty:e,recipes:e,red:e,redstone:e,redumbrella:e,rehab:e,reise:e,reisen:e,reit:e,reliance:e,ren:e,rent:e,rentals:e,repair:e,report:e,republican:e,rest:e,restaurant:e,review:e,reviews:e,rexroth:e,rich:e,richardli:e,ricoh:e,ril:e,rio:e,rip:[1,{clan:t}],rocks:[1,{myddns:t,stackit:t,"lima-city":t,webspace:t}],rodeo:e,rogers:e,room:e,rsvp:e,rugby:e,ruhr:e,run:[1,{appwrite:r,development:t,ravendb:t,liara:[2,{iran:t}],servers:t,build:r,code:r,database:r,migration:r,onporter:t,repl:t,stackit:t,val:[2,{web:t}],wix:t}],rwe:e,ryukyu:e,saarland:e,safe:e,safety:e,sakura:e,sale:e,salon:e,samsclub:e,samsung:e,sandvik:e,sandvikcoromant:e,sanofi:e,sap:e,sarl:e,sas:e,save:e,saxo:e,sbi:e,sbs:e,scb:e,schaeffler:e,schmidt:e,scholarships:e,school:e,schule:e,schwarz:e,science:e,scot:[1,{gov:[2,{service:t}]}],search:e,seat:e,secure:e,security:e,seek:e,select:e,sener:e,services:[1,{loginline:t}],seven:e,sew:e,sex:e,sexy:e,sfr:e,shangrila:e,sharp:e,shell:e,shia:e,shiksha:e,shoes:e,shop:[1,{base:t,hoplix:t,barsy:t,barsyonline:t,shopware:t}],shopping:e,shouji:e,show:e,silk:e,sina:e,singles:e,site:[1,{square:t,canva:h,cloudera:r,convex:t,cyon:t,fastvps:t,figma:t,heyflow:t,jele:t,jouwweb:t,loginline:t,barsy:t,notion:t,omniwe:t,opensocial:t,madethis:t,platformsh:r,tst:r,byen:t,srht:t,novecore:t,cpanel:t,wpsquared:t}],ski:e,skin:e,sky:e,skype:e,sling:e,smart:e,smile:e,sncf:e,soccer:e,social:e,softbank:e,software:e,sohu:e,solar:e,solutions:e,song:e,sony:e,soy:e,spa:e,space:[1,{myfast:t,heiyu:t,hf:[2,{static:t}],"app-ionos":t,project:t,uber:t,xs4all:t}],sport:e,spot:e,srl:e,stada:e,staples:e,star:e,statebank:e,statefarm:e,stc:e,stcgroup:e,stockholm:e,storage:e,store:[1,{barsy:t,sellfy:t,shopware:t,storebase:t}],stream:e,studio:e,study:e,style:e,sucks:e,supplies:e,supply:e,support:[1,{barsy:t}],surf:e,surgery:e,suzuki:e,swatch:e,swiss:e,sydney:e,systems:[1,{knightpoint:t}],tab:e,taipei:e,talk:e,taobao:e,target:e,tatamotors:e,tatar:e,tattoo:e,tax:e,taxi:e,tci:e,tdk:e,team:[1,{discourse:t,jelastic:t}],tech:[1,{cleverapps:t}],technology:S,temasek:e,tennis:e,teva:e,thd:e,theater:e,theatre:e,tiaa:e,tickets:e,tienda:e,tips:e,tires:e,tirol:e,tjmaxx:e,tjx:e,tkmaxx:e,tmall:e,today:[1,{prequalifyme:t}],tokyo:e,tools:[1,{addr:fe,myaddr:t}],top:[1,{ntdll:t,wadl:r}],toray:e,toshiba:e,total:e,tours:e,town:e,toyota:e,toys:e,trade:e,trading:e,training:e,travel:e,travelers:e,travelersinsurance:e,trust:e,trv:e,tube:e,tui:e,tunes:e,tushu:e,tvs:e,ubank:e,ubs:e,unicom:e,university:e,uno:e,uol:e,ups:e,vacations:e,vana:e,vanguard:e,vegas:e,ventures:e,verisign:e,versicherung:e,vet:e,viajes:e,video:e,vig:e,viking:e,villas:e,vin:e,vip:e,virgin:e,visa:e,vision:e,viva:e,vivo:e,vlaanderen:e,vodka:e,volvo:e,vote:e,voting:e,voto:e,voyage:e,wales:e,walmart:e,walter:e,wang:e,wanggou:e,watch:e,watches:e,weather:e,weatherchannel:e,webcam:e,weber:e,website:Ee,wed:e,wedding:e,weibo:e,weir:e,whoswho:e,wien:e,wiki:Ee,williamhill:e,win:e,windows:e,wine:e,winners:e,wme:e,wolterskluwer:e,woodside:e,work:e,works:e,world:e,wow:e,wtc:e,wtf:e,xbox:e,xerox:e,xihuan:e,xin:e,"xn--11b4c3d":e,\u0915\u0949\u092E:e,"xn--1ck2e1b":e,\u30BB\u30FC\u30EB:e,"xn--1qqw23a":e,\u4F5B\u5C/* REMOVED_COMMON_BLOCK_2449 */3E\u0447\u0438:t,\u0441\u043F\u0431:t,\u044F:t}],"xn--pssy2u":e,\u5927\u62FF:e,"xn--q9jyb4c":e,\u307F\u3093\u306A:e,"xn--qcka1pmc":e,\u30B0\u30FC\u30B0\u30EB:e,"xn--rhqv96g":e,\u4E16\u754C:e,"xn--rovu88b":e,\u66F8\u7C4D:e,"xn--ses554g":e,\u7F51\u5740:e,"xn--t60b56a":e,\uB2F7\uB137:e,"xn--tckwe":e,\u30B3\u30E0:e,"xn--tiq49xqyj":e,\u5929\u4E3B\u6559:e,"xn--unup4y":e,\u6E38\u620F:e,"xn--vermgensberater-ctb":e,verm\u00F6gensberater:e,"xn--vermgensberatung-pwb":e,verm\u00F6gensberatung:e,"xn--vhquv":e,\u4F01\u4E1A:e,"xn--vuq861b":e,\u4FE1\u606F:e,"xn--w4r85el8fhu5dnra":e,\u5609\u91CC\u5927\u9152\u5E97:e,"xn--w4rs40l":e,\u5609\u91CC:e,"xn--xhq521b":e,\u5E7F\u4E1C:e,"xn--zfr164b":e,\u653F\u52A1:e,xyz:[1,{botdash:t,telebit:r}],yachts:e,yahoo:e,yamaxun:e,yandex:e,yodobashi:e,yoga:e,yokohama:e,you:e,youtube:e,yun:e,zappos:e,zara:e,zero:e,zip:e,zone:[1,{cloud66:t,triton:r,stackit:t,lima:t}],zuerich:e}]}();var Yk=Ic();var lf=Hr.alert;function ea(){try{let e=[ke().EBOOK_BUILDER_URL,ke().EBOOK_VIEWER_URL,ke().SUBTITLE_BUILDER_URL,ke().HTML_VIEWER_URL,ke().PDF_VIEWER_URL].filter(r=>!!r),t=["app."+Le];Wt()||t.push("localhost:38001");let a=globalThis.location.pathname;return e.find(r=>{let i=new URL(r);return a.startsWith(i.pathname)&&t.includes(i.host)})?!1:globalThis.self!==globalThis.top}catch{return!0}}var B1=pe();function Zr(e){return e?typeof e=="string"?document.querySelector(e)!==null:e.some(t=>document.querySelector(t)):!1}function _o(){if(!globalThis||!globalThis.location)return"https://example.com";if(!ea())return globalThis.location.href;try{let t=globalThis.location.href,a=new URL(t);if(a.protocol==="about:"||a.protocol==="blob:"){if(globalThis.location.ancestorOrigins&&globalThis.location.ancestorOrigins.length>0)return globalThis.location.ancestorOrigins[0];let n="";try{n=globalThis.parent.location.href}catch{}return n||(globalThis.location!=globalThis.parent.location?document.referrer:document.location.href)}else return t}catch{}return globalThis.location.href}var _c,ge,jc,cf,Vn,Pc,Lc,Nc={},Oc=[],df=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function pa(e,t){for(var a in t)e[a]=t[a];return e}function zc(e){var t=e.parentNode;t&&t.removeChild(e)}function jo(e,t,a,n,r){var i={type:e,props:t,key:a,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:r??++jc};return r==null&&ge.vnode!=null&&ge.vnode(i),i}function Ba(e){return e.children}function Xr(e,t){this.props=e,this.context=t}function cn(e,t){if(t==null)return e.__?cn(e.__,e.__.__k.indexOf(e)+1):null;for(var a;t<e.__k.length;t++)if((a=e.__k[t])!=null&&a.__e!=null)return a.__e;return typeof e.type=="function"?cn(e):null}function Uc(e){var t,a;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((a=e.__k[t])!=null&&a.__e!=null){e.__e=e.__c.base=a.__e;break}return Uc(e)}}function Lo(e){(!e.__d&&(e.__d=!0)&&Vn.push(e)&&!$r.__r++||Pc!==ge.debounceRendering)&&((Pc=ge.debounceRendering)||setTimeout)($r)}function $r(){for(var e;$r.__r=Vn.length;)e=Vn.sort(function(t,a){return t.__v.__b-a.__v.__b}),Vn=[],e.some(function(t){var a,n,r,i,o,s;t.__d&&(o=(i=(a=t).__v).__e,(s=a.__P)&&(n=[],(r=pa({},i)).__v=i.__v+1,Kc(s,i,r,a.__n,s.ownerSVGElement!==void 0,i.__h!=null?[o]:null,n,o??cn(i),i.__h),pf(n,i),i.__e!=o&&Uc(i)))})}function qc(e,t,a,n,r,i,o,s,u,c){var l,d,m,p,x,A,S,g=n&&n.__k||Oc,M=g.length;for(a.__k=[],l=0;l<t.length;l++)if((p=a.__k[l]=(p=t[l])==null||typeof p=="boolean"?null:typeof p=="string"||typeof p=="number"||typeof p=="bigint"?jo(null,p,null,null,p):Array.isArray(p)?jo(Ba,{children:p},null,null,null):p.__b>0?jo(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)!=null){if(p.__=a,p.__b=a.__b+1,(m=g[l])===null||m&&p.key==m.key&&p.type===m.type)g[l]=void 0;else for(d=0;d<M;d++){if((m=g[d])&&p.key==m.key&&p.type===m.type){g[d]=void 0;break}m=null}Kc(e,p,m=m||Nc,r,i,o,s,u,c),x=p.__e,(d=p.ref)&&m.ref!=d&&(S||(S=[]),m.ref&&S.push(m.ref,null,p),S.push(d,p.__c||x,p)),x!=null?(A==null&&(A=x),typeof p.type=="function"&&p.__k===m.__k?p.__d=u=Gc(p,u,e):u=Hc(e,p,m,g,x,u),typeof a.type=="function"&&(a.__d=u)):u&&m.__e==u&&u.parentNode!=e&&(u=cn(m))}for(a.__e=A,l=M;l--;)g[l]!=null&&(typeof a.type=="function"&&g[l].__e!=null&&g[l].__e==a.__d&&(a.__d=cn(n,l+1)),Qc(g[l],g[l]));if(S)for(l=0;l<S.length;l++)Wc(S[l],S[++l],S[++l])}function Gc(e,t,a){for(var n,r=e.__k,i=0;r&&i<r.length;i++)(n=r[i])&&(n.__=e,t=typeof n.type=="function"?Gc(n,t,a):Hc(a,n,n,r,n.__e,t));return t}function Hc(e,t,a,n,r,i){var o,s,u;if(t.__d!==void 0)o=t.__d,t.__d=void 0;else if(a==null||r!=i||r.parentNode==null)e:if(i==null||i.parentNode!==e)e.appendChild(r),o=null;else{for(s=i,u=0;(s=s.nextSibling)&&u<n.length;u+=2)if(s==r)break e;e.insertBefore(r,i),o=i}return o!==void 0?o:r.nextSibling}function mf(e,t,a,n,r){var i;for(i in a)i==="children"||i==="key"||i in t||ei(e,i,null,a[i],n);for(i in t)r&&typeof t[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||a[i]===t[i]||ei(e,i,t[i],a[i],n)}function Fc(e,t,a){t[0]==="-"?e.setProperty(t,a):e[t]=a==null?"":typeof a!="number"||df.test(t)?a:a+"px"}function ei(e,t,a,n,r){var i;e:if(t==="style")if(typeof a=="string")e.style.cssText=a;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)a&&t in a||Fc(e.style,t,"");if(a)for(t in a)n&&a[t]===n[t]||Fc(e.style,t,a[t])}else if(t[0]==="o"&&t[1]==="n")i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=a,a?n||e.addEventListener(t,i?Rc:Bc,i):e.removeEventListener(t,i?Rc:Bc,i);else if(t!=="dangerouslySetInnerHTML"){if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=a??"";break e}catch{}typeof a=="function"||(a!=null&&(a!==!1||t[0]==="a"&&t[1]==="r")?e.setAttribute(t,a):e.removeAttribute(t))}}function Bc(e){this.l[e.type+!1](ge.event?ge.event(e):e)}function Rc(e){this.l[e.type+!0](ge.event?ge.event(e):e)}function Kc(e,t,a,n,r,i,o,s,u){var c,l,d,m,p,x,A,S,g,M,h,k,L,E=t.type;if(t.constructor!==void 0)return null;a.__h!=null&&(u=a.__h,s=t.__e=a.__e,t.__h=null,i=[s]),(c=ge.__b)&&c(t);try{e:if(typeof E=="function"){if(S=t.props,g=(c=E.contextType)&&n[c.__c],M=c?g?g.props.value:c.__:n,a.__c?A=(l=t.__c=a.__c).__=l.__E:("prototype"in E&&E.prototype.render?t.__c=l=new E(S,M):(t.__c=l=new Xr(S,M),l.constructor=E,l.render=hf),g&&g.sub(l),l.props=S,l.state||(l.state={}),l.context=M,l.__n=n,d=l.__d=!0,l.__h=[]),l.__s==null&&(l.__s=l.state),E.getDerivedStateFromProps!=null&&(l.__s==l.state&&(l.__s=pa({},l.__s)),pa(l.__s,E.getDerivedStateFromProps(S,l.__s))),m=l.props,p=l.state,d)E.getDerivedStateFromProps==null&&l.componentWillMount!=null&&l.componentWillMount(),l.componentDidMount!=null&&l.__h.push(l.componentDidMount);else{if(E.getDerivedStateFromProps==null&&S!==m&&l.componentWillReceiveProps!=null&&l.componentWillReceiveProps(S,M),!l.__e&&l.shouldComponentUpdate!=null&&l.shouldComponentUpdate(S,l.__s,M)===!1||t.__v===a.__v){l.props=S,l.state=l.__s,t.__v!==a.__v&&(l.__d=!1),l.__v=t,t.__e=a.__e,t.__k=a.__k,t.__k.forEach(function(v){v&&(v.__=t)}),l.__h.length&&o.push(l);break e}l.componentWillUpdate!=null&&l.componentWillUpdate(S,l.__s,M),l.componentDidUpdate!=null&&l.__h.push(function(){l.componentDidUpdate(m,p,x)})}if(l.context=M,l.props=S,l.__v=t,l.__P=e,h=ge.__r,k=0,"prototype"in E&&E.prototype.render)l.state=l.__s,l.__d=!1,h&&h(t),c=l.render(l.props,l.state,l.context);else do l.__d=!1,h&&h(t),c=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++k<25);l.state=l.__s,l.getChildContext!=null&&(n=pa(pa({},n),l.getChildContext())),d||l.getSnapshotBeforeUpdate==null||(x=l.getSnapshotBeforeUpdate(m,p)),L=c!=null&&c.type===Ba&&c.key==null?c.props.children:c,qc(e,Array.isArray(L)?L:[L],t,a,n,r,i,o,s,u),l.base=t.__e,t.__h=null,l.__h.length&&o.push(l),A&&(l.__E=l.__=null),l.__e=!1}else i==null&&t.__v===a.__v?(t.__k=a.__k,t.__e=a.__e):t.__e=gf(a.__e,t,a,n,r,i,o,u);(c=ge.diffed)&&c(t)}catch(v){t.__v=null,(u||i!=null)&&(t.__e=s,t.__h=!!u,i[i.indexOf(s)]=null),ge.__e(v,t,a)}}function pf(e,t){ge.__c&&ge.__c(t,e),e.some(function(a){try{e=a.__h,a.__h=[],e.some(function(n){n.call(a)})}catch(n){ge.__e(n,a.__v)}})}function gf(e,t,a,n,r,i,o,s){var u,c,l,d=a.props,m=t.props,p=t.type,x=0;if(p==="svg"&&(r=!0),i!=null){for(;x<i.length;x++)if((u=i[x])&&"setAttribute"in u==!!p&&(p?u.localName===p:u.nodeType===3)){e=u,i[x]=null;break}}if(e==null){if(p===null)return document.createTextNode(m);e=r?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,m.is&&m),i=null,s=!1}if(p===null)d===m||s&&e.data===m||(e.data=m);else{if(i=i&&_c.call(e.childNodes),c=(d=a.props||Nc).dangerouslySetInnerHTML,l=m.dangerouslySetInnerHTML,!s){if(i!=null)for(d={},x=0;x<e.attributes.length;x++)d[e.attributes[x].name]=e.attributes[x].value;(l||c)&&(l&&(c&&l.__html==c.__html||l.__html===e.innerHTML)||(e.innerHTML=l&&l.__html||""))}if(mf(e,m,d,r,s),l)t.__k=[];else if(x=t.props.children,qc(e,Array.isArray(x)?x:[x],t,a,n,r&&p!=="foreignObject",i,o,i?i[0]:a.__k&&cn(a,0),s),i!=null)for(x=i.length;x--;)i[x]!=null&&zc(i[x]);s||("value"in m&&(x=m.value)!==void 0&&(x!==e.value||p==="progress"&&!x||p==="option"&&x!==d.value)&&ei(e,"value",x,d.value,!1),"checked"in m&&(x=m.checked)!==void 0&&x!==e.checked&&ei(e,"checked",x,d.checked,!1))}return e}function Wc(e,t,a){try{typeof e=="function"?e(t):e.current=t}catch(n){ge.__e(n,a)}}function Qc(e,t,a){var n,r;if(ge.unmount&&ge.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||Wc(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(i){ge.__e(i,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(r=0;r<n.length;r++)n[r]&&Qc(n[r],t,typeof e.type!="function");a||e.__e==null||zc(e.__e),e.__=e.__e=e.__d=void 0}function hf(e,t,a){return this.constructor(e,a)}function No(e,t){var a={__c:t="__cC"+Lc++,__:e,Consumer:function(n,r){return n.children(r)},Provider:function(n){var r,i;return this.getChildContext||(r=[],(i={})[t]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(o){this.props.value!==o.value&&r.some(Lo)},this.sub=function(o){r.push(o);var s=o.componentWillUnmount;o.componentWillUnmount=function(){r.splice(r.indexOf(o),1),s&&s.call(o)}}),n.children}};return a.Provider.__=a.Consumer.contextType=a}_c=Oc.slice,ge={__e:function(e,t,a,n){for(var r,i,o;t=t.__;)if((r=t.__c)&&!r.__)try{if((i=r.constructor)&&i.getDerivedStateFromError!=null&&(r.setState(i.getDerivedStateFromError(e)),o=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,n||{}),o=r.__d),o)return r.__E=r}catch(s){e=s}throw e}},jc=0,cf=function(e){return e!=null&&e.constructor===void 0},Xr.prototype.setState=function(e,t){var a;a=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=pa({},this.state),typeof e=="function"&&(e=e(pa({},a),this.props)),e&&pa(a,e),e!=null&&this.__v&&(t&&this.__h.push(t),Lo(this))},Xr.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Lo(this))},Xr.prototype.render=Ba,Vn=[],$r.__r=0,Lc=0;var ff,Nt,Oo,Vc;var td=[],zo=[],Yc=ge.__b,Jc=ge.__r,Zc=ge.diffed,Xc=ge.__c,$c=ge.unmount;function bf(){for(var e;e=td.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(ti),e.__H.__h.forEach(Uo),e.__H.__h=[]}catch(t){e.__H.__h=[],ge.__e(t,e.__v)}}ge.__b=function(e){typeof e.type!="function"||e.o||e.type===Ba?e.o||(e.o=e.__&&e.__.o?e.__.o:""):e.o=(e.__&&e.__.o?e.__.o:"")+(e.__&&e.__.__k?e.__.__k.indexOf(e):0),Nt=null,Yc&&Yc(e)},ge.__r=function(e){Jc&&Jc(e),ff=0;var t=(Nt=e.__c).__H;t&&(Oo===Nt?(t.__h=[],Nt.__h=[],t.__.forEach(function(a){a.__N&&(a.__=a.__N),a.__V=zo,a.__N=a.i=void 0})):(t.__h.forEach(ti),t.__h.forEach(Uo),t.__h=[])),Oo=Nt},ge.diffed=function(e){Zc&&Zc(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(td.push(t)!==1&&Vc===ge.requestAnimationFrame||((Vc=ge.requestAnimationFrame)||yf)(bf)),t.__H.__.forEach(function(a){a.i&&(a.__H=a.i),a.__V!==zo&&(a.__=a.__V),a.i=void 0,a.__V=zo})),Oo=Nt=null},ge.__c=function(e,t){t.some(function(a){try{a.__h.forEach(ti),a.__h=a.__h.filter(function(n){return!n.__||Uo(n)})}catch(n){t.some(function(r){r.__h&&(r.__h=[])}),t=[],ge.__e(n,a.__v)}}),Xc&&Xc(e,t)},ge.unmount=function(e){$c&&$c(e);var t,a=e.__c;a&&a.__H&&(a.__H.__.forEach(function(n){try{ti(n)}catch(r){t=r}}),a.__H=void 0,t&&ge.__e(t,a.__v))};var ed=typeof requestAnimationFrame=="function";function yf(e){var t,a=function(){clearTimeout(n),ed&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(a,100);ed&&(t=requestAnimationFrame(a))}function ti(e){var t=Nt,a=e.__c;typeof a=="function"&&(e.__c=void 0,a()),Nt=t}function Uo(e){var t=Nt;e.__c=e.__(),Nt=t}var nd=["*://*/*","*","*://*"],id="immersive-translate-wildcard-placeholder.com";function Go(e,t){try{let a=[];if(!t||(t&&!Array.isArray(t)?a=[t]:a=t,a.length===0))return null;if(a.some(s=>nd.includes(s)))return e;let n=new URL(e);n.hash="",n.search="";let r=n.href,i=n.hostname,o=n.port;if(a&&a.length>0){let s=a.find(u=>{if(!u)return!1;if(u===i)return!0;if(nd.includes(u))return!0;if(!u.includes("*")&&u.includes("://")){try{let c=new URL(u);if(c.pathname==="/"&&!u.endsWith("/")){let l=c.hostname===i,d=c.port===o;return c.port?l&&d:l}else return Af(r,u)}catch{}return!1}else{let c,l=u;if(u.includes("://")){let S=u.split("://");c=S[0],c==="*"&&S.length>1&&(c="*",u="https://"+S[1])}else c="*",u="https://"+u;let d=u.replace(/\*/g,id),m;try{m=new URL(d)}catch{return X.debug("invalid match pattern",d,"raw match value:",l),!1}let p=m.host,x=m.pathname;x==="/"&&(l.replace("://","").includes("/")||(x="/*"));let A=wf(c+":",rd(p),rd(x));return A?A.test(e):!1}});if(s)return s}return null}catch{return null}}function rd(e){return e.replaceAll(id,"*")}function wf(e,t,a){let n="^";return e==="*:"?n+="(http:|https:|file:)":n+=e,n+="//",t&&(e==="file:"||(t==="*"?n+="[^/]+?":(t.match(/^\*\./)&&(n+="[^/]*?",t=t.substring(1)),n+=t.replace(/\./g,"\\.").replace(/\*/g,"[^/]*")))),a?a==="*"||a==="/*"?n+="(/.*)?":a.includes("*")?(n+=a.replace(/\*/g,".*?"),n+="/?"):n+=a.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):n+="/?",n+="$",new RegExp(n)}function yt(e,t){return Go(e,t)!==null}function Af(e,t){let a=new URL(e),n=new URL(t);return a.hostname===n.hostname&&a.pathname===n.pathname&&a.protocol===n.protocol&&a.port===n.port}var ai=function(){return ai=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++){t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},ai.apply(this,arguments)};function Et(e,t,a,n){function r(i){return i instanceof a?i:new a(function(o){o(i)})}return new(a||(a=Promise))(function(i,o){function s(l){try{c(n.next(l))}catch(d){o(d)}}function u(l){try{c(n.throw(l))}catch(d){o(d)}}function c(l){l.done?i(l.value):r(l.value).then(s,u)}c((n=n.apply(e,t||[])).next())})}function St(e,t){var a={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,r,i,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(l){return u([c,l])}}function u(c){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(a=0)),a;)try{if(n=1,r&&(i=c[0]&2?r.return:c[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,c[1])).done)return i;switch(r=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return a.label++,{value:c[1],done:!1};case 5:a.label++,r=c[1],c=[0];continue;case 7:c=a.ops.pop(),a.trys.pop();continue;default:if(i=a.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){a=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){a.label=c[1];break}if(c[0]===6&&a.label<i[1]){a.label=i[1],i=c;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(c);break}i[2]&&a.ops.pop(),a.trys.pop();continue}c=t.call(e,a)}catch(l){c=[6,l],r=0}finally{n=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Ho(e,t,a){if(a||arguments.length===2)for(var n=0,r=t.length,i;n<r;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}var vd="4.5.0";function oi(e,t){return new Promise(function(a){return setTimeout(a,e,t)})}function Ef(){return new Promise(function(e){var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(null)})}function Sf(e,t){t===void 0&&(t=1/0);var a=window.requestIdleCallback;return a?new Promise(function(n){return a.call(window,function(){return n()},{timeout:t})}):oi(Math.min(e,t))}function xd(e){return!!e&&typeof e.then=="function"}function od(e,t){try{var a=e();xd(a)?a.then(function(n){return t(!0,n)},function(n){return t(!1,n)}):t(!0,a)}catch(n){t(!1,n)}}function sd(e,t,a){return a===void 0&&(a=16),Et(this,void 0,void 0,function(){var n,r,i,o;return St(this,function(s){switch(s.label){case 0:n=Array(e.length),r=Date.now(),i=0,s.label=1;case 1:return i<e.length?(n[i]=t(e[i],i),o=Date.now(),o>=r+a?(r=o,[4,Ef()]):[3,3]):[3,4];case 2:s.sent(),s.label=3;case 3:return++i,[3,1];case 4:return[2,n]}})})}function Yn(e){return e.then(void 0,function(){}),e}function kf(e,t){for(var a=0,n=e.length;a<n;++a)if(e[a]===t)return!0;return!1}function Tf(e,t){return!kf(e,t)}function Jo(e){return parseInt(e)}function kt(e){return parseFloat(e)}function ta(e,t){return typeof e=="number"&&isNaN(e)?t:e}function dt(e){return e.reduce(function(t,a){return t+(a?1:0)},0)}function wd(e,t){if(t===void 0&&(t=1),Math.abs(t)>=1)return Math.round(e/t)*t;var a=1/t;return Math.round(e*a)/a}function Df(e){for(var t,a,n="Unexpected syntax '".concat(e,"'"),r=/^\s*([a-z-]*)(.*)$/i.exec(e),i=r[1]||void 0,o={},s=/([.:#][\w-]+|\[.+?\])/gi,u=function(m,p){o[m]=o[m]||[],o[m].push(p)};;){var c=s.exec(r[2]);if(!c)break;var l=c[0];switch(l[0]){case".":u("class",l.slice(1));break;case"#":u("id",l.slice(1));break;case"[":{var d=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(l);if(d)u(d[1],(a=(t=d[4])!==null&&t!==void 0?t:d[5])!==null&&a!==void 0?a:"");else throw new Error(n);break}default:throw new Error(n)}}return[i,o]}function Cf(e){for(var t=new Uint8Array(e.length),a=0;a<e.length;a++){var n=e.charCodeAt(a);if(n>127)return new TextEncoder().encode(e);t[a]=n}return t}function ga(e,t){var a=e[0]>>>16,n=e[0]&65535,r=e[1]>>>16,i=e[1]&65535,o=t[0]>>>16,s=t[0]&65535,u=t[1]>>>16,c=t[1]&65535,l=0,d=0,m=0,p=0;p+=i+c,m+=p>>>16,p&=65535,m+=r+u,d+=m>>>16,m&=65535,d+=n+s,l+=d>>>16,d&=65535,l+=a+o,l&=65535,e[0]=l<<16|d,e[1]=m<<16|p}function vt(e,t){var a=e[0]>>>16,n=e[0]&65535,r=e[1]>>>16,i=e[1]&65535,o=t[0]>>>16,s=t[0]&65535,u=t[1]>>>16,c=t[1]&65535,l=0,d=0,m=0,p=0;p+=i*c,m+=p>>>16,p&=65535,m+=r*c,d+=m>>>16,m&=65535,m+=i*u,d+=m>>>16,m&=65535,d+=n*c,l+=d>>>16,d&=65535,d+=r*u,l+=d>>>16,d&=65535,d+=i*s,l+=d>>>16,d&=65535,l+=a*c+n*u+r*s+i*o,l&=65535,e[0]=l<<16|d,e[1]=m<<16|p}function dn(e,t){var a=e[0];t%=64,t===32?(e[0]=e[1],e[1]=a):t<32?(e[0]=a<<t|e[1]>>>32-t,e[1]=e[1]<<t|a>>>32-t):(t-=32,e[0]=e[1]<<t|a>>>32-t,e[1]=a<<t|e[1]>>>32-t)}function ct(e,t){t%=64,t!==0&&(t<32?(e[0]=e[1]>>>32-t,e[1]=e[1]<<t):(e[0]=e[1]<<t-32,e[1]=0))}function Pe(e,t){e[0]^=t[0],e[1]^=t[1]}var Mf=[4283543511,3981806797],If=[3301882366,444984403];function ud(e){var t=[0,e[0]>>>1];Pe(e,t),vt(e,Mf),t[1]=e[0]>>>1,Pe(e,t),vt(e,If),t[1]=e[0]>>>1,Pe(e,t)}var ni=[2277735313,289559509],ri=[1291169091,658871167],ld=[0,5],Pf=[0,1390208809],Ff=[0,944331445];function Bf(e,t){var a=Cf(e);t=t||0;var n=[0,a.length],r=n[1]%16,i=n[1]-r,o=[0,t],s=[0,t],u=[0,0],c=[0,0],l;for(l=0;l<i;l=l+16)u[0]=a[l+4]|a[l+5]<<8|a[l+6]<<16|a[l+7]<<24,u[1]=a[l]|a[l+1]<<8|a[l+2]<<16|a[l+3]<<24,c[0]=a[l+12]|a[l+13]<<8|a[l+14]<<16|a[l+15]<<24,c[1]=a[l+8]|a[l+9]<<8|a[l+10]<<16|a[l+11]<<24,vt(u,ni),dn(u,31),vt(u,ri),Pe(o,u),dn(o,27),ga(o,s),vt(o,ld),ga(o,Pf),vt(c,ri),dn(c,33),vt(c,ni),Pe(s,c),dn(s,31),ga(s,o),vt(s,ld),ga(s,Ff);u[0]=0,u[1]=0,c[0]=0,c[1]=0;var d=[0,0];switch(r){case 15:d[1]=a[l+14],ct(d,48),Pe(c,d);case 14:d[1]=a[l+13],ct(d,40),Pe(c,d);case 13:d[1]=a[l+12],ct(d,32),Pe(c,d);case 12:d[1]=a[l+11],ct(d,24),Pe(c,d);case 11:d[1]=a[l+10],ct(d,16),Pe(c,d);case 10:d[1]=a[l+9],ct(d,8),Pe(c,d);case 9:d[1]=a[l+8],Pe(c,d),vt(c,ri),dn(c,33),vt(c,ni),Pe(s,c);case 8:d[1]=a[l+7],ct(d,56),Pe(u,d);case 7:d[1]=a[l+6],ct(d,48),Pe(u,d);case 6:d[1]=a[l+5],ct(d,40),Pe(u,d);case 5:d[1]=a[l+4],ct(d,32),Pe(u,d);case 4:d[1]=a[l+3],ct(d,24),Pe(u,d);case 3:d[1]=a[l+2],ct(d,16),Pe(u,d);case 2:d[1]=a[l+1],ct(d,8),Pe(u,d);case 1:d[1]=a[l],Pe(u,d),vt(u,ni),dn(u,31),vt(u,ri),Pe(o,u)}return Pe(o,n),Pe(s,n),ga(o,s),ga(s,o),ud(o),ud(s),ga(o,s),ga(s,o),("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)+("00000000"+(s[0]>>>0).toString(16)).slice(-8)+("00000000"+(s[1]>>>0).toString(16)).slice(-8)}function Rf(e){var t;return ai({name:e.name,message:e.message,stack:(t=e.stack)===null||t===void 0?void 0:t.split(`
`)},e)}function _f(e){return/^function\s.*?\{\s*\[native code]\s*}$/.test(String(e))}function jf(e){return typeof e!="function"}function Lf(e,t){var a=Yn(new Promise(function(n){var r=Date.now();od(e.bind(null,t),function(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];var s=Date.now()-r;if(!i[0])return n(function(){return{error:i[1],duration:s}});var u=i[1];if(jf(u))return n(function(){return{value:u,duration:s}});n(function(){return new Promise(function(c){var l=Date.now();od(u,function(){for(var d=[],m=0;m<arguments.length;m++)d[m]=arguments[m];var p=s+Date.now()-l;if(!d[0])return c({error:d[1],duration:p});c({value:d[1],duration:p})})})})})}));return function(){return a.then(function(n){return n()})}}function Nf(e,t,a,n){var r=Object.keys(e).filter(function(o){return Tf(a,o)}),i=Yn(sd(r,function(o){return Lf(e[o],t)},n));return function(){return Et(this,void 0,void 0,function(){var o,s,u,c,l;return St(this,function(d){switch(d.label){case 0:return[4,i];case 1:return o=d.sent(),[4,sd(o,function(m){return Yn(m())},n)];case 2:return s=d.sent(),[4,Promise.all(s)];case 3:for(u=d.sent(),c={},l=0;l<r.length;++l)c[r[l]]=u[l];return[2,c]}})})}}function Ad(){var e=window,t=navigator;return dt(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])>=4}function Of(){var e=window,t=navigator;return dt(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in t,"msSaveBlob"in t])>=3&&!Ad()}function si(){var e=window,t=navigator;return dt(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,t.vendor.indexOf("Google")===0,"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function Tt(){var e=window,t=navigator;return dt(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,t.vendor.indexOf("Apple")===0,"RGBColor"in e,"WebKitMediaKeys"in e])>=4}function Zo(){var e=window,t=e.HTMLElement,a=e.Document;return dt(["safari"in e,!("ongestureend"in e),!("TouchEvent"in e),!("orientation"in e),t&&!("autocapitalize"in t.prototype),a&&"pointerLockElement"in a.prototype])>=4}function Jn(){var e=window;return _f(e.print)&&String(e.browser)==="[object WebPageNamespace]"}function Ed(){var e,t,a=window;return dt(["buildID"in navigator,"MozAppearance"in((t=(e=document.documentElement)===null||e===void 0?void 0:e.style)!==null&&t!==void 0?t:{}),"onmozfullscreenchange"in a,"mozInnerScreenX"in a,"CSSMozDocumentRule"in a,"CanvasCaptureMediaStream"in a])>=4}function zf(){var e=window;return dt([!("MediaSettingsRange"in e),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3}function Uf(){var e=window;return dt(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3}function Zn(){var e=window,t=navigator,a=e.CSS,n=e.HTMLButtonElement;return dt([!("getStorageUpdates"in t),n&&"popover"in n.prototype,"CSSCounterStyleRule"in e,a.supports("font-size-adjust: ex-height 0.5"),a.supports("text-transform: full-width")])>=4}function qf(){if(navigator.platform==="iPad")return!0;var e=screen,t=e.width/e.height;return dt(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,t>.65&&t<1.53])>=2}function Gf(){var e=document;return e.fullscreenElement||e.msFullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement||null}function Hf(){var e=document;return(e.exitFullscreen||e.msExitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen).call(e)}function Xo(){var e=si(),t=Ed(),a=window,n=navigator,r="connection";return e?dt([!("SharedWorker"in a),n[r]&&"ontypechange"in n[r],!("sinkId"in new window.Audio)])>=2:t?dt(["onorientationchange"in a,"orientation"in a,/android/i.test(navigator.appVersion)])>=2:!1}function Kf(){return Vf()?-4:Wf()}function Wf(){var e=window,t=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!t)return-2;if(Qf())return-1;var a=4500,n=5e3,r=new t(1,n,44100),i=r.createOscillator();i.type="triangle",i.frequency.value=1e4;var o=r.createDynamicsCompressor();o.threshold.value=-50,o.knee.value=40,o.ratio.value=12,o.attack.value=0,o.release.value=.25,i.connect(o),o.connect(r.destination),i.start(0);var s=Yf(r),u=s[0],c=s[1],l=Yn(u.then(function(d){return Jf(d.getChannelData(0).subarray(a))},function(d){if(d.name==="timeout"||d.name==="suspended")return-3;throw d}));return function(){return c(),l}}function Qf(){return Tt()&&!Zo()&&!Uf()}function Vf(){return Tt()&&Zn()&&Jn()}function Yf(e){var t=3,a=500,n=500,r=5e3,i=function(){},o=new Promise(function(s,u){var c=!1,l=0,d=0;e.oncomplete=function(x){return s(x.renderedBuffer)};var m=function(){setTimeout(function(){return u(cd("timeout"))},Math.min(n,d+r-Date.now()))},p=function(){try{var x=e.startRendering();switch(xd(x)&&Yn(x),e.state){case"running":d=Date.now(),c&&m();break;case"suspended":document.hidden||l++,c&&l>=t?u(cd("suspended")):setTimeout(p,a);break}}catch(A){u(A)}};p(),i=function(){c||(c=!0,d>0&&m())}});return[o,i]}function Jf(e){for(var t=0,a=0;a<e.length;++a)t+=Math.abs(e[a]);return t}function cd(e){var t=new Error(e);return t.name=e,t}function Sd(e,t,a){var n,r,i;return a===void 0&&(a=50),Et(this,void 0,void 0,function(){var o,s;return St(this,function(u){switch(u.label){case 0:o=document,u.label=1;case 1:return o.body?[3,3]:[4,oi(a)];case 2:return u.sent(),[3,1];case 3:s=o.createElement("iframe"),u.label=4;case 4:return u.trys.push([4,10,11]),[4,new Promise(function(c,l){var d=!1,m=function(){d=!0,c()},p=function(S){d=!0,l(S)};s.onload=m,s.onerror=p;var x=s.style;x.setProperty("display","block","important"),x.position="absolute",x.top="0",x.left="0",x.visibility="hidden",t&&"srcdoc"in s?s.srcdoc=t:s.src="about:blank",o.body.appendChild(s);var A=function(){var S,g;d||(((g=(S=s.contentWindow)===null||S===void 0?void 0:S.document)===null||g===void 0?void 0:g.readyState)==="complete"?m():setTimeout(A,10))};A()})];case 5:u.sent(),u.label=6;case 6:return!((r=(n=s.contentWindow)===null||n===void 0?void 0:n.document)===null||r===void 0)&&r.body?[3,8]:[4,oi(a)];case 7:return u.sent(),[3,6];case 8:return[4,e(s,s.contentWindow)];case 9:return[2,u.sent()];case 10:return(i=s.parentNode)===null||i===void 0||i.removeChild(s),[7];case 11:return[2]}})})}function Zf(e){for(var t=Df(e),a=t[0],n=t[1],r=document.createElement(a??"div"),i=0,o=Object.keys(n);i<o.length;i++){var s=o[i],u=n[s].join(" ");s==="style"?Xf(r.style,u):r.setAttribute(s,u)}return r}function Xf(e,t){for(var a=0,n=t.split(";");a<n.length;a++){var r=n[a],i=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(r);if(i){var o=i[1],s=i[2],u=i[4];e.setProperty(o,s,u||"")}}}function $f(){for(var e=window;;){var t=e.parent;if(!t||t===e)return!1;try{if(t.location.origin!==e.location.origin)return!0}catch(a){if(a instanceof Error&&a.name==="SecurityError")return!0;throw a}e=t}}var e4="mmMwWLliI0O&1",t4="48px",mn=["monospace","sans-serif","serif"],dd=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function a4(){var e=this;return Sd(function(t,a){var n=a.document;return Et(e,void 0,void 0,function(){var r,i,o,s,u,c,l,d,m,p,x,A;return St(this,function(S){for(r=n.body,r.style.fontSize=t4,i=n.createElement("div"),i.style.setProperty("visibility","hidden","important"),o={},s={},u=function(g){var M=n.createElement("span"),h=M.style;return h.position="absolute",h.top="0",h.left="0",h.fontFamily=g,M.textContent=e4,i.appendChild(M),M},c=function(g,M){return u("'".concat(g,"',").concat(M))},l=function(){return mn.map(u)},d=function(){for(var g={},M=function(E){g[E]=mn.map(function(v){return c(E,v)})},h=0,k=dd;h<k.length;h++){var L=k[h];M(L)}return g},m=function(g){return mn.some(function(M,h){return g[h].offsetWidth!==o[M]||g[h].offsetHeight!==s[M]})},p=l(),x=d(),r.appendChild(i),A=0;A<mn.length;A++)o[mn[A]]=p[A].offsetWidth,s[mn[A]]=p[A].offsetHeight;return[2,dd.filter(function(g){return m(x[g])})]})})})}function n4(){var e=navigator.plugins;if(e){for(var t=[],a=0;a<e.length;++a){var n=e[a];if(n){for(var r=[],i=0;i<n.length;++i){var o=n[i];r.push({type:o.type,suffixes:o.suffixes})}t.push({name:n.name,description:n.description,mimeTypes:r})}}return t}}function r4(){return i4(m4())}function i4(e){var t,a=!1,n,r,i=o4(),o=i[0],s=i[1];return s4(o,s)?(a=u4(s),e?n=r="skipped":(t=l4(o,s),n=t[0],r=t[1])):n=r="unsupported",{winding:a,geometry:n,text:r}}function o4(){var e=document.createElement("canvas");return e.width=1,e.height=1,[e,e.getContext("2d")]}function s4(e,t){return!!(t&&e.toDataURL)}function u4(e){return e.rect(0,0,10,10),e.rect(2,2,6,6),!e.isPointInPath(5,5,"evenodd")}function l4(e,t){c4(e,t);var a=Ko(e),n=Ko(e);if(a!==n)return["unstable","unstable"];d4(e,t);var r=Ko(e);return[r,a]}function c4(e,t){e.width=240,e.height=60,t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(100,1,62,20),t.fillStyle="#069",t.font='11pt "Times New Roman"';var a="Cwm fjordbank gly ".concat("\u{1F603}");t.fillText(a,2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText(a,4,45)}function d4(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var a=0,n=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];a<n.length;a++){var r=n[a],i=r[0],o=r[1],s=r[2];t.fillStyle=i,t.beginPath(),t.arc(o,s,40,0,Math.PI*2,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,Math.PI*2,!0),t.arc(60,60,20,0,Math.PI*2,!0),t.fill("evenodd")}function Ko(e){return e.toDataURL()}function m4(){return Tt()&&Zn()&&Jn()}function p4(){var e=navigator,t=0,a;e.maxTouchPoints!==void 0?t=Jo(e.maxTouchPoints):e.msMaxTouchPoints!==void 0&&(t=e.msMaxTouchPoints);try{document.createEvent("TouchEvent"),a=!0}catch{a=!1}var n="ontouchstart"in window;return{maxTouchPoints:t,touchEvent:a,touchStart:n}}function g4(){return navigator.oscpu}function h4(){var e=navigator,t=[],a=e.language||e.userLanguage||e.browserLanguage||e.systemLanguage;if(a!==void 0&&t.push([a]),Array.isArray(e.languages))si()&&zf()||t.push(e.languages);else if(typeof e.languages=="string"){var n=e.languages;n&&t.push(n.split(","))}return t}function f4(){return window.screen.colorDepth}function b4(){return ta(kt(navigator.deviceMemory),void 0)}function y4(){if(!(Tt()&&Zn()&&Jn()))return v4()}function v4(){var e=screen,t=function(n){return ta(Jo(n),null)},a=[t(e.width),t(e.height)];return a.sort().reverse(),a}var x4=2500,w4=10,ii,Wo;function A4(){if(Wo===void 0){var e=function(){var t=Vo();Yo(t)?Wo=setTimeout(e,x4):(ii=t,Wo=void 0)};e()}}function E4(){var e=this;return A4(),function(){return Et(e,void 0,void 0,function(){var t;return St(this,function(a){switch(a.label){case 0:return t=Vo(),Yo(t)?ii?[2,Ho([],ii,!0)]:Gf()?[4,Hf()]:[3,2]:[3,2];case 1:a.sent(),t=Vo(),a.label=2;case 2:return Yo(t)||(ii=t),[2,t]}})})}}function S4(){var e=this;if(Tt()&&Zn()&&Jn())return function(){return Promise.resolve(void 0)};var t=E4();return function(){return Et(e,void 0,void 0,function(){var a,n;return St(this,function(r){switch(r.label){case 0:return[4,t()];case 1:return a=r.sent(),n=function(i){return i===null?null:wd(i,w4)},[2,[n(a[0]),n(a[1]),n(a[2]),n(a[3])]]}})})}}function Vo(){var e=screen;return[ta(kt(e.availTop),null),ta(kt(e.width)-kt(e.availWidth)-ta(kt(e.availLeft),0),null),ta(kt(e.height)-kt(e.availHeight)-ta(kt(e.availTop),0),null),ta(kt(e.availLeft),null)]}function Yo(e){for(var t=0;t<4;++t)if(e[t])return!1;return!0}function k4(){return ta(Jo(navigator.hardwareConcurrency),void 0)}function T4(){var e,t=(e=window.Intl)===null||e===void 0?void 0:e.DateTimeFormat;if(t){var a=new t().resolvedOptions().timeZone;if(a)return a}var n=-D4();return"UTC".concat(n>=0?"+":"").concat(n)}function D4(){var e=new Date().getFullYear();return Math.max(kt(new Date(e,0,1).getTimezoneOffset()),kt(new Date(e,6,1).getTimezoneOffset()))}function C4(){try{return!!window.sessionStorage}catch{return!0}}function M4(){try{return!!window.localStorage}catch{return!0}}function I4(){if(!(Ad()||Of()))try{return!!window.indexedDB}catch{return!0}}function P4(){return!!window.openDatabase}function F4(){return navigator.cpuClass}function B4(){var e=navigator.platform;return e==="MacIntel"&&Tt()&&!Zo()?qf()?"iPad":"iPhone":e}function R4(){return navigator.vendor||""}function _4(){for(var e=[],t=0,a=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<a.length;t++){var n=a[t],r=window[n];r&&typeof r=="object"&&e.push(n)}return e.sort()}function j4(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=e.cookie.indexOf("cookietest=")!==-1;return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch{return!1}}function L4(){var e=atob;return{abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',e("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",e("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",e("LnNwb25zb3JpdA=="),".ylamainos",e("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi/* REMOVED_COMMON_BLOCK_739 */dmU7Il0="),e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[e("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),e("I2xpdmVyZUFkV3JhcHBlcg=="),e("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),e("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[e("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",e("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),e("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),e("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[e("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),e("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),e("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",e("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),e("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),e("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),e("ZGl2I3NrYXBpZWNfYWQ=")],ro:[e("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),e("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[e("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),e("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),e("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",e("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),e("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",e("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}}function N4(e){var t=e===void 0?{}:e,a=t.debug;return Et(this,void 0,void 0,function(){var n,r,i,o,s,u;return St(this,function(c){switch(c.label){case 0:return O4()?(n=L4(),r=Object.keys(n),i=(u=[]).concat.apply(u,r.map(function(l){return n[l]})),[4,z4(i)]):[2,void 0];case 1:return o=c.sent(),a&&U4(n,o),s=r.filter(function(l){var d=n[l],m=dt(d.map(function(p){return o[p]}));return m>d.length*.6}),s.sort(),[2,s]}})})}function O4(){return Tt()||Xo()}function z4(e){var t;return Et(this,void 0,void 0,function(){var a,n,r,i,u,o,s,u;return St(this,function(c){switch(c.label){case 0:for(a=document,n=a.createElement("div"),r=new Array(e.length),i={},md(n),u=0;u<e.length;++u)o=Zf(e[u]),o.tagName==="DIALOG"&&o.show(),s=a.createElement("div"),md(s),s.appendChild(o),n.appendChild(s),r[u]=o;c.label=1;case 1:return a.body?[3,3]:[4,oi(50)];case 2:return c.sent(),[3,1];case 3:a.body.appendChild(n);try{for(u=0;u<e.length;++u)r[u].offsetParent||(i[e[u]]=!0)}finally{(t=n.parentNode)===null||t===void 0||t.removeChild(n)}return[2,i]}})})}function md(e){e.style.setProperty("visibility","hidden","important"),e.style.setProperty("display","block","important")}function U4(e,t){for(var a="DOM blockers debug:\n```",n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];a+=`
`.concat(i,":");for(var o=0,s=e[i];o<s.length;o++){var u=s[o];a+=`
  `.concat(t[u]?"\u{1F6AB}":"\u27A1\uFE0F"," ").concat(u)}}}function q4(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var a=t[e];if(matchMedia("(color-gamut: ".concat(a,")")).matches)return a}}function G4(){if(pd("inverted"))return!0;if(pd("none"))return!1}function pd(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function H4(){if(gd("active"))return!0;if(gd("none"))return!1}function gd(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}var K4=100;function W4(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=K4;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}}function Q4(){if(pn("no-preference"))return 0;if(pn("high")||pn("more"))return 1;if(pn("low")||pn("less"))return-1;if(pn("forced"))return 10}function pn(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function V4(){if(hd("reduce"))return!0;if(hd("no-preference"))return!1}function hd(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function Y4(){if(fd("reduce"))return!0;if(fd("no-preference"))return!1}function fd(e){return matchMedia("(prefers-reduced-transparency: ".concat(e,")")).matches}function J4(){if(bd("high"))return!0;if(bd("standard"))return!1}function bd(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}var xe=Math,tt=function(){return 0};function Z4(){var e=xe.acos||tt,t=xe.acosh||tt,a=xe.asin||tt,n=xe.asinh||tt,r=xe.atanh||tt,i=xe.atan||tt,o=xe.sin||tt,s=xe.sinh||tt,u=xe.cos||tt,c=xe.cosh||tt,l=xe.tan||tt,d=xe.tanh||tt,m=xe.exp||tt,p=xe.expm1||tt,x=xe.log1p||tt,A=function(w){return xe.pow(xe.PI,w)},S=function(w){return xe.log(w+xe.sqrt(w*w-1))},g=function(w){return xe.log(w+xe.sqrt(w*w+1))},M=function(w){return xe.log((1+w)/(1-w))/2},h=function(w){return xe.exp(w)-1/xe.exp(w)/2},k=function(w){return(xe.exp(w)+1/xe.exp(w))/2},L=function(w){return xe.exp(w)-1},E=function(w){return(xe.exp(2*w)-1)/(xe.exp(2*w)+1)},v=function(w){return xe.log(1+w)};return{acos:e(.12312423423423424),acosh:t(1e308),acoshPf:S(1e154),asin:a(.12312423423423424),asinh:n(1),asinhPf:g(1),atanh:r(.5),atanhPf:M(.5),atan:i(.5),sin:o(-1e300),sinh:s(1),sinhPf:h(1),cos:u(10.000000000123),cosh:c(1),coshPf:k(1),tan:l(-1e300),tanh:d(1),tanhPf:E(1),exp:m(1),expm1:p(1),expm1Pf:L(1),log1p:x(10),log1pPf:v(10),powPI:A(-100)}}var X4="mmMwWLliI0fiflO&1",Qo={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]};function $4(){return eb(function(e,t){for(var a={},n={},r=0,i=Object.keys(Qo);r<i.length;r++){var o=i[r],s=Qo[o],u=s[0],c=u===void 0?{}:u,l=s[1],d=l===void 0?X4:l,m=e.createElement("span");m.textContent=d,m.style.whiteSpace="nowrap";for(var p=0,x=Object.keys(c);p<x.length;p++){var A=x[p],S=c[A];S!==void 0&&(m.style[A]=S)}a[o]=m,t.append(e.createElement("br"),m)}for(var g=0,M=Object.keys(Qo);g<M.length;g++){var o=M[g];n[o]=a[o].getBoundingClientRect().width}return n})}function eb(e,t){return t===void 0&&(t=4e3),Sd(function(a,n){var r=n.document,i=r.body,o=i.style;o.width="".concat(t,"px"),o.webkitTextSizeAdjust=o.textSizeAdjust="none",si()?i.style.zoom="".concat(1/n.devicePixelRatio):Tt()&&(i.style.zoom="reset");var s=r.createElement("div");return s.textContent=Ho([],Array(t/20<<0),!0).map(function(){return"word"}).join(" "),i.appendChild(s),e(r,i)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')}function tb(){return navigator.pdfViewerEnabled}function ab(){var e=new Float32Array(1),t=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],t[3]}function nb(){var e=window.ApplePaySession;if(typeof e?.canMakePayments!="function")return-1;if(rb())return-3;try{return e.canMakePayments()?1:0}catch(t){return ib(t)}}var rb=$f;function ib(e){if(e instanceof Error&&e.name==="InvalidAccessError"&&/\bfrom\b.*\binsecure\b/i.test(e.message))return-2;throw e}function ob(){var e,t=document.createElement("a"),a=(e=t.attributionSourceId)!==null&&e!==void 0?e:t.attributionsourceid;return a===void 0?void 0:String(a)}var kd=-1,Td=-2,sb=new Set([10752,2849,2884,2885,2886,2928,2929,2930,2931,2932,2960,2961,2962,2963,2964,2965,2966,2967,2968,2978,3024,3042,3088,3089,3106,3107,32773,32777,32777,32823,32824,32936,32937,32938,32939,32968,32969,32970,32971,3317,33170,3333,3379,3386,33901,33902,34016,34024,34076,3408,3410,3411,3412,3413,3414,3415,34467,34816,34817,34818,34819,34877,34921,34930,35660,35661,35724,35738,35739,36003,36004,36005,36347,36348,36349,37440,37441,37443,7936,7937,7938]),ub=new Set([34047,35723,36063,34852,34853,34854,34229,36392,36795,38449]),lb=["FRAGMENT_SHADER","VERTEX_SHADER"],cb=["LOW_FLOAT","MEDIUM_FLOAT","HIGH_FLOAT","LOW_INT","MEDIUM_INT","HIGH_INT"],Dd="WEBGL_debug_renderer_info",db="WEBGL_polygon_mode";function mb(e){var t,a,n,r,i,o,s=e.cache,u=Cd(s);if(!u)return kd;if(!Id(u))return Td;var c=Md()?null:u.getExtension(Dd);return{version:((t=u.getParameter(u.VERSION))===null||t===void 0?void 0:t.toString())||"",vendor:((a=u.getParameter(u.VENDOR))===null||a===void 0?void 0:a.toString())||"",vendorUnmasked:c?(n=u.getParameter(c.UNMASKED_VENDOR_WEBGL))===null||n===void 0?void 0:n.toString():"",renderer:((r=u.getParameter(u.RENDERER))===null||r===void 0?void 0:r.toString())||"",rendererUnmasked:c?(i=u.getParameter(c.UNMASKED_RENDERER_WEBGL))===null||i===void 0?void 0:i.toString():"",shadingLanguageVersion:((o=u.getParameter(u.SHADING_LANGUAGE_VERSION))===null||o===void 0?void 0:o.toString())||""}}function pb(e){var t=e.cache,a=Cd(t);if(!a)return kd;if(!Id(a))return Td;var n=a.getSupportedExtensions(),r=a.getContextAttributes(),i=[],o=[],s=[],u=[],c=[];if(r)for(var l=0,d=Object.keys(r);l<d.length;l++){var m=d[l];o.push("".concat(m,"=").concat(r[m]))}for(var p=yd(a),x=0,A=p;x<A.length;x++){var S=A[x],g=a[S];s.push("".concat(S,"=").concat(g).concat(sb.has(g)?"=".concat(a.getParameter(g)):""))}if(n)for(var M=0,h=n;M<h.length;M++){var k=h[M];if(!(k===Dd&&Md()||k===db&&fb())){var L=a.getExtension(k);if(!L){i.push(k);continue}for(var E=0,v=yd(L);E<v.length;E++){var S=v[E],g=L[S];u.push("".concat(S,"=").concat(g).concat(ub.has(g)?"=".concat(a.getParameter(g)):""))}}}for(var w=0,y=lb;w<y.length;w++)for(var R=y[w],F=0,C=cb;F<C.length;F++){var j=C[F],Y=gb(a,R,j);c.push("".concat(R,".").concat(j,"=").concat(Y.join(",")))}return u.sort(),s.sort(),{contextAttributes:o,parameters:s,shaderPrecisions:c,extensions:n,extensionParameters:u,unsupportedExtensions:i}}function Cd(e){if(e.webgl)return e.webgl.context;var t=document.createElement("canvas"),a;t.addEventListener("webglCreateContextError",function(){return a=void 0});for(var n=0,r=["webgl","experimental-webgl"];n<r.length;n++){var i=r[n];try{a=t.getContext(i)}catch{}if(a)break}return e.webgl={context:a},a}function gb(e,t,a){var n=e.getShaderPrecisionFormat(e[t],e[a]);return n?[n.rangeMin,n.rangeMax,n.precision]:[]}function yd(e){var t=Object.keys(e.__proto__);return t.filter(hb)}function hb(e){return typeof e=="string"&&!e.match(/[^A-Z0-9_x]/)}function Md(){return Ed()}function fb(){return si()||Tt()}function Id(e){return typeof e.getParameter=="function"}function bb(){var e,t=Xo()||Tt();return t?window.AudioContext&&(e=new AudioContext().baseLatency)!==null&&e!==void 0?e:-1:-2}var yb={fonts:a4,domBlockers:N4,fontPreferences:$4,audio:Kf,screenFrame:S4,canvas:r4,osCpu:g4,languages:h4,colorDepth:f4,deviceMemory:b4,screenResolution:y4,hardwareConcurrency:k4,timezone:T4,sessionStorage:C4,localStorage:M4,indexedDB:I4,openDatabase:P4,cpuClass:F4,platform:B4,plugins:n4,touchSupport:p4,vendor:R4,vendorFlavors:_4,cookiesEnabled:j4,colorGamut:q4,invertedColors:G4,forcedColors:H4,monochrome:W4,contrast:Q4,reducedMotion:V4,reducedTransparency:Y4,hdr:J4,math:Z4,pdfViewerEnabled:tb,architecture:ab,applePay:nb,privateClickMeasurement:ob,audioBaseLatency:bb,webGlBasics:mb,webGlExtensions:pb};function vb(e){return Nf(yb,e,[])}var xb="$ if upgrade to Pro: https://fpjs.dev/pro";function wb(e){var t=Ab(e),a=Eb(t);return{score:t,comment:xb.replace(/\$/g,"".concat(a))}}function Ab(e){if(Xo())return .4;if(Tt())return Zo()&&!(Zn()&&Jn())?.5:.3;var t="value"in e.platform?e.platform.value:"";return/^Win/.test(t)?.6:/^Mac/.test(t)?.5:.7}function Eb(e){return wd(.99+.01*e,1e-4)}function Sb(e){for(var t="",a=0,n=Object.keys(e).sort();a<n.length;a++){var r=n[a],i=e[r],o="error"in i?"error":JSON.stringify(i.value);t+="".concat(t?"|":"").concat(r.replace(/([:|\\])/g,"\\$1"),":").concat(o)}return t}function kb(e){return JSON.stringify(e,function(t,a){return a instanceof Error?Rf(a):a},2)}function Pd(e){return Bf(Sb(e))}function Tb(e){var t,a=wb(e);return{get visitorId(){return t===void 0&&(t=Pd(this.components)),t},set visitorId(n){t=n},confidence:a,components:e,version:vd}}function Db(e){return e===void 0&&(e=50),Sf(e,e*2)}function Cb(e,t){var a=Date.now();return{get:function(n){return Et(this,void 0,void 0,function(){var r,i,o;return St(this,function(s){switch(s.label){case 0:return r=Date.now(),[4,e()];case 1:return i=s.sent(),o=Tb(i),t||n?.debug,[2,o]}})})}}}function Mb(){if(!(window.__fpjs_d_m||Math.random()>=.001))try{var e=new XMLHttpRequest;e.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(vd,"/npm-monitoring"),!0),e.send()}catch{}}function Ib(e){var t;return e===void 0&&(e={}),Et(this,void 0,void 0,function(){var a,n,r;return St(this,function(i){switch(i.label){case 0:return(!((t=e.monitoring)!==null&&t!==void 0)||t)&&Mb(),a=e.delayFallback,n=e.debug,[4,Db(a)];case 1:return i.sent(),r=vb({cache:{},debug:n}),[2,Cb(r,n)]}})})}var $o={load:Ib,hashComponents:Pd,componentsToDebugString:kb};async function Xn(){let e=await ut("fakeUserId","");e||(e=await Ia("fakeUserId",""),e&&await Ke("fakeUserId",e));let t=new Date,a=await ut("installedAt","");return a||(a=await Ia("installedAt",""),a&&await Ke("installedAt",a)),e?a||(a=new Date(0).toISOString(),await Ke("installedAt",a)):(e=await Bb(64),await Ke("fakeUserId",e)),a||(a=t.toISOString(),await Ke("installedAt",a)),{fakeUserId:e,installedAt:a}}var Pb=10;async function Ra(){let e=await ut("userTag","");if(e)return e;let{fakeUserId:t}=await Xn(),n=t.charCodeAt(0)%Pb;return e=String.fromCharCode(n+"a".charCodeAt(0)),await Ke("userTag",e),e}function Fb(e){let t="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=a.length,r=0;for(;r<e;)t+=a.charAt(Math.floor(Math.random()*n)),r+=1;return t}async function Bb(e){try{let a=await(await $o.load()).get(),n=new Date().getTime(),r=Math.random().toString(36).substring(2,15),i=`${a.visitorId}-${n}-${r}`;return crypto?.subtle?.digest?crypto.subtle.digest("SHA-256",new TextEncoder().encode(i)).then(o=>Array.from(new Uint8Array(o)).map(c=>c.toString(16).padStart(2,"0")).join("").substring(0,e)):Rb(i,e)}catch{return Fb(64)}}function Rb(e,t=32){let a="";for(let r=0;r<e.length;r++)a+=(e.charCodeAt(r)*(r+1)).toString(36);a=a.repeat(Math.ceil(t/a.length)).substring(0,t);let n="";for(let r=0;r<t;r++){let i=Math.floor(Math.random()*a.length);n+=a[i]}return n}async function $n(){return await ut("abGroup","")}async function er(){return await ut("campaign","")}async function Fd(){return await ut("installedAt","")}function es(e){return Xt()?"imtAndroid":Zt()?"imtIOS":pe(e)?"userscript":pt()?"safari":Rn()?"firefox":Xa()?"chrome":"other"}var _b={initial:0,buildContainer:0,consumeContainer:0,consumeParagraph:0,parseParagraph:0,translated:0,inserted:0},$T={..._b};function ha(e,t){let a=gn(e),n=gn(t);return a>=n}function gn(e){let t=e.split(".").reverse(),a=0,n=1;for(let r=0;r<3;r++)a+=n*Number(t[r]||"0"),n*=100;return a}function ts(e){e.message?.indexOf("token invalid")>=0&&(st.remove(Xe),Rd(),_d())}function Bd(e,t){if(!e.localUpdatedAt||t<=0)return;let a=t-Date.now();if(Math.abs(a)>7*24*3600)return;X.debug("device diffTimestamp",a);let n=new Date(e.localUpdatedAt).getTime()+a;e.updatedAt=new Date(n).toISOString()}function jd(e,t,a){if(!a||!e||Object.keys(e).length===0)return e;let n=t.translationServices||{},r=Object.values(n).filter(o=>o?.group==="pro").map(o=>o?.type),i=Object.values(n).filter(o=>o?.group==="free").map(o=>o?.type);return e.translationServices&&Object.keys(e.translationServices).forEach(o=>{if(o==="zhipu-pro"||o.startsWith("mock")||!e.translationServices)return;let s=e.translationServices?.[o];if(!s)return;let u=s?.provider==="custom";if(s={...s},delete s.provider,s.type==="custom-ai"){e.translationServices[o]={...s,group:"custom"};return}let c=an[o];if(!c)return;let l=c.allProps?.filter(g=>g.sensitive===!0).map(g=>g.name)||[],d=i.includes(o),m=!1;if(d&&!e.translationServices[`${o}-free`]){let g=JSON.parse(JSON.stringify(s));l.forEach(M=>{delete g[M]}),delete g.name,m=!!(g.model&&n[o]?.freeModels?.includes(g.model)),!m&&g.model&&delete g.model,Object.keys(g).length>0&&(e.translationServices[`${o}-free`]={...g,migrateFrom:o})}let p=r.includes(o);if(p&&!e.translationServices[`${o}-pro`]){let g=JSON.parse(JSON.stringify(s));l.forEach(M=>{delete g[M]}),delete g.name,Object.keys(g).length>0&&(e.translationServices[`${o}-pro`]={...g,migrateFrom:o})}let x=l.some(g=>s[g]),A=!p&&!(d&&m)||x||u,S=e.translationServices[`${o}-custom`];A&&!S?.extends&&o!=="ai"&&(e.translationServices[`${o}-custom`]={...s,migrateFrom:o,extends:o,visible:!0,group:"custom",type:o})}),e}function Ld(e,t){e.userTranslationServices={...e.userTranslationServices};for(let a of to){let n=e[a];t?n&&!e.userTranslationServices[a]&&(e.userTranslationServices[a]=jb(n,e)):e.userTranslationServices[a]=n}for(let a of to){let n=e.userTranslationServices[a];n&&(e[a]=n)}return e}function jb(e,t){if(e==="inherit"||e.startsWith("mock")||e==="zhipu-pro"||t.translationServices?.[e]?.group)return e;let a=t.translationServices?.[e];return!a||a.type==="custom-ai"?e:Lb(e,t)==="pro"?`${e}-pro`:Ob(e,t)?`${e}-free`:`${e}-custom`}function Lb(e,t){let a=t.translationServices?.[e];return a?.provider?a?.provider:"custom"}function Nb(e,t){return Object.values(t.translationServices||{}).filter(n=>n?.group==="free").map(n=>n?.type).includes(e)}function Ob(e,t){if(!Nb(e,t))return!1;let n=t.translationServices?.[e];return n.model?n?.freeModels?.includes(n.model):!0}function Nd(e,t){let a=e.translationServices?.[t],n=Ye.bind(null,e.interfaceLanguage),r=a?.type||t,i=`translationServices.${r}`,o=n(i);return o==i&&(o=""),a?.name||o||r}function as(e,t){let a=e.translationServices?.[t];return a?.group==="pro"||a?.group==="max"}function ui(e){return e.prompt?.trim().length>0}function Od(){let e=On();return ha(e,"1.18.2")}var y2=1e3*3600*24;var w2=new String(/* MULTIPLE_REMOVED_BLOCKS */u6535\u4F51\u4EF2\u4EDD\u4FEA\u4F3E\u6C14\u4FB4\u50F3\u4EBF\u6002\u4ED1\u8D37\u948D\u9488\u9553\u956A\u94A9\u9486\u94F1\u94F3\u953F\u988C\u4F1B\u4F32\u65E8\u52FA\u5FFE\u5FC9\u6293\u605D\u6269\u7118\u64E2\u624E\u9091\u5457\u53ED\u5693\u5627\u53FB\u5514\u5459\u5565\u5423\u54D0\u9E2E\u55EC\u622E\u5C50\u6170\u5C39\u5201\u90B5\u5DF2\u84E5\u8363\u8314\u85D0\u84AF\u8484\u4E1A\u6B49\u60CE\u83B0\u8605\u84CF\u830B\u839E\u827E\u827D\u911A\u8327\u828B\u82C8\u911E\u8638\u828E\u830C\u82A5\u8288\u9100\u5E76\u8585\u66F2\u8335\u750D\u8359\u849F\u5C7A\u5C7E\u53B6\u59AB\u5E7B\u7EF7\u5A86\u6215\u7ED2\u598C\u7E9F\u56F0\u56D7\u624C\u6C69\u6E25\u5E86\u9E80\u9E38\u98DE\u6C3D\u4F08\u94A1\u69CA\u836E\u8FBE\u8FE5\u5955\u989C\u5934\u619D\u9057\u6C35\u5FD0\u4EAD\u70B9\u8FF8\u8182\u908B\u7248\u724C\u724D\u7247\u7252\u987E\u6539\u987E"),A2=new String("\u842C\u8207\u919C\u5C08\u696D\u53E2\u6771\u7D72\u4E1F\u5169\u56B4\u55AA\u500B\u723F\u8C50\u81E8\u70BA\u9E97\u8209\u9EBC\u7FA9\u70CF\u6A02\u55AC\u7FD2\u9109\u66F8\u8CB/* MULTIPLE_REMOVED_BLOCKS */u904E\u9031\u9D99\u671B\u6BC3\u9088\u737B\u904B\u5E1D\u777F\u906F\u8FF5\u750B\u7517\u9117\u6B4A\u6B33\u96E2\u6575\u9059\u9031\u557B\u8FE5\u8C9E\u6572\u65C1\u9F8D\u516D\u5546\u9D90\u9870\u4EA4\u5955\u5E1F\u6207\u5157\u889E\u5F08\u5DD2\u89AA\u9D89\u8668\u65B0\u5B70\u901F\u906B\u9055\u90ED\u6B51\u96DC\u6566\u6579\u56B2\u7763\u9316\u7CB2\u9024\u58D1\u97F0\u9910\u60C4\u88FB\u900D\u8FF7\u9074\u53D4\u9D81\u5C31\u52CD\u654A\u4E3B\u9035\u9076\u6BB6\u6C03\u893B\u8667\u8FFC\u9060\u9068\u9054\u8FEB\u9CEA\u9051\u4EB9\u900F\u9036\u890E\u8FD4\u9022\u9004\u9020\u5F65\u7522\u905B\u8922\u9002\u9041\u528C\u983B\u986A\u984F\u8FD1\u907E\u9005\u4EA2\u8FED\u9020\u8FFD\u65B9\u904D\u9080\u7FFD\u908A\u8863\u8FCE\u908D\u5EC9\u88D2\u905E\u80B2\u9021\u8FF0\u719F\u7385\u587E\u5145\u6594\u900B\u8FF0\u9011\u8FD6\u87A4\u7388\u7387\u8FE8\u88AC\u68C4\u906E\u7386\u6597\u9083\u8FA3\u9D6B\u8D1B\u5F70\u74E3\u7AF7\u8FA8\u8FA6\u9123\u902D\u8FAE\u9023\u5E76\u74F6\u8FAF\u6587\u6595\u8FF6\u907C\u9D41\u9CFC\u9DDF\u9E06\u6596\u9016\u901B\u6592\u6548\u8803\u981D\u5F65\u7522\u658C\u90CA\u6548\u5FDE\u619D\u9070\u6489\u541D\u8FE6\u7D0A\u9F7E\u5E02\u902E\u8877\u6B62\u907A\u6B65\u9003\u9063\u4EA6\u8FEA\u4E0A\u80AF\u8D07\u89B7\u8FFA\u8FC2\u76BD\u76BB\u9087\u8FCB\u6B6D\u9E07\u6C08\u6B72\u6B67\u8FD7\u52EF\u5277\u986B\u9090\u8A00\u901C\u8FFE\u6B54\u6B64\u5FD0\u96CC\u9017\u903C\u9010\u9F52\u8FD3\u9015\u9077\u5361\u9038\u9082\u8D0F\u905C\u9E01\u9079\u88A4\u901A\u8FFB\u88B2\u8FC5\u8803\u980F\u909F\u9086\u7FB8\u5B34\u81DD\u8912\u9032\u6BC5\u8FD5\u52BE\u523B\u5287\u9826\u9014\u903E\u903E\u8FC4\u591C\u6B2C\u5352\u8FE4\u8FEE\u864D\u8FFF\u7725\u8CB2\u922D\u67F4\u8FC6\u58DF\u9DFE\u9E17\u8655\u8656\u864E\u864E\u52F4\u882A\u9F91\u4E9B\u7961\u9F92\u7826\u7931\u9B33\u8C66\u89DC\u9B86\u80D4\u98FA\u8654\u8659\u5470\u865E\u865E\u807E\u8650\u8FE3\u9F94\u865B\u8661\u8FCD\u888C\u7D2B\u819A\u76E7\u865C\u6B76\u616E\u9F98\u8972\u8654\u8A3E\u8B8B\u901D\u5360\u8ADF\u8B4B\u8B95\u8B01\u88DB\u8B3E\u818F\u8ABF\u8AC2\u8AE2\u8A5B\u8AFC\u8A77\u4EAD\u8C6A\u8B20\u4EB3\u8ABF\u9AD8\u8A57\u8ACD\u4EAE\u6BEB\u8998\u8B11\u8A1F\u8AE1\u8AAA\u8AC3\u8A56\u8A0E\u8A23\u8AF1\u8ABA\u8AEB\u8ABB\u4EAC\u8A9A\u8B9C\u8AC7\u8A2C\u8A4A\u8B9F\u8A69\u8A7F\u8B4A\u8B78\u8A8C\u8A70\u8B46\u8B80\u8AFB\u8B3B\u8A98\u8AC9\u8B52\u8A7B\u901E\u8B6D\u8AA5\u8A75\u8B1D\u8A85\u8AC8\u8A71\u8A1E\u8B51\u8A22\u8A6C\u8A34\u8A2D\u8AF7\u8B57\u8A17\u8AD9\u8A46\u8A44\u8ADE\u8B06\u8B9A\u54C0\u8AC0\u8B25\u8B0F\u8ADB\u8ABD\u8B15\u8A8F\u8AF4\u8AA0\u8AD3\u8AA7\u8A39\u8A59\u8A27\u8A76\u8A60\u8A92\u8AEE\u8A66\u8A52\u8AA1\u8B67\u8B36\u8A08\u8B13\u8A96\u8AF5\u8ABC\u8A6B\u8B85\u8B53\u8AF8\u8AE0\u8A51\u8A41\u8A74\u8A7C\u8A91\u8B47\u8A87\u8A13\u8B31\u8A82\u8AB9\u5256\u8A0C\u8B23\u8A0F\u8A55\u901E\u8B1C\u8B88\u8A10\u8B40\u8A9E\u8AAB\u8A02\u8A36\u8AA3\u8AEE\u8AE8\u8AD1\u8B56\u8A1D\u8A99\u74FF\u8B24\u8AF2\u8B5A\u8A3C\u8B82\u8B6B\u4EAB\u70F9\u8B4E\u8AC2\u8AA6\u8A83\u8A7A\u8A0A\u8A95\u90E8\u8B94\u8A6D\u4EA8\u8ADD\u8B49\u8B0B\u8B92\u8AB8\u8A25\u8AB0\u8B22\u8B59\u8A3A\u8A45\u8B96\u8AD7\u8B12\u8A31\u6568\u8AD6\u8A6E\u8AED\u8B63\u8AED\u8A16\u8A1B\u8A50\u8B55\u8AA8\u8A62\u8A63\u8A11\u8B10\u8B7A\u8AE7\u8A4D\u8ADC\u8A30\u8B05\u8A84\u8B7F\u8ACB\u8B2E\u8AD8\u8B5F\u8B42\u8AE4\u907B\u8AFF\u907B\u8B54\u8944\u8AA4\u8B5D\u9050\u8A12\u8A54\u8B45\u8A8D\u8ACF\u8B98\u8B35\u8A86\u8B2C\u8A61\u8A5E\u8B33\u8A4E\u8A18\u8A8B\u8B18\u8B28\u8B6A\u8B7E\u8B5C\u8B1A\u8B70\u56C8\u8AFE\u8B8C\u8B39\u8A4C\u8B5C\u8AC6\u8B00\u8B41\u8AF6\u8B77\u8A73\u8B99\u8A81\u8B1B\u8B50\u8B68\u8B19\u8B0A\u8A15\u4E69\u9078\u8AEF\u8A58\u8B4F\u8A4F\u8B02\u8B16\u8AB2\u8B6F\u8AF0\u8B84\u8A03\u8AE6\u8B9E\u8B17\u8B2B\u8B2A\u8A68\u8B48\u6541\u8B0E\u8AD4\u8A3B\u8AFA\u8A2A\u8B58\u8B30\u8AFA\u8B74\u8A3F\u8AB6\u8B3C\u8B14\u8B2F\u8AD5\u8A40\u8B1E\u8AC4\u8AD2\u8B79\u8B93\u8AF3\u8B27\u8A72\u8B60\u8B2B\u8FC9\u9072\u892D\u8FE2\u4E0E\u74EC\u90A1\u65C3\u65D3\u65DF\u65C2\u65CC\u65BB\u65C4\u65C5\u65DB\u65C6\u653E\u65D6\u65D0\u65DA\u65BF\u65CB\u65DD\u65CD\u65CF\u65BD\u65CE\u65D7\u65BC\u65D2\u8FE1\u907F\u65DE\u7ACB\u97F3\u7ADF\u7AE0\u610F\u7AED\u7AEB\u9052\u7AE6\u9E15\u9D17\u98AF\u9053\u6232\u7AE3\u8F9B\u7AE4\u4F47\u9001\u7AD1\u903D\u9871\u9073\u9042\u7AD8\u9756\u7FCA\u7AEE\u8FF8\u9058\u9006\u6EAF\u7AEF\u59BE\u906D\u9081\u7AE5\u9075\u7AD9\u8FBF\u9F66\u9F5F\u8D19\u89A4\u5C0E\u53E1\u9DF2\u5F6A\u9F72\u9F57\u9F6F\u8665\u9F5D\u9F7B\u79BB\u9F6E\u9F65\u9044\u9F6C\u9F56\u9F61\u866A\u9F58\u9F55\u9F54\u9F63\u9F59\u9F5B\u9F7A\u9F5E\u9F76\u9F6A\u9F60\u9F71\u9F77\u9F70\u9F78\u751D\u9F6B\u9F75\u9F74\u9F5C\u8664\u7AF6\u4EA1\u8093\u8182\u88D4\u727D\u76F2\u6757\u58C5\u7515\u7F4B\u9954\u8841\u96CD\u5DDF\u4EA5\u74E4\u7384\u755C\u52F7\u902F\u9099\u8CCC\u5FD8\u6C13\u5984\u5DE1\u908B\u88F9\u88CF\u9E79\u9E75\u9047\u905D\u908F\u9084\u8931\u8870\u9110\u755D\u9E7C\u8FF4\u7A1F\u7A1F\u4EB6\u5363\u9E7A\u9F4A\u9F4D\u9F4E\u9F4B\u9F4C\u5291\u9F4F\u535E\u9034\u9049\u9069\u8FD2\u8DE1\u9019\u907D\u8FE0\u904A\u9067\u9085\u900C\u5159\u515B\u515E\u515D\u5161\u5163\u55E7\u74E9\u7CCE");var tr=class{from;to;constructor(t){this.from=t.from,this.to=t.to}sendMessages(t){let a={type:t.type,data:t.data,id:t.id||this.getRandomId(),isAsync:!1};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(a)}))}getRandomId(){return Math.random()*1e17+new Date().getTime()}sendAsyncMessages({type:t,data:a}){return new Promise(n=>{let r=this.handleMessages(s=>{s.id===i&&(n(s.data),r())}),i=this.getRandomId(),o={type:t,data:a,id:i,isAsync:!0};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(o)}))})}handleMessageOnce(t){return new Promise(a=>{let n=this.handleMessage(t,r=>{a(r.data),n()})})}handleMessage(t,a){return this.handleMessages(n=>{n.type===t&&a(n)})}handleMessages(t){let a=n=>{let i=JSON.parse(n.detail||"{}");i&&typeof i=="object"&&t(i)};return globalThis.document.addEventListener(this.from,a),()=>{globalThis.document.removeEventListener(this.from,a)}}},zd={get(e,t,a){return t in e?(...n)=>{let r=e[t];return typeof r=="function"?r.apply(e,n):Reflect.get(e,t,a)}:n=>e.sendAsyncMessages({type:t,data:n})}};var Ud="imt-browser-bridge-event-to-content-script",qd="imt-browser-bridge-event-to-inject",ns=new tr({from:Ud,to:qd}),li=new tr({from:qd,to:Ud}),rs=new Proxy(ns,zd);var zb="Original";function is(){return zb}var Ub="auto";function os(e){Ub=e}var ar=new Map,_a=class{fromType;logger;constructor(t,a=!1){this.logger=new Ln,a&&this.logger.setLevel("debug"),this.fromType=t,ar.has(t)||(ar.set(t,new Map),ie.runtime.onMessage.addListener((n,r,i)=>{if(n.target==="offscreen")return;let o=n.from,s=n.to,u,c,l;r.tab&&r.tab.id&&(u=r.tab.id,o=`${o}:${u}`,c=r.tab.url,l=r.tab.active),this.logger.debug(`${n.to} received message [${n.payload.method}] from ${n.from}`,n.payload.data?n.payload.data:" ");let d=us(s),{type:m,name:p}=d;if(m!==t)return!1;let x=us(o),S=ar.get(m).get(p);if(!S)return this.logger.debug(`no message handler for ${m}:${s}, but it's ok`),!1;let{messageHandler:g,sync:M}=S,h={type:t,name:x.name,id:u,url:c,active:l};if(M){try{let k=g(n.payload,h);i({ok:!0,data:k})}catch(k){i({ok:!1,errorName:k.name,errorMessage:k.message,errorDetails:k.details,errorStatus:k.status})}return!1}else return g(n.payload,h).then(k=>{i({ok:!0,data:k})}).catch(k=>{i({ok:!1,errorName:k.name,errorMessage:k.message,errorDetails:k.message,errorStatus:k.status})}),!0}))}getConnection(t,a,n){let r=!1;n&&n.sync&&(r=!0);let i=this.fromType,o=ar.get(i);if(o.has(t))return o.get(t).connectionInstance;{let s=new ss(`${i}:${t}`,this.logger);return ar.get(i).set(t,{messageHandler:a,sync:r,connectionInstance:s}),s}}},ss=class{from;logger;constructor(t,a){this.from=t,this.logger=a}async sendMessage(t,a){let n=us(t),{type:r,id:i}=n;if(r!=="content_script"){let o={to:t,from:this.from,payload:a};this.logger.debug(`${o.from} send message [${o.payload.method}] to ${o.to}`,o.payload.data?o.payload.data:" ");try{let s=await ie.runtime.sendMessage(o);return Gd(o,s,this.logger)}catch(s){if(r==="popup"){let u=`popup ${t} is not active, so the message does not send, ignore this error, ${JSON.stringify(a)}`;return this.logger.debug(u,a,t,s),Promise.resolve({message:u})}else throw s}}else{let o={from:this.from,to:t,payload:a};this.logger.debug(`${o.from} send message [${o.payload.method}] to ${o.to}`,o.payload.data?o.payload.data:" ");let s=await ie.tabs.sendMessage(i,o);return Gd(o,s,this.logger)}}};function Gd(e,t,a){if(t){if(t.ok)return a.debug(`${e.from} received response from ${e.to}:`,t.data?t.data:" "),t.data;throw new Dt(t.errorName||"UnknownError",t.errorMessage||"Unknown error").initNetWork(t.errorStatus)}else throw new Dt("noResponse","Unknown error")}function us(e){let t=e.split(":");if(t.length<2)throw new Error("not a valid to string");let a={type:t[0],name:t[1]};if(t[0]==="content_script"){let n=parseInt(t[2]);if(!isNaN(n))a.id=n;else throw new Error("tab id not a valid number")}return a}function ls(e){return(e?.id?.endsWith("pdfWebPage")||!1)&&globalThis.top==globalThis.self}async function Hd(){return globalThis.top!==globalThis.self||!document.body?!1:document.body.children.length===0&&document.body.hasChildNodes()?!!(await fetch(location.href)).headers.get("content-type")?.includes("application/pdf"):!1}function Kd(e){try{if(!e||!ls(e))return"";let t="",a=e.pdfUrlExtractRule,n=a.selectors||[];a.selector&&n.push(a.selector);let r=a.attributes||[];a.attribute&&r.push(a.attribute);let i=a.queries||[];a.query&&i.push(a.query);for(let s of n)if(!document.querySelectorAll(s))return"";for(let s of n){let u=document.querySelectorAll(s);if(u.length){for(let c of u){for(let l of r)if(t=c.getAttribute(l)||"",t)break;if(t)break}if(t)break}}t||(t=document.querySelector("embed[type='application/pdf']")?.getAttribute("src")||"");let o=qb(location.href,t);for(let s of i){let c=new URL(o).searchParams.get(s)||"";if(c)return c}return o}catch{return""}}function qb(e,t){if(t.startsWith("about:"))return"";if(t.startsWith("//"))try{return new URL(e).protocol+t}catch{return"https:"+t}if(t.startsWith("http://")||t.startsWith("https://"))return t;try{if(t.startsWith("/")){let a=new URL(e);return a.protocol+"//"+a.host+t}else{let a=new URL(e);return new URL(t,a.href).href}}catch{return""}}var de={},Hb=async function(e,t){let{method:a,data:n}=e;if(a==="getIsDulSubtitle")return de.getIsDulSubtitle();if(a==="getPageStatus")return is();if(a==="toggleSidePanel"){let o=de.getPureGlobalContext();Vd(o,"shortcut",n?.isOpen);return}a==="updateContextState"&&await de.updateContextState(n);let r=await de.updateGlobalContext(),i=Date.now();if(X.debug(`content script received message: ${a}`,n||" "),a==="translateTheWholePage")await de.translateTheWholePage(n),Ke(gt,i);else if(a==="translateTheMainPage")await de.translateTheMainPage(n),Ke(gt,i);else if(a==="translateToThePageEndImmediately")await de.translateToThePageEndImmediately(n),Ke(gt,i);else if(a==="toggleTranslateManga")await de.toggleTranslateManga(),Ke(gt,i);else if(a==="toggleTranslatePage"){let o=location.href;if(ls(r?.rule)||await Hd()){let s=Kd(r.rule);Qd(!0,s||o);return}await de.toggleTranslatePage(n),Ke(gt,i)}else if(a==="toggleTranslateTheWholePage")await de.toggleTranslateTheWholePage(n),Ke(gt,i);else if(a==="toggleTranslateTheMainPage")await de.toggleTranslateTheMainPage(n),Ke(gt,i);else if(a==="toggleOnlyTransation")await de.ensureSwitchTranslationMode(n),Ke(gt,i);else if(a=="toggleEnableEditTranslation")de.toggleEnableEditTranslation();else if(a==="translatePage")await de.translatePage(r,n),Ke(gt,i);else if(a==="toggleTranslationMask")await de.toggleTranslationMask(n);else if(a==="restorePage")de.restorePage();else if(a==="retryFailedParagraphs")de.retryFailedParagraphs();else if(a=="change_translate_service")de.reportTranslateService(r,n);else if(a==="switchTranslationMode"){if(r.rule.isPdf)return;n&&n.mode&&(await de.switchTranslationMode(n.mode),await de.reloadSubtitleWithTranslationModeChanged())}else if(a==="autoEnableSubtitleChanged")de.autoEnableSubtitleChanged(r,n);else if(a==="tempDisableSubtitleChanged")de.tempDisableSubtitleChanged(r,n);else if(a=="shareToDraft")globalThis.document.dispatchEvent(new CustomEvent(cl,{detail:n}));else if(a=="toggleTranslateToThePageEndImmediately")await de.toggleTranslateToThePageEndImmediately(n);else if(a==="toggleMouseHoverTranslateDirectly")globalThis.document.dispatchEvent(new CustomEvent(dl,{detail:n}));else if(a==="translateWithOpenAI")await de.translatePageWithTranslationService("openai",n);else if(a==="translateWithGoogle")await de.translatePageWithTranslationService("google",n);else if(a==="translateWithDeepL")await de.translatePageWithTranslationService("deepl",n);else if(a==="translateWithBing")await de.translatePageWithTranslationService("bing",n);else if(a==="translateWithTransmart")await de.translatePageWithTranslationService("transmart",n);else if(a==="translateWithGemini")await de.translatePageWithTranslationService("gemini",n);else if(a==="translateWithClaude")await de.translatePageWithTranslationService("claude",n);else if(a.startsWith("translateWithCustom"))await de.translatePageWithTranslationService(r.config.rawUserConfig?.shortcuts?.translateWithCustomServices?.[a]??"bing",n);else if(a==="translateInputBox")await de.translateInputBoxWithShortcut(r);else if(a!=="updateGlobalCtx")if(a==="toggleVideoSubtitlePreTranslation")ea()||await de.toggleVideoSubtitlePreTranslation();else if(a==="getAsyncContextString"){if(!ea())return JSON.stringify(r);await qn(5e3)}else if(a==="inputSelectedTextTranslate")await de.inputSelectedTextTranslate(r,n);else{if(a==="popupEventReport")return de.popupEventReport(r,n);if(a==="updateFloatBallEnable")return de.updateFloatBallEnable();if(a==="selectionTranslate")return de.selectionTranslate(r,n);a==="webReport"?document.dispatchEvent(new CustomEvent(ul,{detail:{type:"webReport"}})):a===ml&&await de.translateSelectImage(r,n)}};var ci;function Wd(){return ci||(ci=new _a("content_script",!1).getConnection("main",Hb),ci)}var Kb=[["auto","auto"],["zh-CN","zh"],["zh-TW","zh-TW"],["yue","ct"],["de","de"],["en","en"],["es","es"],["fr","fr"],["id","id"],["it","it"],["ja","ja"],["ko","ko"],["ms","ms"],["pt","pt"],["ru","ru"],["th","th"],["tr","tr"],["vi","vi"]],Wb="https://transmart.qq.com/api/imt",cs=class e{static langMapReverse=new Map(Kb.map(([t,a])=>[a,t]));static getClientKey(){return"tencent_transmart_crx_"+btoa(navigator.userAgent).slice(0,100)}static async detectLanguageRemotelyByTransmart(t){let a={header:{fn:"text_analysis",client_key:e.getClientKey()},text:t.slice(0,280)},n=await rt({url:Qb(Wb),method:"POST",body:JSON.stringify(a)});if(n.header.ret_code!=="succ")throw new Error(n.message||n.header.ret_code);let r=n.language,i=e.langMapReverse.get(r);return i||r}};function Qb(e){if(!pe())return e;let t=new URL(e);return t.searchParams.set("timestamp",Date.now().toString()),t.toString()}function fa(e){let a=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];return a.endsWith(",")&&(a=a.slice(0,-1)),a}function di(e){try{let a=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];a.endsWith(",")&&(a=a.slice(0,-1));let[n,r,i]=a.split("/");return a=`${i}-${n}-${r}`,a}catch{return"unknown"}}function Yd(e){let[t,a,n]=e.split("-");return`${t}-${a.padStart(2,"0")}-${n.padStart(2,"0")}`}function mi(e){e.setMinutes(e.getMinutes()+e.getTimezoneOffset()+8*60),e.setDate(e.getDate()+4-(e.getDay()||7));let t=new Date(e.getFullYear(),0,1);return{week:Math.ceil(((e.getTime()-t.getTime())/864e5+1)/7),year:e.getFullYear()}}async function Jd(e,t,a){try{let n=await window.crypto.subtle.importKey("raw",ms(t),"AES-GCM",!0,["encrypt","decrypt"]),r=ms(e),i=await globalThis.crypto.subtle.encrypt({name:"AES-GCM",iv:ms(a)},n,r);return Vb(i)}catch{return Promise.reject("Encryption failed")}}function Vb(e){let t="",a=new Uint8Array(e),n=a.byteLength;for(let r=0;r<n;r++)t+=String.fromCharCode(a[r]);return globalThis.btoa(t)}function ms(e){return new TextEncoder().encode(e)}var Zd={ai_assistant:"ex_char_arg1",ai_assistant_use:"ex_char_arg2",selection_text:"ex_char_arg3",translation_result:"ex_char_arg4",has_pined:"ex_char_arg5",user_id:"ex_int_arg1",enable_side_panel:"ex_char_arg7",popup_switch_extend_field_enabled:"ex_int_arg2",popup_switch_extend_field:"ex_char_arg2"};function rr(e){return e?.install_day&&(e.install_day=Yd(e.install_day)),e.temp_translate_domain_minutes&&(e.temp_translate_domain_minutes=parseInt(e.temp_translate_domain_minutes.toString()),e.temp_translate_domain_minutes>1e8&&(e.temp_translate_domain_minutes=1e8)),Object.keys(Zd).forEach(t=>{e[t]!==void 0&&(e[Zd[t]]=e[t],delete e[t])}),e.site_host&&delete e.site_host,e}var hn="input is invalid type",ps=typeof window=="object",ba=ps?window:{};ba.JS_SHA256_NO_WINDOW&&(ps=!1);var Yb=!ps&&typeof self=="object",Jb=!ba.JS_SHA256_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;Jb?ba=global:Yb&&(ba=self);var g6=!ba.JS_SHA256_NO_COMMON_JS&&typeof module=="object"&&module.exports,h6=typeof define=="function"&&define.amd,ir=!ba.JS_SHA256_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",K="0123456789abcdef".split(""),Zb=[-2147483648,8388608,32768,128],Ct=[24,16,8,0],pi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],gi=["hex","array","digest","arrayBuffer"],qe=[];(ba.JS_SHA256_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"});ir&&(ba.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(e){return typeof e=="object"&&e.buffer&&e.buffer.constructor===ArrayBuffer});var Xd=function(e,t){return function(a){return new Ge(t,!0).update(a)[e]()}},e0=function(e){var t=Xd("hex",e);t.create=function(){return new Ge(e)},t.update=function(r){return t.create().update(r)};for(var a=0;a<gi.length;++a){var n=gi[a];t[n]=Xd(n,e)}return t},$d=function(e,t){return function(a,n){return new hi(a,t,!0).update(n)[e]()}},t0=function(e){var t=$d("hex",e);t.create=function(r){return new hi(r,e)},t.update=function(r,i){return t.create(r).update(i)};for(var a=0;a<gi.length;++a){var n=gi[a];t[n]=$d(n,e)}return t};function Ge(e,t){t?(qe[0]=qe[16]=qe[1]=qe[2]=qe[3]=qe[4]=qe[5]=qe[6]=qe[7]=qe[8]=qe[9]=qe[10]=qe[11]=qe[12]=qe[13]=qe[14]=qe[15]=0,this.blocks=qe):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e?(this.h0=3238371032,this.h1=914150663,this.h2=812702999,this.h3=4144912697,this.h4=4290775857,this.h5=1750603025,this.h6=1694076839,this.h7=3204075428):(this.h0=1779033703,this.h1=3144134277,this.h2=1013904242,this.h3=2773480762,this.h4=1359893119,this.h5=2600822924,this.h6=528734635,this.h7=1541459225),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=e}Ge.prototype.update=function(e){if(!this.finalized){var t,a=typeof e;if(a!=="string"){if(a==="object"){if(e===null)throw new Error(hn);if(ir&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!ir||!ArrayBuffer.isView(e)))throw new Error(hn)}else throw new Error(hn);t=!0}for(var n,r=0,i,o=e.length,s=this.blocks;r<o;){if(this.hashed&&(this.hashed=!1,s[0]=this.block,s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),t)for(i=this.start;r<o&&i<64;++r)s[i>>2]|=e[r]<<Ct[i++&3];else for(i=this.start;r<o&&i<64;++r)n=e.charCodeAt(r),n<128?s[i>>2]|=n<<Ct[i++&3]:n<2048?(s[i>>2]|=(192|n>>6)<<Ct[i++&3],s[i>>2]|=(128|n&63)<<Ct[i++&3]):n<55296||n>=57344?(s[i>>2]|=(224|n>>12)<<Ct[i++&3],s[i>>2]|=(128|n>>6&63)<<Ct[i++&3],s[i>>2]|=(128|n&63)<<Ct[i++&3]):(n=65536+((n&1023)<<10|e.charCodeAt(++r)&1023),s[i>>2]|=(240|n>>18)<<Ct[i++&3],s[i>>2]|=(128|n>>12&63)<<Ct[i++&3],s[i>>2]|=(128|n>>6&63)<<Ct[i++&3],s[i>>2]|=(128|n&63)<<Ct[i++&3]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.block=s[16],this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}};Ge.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=Zb[t&3],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}};Ge.prototype.hash=function(){var e=this.h0,t=this.h1,a=this.h2,n=this.h3,r=this.h4,i=this.h5,o=this.h6,s=this.h7,u=this.blocks,c,l,d,m,p,x,A,S,g,M,h;for(c=16;c<64;++c)p=u[c-15],l=(p>>>7|p<<25)^(p>>>18|p<<14)^p>>>3,p=u[c-2],d=(p>>>17|p<<15)^(p>>>19|p<<13)^p>>>10,u[c]=u[c-16]+l+u[c-7]+d<<0;for(h=t&a,c=0;c<64;c+=4)this.first?(this.is224?(S=300032,p=u[0]-1413257819,s=p-150054599<<0,n=p+24177077<<0):(S=704751109,p=u[0]-210244248,s=p-1521486534<<0,n=p+143694565<<0),this.first=!1):(l=(e>>>2|e<<30)^(e>>>13|e<<19)^(e>>>22|e<<10),d=(r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7),S=e&t,m=S^e&a^h,A=r&i^~r&o,p=s+d+A+pi[c]+u[c],x=l+m,s=n+p<<0,n=p+x<<0),l=(n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10),d=(s>>>6|s<<26)^(s>>>11|s<<21)^(s>>>25|s<<7),g=n&e,m=g^n&t^S,A=s&r^~s&i,p=o+d+A+pi[c+1]+u[c+1],x=l+m,o=a+p<<0,a=p+x<<0,l=(a>>>2|a<<30)^(a>>>13|a<<19)^(a>>>22|a<<10),d=(o>>>6|o<<26)^(o>>>11|o<<21)^(o>>>25|o<<7),M=a&n,m=M^a&e^g,A=o&s^~o&r,p=i+d+A+pi[c+2]+u[c+2],x=l+m,i=t+p<<0,t=p+x<<0,l=(t>>>2|t<<30)^(t>>>13|t<<19)^(t>>>22|t<<10),d=(i>>>6|i<<26)^(i>>>11|i<<21)^(i>>>25|i<<7),h=t&a,m=h^t&n^M,A=i&o^~i&s,p=r+d+A+pi[c+3]+u[c+3],x=l+m,r=e+p<<0,e=p+x<<0;this.h0=this.h0+e<<0,this.h1=this.h1+t<<0,this.h2=this.h2+a<<0,this.h3=this.h3+n<<0,this.h4=this.h4+r<<0,this.h5=this.h5+i<<0,this.h6=this.h6+o<<0,this.h7=this.h7+s<<0};Ge.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,a=this.h2,n=this.h3,r=this.h4,i=this.h5,o=this.h6,s=this.h7,u=K[e>>28&15]+K[e>>24&15]+K[e>>20&15]+K[e>>16&15]+K[e>>12&15]+K[e>>8&15]+K[e>>4&15]+K[e&15]+K[t>>28&15]+K[t>>24&15]+K[t>>20&15]+K[t>>16&15]+K[t>>12&15]+K[t>>8&15]+K[t>>4&15]+K[t&15]+K[a>>28&15]+K[a>>24&15]+K[a>>20&15]+K[a>>16&15]+K[a>>12&15]+K[a>>8&15]+K[a>>4&15]+K[a&15]+K[n>>28&15]+K[n>>24&15]+K[n>>20&15]+K[n>>16&15]+K[n>>12&15]+K[n>>8&15]+K[n>>4&15]+K[n&15]+K[r>>28&15]+K[r>>24&15]+K[r>>20&15]+K[r>>16&15]+K[r>>12&15]+K[r>>8&15]+K[r>>4&15]+K[r&15]+K[i>>28&15]+K[i>>24&15]+K[i>>20&15]+K[i>>16&15]+K[i>>12&15]+K[i>>8&15]+K[i>>4&15]+K[i&15]+K[o>>28&15]+K[o>>24&15]+K[o>>20&15]+K[o>>16&15]+K[o>>12&15]+K[o>>8&15]+K[o>>4&15]+K[o&15];return this.is224||(u+=K[s>>28&15]+K[s>>24&15]+K[s>>20&15]+K[s>>16&15]+K[s>>12&15]+K[s>>8&15]+K[s>>4&15]+K[s&15]),u};Ge.prototype.toString=Ge.prototype.hex;Ge.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,a=this.h2,n=this.h3,r=this.h4,i=this.h5,o=this.h6,s=this.h7,u=[e>>24&255,e>>16&255,e>>8&255,e&255,t>>24&255,t>>16&255,t>>8&255,t&255,a>>24&255,a>>16&255,a>>8&255,a&255,n>>24&255,n>>16&255,n>>8&255,n&255,r>>24&255,r>>16&255,r>>8&255,r&255,i>>24&255,i>>16&255,i>>8&255,i&255,o>>24&255,o>>16&255,o>>8&255,o&255];return this.is224||u.push(s>>24&255,s>>16&255,s>>8&255,s&255),u};Ge.prototype.array=Ge.prototype.digest;Ge.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(this.is224?28:32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),this.is224||t.setUint32(28,this.h7),e};function hi(e,t,a){var n,r=typeof e;if(r==="string"){var i=[],o=e.length,s=0,u;for(n=0;n<o;++n)u=e.charCodeAt(n),u<128?i[s++]=u:u<2048?(i[s++]=192|u>>6,i[s++]=128|u&63):u<55296||u>=57344?(i[s++]=224|u>>12,i[s++]=128|u>>6&63,i[s++]=128|u&63):(u=65536+((u&1023)<<10|e.charCodeAt(++n)&1023),i[s++]=240|u>>18,i[s++]=128|u>>12&63,i[s++]=128|u>>6&63,i[s++]=128|u&63);e=i}else if(r==="object"){if(e===null)throw new Error(hn);if(ir&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!ir||!ArrayBuffer.isView(e)))throw new Error(hn)}else throw new Error(hn);e.length>64&&(e=new Ge(t,!0).update(e).array());var c=[],l=[];for(n=0;n<64;++n){var d=e[n]||0;c[n]=92^d,l[n]=54^d}Ge.call(this,t,a),this.update(l),this.oKeyPad=c,this.inner=!0,this.sharedMemory=a}hi.prototype=new Ge;hi.prototype.finalize=function(){if(Ge.prototype.finalize.call(this),this.inner){this.inner=!1;var e=this.array();Ge.call(this,this.is224,this.sharedMemory),this.update(this.oKeyPad),this.update(e),Ge.prototype.finalize.call(this)}};var fn=e0();fn.sha256=fn;fn.sha224=e0(!0);fn.sha256.hmac=t0();fn.sha224.hmac=t0(!0);var a0=fn;var Xb=a0.sha256;function or(e){return Promise.resolve(Xb(e))}function n0({key:e,params:t,ctx:a,forceDaily:n,ignoreGA:r}){if(e=="init_page_daily"){let i="-1";a?.config?.pcFloatBall?.enableSidePanel==!0?i="1":a?.config?.pcFloatBall?.enableSidePanel==!1&&(i="0"),t={...t,enable_side_panel:i}}return $b(e,[{name:e,params:t}],a,n,r)}async function $b(e,t,a,n=!1,r=!1){if(a&&!a.config?.rawUserConfig?.disableReport)try{let i=zn(),o=ke(),s=o.INSTALL_FROM==="firefox_store";i?.name?.startsWith("ImtFx")&&(s=!1);let u=ea(),c=`report_${e}`,l=e.endsWith("_daily");if(l){if(u)return;let A=await Ia(c,0),S=fa(new Date(A)),g=Date.now(),M=fa(new Date(g));if(S===M&&!n)return;await Dl(c,g)}else if(!a.config.telemetry)return;let{fakeUserId:d,installedAt:m}=await Xn(),p=Or(),x=await ry({events:t,ctx:a,env:o,isDaily:l,isInIframe:u,installedAt:m,imtBrowser:i});if(s)return;r||p.forEach(async A=>{let S=await rt({responseType:"text",url:gs(A),method:"POST",body:JSON.stringify({client_id:d,user_id:d,events:x})})}),a.config.enableSelfServiceReport&&ey(a,d,x,a.config.disableReportHash),a.config.enablePerformanceReport&&ny(a,x)}catch(i){X.debug("report error",i)}}function ey(e,t,a,n){try{if(ka())return;a.forEach(async r=>{let i={...r.params,event_name:r.name,device_id:t};e.user?.id&&(i.user_id=e.user.id),i.site_host&&!n&&(i.ex_char_arg6=await or(i.site_host)),rr(i);let o=Date.now()+(Math.random()*100).toFixed(0);rt({url:gs(Ca),method:"POST",responseType:"text",body:JSON.stringify({nonce:o,subject:"user_behaviour",logs:[JSON.stringify(i)]})})})}catch(r){X.debug("report self service error",r)}}var ty=Date.now(),ay=["translate_page","translate_video_subtitle"];function ny(e,t){try{if(ka())return;t.forEach(async a=>{if(!ay.includes(a.name)||e.config.performanceBlockUrls?.some(c=>yt(globalThis.location.href,c)))return;let i={...a.params,event_name:"performance"};rr(i);let o={type:a.name,u:globalThis.location.href,preload_time:Date.now()-ty},s=await Jd(JSON.stringify(o),yl,vl);i.ex_char_arg1=s;let u=Date.now()+(Math.random()*100).toFixed(0);rt({url:gs(Ca),method:"POST",responseType:"text",body:JSON.stringify({nonce:u,subject:"user_behaviour",logs:[JSON.stringify(i)]})})})}catch(a){X.debug("report self service error",a)}}function gs(e){if(!pe())return e;let t=new URL(e);return t.searchParams.set("timestamp",Date.now().toString()),t.toString()}async function ry(e){let{events:t,ctx:a,env:n,isDaily:r,isInIframe:i,installedAt:o,imtBrowser:s}=e,u=ht(),c=new Date,l=await Ra(),d=await $n(),m=await er(),p=pe(),x=new Date(o),A=fa(x),S=fa(c),g=A===S,M=24*60*60*1e3,h=c.getTime()-x.getTime()<7*M,k=c.getTime()-x.getTime()<30*M,L=c.getTime()-x.getTime()<365*M,E=un.parse(window.navigator.userAgent);return t.map(w=>{let y=w.params||{};if(E.os&&(y.os_name=E.os.name||"unknown",y.os_version=E.os.version||"unknown",y.os_version_name=E.os.versionName||"unknown"),E.browser&&(y.browser_name=E.browser.name||"unknown",y.browser_version=E.browser.version||"unknown",s&&(y.browser_name=s.name,y.browser_version=s.version)),E.platform&&(y.platform_type=E.platform.type||"unknown"),E.engine&&(y.engine_name=E.engine.name||"unknown",y.engine_version=E.engine.version||"unknown"),a.translationService){y.translation_service||(y.translation_service=a.translationService);let F=a.config.translationServices?.[y.translation_service];F?.type&&(y.translation_service=F.type),!y.ai_assistant&&y.translation_service&&a.specialAiAssistant?.applyTranslationService==y.translation_service&&(y.ai_assistant=a?.specialAiAssistant.id),F&&(F.enableAIContext&&(y.ai_assistant="ai_context"),F.provider&&(y.translation_service_provider=F.provider),F.group=="max"&&(y.translation_service_provider="max"),a.translationService==="openai"&&F.provider==="custom"&&(F.apiUrl?F.apiUrl.startsWith("https://api.openai.com/")?y.openai_is_official="1":y.openai_is_official="0":y.openai_is_official="1"),ui(F)&&!y.ai_assistant&&(y.ai_assistant="common"))}if(y.translation_service&&y.translation_service.startsWith("custom")&&(y.translation_service="custom-ai"),y.ai_assistant&&y.ai_assistant.startsWith("custom")&&(y.ai_assistant="custom"),y.ai_assistant_use&&y.ai_assistant_use.startsWith("custom")&&(y.ai_assistant_use="custom"),a.targetLanguage&&!y.target_language&&(y.target_language=a.targetLanguage),a.config.interfaceLanguage&&(y.interface_language=a.config.interfaceLanguage),a.config.enableDefaultAlwaysTranslatedUrls?y.enable_default_always_translated_urls=a.config.enableDefaultAlwaysTranslatedUrls?"1":"0":y.enable_default_always_translated_urls="0",u&&(y.version=u),a.config.enableInputTranslation?y.enable_input_translation=a.config.enableInputTranslation?"1":"0":y.enable_input_translation="0",a.rule.selectionTranslation?.enable?y.enable_selection_translation=a.rule.selectionTranslation.enable?"1":"0":y.enable_selection_translation="0",a.config.translationTheme&&(y.translation_theme=a.config.translationTheme),a.config.alpha&&(y.alpha=a.config.alpha.toString()),a.config.translationLanguagePattern&&a.config.translationLanguagePattern.matches?.length>0?y.always_translate_languages=a.config.translationLanguagePattern.matches.join(","):y.always_translate_languages="none",n.INSTALL_FROM&&(y.install_from=n.INSTALL_FROM),a.config.beta&&(y.beta=a.config.alpha.toString()),a.config.translationArea&&(y.translation_area=a.config.translationArea),A){y.install_day=di(x);let F=mi(x);y.install_week=`${F.year}${F.week}`}if(a.user){let F=Sl(a.user);F&&Object.keys(F).forEach(C=>{y[C]=F[C]})}else y.user_type="anonymous";a.config.translationMode&&(y.translation_mode=a.config.translationMode),y.userscript=p.toString(),g?y.is_new_user_today="1":y.is_new_user_today="0",y.is_new_user_this_week=h?"1":"0",y.is_new_user_this_month=k?"1":"0",y.is_new_user_this_year=L?"1":"0",a.config.tempTranslateDomainMinutes?y.temp_translate_domain_minutes=a.config.tempTranslateDomainMinutes.toString():y.temp_translate_domain_minutes="0";let R="html";if(a.rule.pageType&&(R=a.rule.pageType),y.page_type=R,i?y.main_frame=0:y.main_frame=1,!r){let F=a.url;try{let C=new URL(F);y.site_host=C.hostname}catch{y.site_host="unknown"}a.sourceLanguage&&(y.source_language=a.sourceLanguage)}return l&&(y.ab_tag=l),d&&(y.ab_group=d),y.campaign=m||"none",{...w,params:y}})}var hs={getIsOpenSidePanel:()=>!1,toggleMockSidePanel:e=>{},closeMockSidePanel:()=>{}};async function na(e){return await Wd().sendMessage("background:main",e)}function rt(e){return pe()||Mr()?(e.fetchPolyfill=globalThis.GM_fetch,ja(e)):na({method:"fetch",data:e})}function fs(){return pe()?ya():na({method:"getLocalConfig"})}function r0(e){return pe()?Mt(e):na({method:"setLocalConfig",data:e})}function Qd(e=!1,t){return pe()?(ie.extra.openPdfViewerPage(e),Promise.resolve()):na({method:"openPdfViewerPage",data:{url:t}})}function Rd(){pe()||na({method:"updateImageMenu"})}function _d(){pe()||na({method:"updateUninstallUrl"})}async function Vd(e,t,a){let n=e.config,r=document.body.clientWidth;if(setTimeout(()=>{document.body.clientWidth<r&&n0({key:"openSidePanel",ctx:e,params:{trigger:t}})},1500),pe()||!Xa()||hs.getIsOpenSidePanel()){hs.toggleMockSidePanel(n);return}let i=a??await na({method:"isOpenSidePanel"});if(!(t==="shortcut"&&!i))return setTimeout(async()=>{let o=document.body.clientWidth,s=await na({method:"isOpenSidePanel"});if(o==r&&!s){hs.toggleMockSidePanel(n);return}},500),na({method:"toggleSidePanel",data:{isOpen:i}})}var i0={nologin:"\u672A\u767B\u5F55",loginForSafari:"\u767B\/* MULTIPLE_REMOVED_BLOCKS */\u514D\u8D39\u4F53\u9A8C {free_trial_minutes} \u5206\u949F","subtitle.liveFreeTrialEndTip":"\u76F4\u64AD\u89C6\u9891\u5B57\u5E55\u7FFB\u8BD1\u8BD5\u7528\u5DF2\u7ED3\u675F\uFF0C\u5F00\u901A\u4F1A\u5458\u53EF\u7EE7\u7EED\u4F7F\u7528","subtitle.meetingAutoEnableSubtitle":"\u4F1A\u8BAE\u5E73\u53F0\u81EA\u52A8\u5F00\u542F\u53CC\u8BED\u5B57\u5E55","subtitle.meetingAutoEnableSubtitleDescription":"\u5F00\u542F\u540E\uFF0CTeams\u3001Zoom\u3001Google Meet \u4F1A\u8BAE\u5E73\u53F0\u4F1A\u81EA\u52A8\u5F00\u542F\u53CC\u8BED\u5B57\u5E55","translationServices.qwen-mt":"Qwen-MT","labelKey.domains":"domains \u9886\u57DF\u63D0\u793A","description.qwenMtDomains":"\u5982\u679C\u60A8\u5E0C\u671B\u7FFB\u8BD1\u7684\u98CE\u683C\u66F4\u7B26\u5408\u67D0\u4E2A\u9886\u57DF\u7684\u7279\u6027\uFF0C\u5982\u6CD5\u5F8B\u3001\u653F\u52A1\u9886\u57DF\u7FFB\u8BD1\u7528\u8BED\u5E94\u5F53\u4E25\u8083\u6B63\u5F0F\uFF0C\u793E\u4EA4\u9886\u57DF\u7528\u8BED\u5E94\u5F53\u53E3\u8BED\u5316\uFF0C\u53EF\u4EE5\u7528\u4E00\u6BB5\u81EA\u7136\u8BED\u8A00\u6587\u672C\u63CF\u8FF0\u60A8\u7684\u9886\u57DF\uFF0C\u5C06\u5176\u63D0\u4F9B\u7ED9\u5927\u6A21\u578B\u4F5C\u4E3A\u63D0\u793A\u3002\u9886\u57DF\u63D0\u793A\u8BED\u53E5\u6682\u65F6\u53EA\u652F\u6301\u82F1\u6587\u3002"};var o0={nologin:"\u672A\u767B\u5165",loginForSafari:"\u767B\u5165\u6216\u8A3B\u518A",login:"\u767B\u5165",goLogin:"\u53BB\u767B\u5165",manageAccount:"\u7BA1\u7406\u5E33\u6236",openPremium:"\u5347\u7D1A\u6210\u5C08\u696D\u7248",logout:"\u767B\u51FA",curentPlan:"\/* MULTIPLE_REMOVED_BLOCKS */sageTips":"\u6C89\u6D78\u5F0F\u7FFB\u8B6F\u7528\u91CF\u63D0\u793A","reportInfo.emailPlaceholder":"\u806F\u7D61\u4FE1\u7BB1\uFF08\u5FC5\u586B\uFF09","subtitle.quickButton.upgradePro":"\u5347\u7D1A\u70BA\u6703\u54E1","subtitle.quickButton.liveOnlyPro":"\u76F4\u64AD\u5B57\u5E55\u7FFB\u8B6F\u529F\u80FD\u50C5\u6703\u54E1\u53EF\u7528","subtitle.liveFreeTrialTip":"\u7FFB\u8B6F\u76F4\u64AD\u5B57\u5E55\u662F\u6703\u54E1\u5C08\u5C6C\u529F\u80FD\uFF0C\u60A8\u53EF\u514D\u8CBB\u9AD4\u9A57{free_trial_minutes} \u5206\u9418","subtitle.liveFreeTrialEndTip":"\u76F4\u64AD\u8996\u8A0A\u5B57\u5E55\u7FFB\u8B6F\u8A66\u7528\u5DF2\u7D50\u675F\uFF0C\u958B\u901A\u6703\u54E1\u53EF\u7E7C\u7E8C\u4F7F\u7528","subtitle.meetingAutoEnableSubtitle":"\u6703\u8B70\u5E73\u53F0\u81EA\u52D5\u958B\u555F\u96D9\u8A9E\u5B57\u5E55","subtitle.meetingAutoEnableSubtitleDescription":"\u958B\u555F\u5F8C\uFF0CTeams\u3001Zoom\u3001Google Meet \u6703\u8B70\u5E73\u53F0\u6703\u81EA\u52D5\u958B\u555F\u96D9\u8A9E\u5B57\u5E55","translationServices.qwen-mt":"Qwen-MT","labelKey.domains":"domains \u9818\u57DF\u63D0\u793A","description.qwenMtDomains":"\u5982\u679C\u60A8\u5E0C\u671B\u7FFB\u8B6F\u7684\u98A8\u683C\u66F4\u7B26\u5408\u67D0\u500B\u9818\u57DF\u7684\u7279\u6027\uFF0C\u5982\u6CD5\u5F8B\u3001\u653F\u52D9\u9818\u57DF\u7FFB\u8B6F\u7528\u8A9E\u61C9\u7576\u56B4\u8085\u6B63\u5F0F\uFF0C\u793E\u4EA4\u9818\u57DF\u7528\u8A9E\u61C9\u7576\u53E3\u8A9E\u5316\uFF0C\u53EF\u4EE5\u7528\u4E00\u6BB5\u81EA\u7136\u8A9E\u8A00\u6587\u5B57\u63CF\u8FF0\u60A8\u7684\u9818\u57DF\uFF0C\u5C07\u5176\u63D0\u4F9B\u7D66\u5927\u6A21\u578B\u4F5C\u70BA\u63D0\u793A\u3002\u9818\u57DF\u63D0\u793A\u8A9E\u53E5\u66AB\u6642\u53EA\u652F\u63F4\u82F1\u6587\u3002"};var s0={nologin:"Not logged in",loginForSafari:"Sign In or Sign Up",login:"Log In",goLogin:"Sign In",manageAccount:"Manage Account",openPremium:"Upgrade to Pro",logout:"Logout",lineBreakMaxTextCount:"Maximum characters per sentence after line break","translate-pdf":"Translate PDF","noSupportTranslate-pdf":/* MULTIPLE_REMOVED_BLOCKS */e.requestAiSubtitleMaxDurationLimitTip":"AI subtitle generation is not supported if the current video is longer than {maxDurationHours}.",disableOpenUpgradePage:"Disable major version upgrade notification","field.enableSingleTranslate":"Enable single translation","description.enableSingleTranslate":"When enabled, translation will stop immediately when the URL changes","field.enableSiteAutoTranslate":"Enable site auto-translation","description.enableSiteAutoTranslate":"When enabled, after translating the current page, navigation to other pages within the same site will also auto-translate",sidePanelTooltip:"Toggle Side Panel","error.maxQuotaError":"Insufficient Max translation quota",disableRewardCenter:"Hide reward center button",iKnow:"I Know",notShowAgain:"No more reminders","error.usageTips":"Immersive Translate usage tips","subtitle.filterAmbientSounds":"AI subtitles focus on dialogue content","error.maxQuotaUsageTips":"Your usage of the <span style='color: #FF7D00;'>Top Model</span> ({maxAIQuota} token limit) has exceeded <span style='color: #FF7D00;'>{maxAIUsed}</span> this month. <1>View usage</1>","subtitle.filterAmbientSoundsDescription":"After enabling this option, the subtitles will primarily display the spoken content of the speaker. Environmental sound annotations in the recognition results will be filtered out as much as possible, such as: (snoring), (thunder).","reportInfo.emailPlaceholder":"Contact email (required)","subtitle.quickButton.upgradePro":"Upgrade to membership","subtitle.quickButton.liveOnlyPro":"Live subtitle translation feature is only available to members.","subtitle.liveFreeTrialTip":"Live subtitle translation is a member-only feature. You can try it for free for {free_trial_minutes} minutes.","subtitle.liveFreeTrialEndTip":"The trial period for live video subtitle translation has ended. Please subscribe to continue using this service.","subtitle.meetingAutoEnableSubtitleDescription":"Once enabled, Teams, Zoom, and Google Meet meeting platforms will automatically enable bilingual subtitles.","subtitle.meetingAutoEnableSubtitle":"The conference platform automatically activates bilingual subtitles.","translationServices.qwen-mt":"Qwen-MT Model","labelKey.domains":"domains field prompt","description.qwenMtDomains":"If you want the translation style to better match the characteristics of a specific field, such as legal and government domain translations should be serious and formal, while social domain language should be colloquial, you can use natural language text to describe your domain and provide it to the large model as a prompt. Domain prompt statements currently only support English."};var uy=[{code:"zh-CN",messages:i0},{code:"zh-TW",messages:o0},{code:"en",messages:s0}],u0=pe(!1,!0)?["zh-CN","zh-TW","en"]:["zh-CN","zh-TW","en","ja","ar","de","es","fa","fr","he","hi","hu","it","ru","ko","pt-PT","pt-BR","tr"],vn={};for(let e of uy)vn[e.code]=e.messages;function ly(e,t){let a=e;return t&&Object.keys(t).forEach(n=>{let r=t[n];if(r===void 0)return;let i=dy(n);if(typeof r=="object"||i){let o=r;i&&typeof o=="string"&&(o={tag:"a",href:o,target:"_blank",class:Ae+"-link"});let s=`<${n}>`,u=a.indexOf(s);if(u!==-1){let c=o.tag||"a",l=a.indexOf(`</${n}>`);if(l!==-1){let d=a.substring(u+s.length,l),m=Object.keys(o).filter(p=>p!=="tag").map(p=>`${p}="${o[p]}"`).join(" ");a=a.replace(`${s}${d}</${n}>`,`<${c} ${m}>${d}</${c}>`)}}}else if(r){let o=new RegExp("{"+n+"}","gm");a=a.replace(o,r.toString())}}),a}function l0(e,t,a){let n=e[t];if(!n)return a;if(!a)return"";let r=a.split("."),i="";do{i+=r.shift();let o=n[i];o!==void 0&&(typeof o=="object"||!r.length)?(n=o,i=""):r.length?i+=".":n=a}while(r.length);return n}function cy(e,t,a,n){if(!vn.hasOwnProperty(t)&&!vn.hasOwnProperty(a))return e;let r=l0(vn,t,e);return r===e&&t!==a&&(r=l0(vn,a,e)),ly(r,n)}function dy(e){if(typeof e=="number")return!0;if(e){let t=parseInt(e);return!isNaN(t)}else return!1}function Ye(e,t,a){return cy(t,e,"en",a)}function c0(e,t){let a=new Date(e),n=a.getFullYear().toString(),r=(a.getMonth()+1).toString().padStart(2,"0"),i=a.getDate().toString().padStart(2,"0"),o=a.getHours().toString().padStart(2,"0"),s=a.getMinutes().toString().padStart(2,"0"),u=a.getSeconds().toString().padStart(2,"0");return t.replace("YYYY",n).replace("MM",r).replace("DD",i).replace("HH",o).replace("mm",s).replace("ss",u)}function d0(e){return new Date(e).getTime()}var Dt=class extends Error{status;errorUIConfig;serviceName;serviceId="";data;constructor(t,a){if(t&&a){super(a),this.name=t;return}super(t)}initNetWork(t){return t&&(this.status=t),this}initStack(t){return t&&(this.stack=t),this}initData(t){return this.data=t,this}uiConfig(t){if(this.errorUIConfig)return this.errorUIConfig;if(!this.message)return{};let a=Ye.bind(null,t.config.interfaceLanguage);this.serviceId=this.data?.translationService||t.translationService,this.serviceName=Nd(t.config,this.serviceId);let n=null;if(n=this.handleServiceDiscontinued(t)||this.handleContextInvalidatedError(t)||this.handleMangaError(t)||this.handleProQuota(t)||this.handleUnavailableError(t)||this.handleProUser(t)||this.handleServiceMissingConfig(t)||this.handleNetwork(t)||this.handleFetchError(t),!n){let r=this.getErrorMsg(t);n={type:"error",title:`[${this.serviceName}] `+a("networkError"),errMsg:a("error.serveUnavailable",{serverName:this.serviceName,1:Nr,2:P+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error",3:P+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error"})+"<br/><br/>"+a("errorReason",{message:r}),action:"changeService"}}return n.translationService=this.serviceName,n}getErrorMsg(t){return this.status?this.status<0?this.message=="Failed to fetch"?Ye.bind(null,t.config.interfaceLanguage)("error.failToFetch"):this.message:`${this.status}: ${this.message}`:this.message}handleUnavailableError(t){let a=Ye.bind(null,t.config.interfaceLanguage),n=this.message.startsWith("bingAuth"),r=this.data?.translationService==="transmart"&&this.message.startsWith("Server is busy now");if(n||r)return this.message=this.message.replace("bingAuth:",""),{type:"network",title:`[${this.serviceName}] `+a("networkError"),errMsg:a("error.serveUnavailable",{serverName:this.serviceName,1:Nr,2:P+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error",3:P+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error"})+"<br/><br/>"+a("errorReason",{message:this.message}),action:"changeService"}}handleServiceMissingConfig(t){let a=Ye.bind(null,t.config.interfaceLanguage);if(this.message.endsWith(" are required")||this.message.includes("You didn't provide an API key"))return{type:"configError",title:a("error.serveConfigError"),errMsg:this.getErrorMsg(t)+"<br /><br />"+a("error.reloadPageOfSetting"),action:"setting"}}handleNetwork(t){let a=Ye.bind(null,t.config.interfaceLanguage),n="retry",r="network",i=`[${this.serviceName}] `+a("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${a("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${a("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${a("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${a("error.gemini.429")}<br/><br/> ${o}`:o=`${a("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${a("error.claude.403")}<br/><br/>${o}`:o=`${a("error.403")}<br/><br/>${o}`:this.status===400?o=`${a("error.400")}<br/><br/> ${o}`:this.status===502?o=`${a("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${a("error.subscriptionExpired")}<br/><br/> ${o}`,n="setting",r="configError",i=a("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${a("error.azure.401")}<br/><br/> ${o}`),{type:r,title:i,errMsg:o,action:n}}handleFetchError(t){let a=Ye.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let n=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] `+a("networkError"),errMsg:a("error.serveUnavailable",{serverName:this.serviceName,1:Nr,2:P+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error",3:P+"pricing/?utm_source=extension&utm_medium=webpage&utm_campaign=service_error"})+"<br/><br/>"+a("errorReason",{message:n}),action:"changeService"}}handleProUser(t){let a=Ye.bind(null,t.config.interfaceLanguage);if(as(t.config,this.serviceId)){if(this.message.indexOf("token invalid")>=0||this.message.indexOf("Login required")>=0)return{type:"notLogin",title:a("notLoginPro"),errMsg:a("error.proTokenInvalid"),action:"login"};if(this.message.indexOf("activate Pro")>=0)return t.user?{type:"upgrade",title:a("upgradeToProErrorTitle",{service:this.serviceName}),errMsg:a("error.proUpgrade"),action:"upgrade"}:{type:"notLogin",title:a("notLoginPro"),errMsg:a("error.proTokenInvalid"),action:"login"};if(this.message.indexOf("subscription not found")>=0)return{type:"subscriptionExpires",title:a("error.subscriptionExpiredTitle"),errMsg:a("error.subscriptionExpired"),action:"upgrade"};if(t.config.translationServices?.[this.serviceId]?.provider=="pro"){let n=this.getErrorMsg(t);return{type:"network",title:"",errMsg:a("error.serveProUnavailable",{serverName:this.serviceName})+"<br/><br/>"+a("errorReason",{message:n}),action:"changeProService"}}}}handleMangaError(t){if(this.name!="manga")return;let a=Ye.bind(null,t.config.interfaceLanguage);if(this.message.includes("Comics quota exceeded")){let r=[a("mangaQuotaError.package",{1:hl})];m0(t.user,r,a);let i=a("errorReason",{message:`
        ${a("mangaQuotaError.solvedTitle")}<br/></br>
        ${r.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        `});return{type:"ProQuotaExceeded",title:a("intro.mangaImageTitle"),errMsg:i,action:"none"}}if(this.message.includes("ProQuota")){let r=this.getNewProQuotaError(t,this.message);return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:r,action:"none"}}if(this.message.includes("Image too large"))return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:a("error.imageTooLarge"),action:"none"};if(this.message.includes("Tainted canvases may not be exported"))return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:a("error.siteDisableManga"),action:"none"};let n=a("errorReason",{message:this.message});return{type:"networkError",title:a("intro.mangaImageTitle"),errMsg:n,action:"none"}}handleProQuota(t){if(!this.message.includes("ProQuota:")||!t.user?.subscription)return;this.message.includes("NewProQuota")&&(this.message=this.message.replace("NewProQuota:",""));let a=this.message;try{a=JSON.parse(this.message).error}catch{}let{errMsg:n,title:r}=this.getNewProQuotaError(t,a);return{type:"ProQuotaExceeded",title:r,errMsg:n,action:"changeProService"}}handleContextInvalidatedError(t){return this.name!=="contextInvalidated"?void 0:{type:"ContextInvalidated",title:"",errMsg:Ye.bind(null,t.config.interfaceLanguage)("ctxInvalidatedError"),action:"refreshPage"}}getNewProQuotaError(t,a){let n=Ye.bind(null,t.config.interfaceLanguage),r=t.user.subscription,{isTrial:i,memberShip:o}=r,s="",u=[],c="";c=$i;let l=/Max translation quota/ig.test(a);l&&(c=$i+"&type=top_model",s=n("error.maxQuotaError"));let d=o=="pro"&&l;d&&u.push(n("translationServices.upgradeMaxUser",{1:fl+"error_modal"})),u.push(n("proQuotaError.newPackage",{1:c})),i&&!d&&u.push(n("proQuotaError.trail",{1:bl})),!i&&!d&&m0(t.user,u,n);let m=a+`${n("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((p,x)=>`${x+1}. ${p}`).join("<br/>")}`;return{title:s,errMsg:n("errorReason",{message:m})}}handleServiceDiscontinued(t){let a=Ye.bind(null,t.config.interfaceLanguage);if(this.name==="translationServiceDiscontinued")return{type:"network",title:a("error.serviceDiscontinued"),errMsg:a("error.serviceDiscontinuedMessage",{translationService:this.serviceName}),action:"changeService"}}};function m0(e,t,a){if(!e||!e?.subscription)return;let n=e?.subscription,{isTrial:r,openAITokenUsedCountResetTime:i,subscriptionTo:o}=n;!r&&(n.cancelAtPeriodEnd==="false"||d0(o)>i)&&t.push(a("proQuotaError.resetTime",{resetTime:c0(i,"YYYY-MM-DD HH:mm:ss")}))}var bs=class extends Error{constructor(t,a){super(`Exceeded max retry count (${a})`),this.name="RetryError",this.cause=t}},my={multiplier:2,maxTimeout:6e4,maxAttempts:5,minTimeout:1e3};async function p0(e,t){let a={...my,...t};if(a.maxTimeout>=0&&a.minTimeout>a.maxTimeout)throw new RangeError("minTimeout is greater than maxTimeout");let n=a.minTimeout,r;for(let i=0;i<a.maxAttempts;i++)try{return await e()}catch(o){if(o.message.includes("Request timeout")||o.message.includes("User subscription not found"))throw o;await new Promise(s=>setTimeout(s,n)),n*=a.multiplier,n=Math.max(n,a.minTimeout),a.maxTimeout>=0&&(n=Math.min(n,a.maxTimeout)),r=o}throw new bs(r,a.maxAttempts)}function g0(e){if(!e)return e;let t=new FormData;return Object.entries(e).forEach(([a,n])=>{if(a.startsWith("base64_")){let r=ys(n);t.append(`${a.split("base64_")[1]}`,r)}else t.append(a,n)}),t}function h0(e){if(!e)return e;let t=new URLSearchParams;return Object.entries(e).forEach(([a,n])=>{if(Array.isArray(n))for(let r of n)t.append(a,r);else t.append(a,n)}),t.toString()}function ys(e){let{mimeType:t,base64:a}=py(e),n=atob(a),r=[],i=512;for(let s=0;s<n.length;s+=i){let u=n.slice(s,s+i),c=new Array(u.length);for(let d=0;d<u.length;d++)c[d]=u.charCodeAt(d);let l=new Uint8Array(c);r.push(l)}return new Blob(r,{type:t})}function py(e){let t=/^data:(.+?);base64,(.*)$/,a=e.match(t),n="",r="";return a&&a.length===3&&(n=a[1],r=a[2]),{mimeType:n,base64:r}}var f0=Object.prototype.toString;function fi(e){switch(f0.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ot(e,Error)}}function sr(e,t){return f0.call(e)===`[object ${t}]`}function bi(e){return sr(e,"ErrorEvent")}function b0(e){return sr(e,"DOMException")}function ra(e){return sr(e,"String")}function ur(e){return e===null||typeof e!="object"&&typeof e!="function"}function It(e){return sr(e,"Object")}function lr(e){return typeof Event<"u"&&Ot(e,Event)}function y0(e){return!1}function v0(e){return sr(e,"RegExp")}function xn(e){return!!(e&&e.then&&typeof e.then=="function")}function x0(e){return It(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function w0(e){return typeof e=="number"&&e!==e}function Ot(e,t){try{return e instanceof t}catch{return!1}}function cr(e,t){try{let a=e,n=5,r=80,i=[],o=0,s=0,u=" > ",c=u.length,l;for(;a&&o++<n&&(l=gy(a,t),!(l==="html"||o>1&&s+i.length*c+l.length>=r));)i.push(l),s+=l.length,a=a.parentNode;return i.reverse().join(u)}catch{return"<unknown>"}}function gy(e,t){let a=e,n=[],r,i,o,s,u;if(!a||!a.tagName)return"";n.push(a.tagName.toLowerCase());let c=t&&t.length?t.filter(d=>a.getAttribute(d)).map(d=>[d,a.getAttribute(d)]):null;if(c&&c.length)c.forEach(d=>{n.push(`[${d[0]}="${d[1]}"]`)});else if(a.id&&n.push(`#${a.id}`),r=a.className,r&&ra(r))for(i=r.split(/\s+/),u=0;u<i.length;u++)n.push(`.${i[u]}`);let l=["type","name","title","alt"];for(u=0;u<l.length;u++)o=l[u],s=a.getAttribute(o),s&&n.push(`[${o}="${s}"]`);return n.join("")}function A0(){try{return location.href}catch{return""}}var _e=class extends Error{constructor(a){super(a);this.message=a;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var hy=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function fy(e){return e==="http"||e==="https"}function wn(e,t=!1){let{host:a,path:n,pass:r,port:i,projectId:o,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&r?`:${r}`:""}@${a}${i?`:${i}`:""}/${n&&`${n}/`}${o}`}function by(e){let t=hy.exec(e);if(!t)throw new _e(`Invalid Sentry Dsn: ${e}`);let[a,n,r="",i,o="",s]=t.slice(1),u="",c=s,l=c.split("/");if(l.length>1&&(u=l.slice(0,-1).join("/"),c=l.pop()),c){let d=c.match(/^\d+/);d&&(c=d[0])}return E0({host:i,pass:r,path:u,projectId:c,port:o,protocol:a,publicKey:n})}function E0(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function yy(e){if(!!1)return;let{port:t,projectId:a,protocol:n}=e;if(["protocol","publicKey","host","projectId"].forEach(i=>{if(!e[i])throw new _e(`Invalid Sentry Dsn: ${String(i)} missing`)}),!a.match(/^\d+$/))throw new _e(`Invalid Sentry Dsn: Invalid projectId ${a}`);if(!fy(n))throw new _e(`Invalid Sentry Dsn: Invalid protocol ${n}`);if(t&&isNaN(parseInt(t,10)))throw new _e(`Invalid Sentry Dsn: Invalid port ${t}`);return!0}function vs(e){let t=typeof e=="string"?by(e):E0(e);return yy(t),t}var vy={};function De(){return typeof window<"u"?window:typeof self<"u"?self:vy}function An(e,t,a){let n=a||De(),r=n.__SENTRY__=n.__SENTRY__||{};return r[e]||(r[e]=t())}var dr=["debug","info","warn","error","log","assert","trace"];function xs(e){if(!("console"in De()))return e();let a=console,n={};dr.forEach(r=>{let i=a[r]&&a[r].__sentry_original__;r in console&&i&&(n[r]=a[r],a[r]=i)});try{return e()}finally{Object.keys(n).forEach(r=>{a[r]=n[r]})}}function S0(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1}};return!1?dr.forEach(a=>{t[a]=(...n)=>{e&&xs(()=>{})}}):dr.forEach(a=>{t[a]=()=>{}}),t}var te;!1?te=An("logger",S0):te=S0();function La(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.substr(0,t)}...`}function ws(e,t){if(!Array.isArray(e))return"";let a=[];for(let n=0;n<e.length;n++){let r=e[n];try{a.push(String(r))}catch{a.push("[value cannot be serialized]")}}return a.join(t)}function yi(e,t){return ra(e)?v0(t)?t.test(e):typeof t=="string"?e.indexOf(t)!==-1:!1:!1}function Pt(e,t,a){if(!(t in e))return;let n=e[t],r=a(n);if(typeof r=="function")try{Es(r,n)}catch{}e[t]=r}function mr(e,t,a){Object.defineProperty(e,t,{value:a,writable:!0,configurable:!0})}function Es(e,t){let a=t.prototype||{};e.prototype=t.prototype=a,mr(e,"__sentry_original__",t)}function En(e){return e.__sentry_original__}function D0(e){return Object.keys(e).map(t=>`${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`).join("&")}function Ss(e){if(fi(e))return{message:e.message,name:e.name,stack:e.stack,...T0(e)};if(lr(e)){let t={type:e.type,target:k0(e.target),currentTarget:k0(e.currentTarget),...T0(e)};return typeof CustomEvent<"u"&&Ot(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function k0(e){try{return y0(e)?cr(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function T0(e){if(typeof e=="object"&&e!==null){let t={};for(let a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}else return{}}function C0(e,t=40){let a=Object.keys(Ss(e));if(a.sort(),!a.length)return"[object has no keys]";if(a[0].length>=t)return La(a[0],t);for(let n=a.length;n>0;n--){let r=a.slice(0,n).join(", ");if(!(r.length>t))return n===a.length?r:La(r,t)}return""}function Na(e){return As(e,new Map)}function As(e,t){if(It(e)){let a=t.get(e);if(a!==void 0)return a;let n={};t.set(e,n);for(let r of Object.keys(e))typeof e[r]<"u"&&(n[r]=As(e[r],t));return n}if(Array.isArray(e)){let a=t.get(e);if(a!==void 0)return a;let n=[];return t.set(e,n),e.forEach(r=>{n.push(As(r,t))}),n}return e}var xy=50;function Ts(...e){let t=e.sort((a,n)=>a[0]-n[0]).map(a=>a[1]);return(a,n=0)=>{let r=[];for(let i of a.split(`
`).slice(n))for(let o of t){let s=o(i);if(s){r.push(s);break}}return wy(r)}}function M0(e){return Array.isArray(e)?Ts(...e):e}function wy(e){if(!e.length)return[];let t=e,a=t[0].function||"",n=t[t.length-1].function||"";return(a.indexOf("captureMessage")!==-1||a.indexOf("captureException")!==-1)&&(t=t.slice(1)),n.indexOf("sentryWrapped")!==-1&&(t=t.slice(0,-1)),t.slice(0,xy).map(r=>({...r,filename:r.filename||t[0].filename,function:r.function||"?"})).reverse()}var ks="<anonymous>";function Ft(e){try{return!e||typeof e!="function"?ks:e.name||ks}catch{return ks}}function I0(){if(!("fetch"in De()))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function P0(e){return e&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function F0(){return!0}var ia=De(),pr={},B0={};function Ay(e){if(!B0[e])switch(B0[e]=!0,e){case"console":Ey();break;case"fetch":Sy();break;case"error":Dy();break;case"unhandledrejection":Cy();break;default:!1&&te.warn("unknown instrumentation type:",e);return}}function Bt(e,t){pr[e]=pr[e]||[],pr[e].push(t),Ay(e)}function Sn(e,t){if(!(!e||!pr[e]))for(let a of pr[e]||[])try{a(t)}catch(n){!1&&te.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${Ft(a)}
Error:`,n)}}function Ey(){"console"in ia&&dr.forEach(function(e){e in console&&Pt(console,e,function(t){return function(...a){Sn("console",{args:a,level:e}),t&&t.apply(console,a)}})})}function Sy(){F0()&&Pt(ia,"fetch",function(e){return function(...t){let a={args:t,fetchData:{method:ky(t),url:Ty(t)},startTimestamp:Date.now()};return Sn("fetch",{...a}),e.apply(ia,t).then(n=>(Sn("fetch",{...a,endTimestamp:Date.now(),response:n}),n),n=>{throw Sn("fetch",{...a,endTimestamp:Date.now(),error:n}),n})}})}function ky(e=[]){return"Request"in ia&&Ot(e[0],Request)&&e[0].method?String(e[0].method).toUpperCase():e[1]&&e[1].method?String(e[1].method).toUpperCase():"GET"}function Ty(e=[]){return typeof e[0]=="string"?e[0]:"Request"in ia&&Ot(e[0],Request)?e[0].url:String(e[0])}var Ds=null;function Dy(){Ds=ia.onerror,ia.onerror=function(e,t,a,n,r){return Sn("error",{column:n,error:r,line:a,msg:e,url:t}),Ds?Ds.apply(this,arguments):!1}}var Cs=null;function Cy(){Cs=ia.onunhandledrejection,ia.onunhandledrejection=function(e){return Sn("unhandledrejection",e),Cs?Cs.apply(this,arguments):!0}}function R0(){let e=typeof WeakSet=="function",t=e?new WeakSet:[];function a(r){if(e)return t.has(r)?!0:(t.add(r),!1);for(let i=0;i<t.length;i++)if(t[i]===r)return!0;return t.push(r),!1}function n(r){if(e)t.delete(r);else for(let i=0;i<t.length;i++)if(t[i]===r){t.splice(i,1);break}}return[a,n]}function oa(){if(crypto&&crypto.randomUUID)return crypto.randomUUID().replace(/-/g,"");let e=crypto&&crypto.getRandomValues?()=>crypto.getRandomValues(new Uint8Array(1))[0]:()=>Math.random()*16;return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,t=>(t^(e()&15)>>t/4).toString(16))}function vi(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let a=t[6]||"",n=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],relative:t[5]+a+n}}function _0(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function sa(e){let{message:t,event_id:a}=e;if(t)return t;let n=_0(e);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||a||"<unknown>":a||"<unknown>"}function gr(e,t,a){let n=e.exception=e.exception||{},r=n.values=n.values||[],i=r[0]=r[0]||{};i.value||(i.value=t||""),i.type||(i.type=a||"Error")}function va(e,t){let a=_0(e);if(!a)return;let n={type:"generic",handled:!0},r=a.mechanism;if(a.mechanism={...n,...r,...t},t&&"data"in t){let i={...r&&r.data,...t.data};a.mechanism.data=i}}function Ms(e){if(e&&e.__sentry_captured__)return!0;try{mr(e,"__sentry_captured__",!0)}catch{}return!1}function j0(){return!1}function ua(e,t=1/0,a=1/0){try{return Is("",e,t,a)}catch(n){return{ERROR:`**non-serializable** (${n})`}}}function Ps(e,t=3,a=100*1024){let n=ua(e,t);return Py(n)>a?Ps(e,t-1,a):n}function Is(e,t,a=1/0,n=1/0,r=R0()){let[i,o]=r;if(t===null||["number","boolean","string"].includes(typeof t)&&!w0(t))return t;let s=My(e,t);if(!s.startsWith("[object "))return s;if(t.__sentry_skip_normalization__)return t;if(a===0)return s.replace("object ","");if(i(t))return"[Circular ~]";let u=t;if(u&&typeof u.toJSON=="function")try{let m=u.toJSON();return Is("",m,a-1,n,r)}catch{}let c=Array.isArray(t)?[]:{},l=0,d=Ss(t);for(let m in d){if(!Object.prototype.hasOwnProperty.call(d,m))continue;if(l>=n){c[m]="[MaxProperties ~]";break}let p=d[m];c[m]=Is(m,p,a-1,n,r),l+=1}return o(t),c}function My(e,t){try{return e==="domain"&&t&&typeof t=="object"&&t._events?"[Domain]":e==="domainEmitter"?"[DomainEmitter]":typeof window<"u"&&t===window?"[Window]":x0(t)?"[SyntheticEvent]":typeof t=="number"&&t!==t?"[NaN]":t===void 0?"[undefined]":typeof t=="function"?`[Function: ${Ft(t)}]`:typeof t=="symbol"?`[${String(t)}]`:typeof t=="bigint"?`[BigInt: ${String(t)}]`:`[object ${Object.getPrototypeOf(t).constructor.name}]`}catch(a){return`**non-serializable** (${a})`}}function Iy(e){return~-encodeURI(e).split(/%..|./).length}function Py(e){return Iy(JSON.stringify(e))}function mt(e){return new zt(t=>{t(e)})}function hr(e){return new zt((t,a)=>{a(e)})}var zt=class e{_state=0;_handlers=[];_value;constructor(t){try{t(this._resolve,this._reject)}catch(a){this._reject(a)}}then(t,a){return new e((n,r)=>{this._handlers.push([!1,i=>{if(!t)n(i);else try{n(t(i))}catch(o){r(o)}},i=>{if(!a)r(i);else try{n(a(i))}catch(o){r(o)}}]),this._executeHandlers()})}catch(t){return this.then(a=>a,t)}finally(t){return new e((a,n)=>{let r,i;return this.then(o=>{i=!1,r=o,t&&t()},o=>{i=!0,r=o,t&&t()}).then(()=>{if(i){n(r);return}a(r)})})}_resolve=t=>{this._setResult(1,t)};_reject=t=>{this._setResult(2,t)};_setResult=(t,a)=>{if(this._state===0){if(xn(a)){a.then(this._resolve,this._reject);return}this._state=t,this._value=a,this._executeHandlers()}};_executeHandlers=()=>{if(this._state===0)return;let t=this._handlers.slice();this._handlers=[],t.forEach(a=>{a[0]||(this._state===1&&a[1](this._value),this._state===2&&a[2](this._value),a[0]=!0)})}};function L0(e){let t=[];function a(){return e===void 0||t.length<e}function n(o){return t.splice(t.indexOf(o),1)[0]}function r(o){if(!a())return hr(new _e("Not adding Promise due to buffer limit reached."));let s=o();return t.indexOf(s)===-1&&t.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s}function i(o){return new zt((s,u)=>{let c=t.length;if(!c)return s(!0);let l=setTimeout(()=>{o&&o>0&&s(!1)},o);t.forEach(d=>{mt(d).then(()=>{--c||(clearTimeout(l),s(!0))},u)})})}return{$:t,add:r,drain:i}}var Fy=["fatal","error","warning","log","info","debug"];function N0(e){return e==="warn"?"warning":Fy.includes(e)?e:"log"}var Bs={nowSeconds:()=>Date.now()/1e3};function By(){if(!performance||!performance.now)return;let e=Date.now()-performance.now();return{now:()=>performance.now(),timeOrigin:e}}var Fs=By(),O0=Fs===void 0?Bs:{nowSeconds:()=>(Fs.timeOrigin+Fs.now())/1e3},xa=Bs.nowSeconds.bind(Bs),Rs=O0.nowSeconds.bind(O0);var xi,CI=(()=>{if(!performance||!performance.now){xi="none";return}let e=3600*1e3,t=performance.now(),a=Date.now(),n=performance.timeOrigin?Math.abs(performance.timeOrigin+t-a):e,r=n<e,i=performance.timeOrigin,s=typeof i=="number"?Math.abs(i+t-a):e,u=s<e;return r||u?n<=s?(xi="timeOrigin",performance.timeOrigin):(xi="navigationStart",i):(xi="dateNow",a)})();var PI=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Oa(e,t=[]){return[e,t]}function z0(e,t){let[a,n]=e;return[a,[...n,t]]}function js(e,t){e[1].forEach(n=>{let r=n[0].type;t(n,r)})}function _s(e,t){return(t||new TextEncoder).encode(e)}function wi(e,t){let[a,n]=e,r=JSON.stringify(a);function i(o){typeof r=="string"?r=typeof o=="string"?r+o:[_s(r,t),o]:r.push(typeof o=="string"?_s(o,t):o)}for(let o of n){let[s,u]=o;i(`
${JSON.stringify(s)}
`),i(typeof u=="string"||u instanceof Uint8Array?u:JSON.stringify(u))}return typeof r=="string"?r:Ry(r)}function Ry(e){let t=e.reduce((r,i)=>r+i.length,0),a=new Uint8Array(t),n=0;for(let r of e)a.set(r,n),n+=r.length;return a}function U0(e,t){let a=typeof e.data=="string"?_s(e.data,t):e.data;return[Na({type:"attachment",length:a.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),a]}var _y={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default"};function Ls(e){return _y[e]}function q0(e,t,a){let n=[{type:"client_report"},{timestamp:a||xa(),discarded_events:e}];return Oa(t?{dsn:t}:{},[n])}var jy=60*1e3;function Ly(e,t=Date.now()){let a=parseInt(`${e}`,10);if(!isNaN(a))return a*1e3;let n=Date.parse(`${e}`);return isNaN(n)?jy:n-t}function Ny(e,t){return e[t]||e.all||0}function G0(e,t,a=Date.now()){return Ny(e,t)>a}function H0(e,{statusCode:t,headers:a},n=Date.now()){let r={...e},i=a&&a["x-sentry-rate-limits"],o=a&&a["retry-after"];if(i)for(let s of i.trim().split(",")){let[u,c]=s.split(":",2),l=parseInt(u,10),d=(isNaN(l)?60:l)*1e3;if(!c)r.all=n+d;else for(let m of c.split(";"))r[m]=n+d}else o?r.all=n+Ly(o,n):t===429&&(r.all=n+60*1e3);return r}function K0(e){return e[0]}function Ns(e){let t=Rs(),a={sid:oa(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>Oy(a)};return e&&Ut(a,e),a}function Ut(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||Rs(),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:oa()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{let a=e.timestamp-e.started;e.duration=a>=0?a:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function Os(e,t){let a={};t?a={status:t}:e.status==="ok"&&(a={status:"exited"}),Ut(e,a)}function Oy(e){return Na({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}var W0=100,Rt=class e{_notifyingListeners;_scopeListeners;_eventProcessors;_breadcrumbs;_user;_tags;_extra;_contexts;_attachments;_sdkProcessingMetadata;_fingerprint;_level;_transactionName;_span;_session;_requestSession;constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={}}static clone(t){let a=new e;return t&&(a._breadcrumbs=[...t._breadcrumbs],a._tags={...t._tags},a._extra={...t._extra},a._contexts={...t._contexts},a._user=t._user,a._level=t._level,a._span=t._span,a._session=t._session,a._transactionName=t._transactionName,a._fingerprint=t._fingerprint,a._eventProcessors=[...t._eventProcessors],a._requestSession=t._requestSession,a._attachments=[...t._attachments]),a}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{},this._session&&Ut(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,a){return this._tags={...this._tags,[t]:a},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,a){return this._extra={...this._extra,[t]:a},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,a){return a===null?delete this._contexts[t]:this._contexts={...this._contexts,[t]:a},this._notifyScopeListeners(),this}setSpan(t){return this._span=t,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){let t=this.getSpan();return t&&t.transaction}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;if(typeof t=="function"){let a=t(this);return a instanceof e?a:this}return t instanceof e?(this._tags={...this._tags,...t._tags},this._extra={...this._extra,...t._extra},this._contexts={...this._contexts,...t._contexts},t._user&&Object.keys(t._user).length&&(this._user=t._user),t._level&&(this._level=t._level),t._fingerprint&&(this._fingerprint=t._fingerprint),t._requestSession&&(this._requestSession=t._requestSession)):It(t)&&(t=t,this._tags={...this._tags,...t.tags},this._extra={...this._extra,...t.extra},this._contexts={...this._contexts,...t.contexts},t.user&&(this._user=t.user),t.level&&(this._level=t.level),t.fingerprint&&(this._fingerprint=t.fingerprint),t.requestSession&&(this._requestSession=t.requestSession)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this}addBreadcrumb(t,a){let n=typeof a=="number"?Math.min(a,W0):W0;if(n<=0)return this;let r={timestamp:xa(),...t};return this._breadcrumbs=[...this._breadcrumbs,r].slice(-n),this._notifyScopeListeners(),this}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(t,a={}){if(this._extra&&Object.keys(this._extra).length&&(t.extra={...this._extra,...t.extra}),this._tags&&Object.keys(this._tags).length&&(t.tags={...this._tags,...t.tags}),this._user&&Object.keys(this._user).length&&(t.user={...this._user,...t.user}),this._contexts&&Object.keys(this._contexts).length&&(t.contexts={...this._contexts,...t.contexts}),this._level&&(t.level=this._level),this._transactionName&&(t.transaction=this._transactionName),this._span){t.contexts={trace:this._span.getTraceContext(),...t.contexts};let n=this._span.transaction&&this._span.transaction.name;n&&(t.tags={transaction:n,...t.tags})}return this._applyFingerprint(t),t.breadcrumbs=[...t.breadcrumbs||[],...this._breadcrumbs],t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...this._sdkProcessingMetadata},this._notifyEventProcessors([...Q0(),...this._eventProcessors],t,a)}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}_notifyEventProcessors(t,a,n,r=0){return new zt((i,o)=>{let s=t[r];if(a===null||typeof s!="function")i(a);else{let u=s({...a},n);!1&&s.id&&u===null&&te.log(`Event processor "${s.id}" dropped event`),xn(u)?u.then(c=>this._notifyEventProcessors(t,c,n,r+1).then(i)).then(null,o):this._notifyEventProcessors(t,u,n,r+1).then(i).then(null,o)}})}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}_applyFingerprint(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this._fingerprint&&(t.fingerprint=t.fingerprint.concat(this._fingerprint)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}};function Q0(){return An("globalEventProcessors",()=>[])}function la(e){Q0().push(e)}var zs=4,zy=100,wa=class{constructor(t,a=new Rt,n=zs){this._version=n;this.getStackTop().scope=a,t&&this.bindClient(t)}_stack=[{}];_lastEventId;isOlderThan(t){return this._version<t}bindClient(t){let a=this.getStackTop();a.client=t,t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){let t=Rt.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return this.getStack().length<=1?!1:!!this.getStack().pop()}withScope(t){let a=this.pushScope();try{t(a)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(t,a){let n=this._lastEventId=a&&a.event_id?a.event_id:oa(),r=new Error("Sentry syntheticException");return this._withClient((i,o)=>{i.captureException(t,{originalException:t,syntheticException:r,...a,event_id:n},o)}),n}captureMessage(t,a,n){let r=this._lastEventId=n&&n.event_id?n.event_id:oa(),i=new Error(t);return this._withClient((o,s)=>{o.captureMessage(t,a,{originalException:t,syntheticException:i,...n,event_id:r},s)}),r}captureEvent(t,a){let n=a&&a.event_id?a.event_id:oa();return t.type!=="transaction"&&(this._lastEventId=n),this._withClient((r,i)=>{r.captureEvent(t,{...a,event_id:n},i)}),n}lastEventId(){return this._lastEventId}addBreadcrumb(t,a){let{scope:n,client:r}=this.getStackTop();if(!n||!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:o=zy}=r.getOptions&&r.getOptions()||{};if(o<=0)return;let u={timestamp:xa(),...t},c=i?xs(()=>i(u,a)):u;c!==null&&n.addBreadcrumb(c,o)}setUser(t){let a=this.getScope();a&&a.setUser(t)}setTags(t){let a=this.getScope();a&&a.setTags(t)}setExtras(t){let a=this.getScope();a&&a.setExtras(t)}setTag(t,a){let n=this.getScope();n&&n.setTag(t,a)}setExtra(t,a){let n=this.getScope();n&&n.setExtra(t,a)}setContext(t,a){let n=this.getScope();n&&n.setContext(t,a)}configureScope(t){let{scope:a,client:n}=this.getStackTop();a&&n&&t(a)}run(t){let a=fr(this);try{t(this)}finally{fr(a)}}getIntegration(t){let a=this.getClient();if(!a)return null;try{return a.getIntegration(t)}catch{return!1&&te.warn(`Cannot retrieve integration ${t.id} from the current Hub`),null}}startTransaction(t,a){return this._callExtensionMethod("startTransaction",t,a)}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this._sendSessionUpdate()}endSession(){let t=this.getStackTop(),a=t&&t.scope,n=a&&a.getSession();n&&Os(n),this._sendSessionUpdate(),a&&a.setSession()}startSession(t){let{scope:a,client:n}=this.getStackTop(),{release:r,environment:i}=n&&n.getOptions()||{},o=De(),{userAgent:s}=o.navigator||{},u=Ns({release:r,environment:i,...a&&{user:a.getUser()},...s&&{userAgent:s},...t});if(a){let c=a.getSession&&a.getSession();c&&c.status==="ok"&&Ut(c,{status:"exited"}),this.endSession(),a.setSession(u)}return u}shouldSendDefaultPii(){let t=this.getClient(),a=t&&t.getOptions();return!!(a&&a.sendDefaultPii)}_sendSessionUpdate(){let{scope:t,client:a}=this.getStackTop();if(!t)return;let n=t.getSession();n&&a&&a.captureSession&&a.captureSession(n)}_withClient(t){let{scope:a,client:n}=this.getStackTop();n&&t(n,a)}_callExtensionMethod(t,...a){let r=br().__SENTRY__;if(r&&r.extensions&&typeof r.extensions[t]=="function")return r.extensions[t].apply(this,a);!1&&te.warn(`Extension method ${t} couldn't be found, doing nothing.`)}};function br(){let e=De();return e.__SENTRY__=e.__SENTRY__||{extensions:{},hub:void 0},e}function fr(e){let t=br(),a=_t(t);return Ai(t,e),a}function be(){let e=br();return(!V0(e)||_t(e).isOlderThan(zs))&&Ai(e,new wa),j0()?Uy(e):_t(e)}function Uy(e){try{let t=br().__SENTRY__,a=t&&t.extensions&&t.extensions.domain&&t.extensions.domain.active;if(!a)return _t(e);if(!V0(a)||_t(a).isOlderThan(zs)){let n=_t(e).getStackTop();Ai(a,new wa(n.client,Rt.clone(n.scope)))}return _t(a)}catch{return _t(e)}}function V0(e){return!!(e&&e.__SENTRY__&&e.__SENTRY__.hub)}function _t(e){return An("hub",()=>new wa,e)}function Ai(e,t){if(!e)return!1;let a=e.__SENTRY__=e.__SENTRY__||{};return a.hub=t,!0}function za(e,t){return be().captureException(e,{captureContext:t})}function yr(e){be().withScope(e)}var qy="7";function Gy(e){let t=e.protocol?`${e.protocol}:`:"",a=e.port?`:${e.port}`:"";return`${t}//${e.host}${a}${e.path?`/${e.path}`:""}/api/`}function Hy(e){return`${Gy(e)}${e.projectId}/envelope/`}function Ky(e,t){return D0({sentry_key:e.publicKey,sentry_version:qy,...t&&{sentry_client:`${t.name}/${t.version}`}})}function vr(e,t={}){let a=typeof t=="string"?t:t.tunnel,n=typeof t=="string"||!t._metadata?void 0:t._metadata.sdk;return a||`${Hy(e)}?${Ky(e,n)}`}function om(e){if(!e||!e.sdk)return;let{name:t,version:a}=e.sdk;return{name:t,version:a}}function Wy(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function sm(e,t,a,n){let r=om(a),i={sent_at:new Date().toISOString(),...r&&{sdk:r},...!!n&&{dsn:wn(t)}},o="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e];return Oa(i,[o])}function um(e,t,a,n){let r=om(a),i=e.type||"event",{transactionSampling:o}=e.sdkProcessingMetadata||{},{method:s,rate:u}=o||{};Wy(e,a&&a.sdk);let c=Qy(e,r,n,t);return delete e.sdkProcessingMetadata,Oa(c,[[{type:i,sample_rates:[{id:s,rate:u}]},e]])}function Qy(e,t,a,n){let r=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.baggage,i=r&&K0(r);return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!a&&{dsn:wn(n)},...e.type==="transaction"&&i&&{trace:Na({...i})}}}var lm=[];function cm(e){return e.reduce((t,a)=>(t.every(n=>a.name!==n.name)&&t.push(a),t),[])}function Us(e){let t=e.defaultIntegrations&&[...e.defaultIntegrations]||[],a=e.integrations,n=[...cm(t)];Array.isArray(a)?n=[...n.filter(o=>a.every(s=>s.name!==o.name)),...cm(a)]:typeof a=="function"&&(n=a(n),n=Array.isArray(n)?n:[n]);let r=n.map(o=>o.name),i="Debug";return r.indexOf(i)!==-1&&n.push(...n.splice(r.indexOf(i),1)),n}function dm(e){let t={};return e.forEach(a=>{t[a.name]=a,lm.indexOf(a.name)===-1&&(a.setupOnce(la,be),lm.push(a.name),!1&&te.log(`Integration installed: ${a.name}`))}),t}var mm="Not capturing exception because it's already been captured.",xr=class{_options;_dsn;_transport;_integrations={};_integrationsInitialized=!1;_numProcessing=0;_outcomes={};constructor(t){if(this._options=t,t.dsn){this._dsn=vs(t.dsn);let a=vr(this._dsn,t);this._transport=t.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:a})}else!1&&te.warn("No DSN provided, client will not do anything.")}captureException(t,a,n){if(Ms(t)){!1&&te.log(mm);return}let r=a&&a.event_id;return this._process(this.eventFromException(t,a).then(i=>this._captureEvent(i,a,n)).then(i=>{r=i})),r}captureMessage(t,a,n,r){let i=n&&n.event_id,o=ur(t)?this.eventFromMessage(String(t),a,n):this.eventFromException(t,n);return this._process(o.then(s=>this._captureEvent(s,n,r)).then(s=>{i=s})),i}captureEvent(t,a,n){if(a&&a.originalException&&Ms(a.originalException)){!1&&te.log(mm);return}let r=a&&a.event_id;return this._process(this._captureEvent(t,a,n).then(i=>{r=i})),r}captureSession(t){if(!this._isEnabled()){!1&&te.warn("SDK not enabled, will not capture session.");return}typeof t.release!="string"?!1&&te.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),Ut(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getTransport(){return this._transport}flush(t){let a=this._transport;return a?this._isClientDoneProcessing(t).then(n=>a.flush(t).then(r=>n&&r)):mt(!0)}close(t){return this.flush(t).then(a=>(this.getOptions().enabled=!1,a))}setupIntegrations(){this._isEnabled()&&!this._integrationsInitialized&&(this._integrations=dm(this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch{return!1&&te.warn(`Cannot retrieve integration ${t.id} from the current Client`),null}}sendEvent(t,a={}){if(this._dsn){let n=um(t,this._dsn,this._options._metadata,this._options.tunnel);for(let r of a.attachments||[])n=z0(n,U0(r,this._options.transportOptions&&this._options.transportOptions.textEncoder));this._sendEnvelope(n)}}sendSession(t){if(this._dsn){let a=sm(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(a)}}recordDroppedEvent(t,a){if(this._options.sendClientReports){let n=`${t}:${a}`;!1&&te.log(`Adding outcome: "${n}"`),this._outcomes[n]=this._outcomes[n]+1||1}}_updateSessionFromEvent(t,a){let n=!1,r=!1,i=a.exception&&a.exception.values;if(i){r=!0;for(let u of i){let c=u.mechanism;if(c&&c.handled===!1){n=!0;break}}}let o=t.status==="ok";(o&&t.errors===0||o&&n)&&(Ut(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new zt(a=>{let n=0,r=1,i=setInterval(()=>{this._numProcessing==0?(clearInterval(i),a(!0)):(n+=r,t&&n>=t&&(clearInterval(i),a(!1)))},r)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._dsn!==void 0}_prepareEvent(t,a,n){let{normalizeDepth:r=3,normalizeMaxBreadth:i=1e3}=this.getOptions(),o={...t,event_id:t.event_id||a.event_id||oa(),timestamp:t.timestamp||xa()};this._applyClientOptions(o),this._applyIntegrationsMetadata(o);let s=n;a.captureContext&&(s=Rt.clone(s).update(a.captureContext));let u=mt(o);if(s){let c=[...a.attachments||[],...s.getAttachments()];c.length&&(a.attachments=c),u=s.applyToEvent(o,a)}return u.then(c=>typeof r=="number"&&r>0?this._normalizeEvent(c,r,i):c)}_normalizeEvent(t,a,n){if(!t)return null;let r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(i=>({...i,...i.data&&{data:ua(i.data,a,n)}}))},...t.user&&{user:ua(t.user,a,n)},...t.contexts&&{contexts:ua(t.contexts,a,n)},...t.extra&&{extra:ua(t.extra,a,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=ua(t.contexts.trace.data,a,n))),t.spans&&(r.spans=t.spans.map(i=>(i.data&&(i.data=ua(i.data,a,n)),i))),r}_applyClientOptions(t){let a=this.getOptions(),{environment:n,release:r,dist:i,maxValueLength:o=250}=a;"environment"in t||(t.environment="environment"in a?n:"production"),t.release===void 0&&r!==void 0&&(t.release=r),t.dist===void 0&&i!==void 0&&(t.dist=i),t.message&&(t.message=La(t.message,o));let s=t.exception&&t.exception.values&&t.exception.values[0];s&&s.value&&(s.value=La(s.value,o));let u=t.request;u&&u.url&&(u.url=La(u.url,o))}_applyIntegrationsMetadata(t){let a=Object.keys(this._integrations);a.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...a])}_captureEvent(t,a={},n){return this._processEvent(t,a,n).then(r=>r.event_id,r=>{!1&&te.warn(r)})}_processEvent(t,a,n){let{beforeSend:r,sampleRate:i}=this.getOptions();if(!this._isEnabled())return hr(new _e("SDK not enabled, will not capture event."));let o=t.type==="transaction";return!o&&typeof i=="number"&&Math.random()>i?(this.recordDroppedEvent("sample_rate","error"),hr(new _e(`Discarding event because it's not included in the random sample (sampling rate = ${i})`))):this._prepareEvent(t,a,n).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new _e("An event processor returned null, will not send event.");if(a.data&&a.data.__sentry__===!0||o||!r)return s;let c=r(s,a);return Vy(c)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new _e("`beforeSend` returned `null`, will not send event.");let u=n&&n.getSession();return!o&&u&&this._updateSessionFromEvent(u,s),this.sendEvent(s,a),s}).then(null,s=>{throw s instanceof _e?s:(this.captureException(s,{data:{__sentry__:!0},originalException:s}),new _e(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${s}`))})}_process(t){this._numProcessing+=1,t.then(a=>(this._numProcessing-=1,a),a=>(this._numProcessing-=1,a))}_sendEnvelope(t){this._transport&&this._dsn?this._transport.send(t).then(null,a=>{!1&&te.error("Error while sending event:",a)}):!1&&te.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(a=>{let[n,r]=a.split(":");return{reason:n,category:r,quantity:t[a]}})}};function Vy(e){let t="`beforeSend` method has to return `null` or a valid event.";if(xn(e))return e.then(a=>{if(!(It(a)||a===null))throw new _e(t);return a},a=>{throw new _e(`beforeSend rejected with ${a}`)});if(!(It(e)||e===null))throw new _e(t);return e}function qs(e,t){t.debug===!0&&!1&&te.enable();let a=be(),n=a.getScope();n&&n.update(t.initialScope);let r=new e(t);a.bindClient(r)}var Yy=30;function Ei(e,t,a=L0(e.bufferSize||Yy)){let n={},r=o=>a.drain(o);function i(o){let s=[];if(js(o,(d,m)=>{let p=Ls(m);G0(n,p)?e.recordDroppedEvent("ratelimit_backoff",p):s.push(d)}),s.length===0)return mt();let u=Oa(o[0],s),c=d=>{js(u,(m,p)=>{e.recordDroppedEvent(d,Ls(p))})},l=()=>t({body:wi(u,e.textEncoder)}).then(d=>{d.statusCode!==void 0&&(d.statusCode<200||d.statusCode>=300)&&!1&&te.warn(`Sentry responded with status code ${d.statusCode} to sent event.`),n=H0(n,d)},d=>{!1&&te.error("Failed while sending event:",d),c("network_error")});return a.add(l).then(d=>d,d=>{if(d instanceof _e)return!1&&te.error("Skipped sending event due to full buffer"),c("queue_overflow"),mt();throw d})}return{send:i,flush:r}}var wr="7.7.0";var Ua={};Iu(Ua,{FunctionToString:()=>kn,InboundFilters:()=>Tn});var pm,kn=class e{static id="FunctionToString";name=e.id;setupOnce(){pm=Function.prototype.toString,Function.prototype.toString=function(...t){let a=En(this)||this;return pm.apply(a,t)}}};var Jy=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],Tn=class e{constructor(t={}){this._options=t}static id="InboundFilters";name=e.id;setupOnce(t,a){let n=r=>{let i=a();if(i){let o=i.getIntegration(e);if(o){let s=i.getClient(),u=s?s.getOptions():{},c=Zy(o._options,u);return Xy(r,c)?null:r}}return r};n.id=this.name,t(n)}};function Zy(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...Jy],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function Xy(e,t){return t.ignoreInternal&&n3(e)?(!1&&te.warn(`Event dropped due to being internal Sentry Error.
Event: ${sa(e)}`),!0):$y(e,t.ignoreErrors)?(!1&&te.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${sa(e)}`),!0):e3(e,t.denyUrls)?(!1&&te.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${sa(e)}.
Url: ${Si(e)}`),!0):t3(e,t.allowUrls)?!1:(!1&&te.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${sa(e)}.
Url: ${Si(e)}`),!0)}function $y(e,t){return!t||!t.length?!1:a3(e).some(a=>t.some(n=>yi(a,n)))}function e3(e,t){if(!t||!t.length)return!1;let a=Si(e);return a?t.some(n=>yi(a,n)):!1}function t3(e,t){if(!t||!t.length)return!0;let a=Si(e);return a?t.some(n=>yi(a,n)):!0}function a3(e){if(e.message)return[e.message];if(e.exception)try{let{type:t="",value:a=""}=e.exception.values&&e.exception.values[0]||{};return[`${a}`,`${t}: ${a}`]}catch{return!1&&te.error(`Cannot extract message for event ${sa(e)}`),[]}return[]}function n3(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function r3(e=[]){for(let t=e.length-1;t>=0;t--){let a=e[t];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}function Si(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?r3(t):null}catch{return!1&&te.error(`Cannot extract url for event ${sa(e)}`),null}}function Ks(e,t){let a=Ws(e,t),n={type:t&&t.name,value:l3(t)};return a.length&&(n.stacktrace={frames:a}),n.type===void 0&&n.value===""&&(n.value="Unrecoverable error caught"),n}function o3(e,t,a,n){let r={exception:{values:[{type:lr(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:`Non-Error ${n?"promise rejection":"exception"} captured with keys: ${C0(t)}`}]},extra:{__serialized__:Ps(t)}};if(a){let i=Ws(e,a);i.length&&(r.exception.values[0].stacktrace={frames:i})}return r}function Gs(e,t){return{exception:{values:[Ks(e,t)]}}}function Ws(e,t){let a=t.stacktrace||t.stack||"",n=u3(t);try{return e(a,n)}catch{}return[]}var s3=/Minified React error #\d+;/i;function u3(e){if(e){if(typeof e.framesToPop=="number")return e.framesToPop;if(s3.test(e.message))return 1}return 0}function l3(e){let t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:t:"No error message"}function gm(e,t,a,n){let r=a&&a.syntheticException||void 0,i=ki(e,t,r,n);return va(i),i.level="error",a&&a.event_id&&(i.event_id=a.event_id),mt(i)}function hm(e,t,a="info",n,r){let i=n&&n.syntheticException||void 0,o=Hs(e,t,i,r);return o.level=a,n&&n.event_id&&(o.event_id=n.event_id),mt(o)}function ki(e,t,a,n,r){let i;if(bi(t)&&t.error)return Gs(e,t.error);if(b0(t)){let o=t;if("stack"in t)i=Gs(e,t);else{let s=o.name||"DOMException",u=o.message?`${s}: ${o.message}`:s;i=Hs(e,u,a,n),gr(i,u)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return fi(t)?Gs(e,t):It(t)||lr(t)?(i=o3(e,t,a,r),va(i,{synthetic:!0}),i):(i=Hs(e,t,a,n),gr(i,`${t}`,void 0),va(i,{synthetic:!0}),i)}function Hs(e,t,a,n){let r={message:t};if(n&&a){let i=Ws(e,a);i.length&&(r.exception={values:[{value:t,stacktrace:{frames:i}}]})}return r}var Qs="Breadcrumbs",qa=class e{static id=Qs;name=e.id;options;constructor(t){this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t}}setupOnce(){this.options.console&&Bt("console",d3),this.options.dom&&Bt("dom",c3(this.options.dom)),this.options.xhr&&Bt("xhr",m3),this.options.fetch&&Bt("fetch",p3),this.options.history&&Bt("history",g3)}};function c3(e){function t(a){let n,r=typeof e=="object"?e.serializeAttribute:void 0;typeof r=="string"&&(r=[r]);try{n=a.event.target?cr(a.event.target,r):cr(a.event,r)}catch{n="<unknown>"}n.length!==0&&be().addBreadcrumb({category:`ui.${a.name}`,message:n},{event:a.event,name:a.name,global:a.global})}return t}function d3(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:N0(e.level),message:ws(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${ws(e.args.slice(1)," ")||"console.assert"}`,t.data.arguments=e.args.slice(1);else return;be().addBreadcrumb(t,{input:e.args,level:e.level})}function m3(e){if(e.endTimestamp){if(e.xhr.__sentry_own_request__)return;let{method:t,url:a,status_code:n,body:r}=e.xhr.__sentry_xhr__||{};be().addBreadcrumb({category:"xhr",data:{method:t,url:a,status_code:n},type:"http"},{xhr:e.xhr,input:r});return}}function p3(e){e.endTimestamp&&(e.fetchData.url.match(/sentry_key/)&&e.fetchData.method==="POST"||(e.error?be().addBreadcrumb({category:"fetch",data:e.fetchData,level:"error",type:"http"},{data:e.error,input:e.args}):be().addBreadcrumb({category:"fetch",data:{...e.fetchData,status_code:e.response.status},type:"http"},{input:e.args,response:e.response})))}function g3(e){let t=De(),a=e.from,n=e.to,r=vi(t.location.href),i=vi(a),o=vi(n);i.path||(i=r),r.protocol===o.protocol&&r.host===o.host&&(n=o.relative),r.protocol===i.protocol&&r.host===i.host&&(a=i.relative),be().addBreadcrumb({category:"navigation",data:{from:a,to:n}})}var Di=De(),Ti;function Vs(){if(Ti)return Ti;if(P0(fetch))return Ti=fetch.bind(Di);let e=Di.document,t=fetch;if(e&&typeof e.createElement=="function")try{let a=e.createElement("iframe");a.hidden=!0,e.head.appendChild(a);let n=a.contentWindow;n&&n.fetch&&(t=n.fetch),e.head.removeChild(a)}catch(a){!1&&te.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",a)}return Ti=t.bind(Di)}function fm(e,t){Object.prototype.toString.call(Di&&navigator)==="[object Navigator]"&&typeof navigator.sendBeacon=="function"?navigator.sendBeacon.bind(navigator)(e,t):I0()&&Vs()(e,{body:t,method:"POST",credentials:"omit",keepalive:!0}).then(null,i=>{!1&&te.error(i)})}var Ys=De(),Ar=class extends xr{constructor(t){t._metadata=t._metadata||{},t._metadata.sdk=t._metadata.sdk||{name:"sentry.javascript.browser",packages:[{name:"npm:@sentry/browser",version:wr}],version:wr},super(t),t.sendClientReports&&Ys.document&&Ys.document.addEventListener("visibilitychange",()=>{Ys.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,a){return gm(this._options.stackParser,t,a,this._options.attachStacktrace)}eventFromMessage(t,a="info",n){return hm(this._options.stackParser,t,a,n,this._options.attachStacktrace)}sendEvent(t,a){let n=this.getIntegrationById(Qs);n&&n.options&&n.options.sentry&&be().addBreadcrumb({category:`sentry.${t.type==="transaction"?"transaction":"event"}`,event_id:t.event_id,level:t.level,message:sa(t)},{event:t}),super.sendEvent(t,a)}_prepareEvent(t,a,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,a,n)}_flushOutcomes(){let t=this._clearOutcomes();if(t.length===0){!1&&te.log("No outcomes to send");return}if(!this._dsn){!1&&te.log("No dsn provided, will not send outcomes");return}!1&&te.log("Sending outcomes:",t);let a=vr(this._dsn,this._options),n=q0(t,this._options.tunnel&&wn(this._dsn));try{fm(a,wi(n))}catch(r){!1&&te.error(r)}}};function Ci(e,t=Vs()){function a(n){let r={body:n.body,method:"POST",referrerPolicy:"origin",headers:e.headers,...e.fetchOptions};return t(e.url,r).then(i=>({statusCode:i.status,headers:{"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits"),"retry-after":i.headers.get("Retry-After")}}))}return Ei(e,a)}var Mi="?";var h3=30,f3=40,b3=50;function Js(e,t,a,n){let r={filename:e,function:t,in_app:!0};return a!==void 0&&(r.lineno=a),n!==void 0&&(r.colno=n),r}var y3=/^\s*at (?:(.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,v3=/\((\S*)(?::(\d+))(?::(\d+))\)/,x3=e=>{let t=y3.exec(e);if(t){if(t[2]&&t[2].indexOf("eval")===0){let i=v3.exec(t[2]);i&&(t[2]=i[1],t[3]=i[2],t[4]=i[3])}let[n,r]=wm(t[1]||Mi,t[2]);return Js(r,n,t[3]?+t[3]:void 0,t[4]?+t[4]:void 0)}},bm=[h3,x3],w3=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|capacitor).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,A3=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,E3=e=>{let t=w3.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let i=A3.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}let n=t[3],r=t[1]||Mi;return[r,n]=wm(r,n),Js(n,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},ym=[b3,E3],S3=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,k3=e=>{let t=S3.exec(e);return t?Js(t[2],t[1]||Mi,+t[3],t[4]?+t[4]:void 0):void 0},vm=[f3,k3];var xm=[bm,ym,vm],Zs=Ts(...xm),wm=(e,t)=>{let a=e.indexOf("safari-extension")!==-1,n=e.indexOf("safari-web-extension")!==-1;return a||n?[e.indexOf("@")!==-1?e.split("@")[0]:Mi,a?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]};var Xs=0;function $s(){return Xs>0}function T3(){Xs+=1,setTimeout(()=>{Xs-=1})}function Aa(e,t={},a){if(typeof e!="function")return e;try{let r=e.__sentry_wrapped__;if(r)return r;if(En(e))return e}catch{return e}let n=function(){let r=Array.prototype.slice.call(arguments);try{a&&typeof a=="function"&&a.apply(this,arguments);let i=r.map(o=>Aa(o,t));return e.apply(this,i)}catch(i){throw T3(),yr(o=>{o.addEventProcessor(s=>(t.mechanism&&(gr(s,void 0,void 0),va(s,t.mechanism)),s.extra={...s.extra,arguments:r},s)),za(i)}),i}};try{for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}catch{}Es(n,e),mr(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get(){return e.name}})}catch{}return n}var eu={};Iu(eu,{Breadcrumbs:()=>qa,Dedupe:()=>Wa,GlobalHandlers:()=>ca,HttpContext:()=>Ka,LinkedErrors:()=>Ha,TryCatch:()=>Ga});var ca=class e{static id="GlobalHandlers";name=e.id;_options;_installFunc={onerror:D3,onunhandledrejection:C3};constructor(t){this._options={onerror:!0,onunhandledrejection:!0,...t}}setupOnce(){let t=this._options;for(let a in t){let n=this._installFunc[a];n&&t[a]&&(P3(a),n(),this._installFunc[a]=void 0)}}};function D3(){Bt("error",e=>{let[t,a,n]=Sm();if(!t.getIntegration(ca))return;let{msg:r,url:i,line:o,column:s,error:u}=e;if($s()||u&&u.__sentry_own_request__)return;let c=u===void 0&&ra(r)?I3(r,i,o,s):Am(ki(a,u||r,void 0,n,!1),i,o,s);c.level="error",Em(t,u,c,"onerror")})}function C3(){Bt("unhandledrejection",e=>{let[t,a,n]=Sm();if(!t.getIntegration(ca))return;let r=e;try{"reason"in e?r=e.reason:"detail"in e&&"reason"in e.detail&&(r=e.detail.reason)}catch{}if($s()||r&&r.__sentry_own_request__)return!0;let i=ur(r)?M3(r):ki(a,r,void 0,n,!0);i.level="error",Em(t,r,i,"onunhandledrejection")})}function M3(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function I3(e,t,a,n){let r=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i,i=bi(e)?e.message:e,o="Error",s=i.match(r);return s&&(o=s[1],i=s[2]),Am({exception:{values:[{type:o,value:i}]}},t,a,n)}function Am(e,t,a,n){let r=e.exception=e.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{},s=o.stacktrace=o.stacktrace||{},u=s.frames=s.frames||[],c=isNaN(parseInt(n,10))?void 0:n,l=isNaN(parseInt(a,10))?void 0:a,d=ra(t)&&t.length>0?t:A0();return u.length===0&&u.push({colno:c,filename:d,function:"?",in_app:!0,lineno:l}),e}function P3(e){!1&&te.log(`Global Handler attached: ${e}`)}function Em(e,t,a,n){va(a,{handled:!1,type:n}),e.captureEvent(a,{originalException:t})}function Sm(){let e=be(),t=e.getClient(),a=t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[e,a.stackParser,a.attachStacktrace]}var F3=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Ga=class e{static id="TryCatch";name=e.id;_options;constructor(t){this._options={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t}}setupOnce(){let t=De();this._options.setTimeout&&Pt(t,"setTimeout",km),this._options.setInterval&&Pt(t,"setInterval",km),this._options.requestAnimationFrame&&Pt(t,"requestAnimationFrame",B3),this._options.XMLHttpRequest&&"XMLHttpRequest"in t&&Pt(XMLHttpRequest.prototype,"send",R3);let a=this._options.eventTarget;a&&(Array.isArray(a)?a:F3).forEach(_3)}};function km(e){return function(...t){let a=t[0];return t[0]=Aa(a,{mechanism:{data:{function:Ft(e)},handled:!0,type:"instrument"}}),e.apply(this,t)}}function B3(e){return function(t){return e.apply(this,[Aa(t,{mechanism:{data:{function:"requestAnimationFrame",handler:Ft(e)},handled:!0,type:"instrument"}})])}}function R3(e){return function(...t){let a=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in a&&typeof a[r]=="function"&&Pt(a,r,function(i){let o={mechanism:{data:{function:r,handler:Ft(i)},handled:!0,type:"instrument"}},s=En(i);return s&&(o.mechanism.data.handler=Ft(s)),Aa(i,o)})}),e.apply(this,t)}}function _3(e){let t=De(),a=t[e]&&t[e].prototype;!a||!a.hasOwnProperty||!a.hasOwnProperty("addEventListener")||(Pt(a,"addEventListener",function(n){return function(r,i,o){try{typeof i.handleEvent=="function"&&(i.handleEvent=Aa(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Ft(i),target:e},handled:!0,type:"instrument"}}))}catch{}let s=Aa(i,{mechanism:{data:{function:"addEventListener",handler:Ft(i),target:e},handled:!0,type:"instrument"}}),u=[r,s,o];return n.apply(this,u)}}),Pt(a,"removeEventListener",function(n){return function(r,i,o){let s=i;try{let u=s&&s.__sentry_wrapped__;u&&n.call(this,r,u,o)}catch{}return n.call(this,r,s,o)}}))}var j3="cause",L3=5,Ha=class e{static id="LinkedErrors";name=e.id;_key;_limit;constructor(t={}){this._key=t.key||j3,this._limit=t.limit||L3}setupOnce(){let t=be().getClient();t&&la((a,n)=>{let r=be().getIntegration(e);return r?N3(t.getOptions().stackParser,r._key,r._limit,a,n):a})}};function N3(e,t,a,n,r){if(!n.exception||!n.exception.values||!r||!Ot(r.originalException,Error))return n;let i=Tm(e,a,r.originalException,t);return n.exception.values=[...i,...n.exception.values],n}function Tm(e,t,a,n,r=[]){if(!Ot(a[n],Error)||r.length+1>=t)return r;let i=Ks(e,a[n]);return Tm(e,t,a[n],n,[i,...r])}var Dm=De(),Ka=class e{static id="HttpContext";name=e.id;setupOnce(){la(t=>{if(be().getIntegration(e)){if(!navigator&&!location&&!Dm.document)return t;let a=t.request&&t.request.url||location&&location.href,{referrer:n}=Dm.document||{},{userAgent:r}=navigator||{},i={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},o={...a&&{url:a},headers:i};return{...t,request:o}}return t})}};var Wa=class e{static id="Dedupe";name=e.id;_previousEvent;setupOnce(t,a){let n=r=>{let i=a().getIntegration(e);if(i){try{if(O3(r,i._previousEvent))return!1&&te.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{return i._previousEvent=r}return i._previousEvent=r}return r};n.id=this.name,t(n)}};function O3(e,t){return t?!!(z3(e,t)||U3(e,t)):!1}function z3(e,t){let a=e.message,n=t.message;return!(!a&&!n||a&&!n||!a&&n||a!==n||!Pm(e,t)||!Im(e,t))}function U3(e,t){let a=Cm(t),n=Cm(e);return!(!a||!n||a.type!==n.type||a.value!==n.value||!Pm(e,t)||!Im(e,t))}function Im(e,t){let a=Mm(e),n=Mm(t);if(!a&&!n)return!0;if(a&&!n||!a&&n||(a=a,n=n,n.length!==a.length))return!1;for(let r=0;r<n.length;r++){let i=n[r],o=a[r];if(i.filename!==o.filename||i.lineno!==o.lineno||i.colno!==o.colno||i.function!==o.function)return!1}return!0}function Pm(e,t){let a=e.fingerprint,n=t.fingerprint;if(!a&&!n)return!0;if(a&&!n||!a&&n)return!1;a=a,n=n;try{return a.join("")===n.join("")}catch{return!1}}function Cm(e){return e.exception&&e.exception.values&&e.exception.values[0]}function Mm(e){let t=e.exception;if(t)try{return t.values[0].stacktrace.frames}catch{return}}var Bm=[new Ua.InboundFilters,new Ua.FunctionToString,new Ga,new qa,new ca,new Ha,new Wa,new Ka];function tu(e={}){if(e.defaultIntegrations===void 0&&(e.defaultIntegrations=Bm),e.release===void 0){let a=De();a.SENTRY_RELEASE&&a.SENTRY_RELEASE.id&&(e.release=a.SENTRY_RELEASE.id)}e.autoSessionTracking===void 0&&(e.autoSessionTracking=!0),e.sendClientReports===void 0&&(e.sendClientReports=!0);let t={...e,stackParser:M0(e.stackParser||Zs),integrations:Us(e),transport:e.transport||Ci};qs(Ar,t),e.autoSessionTracking&&q3()}function au(e){let t=be().getClient();return t?t.flush(e):(!1&&te.warn("Cannot flush events. No client defined."),mt(!1))}function Fm(e){e.startSession({ignoreDuration:!0}),e.captureSession()}function q3(){if(typeof De().document>"u"){!1&&te.warn("Session tracking in non-browser environment with @sentry/browser is not supported.");return}let a=be();a.captureSession&&(Fm(a),Bt("history",({from:n,to:r})=>{n===void 0||n===r||Fm(be())}))}var Rm={},nu=De();nu.Sentry&&nu.Sentry.Integrations&&(Rm=nu.Sentry.Integrations);var H5={...Rm,...Ua,...eu};var Qa=null;async function _m(){Qa||(Qa=await aa())}function jm(e){if(!e||!e.enable||Rn())return;let t=ht(),a=Ki();if(e.ignoreVersions?.find(s=>s===t))return!1;if(e.ignoreBuildPlatforms?.find(s=>s===a))return;let i=globalThis.navigator.userAgent;if(!e.ignoreUserAgents?.find(s=>i.match(new RegExp(s))))return!0}function H3(){try{let e=be();if(!e)return!1;let t=e.getClient();if(!t)return!1;let a=t.getOptions();return!(!a||!a.dsn)}catch{return!1}}async function K3(){try{if(await _m(),!Qa?.sentryCaptureConfig||!jm(Qa.sentryCaptureConfig)||H3())return;let e=ht(),t=Ki(),{initOptions:a}=Qa.sentryCaptureConfig,n={dsn:xl,environment:ke().PROD==="1"?"production":"develop",defaultIntegrations:!1,beforeSend(r){return r.tags?.trigger!=="user_report"?null:r},...a};a?.ignoreErrors&&(n.ignoreErrors=ru(a.ignoreErrors)),a?.denyUrls&&(n.denyUrls=ru(a.denyUrls)),a?.allowUrls&&(n.allowUrls=ru(a.allowUrls)),n.release=`${t}@${e}`,tu(n)}catch(e){X.debug("init sentry error:",e)}}async function Lm(e,t){try{if(await _m(),!Qa?.sentryCaptureConfig||!jm(Qa.sentryCaptureConfig)||!t.startsWith(Ca))return;await K3(),await new Promise(a=>setTimeout(a,100)),za(e,{tags:{trigger:"user_report",type:"http_error",error_url:t},extra:{url:t}}),await au(2e3)}catch{}}function ru(e){return e.map(t=>new RegExp(t))}async function ja(e){let t;if(e&&e.retry&&e.retry>0)try{t=await p0(Nm.bind(null,e),{multiplier:2,maxAttempts:e.retry})}catch(a){throw a&&a.name==="RetryError"&&a.cause?a.cause:a}else t=await Nm(e);return t}async function Nm(e){e.body;let{url:t,responseType:a,requestType:n,...r}=e;a||(a="json"),n||(n="json"),r={mode:"cors",...r},n=="formData"?r.body=g0(e.body):n=="urlSearchParams"?r.body=h0(e.body):n=="blob"&&e.body&&(r.body=ys(e.body));let i=!0;e.fetchPolyfill&&(i=!1);let o=e.fetchPolyfill||fetch,s=3e4,u;if(e.timeout&&(s=e.timeout),i&&!e.signal){let l=new AbortController,d=l.signal;u=setTimeout(()=>{l.abort()},s),r.signal=d}let c;try{c=await o(t,r)}catch(l){X.debug("fetch error",t,l),Lm(l,t);let d=l.message||"Unknown Error";throw l.name==="AbortError"&&!e.signal&&(d=`Request timeout after ${s}ms`),new Dt("fetchError",d).initNetWork(-999).initStack(l.stack)}finally{u&&clearTimeout(u)}if(c.ok&&c.status>=200&&c.status<400){if(a=="HEAD"){if(c.url!==t)throw new Dt("fetchError","redirect url:"+c.url);return c.statusText}if(a==="json")return await c.json();if(a==="text")return await c.text();if(a==="raw"){let l=await c.text(),d=Object.fromEntries([...c.headers.entries()]),m=c.url;return m||(c.headers.get("X-Final-URL")?m=c.headers.get("X-Final-URL"):m=t),{body:l,headers:d,status:c.status,statusText:c.statusText,url:m}}else if(a==="base64"){let l=await c.blob(),d=new FileReader,m=new Promise((p,x)=>{d.onload=function(){let A=d.result;p(A)},d.onerror=function(){x(d.error)}});return d.readAsDataURL(l),m}else if(a==="stream"){let l=Fo({onEvent:m=>{e.onMessage?.(m)},onError:m=>{X.debug("sseParser error",m),e.onError?.(m)}}),d=c?.body?.getReader();if(!d){e.onError?.(new Dt("fetchError","stream reader not found"));return}try{for(;;){let{done:m,value:p}=await d.read();if(m)break;let x=new TextDecoder().decode(p);l.feed(x)}}catch(m){if(m.name==="AbortError"){e.onFinish?.("request aborted");return}e.onError?.(m)}finally{e.onFinish?.("stream finished"),d.releaseLock()}}}else{let l;try{l=await c.text()}catch(A){X.error("parse response failed",A)}l&&X.error(`fail response: ${t} `,l);let d="";l&&(d=l.slice(0,500));let m=d,x=new URL(t).hostname.endsWith(`.${Le}`);throw t.endsWith("edge.microsoft.com/translate/auth")&&(m="bingAuth:"+d),x&&/translation quota.*reached/.test(d)&&(m="NewProQuota:"+d),new Dt("fetchError",m).initNetWork(c.status)}}var Ii;function iu(){return Ii||(Ii=new _a("content_script",!1).getConnection("pure_main",()=>{}),Ii)}async function Om(e,t){try{let a=ke(),n=a.INSTALL_FROM==="firefox_store";if(zn()?.name?.startsWith("ImtFx")&&(n=!1),n)return;let i=pe(),o=ea(),s=a.PROD==="1",u=ht(),c=new Date,{fakeUserId:l,installedAt:d}=await Xn(),m=await Ra(),p=await $n(),x=await er(),A=new Date(d),S=fa(A),g=fa(c),M=S===g,h=24*60*60*1e3,k=c.getTime()-A.getTime()<7*h,L=c.getTime()-A.getTime()<30*h,E=c.getTime()-A.getTime()<365*h,v=Or(),w="";typeof navigator<"u"&&(w=window.navigator.userAgent);let y=un.parse(w),R=e.map(F=>{let C=F.params||{};if(y.os&&(C.os_name=y.os.name||"unknown",C.os_version=y.os.version||"unknown",C.os_version_name=y.os.versionName||"unknown"),y.browser){C.browser_name=y.browser.name||"unknown",C.browser_version=y.browser.version||"unknown";let j=zn();j&&(C.browser_name=j.name,C.browser_version=j.version)}if(y.platform&&(C.platform_type=y.platform.type||"unknown"),y.engine&&(C.engine_name=y.engine.name||"unknown",C.engine_version=y.engine.version||"unknown"),u&&(C.version=u),a.INSTALL_FROM&&(C.install_from=a.INSTALL_FROM),S){C.install_day=di(A);let j=mi(A);C.install_week=`${j.year}${j.week}`}return C.userscript=i.toString(),M?C.is_new_user_today="1":C.is_new_user_today="0",C.is_new_user_this_week=k?"1":"0",C.is_new_user_this_month=L?"1":"0",C.is_new_user_this_year=E?"1":"0",o?C.main_frame=0:C.main_frame=1,m&&(C.ab_tag=m),p&&(C.ab_group=p),C.campaign=x||"none",{...F,params:C}});v.forEach(async F=>{let C=await zm({responseType:"text",url:F,method:"POST",body:JSON.stringify({client_id:l,user_id:l,events:R})})}),t&&Q3(l,R)}catch{}}async function W3(e){return await iu().sendMessage("background:main",e)}function zm(e){return pe()||Mr()?(e.fetchPolyfill=globalThis.GM_fetch,ja(e)):W3({method:"fetch",data:e})}function Q3(e,t){try{if(ka())return;t.forEach(a=>{let n={...a.params,event_name:a.name,device_id:e};rr(n);let r=Date.now()+(Math.random()*100).toFixed(0);zm({url:Ca,method:"POST",responseType:"text",body:JSON.stringify({nonce:r,subject:"user_behaviour",logs:[JSON.stringify(n)]})})})}catch(a){X.debug("report self service error",a)}}var Dn={releaseVersion:"1.13.5",immediateTranslationTextCount:4999,immediateTranslationScrollLimitScreens:1,translationStartMode:"dynamic",domReadyDetectTimeout:3e3,translationService:"bing",clientImageTranslationService:"inherit",inputTranslationService:"inherit",userTranslationServices:{},m/* MULTIPLE_REMOVED_BLOCKS */]","body.notranslate"],"additionalExcludeSelectors.remove":[".notranslate","[translate=no]"]},{id:"otherMathSites",selectorMatches:["math","mjx-container","[class*='MathJax']","[class*='math-']"],enableRichTranslate:!1,advanceMergeConfig:[{condition:"translationService==zhipu",advanceConfig:{"rich.stayOriginalTags.remove":["SUP","SUB"]}}],"additionalExcludeSelectors.remove":[".notranslate","[translate=no]"]},{id:"htmlLangFirst",selectorMatches:["[lang=he-IL]","[lang=nl-NL]","[lang=ar-SA]","[lang=fa-IR]","[lang=fi]","[lang=fi-FI]"],pageLangDetectWeight:{html:2,body:1,tab:1},_comment:"\u4E3A\u4E86\u5904\u7406 js \u5E93\u9519\u8BEF\u68C0\u6D4B\u5E0C\u4F2F\u6765\u8BED\u6210\u5176\u4ED6\u8BED\u8A00\u7684\u95EE\u9898 "},{id:"deepFrameTranslate",matches:["online.vitalsource.com","anarchothaumaturgist.itch.io","darkpetal16.itch.io","registry.khronos.org","achieve.macmillanlearning.com","mail.shanghai.*","help.autodesk.com"],enableDeepFrameTranslatePage:!0},{id:"common.pdfWebPage",waitForSelectorsTimeout:1,selectorMatches:["embed[type='application/pdf']"]},{id:"finalCommon.pdfWebPage",matches:["https://obgyn.onlinelibrary.wiley.com/doi/pdf/*","https://docs.amd.com/v/u/*/*"],selectorMatches:["embed[type='application/pdf']","[id=myPdfIframe][src*=pdf]","#article [type='application/pdf'][src*=pdf]",".textFrame [type='application/pdf'][src*=pdf]",".ggPdf","[id=pdfCanvasContainer] > iframe[src*=pdf]",".viewercontent-container  iframe[src*=documents]"],pdfUrlExtractRule:{selector:"iframe[src*=pdf]",attribute:"src",selectors:["embed[type='application/pdf']","iframe[src*=pdf]","iframe[src*=documents]","#statements-pdf"],attributes:["src"],queries:["file"]}},{id:"common2.pdfWebPage",matches:["https://obgyn.onlinelibrary.wiley.com/doi/pdf/*"],selectorMatches:["[id=myPdfIframe][src*=pdf]","#article [type='application/pdf'][src*=pdf]",".textFrame [type='application/pdf'][src*=pdf]",".ggPdf"],pdfUrlExtractRule:{selector:"iframe[src*=pdf]",attribute:"src"}},{id:"common4.pdfWebPage",selectorMatches:["#statements-pdf"],pdfUrlExtractRule:{selector:"#statements-pdf",attribute:"src"}},{id:"common-query.pdfWebPage",selectorMatches:["[id=pdfCanvasContainer] > iframe[src*=pdf]"],pdfUrlExtractRule:{selector:"iframe[src*=pdf]",attribute:"src",query:"file"}},{id:"fix-nav2header",matches:["www.acea.auto","news.cgtn.com"],"preTranslateLimiter.add_v.[1.12.1]":{"side.selectors":["aside","[class*='Sidebar']","#sidenav"],"header.selectors":["nav","header","[class^='header-v3']"]}},{id:"strict-fix-nav2header",matches:["www.talkclassical.com"],preTranslateLimiter:{"side.selectors":["aside","#sidenav"],"header.selectors":["nav","header"]},_comment:"\u4E25\u683C\u6A21\u5F0F\u4E0B\uFF0C\u53EA\u7FFB\u8BD1 header \u548C side \u6807\u7B7E"},{id:"fix-header",selectorMatches:["article header","header h1","header h2","header h3","header p","header nav"],"preTranslateLimiter.add_v.[1.12.1]":{"header.enableTranslate":!0},"excludeSelectors.add":[".site-header"],"initialSelectorGlobalAttributes.remove":{header:{}},"extraBlockSelectors.add":[".btn"],_comment:"\u7ED9\u90E8\u5206\u7F51\u9875\u5F00\u540E\u95E8\u7FFB\u8BD1 header\u6807\u7B7E"}]};function Pi(e){return Array.isArray(e)?e:e?[e]:[]}function Um(e,t){let a=[...e];for(let n of t)n.startsWith("[+]")&&!a.includes(n.slice(3))?a.push(n.slice(3)):n.startsWith("[-]")?a=a.filter(r=>r!==n.slice(3)):a.includes(n)||a.push(n);return a}function Er(e,t){let a={...e};return Km(a,t),Y3(a,t),a}function Hm(e,t,a){let n={...e};return at({rule:t,valueIsArray:r=>Array.isArray(e[r]),getMergedValue:r=>e[r],onMergedResult:(r,i)=>n[r]=i}),at({rule:a,valueIsArray:r=>Array.isArray(e[r]),getMergedValue:r=>n[r],onMergedResult:(r,i)=>n[r]=i}),n}function Y3(e,t){if(!t.condition)return;let a=t.condition.enableSubtitle?.true||{},n=t.condition.enableSubtitle?.false||{},r=e.enableSubtitle?a:n;Km(e,r)}function J3(e){return Object.keys(e).sort((t,a)=>{let[n,r,i]=Fi(t),[o,s,u]=Fi(a);return n!==o?t.localeCompare(a):i&&u?ha(i,u)?1:-1:i?1:u?-1:t.localeCompare(a)})}function at({rule:e,getMergedValue:t,valueIsArray:a,onMergedResult:n},r){J3(e).forEach(o=>{let[s,u,c]=Fi(o);if(s=="rich"){if(!r)return n(o,e[o]);let[p,x,A,S]=Fi(o);s=p+"."+x,u=A,c=S}if(!s||e[o]===void 0)return;let l=e[o];a(s)&&(l=Pi(e[o]));let d=t(s);if(d==null){n(s,l);return}let m;if(u=="add_v"){if(!qm(c))return;m=ou(d,l)}else if(u=="remove_v"){if(!qm(c))return;m=Gm(d,l)}else u==="add"?m=ou(d,l):u=="remove"&&(m=Gm(d,l));if(m||u=="remove_v"||u=="remove"){n(s,m);return}Array.isArray(e[s])&&s.startsWith("additional")?m=ou(d,l):m=l,n(s,m)})}function Km(e,t){return at({rule:t,valueIsArray:a=>Array.isArray(e[a]),getMergedValue:a=>e[a],onMergedResult:(a,n)=>{e[a]=n}}),e}function ou(e,t){let a;if(Array.isArray(e)){let n=Pi(t);a=[...e,...n],a=Array.from(new Set(a))}else typeof e=="object"&&typeof t=="object"?a={...e,...t}:a=t;return a}function Fi(e){let t=e.lastIndexOf("["),a="",n=e;return t>0&&(a=e.slice(t+1,e.length-1),n=e.slice(0,t-1)),[...n.split("."),a]}function qm(e){let t=ht();return e&&ha(t,e)}function Gm(e,t){if(Array.isArray(e)){let a=Pi(t);return e=e.filter(n=>!a.includes(n)),Array.from(new Set(e))}else if(typeof e=="object"&&typeof t=="object")Object.keys(t).forEach(a=>{delete e[a]});else return;return e}var Z3="userConfig",X3="userPromptPool",$3="userObjectPool";async function Wm(){let e=await nr(eo)||{};if(Object.keys(e).length>0)return e;let t=await Ym(Z3)||{},a=await nr(X3)||{},n=await nr($3)||{};return ev(t,a,n)}async function Qm(e){await ds(eo,e)}function ev(e,t,a){return tv(e,(n,r,i)=>{(n[r]||"")==i&&(t[i]?n[r]=t[i]:delete n[r])}),av(e,(n,r,i,o)=>{i==o&&(a[o]?n[r]=a[o]:delete n[r])}),e}function tv(e,t){Object.entries(e.translationServices||{}).forEach(([a,n])=>{["systemPrompt","prompt","multiplePrompt","subtitlePrompt"].forEach(r=>{let i=`@imt_${a}.${r}`;t(n,r,i)})})}function av(e,t){(e.independentSyncKeys||["generalRule.glossaries","generalRule.injectedCss","aiAssistantsMatches","customAiAssistants"]).forEach(n=>{let r=n.split("."),i=e,o="",s=i;r.forEach(u=>{i&&(o=u,s=i,i=i[u])}),i&&t(s,o,i,`@imt_${n}`)})}function Bi(e,t){let a=JSON.parse(JSON.stringify(t));return delete a.override,a.DEBUG&&delete a.DEBUG,e!=!1||Object.entries(a.translationServices||{}).forEach(([n,r])=>{let i=Vm(r.type||n);for(let o of i)delete r[o]}),a}function su(e,t,a){if(t.DEBUG&&(a.DEBUG=t.DEBUG),e!=!1||!a.translationServices||!a)return;let n=t.translationServices||{};Object.entries(n).forEach(([r,i])=>{let o=Vm(i.type||r),s=a.translationServices[r];s&&o.forEach(u=>{i[u]&&(s[u]=i[u])})})}function Vm(e){let t=an[e]?.allProps||[];!t.length&&e.endsWith("-custom")&&(t=an[e.replace("-custom","")]?.allProps||[]);let a=[];return t.forEach(n=>{n.sensitive&&a.push(n.name)}),a}function uu(e,t){return Jm(e,a=>{let n=[],r,i;function o(){r=t[n[0]],i=n[1]?.trim(),typeof r=="boolean"&&(i=i=="true")}if(n=a.split(/\s*==\s*/),o(),n.length>1)return r==i;if(n=a.split(/\s*!=\s*/),o(),n.length>1)return r!=i;if(n=a.split(/\s*>\s*/),o(),n.length>1){let s=t[n[0]];return typeof s=="number"?s>Number(i):s>i}if(n=a.split(/\s*<\s*/),o(),n.length>1){let s=t[n[0]];return typeof s=="number"?s<Number(i):s<i}return a=="true"})}function Jm(e,t){let a=!0;e=e.replace(/\((.+?)\)/g,(u,c)=>Jm(c,t)+"");let n=[],r=[],i,o=0,s=/ (&&|\|\|) /g;for(;(i=s.exec(e))!==null;)r.push(e.substring(o,i.index)),n.push(i[0]),o=i.index+i[0].length;if(n.length>0){r.length==n.length&&r.push(e.substring(o));for(let u=0;u<r.length;u++){let c=(n[u-1]||" && ").trim();c=="&&"?a=a&&t(r[u]):c=="||"&&(a=a||t(r[u]))}return a}return t(e)}function Zm(){return navigator.userAgent.indexOf("Mac")!==-1}var nv="";function rv(){return nv||globalThis.navigator.userAgent}function Xm(){let e=rv();if(/iPhone/.test(e))return!1;let t=e.indexOf("Macintosh")!==-1||e.indexOf("Mac OS X")!==-1,a=e.indexOf("Safari")!==-1;return t&&a}var $m=!1;async function aa(){let e={...Dn,buildinConfigUpdatedAt:re.BUILD_TIME};e=await mv(e);let t=await uv(e);e.targetLanguage=t;let a=await st.get(Xe,null),n=ro(),r=iv(),i=await bn(),o=Od();jd(i,e,o),i?.DEBUG&&!$m&&($m=!0,ol(!0));let s=globalThis.IMMERSIVE_TRANSLATE_CONFIG||{},u=await ya(),c=Object.assign({},s,r,JSON.parse(JSON.stringify(i)));if(!c.interfaceLanguage){let d=await ov();c.interfaceLanguage=d}await dv(c,i,e),pv(u,c),e=await fv(e);let l=Object.assign(n,e);return at({rule:e,valueIsArray:d=>Array.isArray(n[d]),getMergedValue:d=>n[d],onMergedResult:(d,m)=>{d!="generalRule"&&(l[d]=m)}}),at({rule:e.generalRule,valueIsArray:d=>Array.isArray(n.generalRule[d]),getMergedValue:d=>n.generalRule[d],onMergedResult:(d,m)=>{l.generalRule[d]=m}}),await cv(l),lv(a,c,l),l=await gv(l,c),l.donateUrl=e.donateUrl,l.minVersion=e.minVersion,l.feedbackUrl=e.feedbackUrl,l.rawUserConfig=i,Ld(l,o),l}function iv(){if(re.PROD==="1")return{};let e={};if(re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_ID&&re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_KEY){let a={secretId:re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_ID,secretKey:re.IMMERSIVE_TRANSLATE_SECRET_TENCENT_SECRET_KEY};e.translationServices={},e.translationServices.tencent=a}if(re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_APPID&&re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_KEY){let a={appid:re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_APPID,key:re.IMMERSIVE_TRANSLATE_SECRET_BAIDU_KEY};e.translationServices||(e.translationServices={}),e.translationServices.baidu=a}if(re.IMMERSIVE_TRANSLATE_SECRET_CAIYUN_TOKEN){let a={token:re.IMMERSIVE_TRANSLATE_SECRET_CAIYUN_TOKEN};e.translationServices||(e.translationServices={}),e.translationServices.caiyun=a}if(re.IMMERSIVE_TRANSLATE_SECRET_OPENL_APIKEY){let a={apikey:re.IMMERSIVE_TRANSLATE_SECRET_OPENL_APIKEY};e.translationServices||(e.translationServices={}),e.translationServices.openl=a}if(re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_ID&&re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_SECRET){let a={appId:re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_ID,appSecret:re.IMMERSIVE_TRANSLATE_SECRET_YOUDAO_APP_SECRET};e.translationServices||(e.translationServices={}),e.translationServices.youdao=a}if(re.IMMERSIVE_TRANSLATE_SECRET_VOLC_ACCESS_KEY_ID&&re.IMMERSIVE_TRANSLATE_SECRET_VOLC_SECRET_ACCESS_KEY){let a={accessKeyId:re.IMMERSIVE_TRANSLATE_SECRET_VOLC_ACCESS_KEY_ID,secretAccessKey:re.IMMERSIVE_TRANSLATE_SECRET_VOLC_SECRET_ACCESS_KEY};e.translationServices||(e.translationServices={}),e.translationServices.volc=a}if(re.IMMERSIVE_TRANSLATE_SECRET_DEEPL_AUTH_KEY){let a={authKey:re.IMMERSIVE_TRANSLATE_SECRET_DEEPL_AUTH_KEY};e.translationServices||(e.translationServices={}),e.translationServices.deepl=a}if(re.DEEPL_PROXY_ENDPOINT&&(e.translationServices||(e.translationServices={}),e.translationServices.deepl||(e.translationServices.deepl={}),e.translationServices.deepl.immersiveTranslateApiUrl=re.DEEPL_PROXY_ENDPOINT),re.IMMERSIVE_TRANSLATE_DEEPL_ENDPOINT&&(e.translationServices||(e.translationServices={}),e.translationServices.deepl||(e.translationServices.deepl={}),e.translationServices.deepl.immersiveTranslateDeeplTokenUrl=re.IMMERSIVE_TRANSLATE_DEEPL_ENDPOINT),re.IMMERSIVE_TRANSLATE_SECRET_OPENAI_API_KEY){let a={APIKEY:re.IMMERSIVE_TRANSLATE_SECRET_OPENAI_API_KEY};e.translationServices||(e.translationServices={}),e.translationServices.openai=a}re.IMMERSIVE_TRANSLATE_SERVICE&&(e.translationService=re.IMMERSIVE_TRANSLATE_SERVICE);let t={};return re.DEBUG==="1"&&(t.debug=!0,t.cache=!1),re.MOCK==="1"&&(t.translationService="mock"),t}async function ya(){let e=await ie.storage.local.get(_n);if(e[_n]){let t=e[_n],a=t.tempTranslationUrlMatches||[],n=a.filter(o=>o.expiredAt>Date.now()),r=!1;n.length!==a.length&&(a=n,r=!0);let i={...t,tempTranslationUrlMatches:[...a]};return r&&await Mt(i),i}else return{}}async function Mt(e){await ie.storage.local.set({[_n]:e})}async function ep(){return re.DEBUG==="1"?void 0:(await ie.storage.local.get(Zi))[Zi]}async function tp(){let e=await ep(),a={...ro(),...Dn,buildinConfigUpdatedAt:re.BUILD_TIME};if(e&&e&&e.buildinConfigUpdatedAt){let n=new Date(e.buildinConfigUpdatedAt),r=new Date(a.buildinConfigUpdatedAt);n>r&&(a=e)}return a}function bn(){return Wm()}function yn(e){return Qm(e)}async function Ym(e){return(await ie.storage.sync.get(e))[e]}async function ds(e,t){await ie.storage.local.set({[e]:t})}async function nr(e){return(await ie.storage.local.get(e))[e]}var ov=async()=>{let e=["zh-CN"];try{e=await ie.i18n.getAcceptLanguages()}catch(n){X.warn("get browser language error:",n)}let a=e.map(n=>Ur(n)).find(n=>u0.find(r=>r===n));return a||"en"},ro=()=>{let e={...Dn,buildinConfigUpdatedAt:re.BUILD_TIME};return{...e,targetLanguage:Yt,interfaceLanguage:"en",translationMode:"dual",inputTranslationMode:"translation",debug:!1,alpha:!1,translationUrlPattern:{matches:[],excludeMatches:[]},translationLanguagePattern:{matches:[],excludeMatches:[]},translationThemePatterns:{},translationParagraphLanguagePattern:{matches:[],excludeMatches:[],selectorMatches:[],excludeSelectorMatches:[]},translationBodyAreaPattern:{matches:[],excludeMatches:[],selectorMatches:[],excludeSelectorMatches:[]},translationTheme:"none",translationService:"bing",inputTranslationService:"inherit",mouseHoverTranslationService:"inherit",subtitleTranslateService:"inherit",translationArea:"main",translationStartMode:"dynamic",translationServices:{},monkeyH5FloatBall:{...e.monkeyH5FloatBall},pcFloatBall:{...e.pcFloatBall},generalRule:{...e.generalRule},translationGeneralConfig:{engine:"bing",_systemExcludeWordRegex:"\\b({word})\\b"},rules:[]}};function sv(e,t){return e=e||[],t=t||[],e.length!==t.length?!0:t.filter(n=>!e.includes(n)).length>0}async function uv(e){try{let t=e?.autoSelectTargetLanguageAfterInstalledAt;if(!t)return Yt;let a=await ut("installedAt","");if(a&&new Date(a)<new Date(t))return Yt;let n=[Yt];if(n=await ie.i18n.getAcceptLanguages(),!n?.length)return Yt;let r=Ur(n[0]);if(!r||r=="auto")return Yt;let i=e?.autoSelectAllTargetLanguageAfterInstalledAt;return r=="en"&&i&&a&&new Date(a)<new Date(i)?Yt:r}catch(t){return X.warn("get browser language error:",t),Yt}}function lv(e,t,a){let n=Jt(e),r={};t.translationServices?.deepl&&(r=t.translationServices.deepl);let i={};t.translationServices?.openai&&(i=t.translationServices.openai),!n&&r.authKey&&!r.provider?(a.translationServices?.deepl||(a.translationServices.deepl={}),a.translationServices.deepl.provider="custom"):r?.provider||(a.translationServices?.deepl||(a.translationServices.deepl={}),a.translationServices.deepl.provider="pro"),!n&&i.APIKEY&&!i.provider?(a.translationServices?.openai||(a.translationServices.openai={}),a.translationServices.openai.provider="custom"):i?.provider||(a.translationServices?.openai||(a.translationServices.openai={}),a.translationServices.openai.provider="pro");let o={};t.translationServices?.deepseek&&(o=t.translationServices.deepseek),!n&&o.APIKEY&&!o.provider?(a.translationServices?.deepseek||(a.translationServices.deepseek={}),a.translationServices.deepseek.provider="custom"):o?.provider||(a.translationServices?.deepseek||(a.translationServices.deepseek={}),a.translationServices.deepseek.provider="pro");let s={};t.translationServices?.gemini&&(s=t.translationServices.gemini),!n&&s.APIKEY&&!s.provider?(a.translationServices?.gemini||(a.translationServices.gemini={}),a.translationServices.gemini.provider="custom"):s&&s.provider||(a.translationServices?.gemini||(a.translationServices.gemini={}),a.translationServices.gemini.provider="pro");let u={};t.translationServices?.claude&&(u=t.translationServices.claude),!n&&u.APIKEY&&!u.provider?(a.translationServices?.claude||(a.translationServices.claude={}),a.translationServices.claude.provider="custom"):u.provider||(a.translationServices?.claude||(a.translationServices.claude={}),a.translationServices.claude.provider="pro")}function ap(e,t){let a=e.translationServices||{},n={...t.translationServices};function r(l){l.hasMerged||(at({rule:l,valueIsArray:d=>Array.isArray(l[d]),getMergedValue:d=>l[d],onMergedResult:(d,m)=>{l[d]=m}}),l.hasMerged=!0)}let i=Object.keys(n).reduce((l,d)=>(n[d].type||(l[d]=n[d]),l),{});Object.keys(i).forEach(l=>{let d=i[l],m=i[d.extends];!m||d.extends==l||(r(m),r(i[l]),i[l]=lu(m,i[l]))}),Object.keys(a).forEach(l=>{let d=a[l];d?.systemPrompt!=null&&d?.multipleSystemPrompt===void 0&&(d.multipleSystemPrompt="")}),at({rule:{...i},valueIsArray:l=>Array.isArray(i[l]),getMergedValue:l=>i[l],onMergedResult:(l,d)=>{i[l]={...i[l],...d}}});let o=Object.keys(n).reduce((l,d)=>(n[d].type&&(l[d]=n[d]),l),{});Object.keys(o).forEach(l=>{let d=o[l],m=i[d.extends];!m||d.extends==l||(r(o[l]),o[l]=lu(m,o[l]))}),at({rule:{...o},valueIsArray:l=>Array.isArray(o[l]),getMergedValue:l=>o[l],onMergedResult:(l,d)=>{o[l]={...o[l],...d}}}),n={...i,...o},Object.keys(a).forEach(l=>{let d=a[l];if(!d.extends)return;let m=n[d.extends];!m||d.extends==l||(a[l]=lu(m,a[l]))}),at({rule:{...a},valueIsArray:l=>Array.isArray(n[l]),getMergedValue:l=>n[l],onMergedResult:(l,d)=>{let m=n[l]?.env||{},p=d.env||{};m={...m,...p},n[l]={...n[l],...d,env:m}}}),new Date(e.updatedAt)<=new Date("2024.4.2")&&Object.keys(a).forEach(l=>{["openai","gemini"].includes(l)&&a[l].prompt!=null&&(a[l].maxTextGroupLengthPerRequest==null&&(n[l].maxTextGroupLengthPerRequest=1),a[l].multiplePrompt==null&&(n[l].multiplePrompt=a[l].prompt))});let u=["html_only","content_type","imt_source_field","imt_trans_field","imt_sub_source_field","imt_sub_trans_field","summary_prompt","terms_prompt","sub_summary_prompt","sub_terms_prompt"],c=(l,d)=>{!l||!l[d]||!l?.env||(l[d]=l[d].replace(/{{(.+?)}}/g,(m,p)=>u.includes(p)?m:l?.env[p]||m))};Object.values(n).forEach(l=>{c(l,"prompt"),c(l,"multiplePrompt"),c(l,"subtitlePrompt")}),t.translationServices=n}async function cv(e){try{let t=await ut("installedAt",""),a=e.generalRule.subtitleRule.youtubeAutoEnableSubtitleAfterInstalledAt;if(!a||new Date(a)>new Date(t))return;e.generalRule.subtitleRule.youtubeAutoEnableSubtitle=!0}catch(t){X.error("updateYoutubeAutoEnableSubtitleWithAfterInstallAt error",t)}}function lu(e,t){let a={...e};["provider","visible"].forEach(i=>{a[i]&&delete a[i]});let r={...a,...t};return at({rule:t,valueIsArray:i=>Array.isArray(a[i]),getMergedValue:i=>a[i],onMergedResult:(i,o)=>{r[i]=o}}),r}async function dv(e,t,a){let n=e.interfaceLanguage==="en",r=e.targetLanguage==="en",i=a&&a.translationLanguagePattern&&a.translationLanguagePattern.matches&&a.translationLanguagePattern.matches.length===0,o=["bing","google","transmart","mock"].includes(e.translationService)||!e.translationService;!n&&!r&&!i&&o?a.translationLanguagePattern||(a.translationLanguagePattern={matches:["en"],excludeMatches:[]}):a.translationLanguagePattern={matches:[],excludeMatches:[]};let s=a&&a.enableDefaultAlwaysTranslatedUrls;e.enableDefaultAlwaysTranslatedUrls===!1&&(s=!1);let u=e&&e.isChangedAlwaysTranslatedUrls,c=[];e.translationUrlPattern&&e.translationUrlPattern.matches&&(c=e.translationUrlPattern.matches||[]);let l=!1;u===void 0&&(c.length>0?u=!0:u=!1,e.isChangedAlwaysTranslatedUrls=u,t.isChangedAlwaysTranslatedUrls=u,l=!0);let d=(e?.translationLanguagePattern?.matches?.length||0)>0,m=await ut("installedAt",""),p=Number(await Ia("translage_page_daily",0)),x=Number(await ut(gt,0)),A;x>0&&(Date.now()-x<a.inactiveDays*24*60*60*1e3?A=!0:A=!1);let S;p>0&&(Date.now()-p<a.inactiveDays*24*60*60*1e3?S=!0:S=!1);let g;if(m){let h=new Date(m);Date.now()-h.getTime()<24*60*60*1e3?g=!0:g=!1}let M=e.modifiedBySystem;s&&!u&&!n&&!r&&o&&!d&&(M===!0||g||S===!1||A===!1||S===void 0&&A===void 0)&&sv(c,a.defaultAlwaysTranslatedUrls)&&(e.translationUrlPattern||(e.translationUrlPattern={}),e.translationUrlPattern.matches||(e.translationUrlPattern.matches=[]),e.translationUrlPattern.excludeMatches||(e.translationUrlPattern.excludeMatches=[]),e.translationUrlPattern.matches=[...a.defaultAlwaysTranslatedUrls],t.translationUrlPattern=e.translationUrlPattern,t.modifiedBySystem=!0,l=!0,Om([{name:"modifyAlwaysTranslatedUrls"}],a.enableSelfServiceReport)),l&&(X.debug("isChangedUserConfig",l),await yn(t))}async function mv(e){let t=await ep();if(t&&t.buildinConfigUpdatedAt){let a=new Date(t.buildinConfigUpdatedAt),n=new Date(e.buildinConfigUpdatedAt);a>n&&(e=t)}return e}function pv(e,t){let a=new Date;if(e&&e.tempTranslationUrlMatches&&e.tempTranslationUrlMatches.length>0){let n=e.tempTranslationUrlMatches.filter(r=>new Date(r.expiredAt)>a);if(n.length>0){let r=t.translationUrlPattern?t.translationUrlPattern?.matches||[]:[],i=Array.isArray(r)?r:[r],o=Array.from(new Set(i.concat(n.map(s=>s.match))));t.translationUrlPattern={...t.translationUrlPattern,matches:o}}}}async function gv(e,t){let a=await hv(),n=Object.keys(e),r=["translationUrlPattern","translationLanguagePattern","immediateTranslationPattern","translationBodyAreaPattern","translationParagraphLanguagePattern","translationThemePatterns","translationGeneralConfig","shortcuts","inputTranslationUrlPattern","inputLanguageCodeAlias","tokenUsageTips"];for(let i of n){let o=i;if(o==="generalRule")typeof t[o]=="object"&&(e[o]=Er(e[o],t[o]));else if(o==="translationServices")ap(t,e);else if(typeof t[o]!="string"&&typeof t[o]!="boolean"&&typeof t[o]!="number"&&r.includes(o))t[o]&&(e[o]=Object.assign(e[o],t[o])),o==="shortcuts"&&(pe()||pt()?e[o]={...e[o],...a}:e[o]={...a});else if(o==="rules"){if(Array.isArray(t[o])){let s=e.rules||[],u={};for(let l of s)l.id&&(u[l.id]=l);let c=t[o].map(l=>l.id&&u[l.id]?Hm(e.generalRule,u[l.id],l):l);e[o]=[...c,...e[o]]}if(re.PROD==="0"&&re.DEV_RULES){let s=JSON.parse(re.DEV_RULES);e[o]=[...s,...e[o]]}}else t[o]!==void 0&&(e[o]=t[o])}return e}async function hv(){let e={};if(!pe()&&ie.commands&&ie.commands.getAll){let t=await ie.commands.getAll();for(let a of t)a.name&&a.shortcut&&(e[a.name]=a.shortcut)}return e}async function np(e,t,a){let n=t;if(!a)return n;let r=await rp();return a.forEach(({condition:i,advanceConfig:o})=>{if(!i||!o||!uu(i,{...e,...r}))return;let{generalRule:u,translationServices:c,...l}=o;at({rule:l,valueIsArray:d=>Array.isArray(e[d]),getMergedValue:d=>e[d],onMergedResult:(d,m)=>{e[d]=m}}),c&&ap(o,e),u&&(n=Er(n,u))}),n}async function fv(e){try{if(!e.advanceMergeConfig)return e;let t=e,a=await rp();return e.advanceMergeConfig.forEach(({condition:n,advanceConfig:r})=>{if(!n||!r||!uu(n,{...e,...a}))return;let{generalRule:o,...s}=r;at({rule:s,valueIsArray:u=>Array.isArray(t[u]),getMergedValue:u=>t[u],onMergedResult:(u,c)=>{t[u]=c}}),o&&at({rule:o,valueIsArray:u=>Array.isArray(t.generalRule[u]),getMergedValue:u=>t.generalRule[u],onMergedResult:(u,c)=>{t.generalRule[u]=c}})}),t}catch(t){return X.error("mergeAdvanceConfig error",t),e}}var Cn=null;async function rp(){try{let e=oo()||"0",t=io()||"0";if(Cn&&Cn.imtAndroidVersion===e&&Cn.imtIOSVersion===t)return Cn;let n=ke().INSTALL_FROM,r=ht(),i=es(),o=so()||"",s=Rl(),u=_l()||"",c=await Ra()||"a",l=c.charCodeAt(0)-"a".charCodeAt(0),d=new Date(await Fd()).getTime();return Cn={abTag:c,abTagNumber:l,version:r,imtAndroidVersion:e,imtIOSVersion:t,installFrom:n,platform:i,iosSafariVersion:o,iosSafariVersionNumber:s,iosSystemVersion:u,isMobile:!!Ta().any,isMacOS:Zm(),isMacSafari:Xm(),versionNumber:gn(r),imtAndroidVersionNumber:gn(e),imtIOSVersionNumber:gn(t),installedAtTimestamp:d},Cn}catch(e){return X.error("getAdvanceConditionInfo error",e),null}}var bv=No(null);async function vv(e,t,a){a=a||await fs();let n=a.aiAssistants||[],r=!1;if(e=="edit"&&ip(t))for(let o=n.length-1;o>=0;o--)n[o].id===t.id&&(n[o]=t,r=!0);else if(e==="add"&&ip(t)){for(let o=n.length-1;o>=0;o--)n[o].id===t.id&&n.splice(o,1);n.push(t),r=!0}else{for(let o=n.length-1;o>=0;o--)n[o].id===t.id&&n.splice(o,1);r=!0}a.aiAssistants=n.sort((o,s)=>o.priority-s.priority);let i=await bn();i.aiAssistantIds=[...new Set(n.map(o=>o.id))];try{await r0(a),await yn(i)}catch{return!1}return r}async function xv(e,t){(await Promise.allSettled(e.map(n=>rt({url:`${Yi}api/plugins/${n}.json`})))).forEach(n=>{if(n.status==="fulfilled"){let r=n.value;r&&vv("add",r,t)}})}async function wv(e,t,a=!0){e||(e=await aa()),t||(t=await fs());let n=t.aiAssistants||[],r=(e.aiAssistantIds||[]).filter(s=>!n.find(u=>u.id===s)),i=[];if(a){let s=await Sv();i=await Ev(t,s)}let o=[...new Set([...r,...i])].filter(s=>!s.startsWith("custom"));o.length!==0&&xv(o,t)}var cu=Fa(wv,1500);function ip(e){return ha(On(),e.extensionVersion)}function Av(e,t){return t?!ha(e.version,t):!1}function Ev(e,t){let a=[];return(e.aiAssistants||[]).forEach(n=>{Av(n,t[n.id]?.version)&&a.push(n.id)}),[...new Set(a)]}async function Sv(){return(await rt({url:`${Yi}api/plugins/meta.json`}))?.plugins||{}}async function mu(e){let{url:t,config:a,state:n}=e,r=new URL(t),i="auto",{translationService:o,translationParagraphLanguagePattern:s,translationServices:u,translationTheme:c,translationThemePatterns:l,translationUrlPattern:d,targetLanguage:m,sourceLanguageUrlPattern:p,immediateTranslationPattern:x}=a,A=m||"zh-CN",S=Mn(t,s),g=Mn(t,x),M=o,h=Object.keys(u);for(let O of h){let ue=u[O];if(Mn(t,ue)){M=O;break}}let k=c,L=Object.keys(l);for(let O of L){let ue=l[O];if(ue&&Mn(t,ue)){k=O;break}}let E=Mn(t,d),v=du(t,d);v||(v=yt(t,pl)),v||(v=yt(t,a.blockUrls));let w=du(t,a.inputTranslationUrlPattern);w||(w=yt(t,a.inputTranslationBlockUrls));let y=du(t,a.generalRule?.selectionTranslation?.urlPattern||{}),R=yt(t,a.mutationBlockUrls),F=Object.keys(p),C={};for(let O of F){let ue=p[O];if(ue&&ue.matches)for(let me of ue.matches)C[me]=O}let j=Object.keys(C),Y=Go(t,j);Y&&(i=C[Y]??"auto",C[Y]&&C[Y]!=="auto"&&os(C[Y]));let Z=r.hostname,ve=await or(Z),Se=r.pathname+r.search+r.hash,H=await or(Se),oe=`https://${ve}.com/${H}`,Fe=await ya(),he=await tp(),T=a.translationStartMode,I=Tv(Fe,a);Fe.aiAssistants=Dv(I,a),T==="dynamic"&&g&&(T="immediate");let U=M;a.inputTranslationService&&a.inputTranslationService!=="inherit"&&(U=a.inputTranslationService);let ne=M;a.mouseHoverTranslationService&&a.mouseHoverTranslationService!=="inherit"&&(ne=a.mouseHoverTranslationService);let Q=M;a.subtitleTranslateService&&a.subtitleTranslateService!=="inherit"&&(Q=a.subtitleTranslateService);let ce=await st.get(Xe,null),fe=!1;ce&&(fe=Jt(ce));let _={targetLanguage:A,config:a,translationService:M,inputTranslationService:U,mouseHoverTranslationService:ne,subtitleTranslateService:Q,clientImageTranslationService:Q,isTranslateUrl:E,sourceLanguage:i,mainFrame:document.body,isTranslateExcludeUrl:v,isMutationTranslationExcludeUrl:R,isInputTranslationExcludeUrl:w,isSelectionTranslationExcludeUrl:y,rule:a.generalRule,url:t,encryptedUrl:oe,state:n?Object.assign({translationMode:a.translationMode,translationArea:a.translationArea,translationStartMode:T,immediateTranslationTextCount:a.immediateTranslationTextCount,isAutoTranslate:!1,translationDebounce:300,isNeedClean:!1,isDetectParagraphLanguage:S,cache:a.cache,translationTheme:k,isTranslateDirectlyOnHover:!1},n):{translationMode:a.translationMode,translationArea:a.translationArea,translationStartMode:T,immediateTranslationTextCount:a.immediateTranslationTextCount,isAutoTranslate:!1,translationDebounce:300,isNeedClean:!1,isDetectParagraphLanguage:S,cache:a.cache,translationTheme:k,isTranslateDirectlyOnHover:!1},localConfig:Fe};ce&&(_.user=ce,fe?_.isPro=!0:_.isPro=!1,_.isMax=El(ce)),_.state.translationArea==="body"&&(_.config.generalRule.excludeTags=_.config.generalRule.excludeTags.filter(O=>!_.config.generalRule.bodyTranslateTags.includes(O)),_.config.generalRule.additionalExcludeSelectors=_.config.generalRule.additionalExcludeSelectors.filter(O=>O!==".btn"));let N=a.translationServices[_.translationService]||{};N.immediateTranslationTextCount!==void 0&&kv(N.immediateTranslationTextCount)&&N.immediateTranslationTextCount>=0&&(_.state.immediateTranslationTextCount=N.immediateTranslationTextCount),_.translationService==="deepl-pro"&&(N?.authKey?.startsWith("immersive_")||N?.provider==="pro"&&_.user?.token)&&N.immediateTranslationTextCountForImmersiveDeepl!==void 0&&N.immediateTranslationTextCountForImmersiveDeepl>=0&&(_.state.immediateTranslationTextCount=N.immediateTranslationTextCountForImmersiveDeepl),N&&N.translationDebounce&&typeof N.translationDebounce=="number"&&(_.state.translationDebounce=N.translationDebounce);let V=he.immediateTranslationTextCount;a.immediateTranslationTextCount!==V&&(_.state.immediateTranslationTextCount=a.immediateTranslationTextCount);let ae=a.rules,$;globalThis.PDFViewerApplication?$=ae.find(O=>O.pageType=="pdfReader"):globalThis.immersiveTranslateEbookViewer?$=ae.find(O=>O.pageType=="ebookReader"):globalThis.immersiveTranslateEbookBuilder?$=ae.find(O=>O.pageType=="ebookBuilder"):($=ae.find(O=>Mn(t,O)),X.debug("match rule.id",$?.id)),$&&$.pageType==="ebookBuilder"&&(_.state.translationStartMode="immediate");let q=a.generalRule;if($&&(_.rule=Object.keys($).length>=Object.keys(q).length?$:Er(q,$),_.rule=await np(a,_.rule,[...q.advanceMergeConfig||[],...$.advanceMergeConfig||[]])),_.state.translationArea==="body"&&_.rule.excludeTags&&(_.rule.excludeTags=_.rule.excludeTags.filter(O=>!_.rule.bodyTranslateTags.includes(O)&&!_.rule.forceTranslateTags.includes(O))),_.rule.mainFrameSelector){let O=document.querySelector(_.rule.mainFrameSelector);O&&(_.mainFrame=O)}return _}function Mn(e,t){if(!t)return!1;let{matches:a,excludeMatches:n,selectorMatches:r,excludeSelectorMatches:i}=t;return n&&n.length>0&&yt(e,n)?!1:a&&a.length>0&&yt(e,a)?!0:i&&i.length>0&&Zr(i)?!1:!!(r&&r.length>0&&Zr(r))}function du(e,t){if(!t)return!1;let{excludeMatches:a,excludeSelectorMatches:n}=t;return!!(a&&a.length>0&&yt(e,a)||n&&n.length>0&&Zr(n))}function kv(e){return typeof e=="number"}function Tv(e,t){let a=[...e.aiAssistants||[]];return(t.rawUserConfig?.customAiAssistants||[]).forEach(r=>{let i=a.findIndex(o=>o.id===r.id);i!==-1?a[i]=r:a.push(r)}),a}function Dv(e,t){try{let a=t.rawUserConfig?.aiAssistantsMatches||{};return e.map(n=>{let r=Um(n.matches||[],a[n.id]?.matches||[]);return{...n,matches:r}})}catch(a){return X.error(a),e}}var In="imt-subtitle-inject",Ri=class{from;to;constructor(t,a){this.from=t,this.to=a}sendMessages(t){globalThis.postMessage({eventType:In,to:this.to,from:this.from,type:t.type,data:t.data,id:t.id||new Date().getTime(),isAsync:!1})}getRandomId(){return(new Date().getTime()+Math.random())*Math.random()}sendAsyncMessages({type:t,data:a}){return new Promise(n=>{let r=this.getRandomId();globalThis.postMessage({eventType:In,to:this.to,from:this.from,type:t,data:a,id:r,isAsync:!0});let i=o=>{let s=o.data;In===s.eventType&&s.id===r&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",i))};globalThis.addEventListener("message",i)})}handleMessageOnce(t){return new Promise(a=>{let n=r=>{let i=r.data;In===i.eventType&&i.type===t&&i.to===this.from&&(a(i.data),globalThis.removeEventListener("message",n))};globalThis.addEventListener("message",n)})}handleMessage(t,a){let n=r=>{let i=r.data;In===i.eventType&&i.type===t&&i.to===this.from&&a(i)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}handleMessages(t){let a=({data:n})=>{In===n.eventType&&n.to===this.from&&t(n)};return globalThis.addEventListener("message",a),()=>{globalThis.removeEventListener("message",a)}}},_i=new Ri("content-script","inject"),Cv=new Ri("inject","content-script"),op={get(e,t,a){return t in e?(...n)=>{let r=e[t];return typeof r=="function"?r.apply(e,n):Reflect.get(e,t,a)}:n=>e.sendAsyncMessages({type:t,data:n})}},Nj=new Proxy(Cv,op),Oj=new Proxy(_i,op);var eL=Fa(Mv,1e3);async function Mv(e,t,a){try{if(t===null)return"noupdate";let n=await ya();if(t.updatedAt){let u=new Date().getTime(),c=new Date(t.updatedAt).getTime();if(u-c<2e3){let l=Bi(t.proSyncAPIKey,t);return await pu(e,l),await Mt(n),"upload"}}let{remoteSetting:r,remoteTimestamp:i}=await lp(e);n.accountLastSyncedAt=Date.now(),X.debug("settings",t),X.debug("remoteSettings",r),X.debug("local settings.updatedAt",Pn(t.updatedAt)),X.debug("remote settings.updatedAt",Pn(r.updatedAt)),X.debug("last synced at",Pn(n.accountLastSyncedAt)),Bd(t,i);let o=!1;if(t.updatedAt&&(!r||!r.updatedAt)&&(o=!0),!o&&t.updatedAt>r.updatedAt&&(o=!0),X.debug("isUpload",o),o){let u=Bi(t.proSyncAPIKey,t);return await pu(e,u),await Mt(n),"upload"}let s=!1;return r.updatedAt&&(!t||!t.updatedAt)&&(s=!0),!s&&t.updatedAt<r.updatedAt&&(s=!0),s?(r.override=!0,su(r.proSyncAPIKey,t,r),await a(up(r,t)),await Mt(n),cu(void 0,n),"override"):(await Mt(n),"noupdate")}catch(n){throw ts(n),n}}var Pn=e=>e?new Date(e).toLocaleString("zh-CN",{timeZone:"Asia/Shanghai"}):"";async function sp(e,t,a){try{if(t===null)return"noupdate";let{remoteSetting:n}=await lp(e),r=await ya();r.accountLastSyncedAt=Date.now(),X.debug("settings",t),X.debug("remoteSettings",n),X.debug("local settings.updatedAt",Pn(t.updatedAt)),X.debug("remote settings.updatedAt",Pn(n.updatedAt)),X.debug("last synced at",Pn(r.accountLastSyncedAt));let i=!1;if(t.updatedAt&&(!n?.updatedAt||Object.keys(n).length<=1)&&(i=!0),X.debug("isUpload",i),i){let s=Bi(t.proSyncAPIKey,t);return await pu(e,s),await Mt(r),"upload"}let o=!0;return(!n?.updatedAt||Object.keys(n).length<=1)&&(o=!1),o?(su(n.proSyncAPIKey,t,n),n.override=!0,await a(up(n,t)),await Mt(r),cu(void 0,r),"override"):(await Mt(r),"noupdate")}catch(n){throw ts(n),n}}function up(e,t){let a={...e};return $u.forEach(r=>{n(r)}),a;function n(r){!Iv(e.translationServices||{},e[r])&&(a[r]=t[r])}}function Iv(e,t){if(!t)return!0;let a=Object.keys(Dn.translationServices).find(n=>t==n);return!a&&t?e[t]?.type=="custom-ai":a}function lp(e){return(location.href?.indexOf("popup.html")>0?ja:rt)({responseType:"json",url:$a+"v1/user/settings",method:"get",headers:{token:e}}).then(a=>({remoteSetting:a.data,remoteTimestamp:a.timestamp*1e3}))}function pu(e,t){return delete t.localUpdatedAt,delete t.override,(location.href?.indexOf("popup.html")>0?ja:rt)({responseType:"json",url:$a+"v1/user/settings",method:"post",headers:{token:e,"content-type":"application/json"},body:JSON.stringify(t)}).then(n=>n.data)}async function cp(){if(Xt())try{await Pv();let e=await st.get(Xe,null),t=await rs.getUserInfo();if(t&&t.token!==e?.token){st.set(Xe,t);let a=await bn();Jt(t)&&await sp(t.token,a,yn),document.dispatchEvent(new CustomEvent(z+"DocumentMessageUser",{detail:t}))}}catch{}}function Pv(){return new Promise(e=>{ns.handleMessageOnce("bridgeReady").then(()=>{e(!0)})})}var gu=class{bridge;waitForBridge(t=1e4){return!Xt()&&!Zt()?Promise.resolve(!1):globalThis.WebViewJavascriptBridge?(this.bridge=globalThis.WebViewJavascriptBridge,Promise.resolve(!0)):new Promise(a=>{let n=Date.now(),r=()=>{if(globalThis.WebViewJavascriptBridge)return this.bridge=globalThis.WebViewJavascriptBridge,a(!0);if(Date.now()-n>t)return a(!1);requestAnimationFrame(r)};r()})}registerHandler(t,a){this.bridge&&this.bridge.registerHandler(t,a)}callHandler(t,a,n){this.bridge&&this.bridge.doSend({type:t,...a},n)}},We=new gu;var hu=class{constructor(){}getRandomId(){return(new Date().getTime()+Math.random())*Math.random()}sendAsyncMessages({type:t,data:a}){return new Promise(n=>{let r=this.getRandomId(),i=this.handleMessage(t,o=>{o.id===r&&(i(),n(o.payload))});this.sendMessages({type:t,id:r,data:a})})}sendMessages(t){globalThis.document.dispatchEvent(new CustomEvent(ll,{detail:JSON.stringify({id:t.id||this.getRandomId(),type:t.type,data:t.data})}))}handleMessages(t){let a=n=>{let r=n;if(r.detail)try{let i=JSON.parse(r.detail);t(i)}catch{}};return globalThis.document.addEventListener(Lr,a),()=>{globalThis.document.removeEventListener(Lr,a)}}handleMessage(t,a){return this.handleMessages(n=>{n.type===t&&a(n)})}},Fv=new hu,Bv={get(e,t,a){return t in e?(...n)=>{let r=e[t];return typeof r=="function"?r.apply(e,n):Reflect.get(e,t,a)}:n=>{if(t.startsWith("getAsync")||t.endsWith("Async"))return e.sendAsyncMessages({type:t,data:n});e.sendMessages({type:t,data:n})}}},jt=new Proxy(Fv,Bv);function dp(e,t){let a="right: unset; bottom: unset; left: 50%; top: 0; transform: translateX(-50%);";globalThis.innerWidth>450&&(a="left: unset; top: 0; right: 20px; bottom: unset; transform: none;"),jt.togglePopup({style:e.style||a,isSheet:e.isSheet||!1,overlayStyle:e.overlayStyle||"background-color: transparent;"}),t({result:!0})}function mp(e,t){let a="right: unset; bottom: unset; left: 50%; top: 0; transform: translateX(-50%);";globalThis.innerWidth>450&&(a="left: unset; top: 0; right: 20px; bottom: unset; transform: none;"),jt.openPopup({style:e.style||a,isSheet:e.isSheet||!1,overlayStyle:e.overlayStyle||"background-color: transparent;"}),t({result:!0})}function pp(e,t){jt.closePopup(),t({result:!0})}function gp(e,t){jt.translatePage(),t({result:!0})}function hp(e,t){jt.restorePage(),t({result:!0})}async function fp(e,t){let a=await jt.getPageStatusAsync();t({result:!0,status:a,pageTranslated:a=="Translated"})}function bp(e,t){jt.openImageTranslationFeedback(),t({result:!0})}function yp(e,t){jt.openWebTranslationFeedback(),t({result:!0})}var ji=[];function vp(e,t){try{let{imageUrl:a}=e;if(!bu(a))return t({result:!1,errMsg:"\u56FE\u7247\u4E0D\u5B58\u5728"});yu({originalUrl:a,triggerResultCallback:t}),jt.triggerTranslateImageBySrc(a)}catch{t({result:!1,errMsg:"\u7FFB\u8BD1\u8FC7\u7A0B\u53D1\u751F\u9519\u8BEF"})}}function xp(e,t){let{imageId:a,imageUrl:n}=e,r="";if(n){let i=bu(n);i||t({result:!1,errMsg:"\u627E\u4E0D\u5230\u539F\u56FE"}),r=i?.getAttribute("bak_src")||""}else{let i=Sr({urlHash:a});if(!i){t({result:!1,errMsg:"\u627E\u4E0D\u5230\u7FFB\u8BD1\u540E\u7684\u56FE"});return}if(!bu(i.originalUrl)){t({result:!1,errMsg:"\u627E\u4E0D\u5230\u539F\u56FE"});return}r=i.originalUrl}jt.cleanTranslateImageBySrc(r)}function wp(e){let{urlHash:t,imgData:a,originalUrl:n}=e,r=Sr({originalUrl:n});r||(r={originalUrl:n,urlHash:t}),r.urlHash=t,yu(r),fu(t,{state:"extension_uploading",errorMsg:""}),We.callHandler("imageTextRecognition",{imageId:t,imageUrl:n,imageData:a},function(i){let{imageId:o,boxes:s,result:u,errMsg:c}=i;u&&s&&fu(o,{state:"saved",errorMsg:"",result:{ocrTime:0,boxesWithText:s}}),!u&&c&&fu(o,{state:"error",errorMsg:c})})}function Ap(e){let{urlHash:t}=e,a=Sr({urlHash:t});if(!a)return;let n=a.imgState;return{urlHash:t,state:n}}function Ep(e){let{imgHash:t,originalUrl:a,ok:n,errMsg:r}=e,i=Sr({originalUrl:a});i&&(yu(i),i.triggerResultCallback?.({result:n,errMsg:r}))}function yu(e){let t=Rv(e);if(t!==-1){ji[t]=e;return}ji.push(e)}function fu(e,t){let a=Sr({urlHash:e});a&&(a.imgState=t)}function Rv(e){return ji.findIndex(t=>e.urlHash===t.urlHash||e.originalUrl===t.originalUrl)}function Sr(e){return ji.find(t=>e.urlHash===t.urlHash||e.originalUrl===t.originalUrl)}function bu(e){let t=document.querySelector(`img[src="${e}"]`)||document.querySelector(`img[bak_src="${e}"]`);if(t)return t;let a=document.querySelector(`[srcset*="${e}"]`)||document.querySelector(`[bak_srcset*="${e}"]`);return a instanceof HTMLSourceElement?a.parentElement?.querySelector("img"):a instanceof HTMLImageElement?a:a instanceof HTMLPictureElement?a.querySelector("img"):null}async function vu(){try{if(!await We.waitForBridge())return;We.registerHandler("translateImage",vp),We.registerHandler("restoreImage",xp),We.registerHandler("translatePage",gp),We.registerHandler("restorePage",hp),We.registerHandler("getPageStatus",fp),We.registerHandler("togglePopup",dp),We.registerHandler("openPopup",mp),We.registerHandler("closePopup",pp),We.registerHandler("openImageTranslationFeedback",bp),We.registerHandler("openWebTranslationFeedback",yp),_v(),li.sendMessages({type:"bridgeReady"})}catch{}}function _v(){li.handleMessages(async e=>{try{let{type:t,data:a}=e,n=null;if(t==="triggerClientTranslateImage")wp(a);else if(t==="queryImageTranslateState")n=Ap(a);else if(t==="notifyClientImageTranslatedResult")Ep(a);else if(t==="getUserInfo")n=await jv();else if(t==="getBaseInfo")n=await Lv();else if(t==="updatePageStatus")We.callHandler("updatePageStatus",a,r=>{});else return;li.sendMessages({type:t,id:e.id,data:n})}catch{}})}function jv(){return new Promise(e=>{We.callHandler("getUserInfo",{},t=>{t.data?e(t.data):e(null)})})}function Lv(){return new Promise(e=>{We.callHandler("getBaseInfo",{},t=>{t.data?e(t.data):e(null)})})}pe()||vu();async function Nv(){try{let e=await Hv(_o(),{});if(cp(),e.isTranslateExcludeUrl)return;Gv(e);let t=e.rule.subtitleRule;t&&t.type&&!t.disabled&&t.isInject&&(Ov(),Uv(e));let a=e.rule.imageRule;a?.enable&&a.type==="manga"&&zv(e),qv(e)}catch{}}function Ov(){if(pe())try{globalThis.trustedTypes.defaultPolicy=trustedTypes.createPolicy("default",{createHTML:e=>e,createScript:e=>e})}catch(e){X.error("breakTrustedTypes error",e)}}function zv(e){if(pe()){let a=ke().IMMERSIVE_TRANSLATE_IMAGE_INJECT,n=document.createElement("script");n.id="imt-image-inject",n.textContent=a,n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}else{let t=ie.runtime.getURL("image/inject.js");if(document.querySelector(`script[src='${t}']`))return;let n=document.createElement("script");n.src=t,n.id="imt-image-inject",n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}}function Uv(e){if(pe()){let a=ke().IMMERSIVE_TRANSLATE_VIDEO_SUBTITLE_INJECT,n=document.createElement("script");n.id="imt-subtitles-inject",n.textContent=a,n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}else{let t=ie.runtime.getURL("video-subtitle/inject.js");if(document.querySelector(`script[src='${t}']`))return;let n=document.createElement("script");n.src=t,n.setAttribute("async","true"),n.id="imt-subtitles-inject",document.head?.insertBefore(n,document.head?.firstChild)}}function qv(e){if(!(!Xt()&&!Zt()))if(pe()){vu();return}else{let t=ie.runtime.getURL("browser-bridge/inject.js");if(document.querySelector(`script[src='${t}']`))return;let n=document.createElement("script");n.src=t,n.id="imt-browser-bridge-inject",n.setAttribute("async","true"),document.head?.insertBefore(n,document.head?.firstChild)}}function Gv(e){_i.handleMessages(({id:t,type:a})=>{if(a==="getConfig"){let n=e.rule.subtitleRule;_i.sendMessages({id:t,data:n})}})}Nv();var Fn=null;async function Hv(e,t){let a=Object.keys(t);if(Fn){let n={url:e,config:Fn.config,state:{...Fn.state,...t}};Fn=await mu(n)}else{let n=await Kv(),r=t;a.length===0&&(r=void 0),Fn=await mu({url:e,config:n,state:r})}return Fn}function Kv(){return pe()?aa():Wv({method:"getConfig",data:{userAgent:globalThis.navigator.userAgent}})}async function Wv(e){return await iu().sendMessage("background:main",e)}})();
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
