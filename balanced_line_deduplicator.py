#!/usr/bin/env python3
"""
平衡的基于行的去重工具
更合理的策略：限制删除比例，保留足够的代码
"""

import os
import json
from typing import List, Dict, Set, Tuple

class BalancedLineDeduplicator:
    def __init__(self):
        self.files_content = {}
        self.files_lines = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件并按行分割"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    self.files_content[file_path] = content
                    self.files_lines[file_path] = lines
                    
                    filename = os.path.basename(file_path)
                    print(f"✓ 加载: {filename} ({len(content):,} 字符, {len(lines)} 行)")
    
    def find_reasonable_cut_point(self, max_remove_ratio: float = 0.6) -> Dict[str, int]:
        """查找合理的切割点，限制删除比例"""
        print(f"\n🔍 查找合理的依赖代码切割点 (最多删除{max_remove_ratio*100:.0f}%)...")
        
        files = list(self.files_lines.items())
        file_names = [os.path.basename(path) for path, _ in files]
        cut_points = {}
        
        for i, (file_path, lines) in enumerate(files):
            filename = file_names[i]
            print(f"\n  分析: {filename} ({len(lines)} 行)")
            
            max_remove_lines = int(len(lines) * max_remove_ratio)
            best_cut_point = 0
            
            # 在前60%的范围内查找连续三行重复
            search_end = min(max_remove_lines, len(lines) - 3)
            
            for start_line in range(0, search_end, 10):  # 每10行检查一次
                if start_line + 3 > len(lines):
                    break
                
                # 获取连续三行
                three_lines = lines[start_line:start_line + 3]
                
                # 跳过主要是空行或注释的三行
                non_empty_lines = [line.strip() for line in three_lines if line.strip() and not line.strip().startswith('//')]
                if len(non_empty_lines) < 2:
                    continue
                
                # 检查这三行是否在其他文件中存在
                found_in_others = 0
                for j, (other_path, other_lines) in enumerate(files):
                    if i == j:
                        continue
                    
                    # 在其他文件中查找这连续三行
                    for k in range(len(other_lines) - 2):
                        if other_lines[k:k+3] == three_lines:
                            found_in_others += 1
                            break
                
                # 如果在至少一半的其他文件中找到
                if found_in_others >= len(files) // 2:
                    best_cut_point = start_line + 3  # 从这三行之后开始保留
                    print(f"    ✓ 在第 {start_line+1}-{start_line+3} 行找到重复，切割点: {best_cut_point}")
                    break
            
            # 如果没找到合适的切割点，使用保守策略
            if best_cut_point == 0:
                # 查找明显的函数或对象定义开始
                for line_num, line in enumerate(lines[:max_remove_lines]):
                    stripped = line.strip()
                    # 查找可能的业务代码开始标志
                    if any(pattern in stripped for pattern in [
                        'function ', 'class ', 'const ', 'let ', 'var ',
                        '= {', '= function', '= class', '= async'
                    ]) and not any(skip in stripped for skip in [
                        'console.', 'debug', 'log', 'error'
                    ]):
                        best_cut_point = line_num
                        print(f"    📍 在第 {line_num+1} 行找到可能的业务代码开始")
                        break
                
                # 如果还是没找到，使用固定比例
                if best_cut_point == 0:
                    best_cut_point = min(len(lines) // 4, 1000)  # 删除前25%或前1000行
                    print(f"    🛡️  使用保守策略: 删除前 {best_cut_point} 行")
            
            cut_points[filename] = best_cut_point
            
            if best_cut_point > 0:
                dependency_lines = best_cut_point
                business_lines = len(lines) - best_cut_point
                print(f"    📊 将删除: {dependency_lines} 行 ({dependency_lines/len(lines)*100:.1f}%)")
                print(f"    📊 将保留: {business_lines} 行 ({business_lines/len(lines)*100:.1f}%)")
            else:
                print(f"    ⚠️  保留整个文件")
        
        return cut_points
    
    def find_duplicate_content_blocks(self) -> List[Dict]:
        """查找重复的内容块"""
        print(f"\n🔍 查找重复内容块...")
        
        duplicate_blocks = []
        files = list(self.files_content.items())
        file_names = [os.path.basename(path) for path, _ in files]
        
        # 查找大型字符串重复
        all_large_strings = []
        
        for file_path, content in files:
            filename = os.path.basename(file_path)
            
            # 查找大型字符串字面量
            for quote in ['`', '"', "'"]:
                i = 0
                while i < len(content):
                    if content[i] == quote:
                        start = i + 1
                        i += 1
                        
                        # 查找字符串结束
                        while i < len(content):
                            if content[i] == quote and (i == 0 or content[i-1] != '\\'):
                                string_content = content[start:i]
                                if len(string_content) >= 800:  # 800字符以上
                                    all_large_strings.append({
                                        'content': string_content,
                                        'size': len(string_content),
                                        'file': filename,
                                        'quote': quote
                                    })
                                break
                            i += 1
                    else:
                        i += 1
        
        # 查找重复的字符串
        processed_contents = set()
        for string_info in all_large_strings:
            content = string_info['content']
            if content in processed_contents:
                continue
            
            files_with_string = []
            for file_path, file_content in files:
                filename = os.path.basename(file_path)
                if content in file_content:
                    files_with_string.append(filename)
            
            if len(files_with_string) >= 2:
                duplicate_blocks.append({
                    'content': content,
                    'size': len(content),
                    'files': files_with_string,
                    'quote': string_info['quote'],
                    'type': 'string'
                })
                processed_contents.add(content)
        
        # 按大小排序
        duplicate_blocks.sort(key=lambda x: x['size'], reverse=True)
        
        print(f"  📊 找到 {len(duplicate_blocks)} 个重复内容块")
        for i, block in enumerate(duplicate_blocks[:5]):
            print(f"    {i+1}. {block['size']:,}字符, {len(block['files'])}个文件")
        
        return duplicate_blocks
    
    def apply_balanced_removal(self, cut_points: Dict[str, int], 
                             duplicate_blocks: List[Dict]) -> Dict:
        """应用平衡的移除策略"""
        print(f"\n🛠️  应用平衡移除策略...")
        
        os.makedirs("balanced_dedup", exist_ok=True)
        results = {}
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            lines = self.files_lines[file_path]
            print(f"\n  处理: {filename}")
            
            cut_point = cut_points.get(filename, 0)
            operations = []
            total_removed_chars = 0
            
            # 策略1: 根据切割点移除前置代码
            if cut_point > 0:
                business_lines = lines[cut_point:]
                cleaned_content = '\n'.join(business_lines)
                
                dependency_content = '\n'.join(lines[:cut_point])
                removed_chars = len(dependency_content)
                total_removed_chars += removed_chars
                operations.append(f"前置依赖: -{cut_point}行 ({removed_chars:,}字符)")
            else:
                cleaned_content = original_content
            
            # 策略2: 移除重复内容块
            for i, block in enumerate(duplicate_blocks[:10]):  # 最多处理10个
                if filename in block['files']:
                    quote = block['quote']
                    full_string = f"{quote}{block['content']}{quote}"
                    
                    if full_string in cleaned_content:
                        old_size = len(cleaned_content)
                        # 替换为空字符串，保持引号
                        cleaned_content = cleaned_content.replace(full_string, f"{quote}{quote}", 1)
                        removed = old_size - len(cleaned_content)
                        total_removed_chars += removed
                        operations.append(f"重复内容{i}: -{removed:,}字符")
            
            # 保存文件
            output_path = f"balanced_dedup/clean_{filename}"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed_chars,
                'cut_point': cut_point,
                'operations': operations,
                'output_path': output_path
            }
            
            print(f"    📊 {original_size:,} → {cleaned_size:,} 字符 ({compression_ratio:.1f}% 压缩)")
            for op in operations:
                print(f"      - {op}")
        
        return results
    
    def run(self) -> Dict:
        """运行平衡的基于行的去重"""
        print("🚀 平衡的基于行去重工具")
        print("💡 策略: 限制删除比例，保留足够的业务代码")
        print("=" * 60)
        
        # 步骤1: 查找合理的切割点
        cut_points = self.find_reasonable_cut_point(0.6)  # 最多删除60%
        
        # 步骤2: 查找重复内容块
        duplicate_blocks = self.find_duplicate_content_blocks()
        
        # 步骤3: 应用平衡移除
        results = self.apply_balanced_removal(cut_points, duplicate_blocks)
        
        return results, cut_points, duplicate_blocks

def main():
    deduplicator = BalancedLineDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("🎯 平衡的基于行去重工具")
    print("⚖️  平衡策略：保留足够的业务代码")
    print("🔒 限制删除比例，避免过度压缩")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("❌ 需要至少2个文件进行比较")
        return
    
    # 运行平衡去重
    results, cut_points, duplicate_blocks = deduplicator.run()
    
    # 保存报告
    summary = {
        'cut_points': cut_points,
        'duplicate_blocks_count': len(duplicate_blocks),
        'file_results': {k: {
            'original_kb': round(v['original_size']/1024, 1),
            'cleaned_kb': round(v['cleaned_size']/1024, 1),
            'compression_pct': round(v['compression_ratio'], 1),
            'cut_point': v['cut_point'],
            'operations': v['operations']
        } for k, v in results.items()}
    }
    
    with open('balanced_dedup_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("✅ 平衡去重完成！")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"📊 平衡去重效果:")
    print(f"   原始大小: {total_original/1024:.1f} KB")
    print(f"   清理后: {total_cleaned/1024:.1f} KB")
    print(f"   压缩比例: {overall_compression:.1f}%")
    print(f"   找到重复块: {len(duplicate_blocks)} 个")
    
    print(f"\n📁 输出文件:")
    print(f"   清理后的JS: balanced_dedup/clean_*.js")
    print(f"   详细报告: balanced_dedup_summary.json")
    
    print(f"\n🎯 平衡策略效果:")
    if overall_compression >= 30:
        print(f"   🎉 平衡策略效果优秀！")
    elif overall_compression >= 15:
        print(f"   ✅ 平衡策略效果良好")
    elif overall_compression >= 5:
        print(f"   ⚠️  平衡策略有一定效果")
    else:
        print(f"   ❌ 平衡策略效果有限")
    
    print(f"\n💡 平衡策略优势:")
    print(f"   - 限制删除比例，避免过度压缩")
    print(f"   - 保留足够的代码用于逆向分析")
    print(f"   - 运行速度快，基于行匹配")
    print(f"   - 可调整删除比例参数")

if __name__ == "__main__":
    main()
