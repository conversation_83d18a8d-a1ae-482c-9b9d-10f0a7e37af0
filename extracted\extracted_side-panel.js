(()=>{var $2=Object.defineProperty;var Wd=(e,t)=>{for(var a in t)$2(e,a,{get:t[a],enumerable:!0})};var P={BUILD_TIME:"2025-07-25T09:38:29.000Z",VERSION:"1.19.6",PROD:"1",PROD_API:"1",BETA:"0",MOCK:"0",DEBUG:"0",INSTALL_FROM:"chrome_zip",IMMERSIVE_TRANSLATE_INJECTED_CSS:""IMMERSIVE_TRANSLATE_INPUT_INJECTED_CSS:""IMMERSIVE_TRANSLATE_PICO_CSS:""IMMERSIVE_TRANSLATE_COMMON_CSS:""IMMERSIVE_TRANSLATE_POPUP_CSS:""IMMERSIVE_TRANSLATE_PAGE_POPUP_CSS:"",IMMERSIVE_TRANSLATE_IMAGE_INJECT:""IMMERSIVE_TRANSLATE_IMAGE_TOOLS_CSS:""IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS:""IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS:"",,,,,};var ty=Object.create,Ru=Object.defineProperty,ay=Object.getOwnPropertyDescriptor,Yd=Object.getOwnPropertyNames,ny=Object.getPrototypeOf,ry=Object.prototype.hasOwnProperty,iy=(e,t)=>function(){return t||(0,e[Yd(e)[0]])((t={exports:{}}).exports,t),t.exports},oy=(e,t)=>{for(var a in t)Ru(e,a,{get:t[a],enumerable:!0})},Fu=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Yd(t))!ry.call(e,r)&&r!==a&&Ru(e,r,{get:()=>t[r],enumerable:!(n=ay(t,r))||n.enumerable});return e},sy=(e,t,a)=>(Fu(e,t,"default"),a&&Fu(a,t,"default")),Zd=(e,t,a)=>(a=e!=null?ty(ny(e)):{},Fu(t||!e||!e.__esModule?Ru(a,"default",{value:e,enumerable:!0}):a,e)),Jd=iy({"../esmd/npm/webextension-polyfill@0.10.0/node_modules/.pnpm/webextension-polyfill@0.10.0/node_modules/webextension-polyfill/dist/browser-polyfill.js"(e,t){(function(a,n){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],n);else if(typeof e<"u")n(t);else{var r={exports:{}};n(r),a.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:e,function(a){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let n="The message port closed before a response was received.""api-metadata.json has not been included in browser-polyfill");class s extends WeakMap{constructor(S,T=void 0){super(T),this.createItem=S}get(S){return this.has(S)||this.set(S,this.createItem(S)),super.get(S)}}let u=A=>A&&typeof A=="object"&&typeof A.then=="function",l=(A,S)=>(...T)=>{i.runtime.lastError?A.reject(new Error(i.runtime.lastError.message)):S.singleCallbackArg||T.length<=1&&S.singleCallbackArg!==!1?A.resolve(T[0]):A.resolve(T)},c=A=>A==1?"argument":"arguments""function")if(typeof S[F]=="function")O=d(A,A[F],S[F]);else if(y(T,F)){let E=m(F,T[F]);O=d(A,A[F],E)}else O=O.bind(A);else if(typeof O=="object"&&O!==null&&(y(S,F)||y(T,F)))O=h(O,S[F],T[F]);else if(y(T,"*"))O=h(O,S[F],T["*""function"?A:function(T){let k=h(T,{},{getContent:{minArgs:0,maxArgs:0}});A(k)}),g=new s(A=>typeof A!="function"?A:function(T,k,R){let j=!1,B,F=new Promise(M=>{B=function(w){j=!0,M(w)}}),L;try{L=A(T,k,B)}catch(M){L=Promise.reject(M)}let O=L!==!0&&u(L);if(L!==!0&&!O&&!j)return!1;let E=M=>{M.then(w=>{R(w)},w=>{let D;w&&(w instanceof Error||typeof w.message=="string")?D=w.message:D="An unexpected error occurred""sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:p.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},C={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return o.privacy={network:{"*":C},services:{"*":C},websites:{"*":C}},h(i,f,o)};a.exports=r(chrome)}else a.exports=globalThis.browser})}}),Xd={};oy(Xd,{default:()=>Ao});var uy=Zd(Jd());sy(Xd,Zd(Jd()));var{default:Qd,...ly}=uy,Ao=Qd!==void 0?Qd:ly;globalThis.immersiveTranslateBrowserAPI=Ao;var Mo,Se,nm,cy,ti,$d,rm,Eo={},im=[],dy=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function mn(e,t){for(var a in t)e[a]=t[a];return e}function om(e){var t=e.parentNode;t&&t.removeChild(e)}function sm(e,t,a){var n,r,i,o={};for(i in t)i=="key"?n=t[i]:i=="ref"?r=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?Mo.call(arguments,2):a),typeof e=="function""function""boolean"?null:typeof y=="string"||typeof y=="number"||typeof y=="bigint"?So(null,y,null,null,y):Array.isArray(y)?So(St,{children:y},null,null,null):y.__b>0?So(y.type,y.props,y.key,y.ref?y.ref:null,y.__v):y)!=null){if(y.__=a,y.__b=a.__b+1,(d=g[c])===null||d&&y.key==d.key&&y.type===d.type)g[c]=void 0;else for(m=0;m<b;m++){if((d=g[m])&&y.key==d.key&&y.type===d.type){g[m]=void 0;break}d=null}_u(e,y,d=d||Eo,r,i,o,s,u,l),h=y.__e,(m=y.ref)&&d.ref!=m&&(x||(x=[]),d.ref&&x.push(d.ref,null,y),x.push(m,y.__c||h,y)),h!=null?(v==null&&(v=h),typeof y.type=="function"&&y.__k===d.__k?y.__d=u=cm(y,u,e):u=dm(e,y,d,g,h,u),typeof a.type=="function"&&(a.__d=u)):u&&d.__e==u&&u.parentNode!=e&&(u=mr(d))}for(a.__e=v,c=b;c--;)g[c]!=null&&(typeof a.type=="function"&&g[c].__e!=null&&g[c].__e==a.__d&&(a.__d=mr(n,c+1)),gm(g[c],g[c]));if(x)for(c=0;c<x.length;c++)pm(x[c],x[++c],x[++c])}function cm(e,t,a){for(var n,r=e.__k,i=0;r&&i<r.length;i++)(n=r[i])&&(n.__=e,t=typeof n.type=="function"?cm(n,t,a):dm(a,n,n,r,n.__e,t));return t}function dm(e,t,a,n,r,i){var o,s,u;if(t.__d!==void 0)o=t.__d,t.__d=void 0;else if(a==null||r!=i||r.parentNode==null)e:if(i==null||i.parentNode!==e)e.appendChild(r),o=null;else{for(s=i,u=0;(s=s.nextSibling)&&u<n.length;u+=2)if(s==r)break e;e.insertBefore(r,i),o=i}return o!==void 0?o:r.nextSibling}function my(e,t,a,n,r){var i;for(i in a)i==="children"||i==="key"||i in t||Do(e,i,null,a[i],n);for(i in t)r&&typeof t[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||a[i]===t[i]||Do(e,i,t[i],a[i],n)}function em(e,t,a){t[0]==="-"?e.setProperty(t,a):e[t]=a==null?"":typeof a!="number"||dy.test(t)?a:a+"px"}function Do(e,t,a,n,r){var i;e:if(t==="style")if(typeof a=="string")e.style.cssText=a;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)a&&t in a||em(e.style,t,"");if(a)for(t in a)n&&a[t]===n[t]||em(e.style,t,a[t])}else if(t[0]==="o"&&t[1]==="n")i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=a,a?n||e.addEventListener(t,i?am:tm,i):e.removeEventListener(t,i?am:tm,i);else if(t!=="dangerouslySetInnerHTML"){if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=a??"";break e}catch{}typeof a=="function"||(a!=null&&(a!==!1||t[0]==="a"&&t[1]==="r")?e.setAttribute(t,a):e.removeAttribute(t))}}function tm(e){this.l[e.type+!1](Se.event?Se.event(e):e)}function am(e){this.l[e.type+!0](Se.event?Se.event(e):e)}function _u(e,t,a,n,r,i,o,s,u){var l,c,m,d,y,h,v,x,g,b,p,f,C,A=t.type;if(t.constructor!==void 0)return null;a.__h!=null&&(u=a.__h,s=t.__e=a.__e,t.__h=null,i=[s]),(l=Se.__b)&&l(t);try{e:if(typeof A=="function"){if(x=t.props,g=(l=A.contextType)&&n[l.__c],b=l?g?g.props.value:l.__:n,a.__c?v=(c=t.__c=a.__c).__=c.__E:("prototype""prototype""svg"&&(r=!0),i!=null){for(;h<i.length;h++)if((u=i[h])&&"setAttribute"in u==!!y&&(y?u.localName===y:u.nodeType===3)){e=u,i[h]=null;break}}if(e==null){if(y===null)return document.createTextNode(d);e=r?document.createElementNS("http://www.w3.org/2000/svg",y):document.createElement(y,d.is&&d),i=null,s=!1}if(y===null)m===d||s&&e.data===d||(e.data=d);else{if(i=i&&Mo.call(e.childNodes),l=(m=a.props||Eo).dangerouslySetInnerHTML,c=d.dangerouslySetInnerHTML,!s){if(i!=null)for(m={},h=0;h<e.attributes.length;h++)m[e.attributes[h].name]=e.attributes[h].value;(c||l)&&(c&&(l&&c.__html==l.__html||c.__html===e.innerHTML)||(e.innerHTML=c&&c.__html||""))}if(my(e,d,m,r,s),c)t.__k=[];else if(h=t.props.children,lm(e,Array.isArray(h)?h:[h],t,a,n,r&&y!=="foreignObject",i,o,i?i[0]:a.__k&&mr(a,0),s),i!=null)for(h=i.length;h--;)i[h]!=null&&om(i[h]);s||("value"in d&&(h=d.value)!==void 0&&(h!==e.value||y==="progress"&&!h||y==="option"&&h!==m.value)&&Do(e,"value",h,m.value,!1),"checked"in d&&(h=d.checked)!==void 0&&h!==e.checked&&Do(e,"checked",h,m.checked,!1))}return e}function pm(e,t,a){try{typeof e=="function"?e(t):e.current=t}catch(n){Se.__e(n,a)}}function gm(e,t,a){var n,r;if(Se.unmount&&Se.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||pm(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(i){Se.__e(i,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(r=0;r<n.length;r++)n[r]&&gm(n[r],t,typeof e.type!="function");a||e.__e==null||om(e.__e),e.__=e.__e=e.__d=void 0}function gy(e,t,a){return this.constructor(e,a)}function Po(e,t,a){var n,r,i;Se.__&&Se.__(e,t),r=(n=typeof a=="function")?null:a&&a.__k||t.__k,i=[],_u(t,e=(!n&&a||t).__k=sm(St,null,[e]),r||Eo,Eo,t.ownerSVGElement!==void 0,!n&&a?[a]:r?null:t.firstChild?Mo.call(t.childNodes):null,i,!n&&a?a:r?r.__e:t.firstChild,n),mm(i,e)}function ai(e,t){var a={__c:t="__cC""function""function"||e.o||e.type===St?e.o||(e.o=e.__&&e.__.o?e.__.o:""):e.o=(e.__&&e.__.o?e.__.o:"""function";function yy(e){var t,a=function(){clearTimeout(n),Cm&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(a,100);Cm&&(t=requestAnimationFrame(a))}function Lo(e){var t=tt,a=e.__c;typeof a=="function"&&(e.__c=void 0,a()),tt=t}function Nu(e){var t=tt;e.__c=e.__(),tt=t}function Am(e,t){return!e||e.length!==t.length||t.some(function(a,n){return a!==e[n]})}function Sm(e,t){return typeof t=="function"?t(e):t}function Be(){return typeof process>"u"&&typeof Deno<"u"?Deno.env.toObject():P}var Ce=Be();function by(){return typeof location>"u"?!1:location.href.includes("side-panel")&&location.href.includes("extension://")}function be(e,t){return!t&&by()?!0:e&&globalThis?.document?.querySelector("meta[name=immersive-translate-options]")?!!globalThis.document?.getElementById("immersive-translate-manifest")?.value?.includes("_isUserscript"):Ce.IMMERSIVE_TRANSLATE_USERSCRIPT==="1"}function ca(){return Ce.PROD==="1"}function Ro(){return Ce.PROD_API==="1"}function Tt(){if(Ce.IMMERSIVE_TRANSLATE_SAFARI==="1")return!0;if(typeof globalThis.immersiveTranslateBrowserAPI<"u"&&globalThis.immersiveTranslateBrowserAPI.runtime&&globalThis.immersiveTranslateBrowserAPI.runtime.getManifest){let t=globalThis.immersiveTranslateBrowserAPI.runtime.getManifest();return!!(t&&t._isSafari)}else return!1}function ii(){return typeof Deno<"u"}var EA=Be().PROD==="1",kA=Be().PROD!=="1";function _n(){return Ce.IMMERSIVE_TRANSLATE_JSSDK==="1"}var oe="immersiveTranslate",jn="Immersive Translate",ge="immersive-translate",Mm="imt",vy="immersivetranslate";var He="immersivetranslate.com",xy=`https://config.${He}/`,oi=`https://app.${He}/`,ee=ca()||Ro()?`https://${He}/`:`https://test.${He}/`,Bo=`https://dash.${He}/`,Ue=ca()||Ro()?`https://api2.${He}/`:`https://test-api2.${He}/`,zu=ca()||Ro()?`https://ai.${He}/`:`https://test-ai.${He}/`,IA=`https://assets.${vy}.cn/`,_o=ee+"accounts/login?from=plugin",Uu=ee+"profile/",st=ee+"auth/pricing/",da=ee+"pricing/";Tt()&&(st=ee+"accounts/safari-iap/",da=ee+"accounts/safari-iap/");function Pm(e){e&&(ee=`https://test.${He}/`,Ue=`https://test-api2.${He}/`,_o=ee+"accounts/login?from=plugin",Uu=ee+"profile/",st=ee+"auth/pricing/",da=ee+"pricing/",Tt()&&(st=ee+"accounts/safari-iap/",da=ee+"accounts/safari-iap/"))}var jo=ca()?`https://onboarding.${He}/`:`https://test-onboarding.${He}/`,Im=`https://github.com/${ge}/${ge}/`,LA=`https://s.${He}/``${oe}Share`,QA=`${oe}ShowFloatBallGuide`,YA=`${oe}ShowPopupModalGuide`,ZA=oe+"DocumentMessageTempEnableSubtitleChanged",JA=oe+"DocumentMessageUpdateQuickButtonAiSubtitle",Bm=`${oe}ToggleMouseHoverTranslateDirectly`,XA=`${oe}ReqDraft`,$A=`${oe}ResDraft`,wy=`${oe}Container`,Ay=`${oe}SpecifiedContainer`,Wu="buildinConfig",si="localConfig";var _m="translateMangaMenuId";var Sy=`${oe}PageTranslatedStatus`,Ty=`${oe}MangaTranslatedStatus`,eS=`${oe}PageUrlChanged`,tS=`${oe}ReceiveCommand`,aS=oe+"LastUseMouseHoverTime",nS=oe+"LastUseInputTime",ta=oe+"LastUseManualTranslatePageTime",pr=`${oe}PopupReceiveMessage`,rS=oe+"DocumentMessageEventTogglePopup""@","#"];var jm=`
`,By=`${ge}-target-wrapper`,TS=`${ge}-pdf-target-container`,ES=`${ge}-target-inner`,kS=`${ge}-source-wrapper`,DS=`${ge}-target-translation-block-wrapper`,MS=`${ge}-root-translation-theme`,PS=`${oe}RootTranslationTheme`,IS=`${ge}-target-translation-vertical-block-wrapper`,LS=`${ge}-target-translation-pdf-block-wrapper`,FS=`${ge}-target-translation-pre-whitespace`,RS=`${ge}-target-translation-inline-wrapper``${ui}download-subtitle/`,lT=`${ui}pdf-pro/`,cT=`${ui}text/`;var Oo=ee+"docs/usage/";var Oy="G-BHWL0KMJB8",zy="7pr-olTJR6GKAjIW48UD0Q",Tm="G-MKMD9LWFTR",Em="sitc4WmvShWYwfU0dANM3Q",km="G-V5H2F5MJFJ",Dm="UBjpGOLISEaY5LVXNj3WvQ""LdgzvqcdlDvNLdxrJVtZqxMTKaIgExlL",Vm="0VmM83i2D1ICuYBf",mT=50*1e4,Km=`[${Mm}-ctx-divider]`,Uy=`${Mm}_context_preview`;var li="fullLocalUserConfig";var Uo=2147483647;var Wm="https://<EMAIL>/4506813369548800",pT=`${oe}_selection_update_params`,gT=`data-${ge}-subtitle-type`,hT=`data-${ge}-ai-subtitle-url`,fT=`data-${ge}-has-subtitle``\x1B[${e.join(";")}m`,close:`\x1B[${t}m`,regexp:new RegExp(`\\x1b\\[${t}m`,"g")}}function Wo(e,t){return Wy?`${t.open}${e.replace(t.regexp,t.open)}${t.close}``"+e+"` is not a valid argument for `n-gram``[${this.serviceName}] `+a("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${a("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${a("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${a("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${a("error.gemini.429")}<br/><br/> ${o}`:o=`${a("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${a("error.claude.403")}<br/><br/>${o}`:o=`${a("error.403")}<br/><br/>${o}`:this.status===400?o=`${a("error.400")}<br/><br/> ${o}`:this.status===502?o=`${a("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${a("error.subscriptionExpired")}<br/><br/> ${o}`,n="setting",r="configError",i=a("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${a("error.azure.401")}<br/><br/> ${o}`),{type:r,title:i,errMsg:o,action:n}}handleFetchError(t){let a=ut.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let n=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${a("mangaQuotaError.solvedTitle")}<br/></br>
        ${r.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${n("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((y,h)=>`${h+1}. ${y}`).join("<br/>")}``Exceeded max retry count (${a})``${a.split("base64_""",r="";return a&&a.length===3&&(n=a[1],r=a[2]),{mimeType:n,base64:r}}function br(){let e,t="pending",a=new Promise((n,r)=>{e={async resolve(i){await i,t="fulfilled",n(i)},reject(i){t="rejected",r(i)}}});return Object.defineProperty(a,"state",{get:()=>t}),Object.assign(a,e)}var yl=class extends Error{constructor(){super("Deadline"),this.name="DeadlineError"}};function as(e,t){let a=br(),n=setTimeout(()=>a.reject(new yl),t);return Promise.race([e,a]).finally(()=>clearTimeout(n))}function _t(e,t={}){let{signal:a,persistent:n}=t;return a?.aborted?Promise.reject(new DOMException("Delay was aborted.","AbortError")):new Promise((r,i)=>{let o=()=>{clearTimeout(u),i(new DOMException("Delay was aborted.","AbortError"))},u=setTimeout(()=>{a?.removeEventListener("abort",o),r()},e);if(a?.addEventListener("abort""string"&&bl.Space_Separator.test(e)},isIdStartChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||bl.ID_Start.test(e))},isIdContinueChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="\u200C"||e==="\u200D"||bl.ID_Continue.test(e))},isDigit(e){return typeof e=="string"&&/[0-9]/.test(e)},isHexDigit(e){return typeof e=="string"&&/[0-9A-Fa-f]/.test(e)}},xl,jt,Za,rs,fn,pa,gt,Al,fi,T9=function(e,t){xl=String(e),jt="start",Za=[],rs=0,fn=1,pa=0,gt=void 0,Al=void 0,fi=void 0;do gt=E9(),M9[jt]();while(gt.type!=="eof");return typeof t=="function"?Cl({"":fi},"",t):fi};function Cl(e,t,a){let n=e[t];if(n!=null&&typeof n=="object")if(Array.isArray(n))for(let r=0;r<n.length;r++){let i=String(r),o=Cl(n,i,a);o===void 0?delete n[i]:Object.defineProperty(n,i,{value:o,writable:!0,enumerable:!0,configurable:!0})}else for(let r in n){let i=Cl(n,r,a);i===void 0?delete n[r]:Object.defineProperty(n,r,{value:i,writable:!0,enumerable:!0,configurable:!0})}return a.call(e,t,n)}var Ee,Te,hi,Ya,Pe;function E9(){for(Ee="default",Te="",hi=!1,Ya=1;;){Pe=Ja();let e=Gp[Ee]();if(e)return e}}function Ja(){if(xl[rs])return String.fromCodePoint(xl.codePointAt(rs))}function K(){let e=Ja();return e===`
`?(fn++,pa=0):e?pa+=e.length:pa++,e&&(rs+=e.length),e}var Gp={default(){switch(Pe){case"	":case"\v":case"\f":case" ":case"\xA0":case"\uFEFF":case`
``
``
`:case"\r":throw We(K());case"\u2028":case"\u2029":break;case void 0:throw We(K())}Te+=K()},start(){switch(Pe){case"{":case"[":return Ke("punctuator",K())}Ee="value"},beforePropertyName(){switch(Pe){case"$":case"_":Te=K(),Ee="identifierName";return;case"\\":K(),Ee="identifierNameStartEscape";return;case"}":return Ke("punctuator",K());case'"':case"'":hi=K()==='"''${P9(e)}' at ${fn}:${pa}`)}function Gn(){return Sl(`JSON5: invalid end of input at ${fn}:${pa}`)}function qp(){return pa-=5,Sl(`JSON5: invalid identifier character at ${fn}:${pa}``f${e}``),this._outcomes[n]=this._outcomes[n]+1||1}}_updateSessionFromEvent(t,a){let n=!1,r=!1,i=a.exception&&a.exception.values;if(i){r=!0;for(let u of i){let l=u.mechanism;if(l&&l.handled===!1){n=!0;break}}}let o=t.status==="ok";(o&&t.errors===0||o&&n)&&(Ba(t,{...n&&{status:"crashed""number""environment"in t||(t.environment="environment"in a?n:"production""SDK not enabled, will not capture event."));let o=t.type==="transaction";return!o&&typeof i=="number"&&Math.random()>i?(this.recordDroppedEvent("sample_rate","error"),Pi(new $e(``[domain]` tag:** Apply strictly with absolute priority, ** overriding any other translation logic for the term**. (Use original Source Term if Source==Translation, else use listed Translation).\n    *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2D\u6587\uFF0C{{html_only}}\u4EC5\u8F93\u51FA\u7FFB\u8BD1\u3002\u5982\u679C\u67D0\u4E9B\u5185\u5BB9\u65E0\u9700\u7FFB\u8BD1\uFF08\u5982\u4E13\u6709\u540D\u8BCD\u3001\u4EE3\u7801\u7B49\uFF09\uFF0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u4E1C\u5317\u4EBA\u7684\u53E3\u543B\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u751F\u6D3B,\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA",multipleSystemPrompt:""},{id:"wyw2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u7CBE\u901A\u53E4\u6587\u7684\u5B66\u8005\uFF0C\u53EA\u8FD4\u56DE\u767D\u8BDD\u6587",prompt:`\u8BF7\u5C06\u6587\u8A00\u6587\u7528\u767D\u8BDD\u6587\u89E3\u91CA:

{{text}}`,multiplePrompt:`\u5C06\u4E0B\u9762 YAML \u683C\u5F0F\u4E2D\u6240\u6709\u7684 {{imt_source_field}} \u5B57\u6BB5\u4E2D\u7684\u6587\u8A00\u6587\u7FFB\u8BD1\u4E3A\u767D\u8BDD\u6587\uFF0C\u5E76\u5C06\u7FFB\u8BD1\u7ED3\u679C\u5199\u5728 {{imt_trans_field}} \u5B57\u6BB5\u4E2D

{{normal_result_yaml_example}}

\u5F00\u59CB\u7FFB\u8BD1:

{{yaml}}`,multipleSystemPrompt:""},{id:"auto2wyw",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u6587\u8A00\u6587\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u6587\u8A00\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are you"}
    }
  ],
  "contextual_analysis": "Analysis of the word''t ","n''m sorry, but I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``/g,"\\`")}${c}': '${(l.v||l.k).replace(/`/g,"\\`")}''"')&&i?.endsWith('"''/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}async signedRequest({secretId:t,secretKey:a,action:n,payload:r,service:i,version:o}){let s=new Date().toISOString(),u=Math.random().toString(36).slice(2),l={Action:n,Version:o,Format:"JSON",AccessKeyId:t,SignatureNonce:u,Timestamp:s,SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0"},m=(v=>{let g=Object.keys(v).sort().map(b=>`${this.encode(b)}=${this.encode(v[b])}`).join("&");return`POST&%2F&${this.encode(g)}`})(Object.assign({},l,r)),d=this.SHA1.b64_hmac(`${a}&`,m),y=new URLSearchParams(Object.assign({},l,{Signature:d})).toString(),h=await super.request({retry:this.retry,url:`https://${i}.aliyuncs.com?${y}``%${t.charCodeAt(0).toString(16).toUpperCase()}`)}catch{return""}},ed=e=>Object.keys(e).map(t=>{let a=e[t];if(typeof a>"u"||a===null)return;let n=$0(t);if(n)return Array.isArray(a)?`${n}=${a.map($0).sort().join(`&${n}=`)}`:`${n}=${$0(a)}``${rt.algorithm} Credential=${t.accessKeyId}/${r}`),n.push(`SignedHeaders=${this.signedHeaders()}`),n.push(`Signature=${await this.signature(t,a)}`),n.join(", ")}async getSignUrl(t,a){let n=this.getDateTime(a),r={...this.request.params},i=this.request.params,o=this.request.headers;t.sessionToken&&(r[rt.tokenHeader]=t.sessionToken),r[rt.dateHeader]=n,r[rt.notSignBody]="",r[rt.credential]=`${t.accessKeyId}/${this.credentialString(n)}``${this.canonicalHeaders()}
`),t.push(this.signedHeaders()),t.push(await this.hexEncodedBodyHash()),t.join(`
`)}canonicalHeaders(){let t=[];Object.keys(this.request.headers).forEach(n=>{t.push([n,this.request.headers[n]])}),t.sort((n,r)=>n[0].toLowerCase()<r[0].toLowerCase()?-1:1);let a=[];return t.forEach(n=>{let r=n[0].toLowerCase();if(this.isSignableHeader(r)){let i=n[1];if(typeof i>"u"||i===null||typeof i.toString!="function")throw new V(`Header ${r} contains invalid value`);a.push(`${r}:${this.canonicalHeaderValues(i.toString())}`)}}),a.join(`
``${rt.kDatePrefix}${t.secretKey}``
``Unsupported language: ${r}`);r=this.langMap.get(r);let i=await this.checkLang(n,a);if(!i)return{text:a,from:n,to:r};n=i;let o=this.handleRequest(a,n,r),s=await super.request(o);return{text:this.handleResponseText(s),from:n,to:r}}async translateList(t){if(!Object.keys(this.apiServiceConfig).length)throw new V("serivce id not found config");let{text:a,from:n,to:r}=t;if(!this.langMap.has(r))throw new V(`Unsupported language: ${r}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${n==="auto"?"":bd.get(n)+"-"}${bd.get(r)}`),a.forEach(l=>{o.append("text",l)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.toString()}``https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${a}``
`+a+`
`+o+`
`+i}}async getCanonicalRequest(t){let a=await bt(t),n="POST",r="/",i="",o=`content-type:application/json; charset=utf-8
host:hunyuan.tencentcloudapi.com
x-tc-action:`+this.action.toLowerCase()+`
`,s="content-type;host;x-tc-action";return{signedHeaders:s,canonicalRequest:n+`
`+r+`
`+i+`
`+o+`
`+s+`
`+a}}getDate(t){let a=t.getUTCFullYear(),n=("0"+(t.getUTCMonth()+1)).slice(-2),r=("0"+t.getUTCDate()).slice(-2);return`${a}-${n}-${r}``${Ue}silicon/get-token?deviceId=${this.deviceId}``${Ue}big-model/get-token?deviceId=${this.deviceId}``Bearer ${this.apiKey}`,...this.headerConfigs}),body:JSON.stringify(s,null,2),timeout:this.requestTimeout,retry:this.retry},l;try{return l=await this.rawRequest(u),{text:this.parseResponse(l),from:n,to:r}}catch(c){throw c}}};var e2=!1;async function yo(e,t=!1){if(be(!1,!0)||!e2&&!t)return null;try{let a=await Aw(e);return W.debug("server language detect:",a),a}catch(a){return W.debug("server language detect error",a),Ew(!1),null}}async function Aw(e,t=1500){let a=new Promise((i,o)=>{setTimeout(()=>o(new Error(`Timeout after ${t}ms`)),t)}),n=Fe({url:`https://lang-detect.${He}/api/predict/batch``${e}`});return}globalThis.open(`${e}`,"_blank")}catch{globalThis.open(`${e}`,"_blank")}return}try{if(!be(!1,!0)&&aa()){re.tabs.create({url:`${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`});return}globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,"_blank")}catch{globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,"_blank")}}async function Ww(){let{userValue:e,localValue:t}=await Eu("rewardCenterOpenTime");e||t||await ku("rewardCenterOpenTime",Date.now().toString())}function Bd({visible:e,onClose:t,ctx:a,refreshKey:n=0,setSettings:r}){Ww();let[i,o]=ve(!1),[s,u]=ve(!1),[l,c]=ve({}),[m,d]=ve(0),{t:y}=vt(),[h,v]=ve(""""}let T=async F=>{if(W.debug("Task start:",F.taskConfig.taskKey),be(!1,!0)&&F.taskConfig.taskKey==="translate_line_1")return;let L=await e4(),O=S(F.taskConfig.taskKey);if(!L){Rd(`${_o}&return_url=${encodeURIComponent(O)}``${E}${w}`:L==="pdf_token"?`${E}${M}`:E},j=F=>{let{taskConfig:L,taken:O,completed:E,enabled:M}=F,w=L.rewards[0],D=L.groupName==="\u9AD8\u7EA7\u4EFB\u52A1",U=E&&O,_=!M&&!E&&!O,q=y("rewardCenter.task.start"),z="reward-task-button",G=!1;return E&&!O?(q=y("rewardCenter.task.claim"),z+=" claim"):E&&O?(q=y("rewardCenter.task.claimed"),z+=" completed"):!M&&!E&&!O?(q=y("rewardCenter.task.start"),z+=" disabled"):D&&M&&!E&&!O&&(n4(F,g)||(q=y("rewardCenter.task.start"),G=!0)),I("div",{className:`reward-task-item ${_?"unavailable":""} ${U?"completed":""}`,children:[I("div",{className:"reward-task-content",children:[I("div",{className:"reward-task-title",children:y(`rewardCenter.task.${L.taskKey}`)}),w&&I("div",{className:"reward-task-reward",children:[y("rewardCenter.reward.get"),"  ",I("span",{className:`${D?"reward-amount-advanced":"reward-amount"}`,children:R(w.rewardAmount,w.rewardType)})," ",k(w.rewardType,!0)]})]}),U?I(C2,{}):be(!1,!0)&&L.taskKey==="translate_line_1"?I(xo,{text:y("rewardCenter.task.translate_line_1.warning",{1:{tag:"a",style:"color: #EA4C89;",href:Qu+"?utm_campaign=reward_center",target:"_blank"}}),position:"left",tipStyle:{width:"150px",maxWidth:"150px",whiteSpace:"normal",wordBreak:"break-word"},children:I("button",{className:`${z} disabled``completed-tasks-arrow ${l[F.groupName]?"expanded":""}`,children:I(Rn,{})})]}),l[F.groupName]&&M.map(w=>j(w))]})]})]},F.groupName)};return!e&&!i?null:I(St,{children:I("div",{className:`reward-center-overlay ${s?"visible":""}`,onClick:C,children:I("div",{className:`reward-center-drawer ${s?"visible":""}``${L}%``${oi}text#${b.sourceLang}/${p}/${f}``action-icon${i?" bounce-animate":""}`,children:I(Cu,{})}),I("span",{className:"reward-center-text",children:r("rewardCenter.title")})]}),I("div",{className:"action-icon",onClick:()=>ei(Oo,"help_center"),children:I(xu,{})}),I("div",{className:"action-icon",onClick:()=>uu(!1,"",!1),children:I(b2,{style:{width:18,height:18},fillColor:"#999"})})]})]})]})}function ei(e,t){try{if(!be(!1,!0)&&aa()){re.tabs.create({url:`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`});return}globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,"_blank")}catch{globalThis.open(`${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}``popup received message: ${a}`,n||" ");let r;t.active&&(r=t.id,globalThis.document.dispatchEvent(new CustomEvent(pr,{detail:{tabId:r,payload:e}})))},Mu;function K2(){Xw()}function Xw(){return Mu||(Mu=new sn("popup",!1).getConnection("main_sync",Jw),Mu)}var W2=document.getElementById("mount");K2();W2&&(async()=>{let e=await wa();await Rf(),e.debug&&W.setLevel("debug"),Po(I(k0,{lang:e.interfaceLanguage,children:I(_d,{})}),W2)})();})();

