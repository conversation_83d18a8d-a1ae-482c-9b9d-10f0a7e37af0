f P.then=="function",a=(P,D)=>(...I)=>{o.runtime.lastError?P.reject(new Error(o.runtime.lastError.message)):D.singleCallbackArg||I.length<=1&&D.singleCallbackArg!==!1?P.resolve(I[0]):P.resolve(I)},d=P=>P==1?"argument":"arguments""function")if(typeof D[K]=="function")$=p(P,P[K],D[K]);else if(m(I,K)){let oe=c(K,I[K]);$=p(P,P[K],oe)}else $=$.bind(P);else if(typeof $=="object"&&$!==null&&(m(D,K)||m(I,K)
)?"":y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1),c&&(g=h=>{var w=new XMLHttpRequest;return w.open("GET",h,!1),w.responseType="arraybuffer",w.send(null),new Uint8Array(w.response)}),m=(h,w,A)=>{var C=new XMLHttpRequest;C.open("GET",h,!0),C.responseType="arraybuffer",C.onload=()=>{C.status==200||C.status==0&&C.response?w(C.response):A()},C.onerror=A,C.send(null)});var S,B=void 0,P=void 0,D=B
t supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = imod(dim, 2);
      return modCoord == 0 ? frag.r : frag.g;
    }

    float getChannel(vec4 frag, vec2 innerDims) {
      ve
ion(I,E,z){let q=!1,j,K=new Promise(Pe=>{j=function(Z){q=!0,Pe(Z)}}),re;try{re=P(I,E,j)}catch(Pe){re=Promise.reject(Pe)}let $=re!==!0&&u(re);if(re!==!0&&!$&&!q)return!1;let oe=Pe=>{Pe.then(Z=>{z(Z)},Z=>{let le;Z&&(Z instanceof Error||typeof Z.message=="string")?le=Z.message:le="An unexpected error occurred""sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:y.bind(null,"sendMessage",{minArgs:2,m
,maxArgs:1},set:{minArgs:1,maxArgs:1}};return l.privacy={network:{"*":B},services:{"*":B},websites:{"*":B}},g(o,S,l)};n.exports=i(chrome)}else n.exports=globalThis.browser})}}),M0={};oT(M0,{default:()=>ts});var uT=_0(P0());sT(M0,_0(P0()));var{default:C0,...lT}=uT,ts=C0!==void 0?C0:lT;globalThis.immersiveTranslateBrowserAPI=ts;var{Deno:F0}=globalThis,cT=typeof F0?.noColor=="boolean"?F0.noColor:!0,dT=
(v2);
    return vec4( b1.r ^^ b2.r ,
                b1.g ^^ b2.g,
                b1.b ^^ b2.b,
                b1.a ^^ b2.a );
  }
  ``X${r}`),inputTypes:Array(e).fill(0),cacheHint:t}),Bm=(e,t,n,r)=>{let i=n[0].dims.slice();if(r>=i.length||r<-1*i.length)throw new Error("axis specified for concat doesn''t use matmul on the given tensors");let l=Ar(o.length),s=Fa(),{activationFunction:u,applyActiva
ode":`${l.code}`}),o}return hu(t)?Op(e,t):On(t)||Mo(t)?(o=q9(e,t,n,i),Br(o,{synthetic:!0}),o):(o=jp(e,t,n,r),jo(o,`${t}``ui.${n.name}`,message:r},{event:n.event,name:n.name,global:n.global})}return t}function K9(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:uw(e.level),message:gp(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${gp(e.args.slice(
=>{postMessage({type:t,err:o})});break}case"copy-from":{let{buffer:r}=n,i=Ps(r);postMessage({type:t,out:i});break}case"create":{let{model:r,options:i}=n;Qc(r,i).then(o=>{postMessage({type:t,out:o})},o=>{postMessage({type:t,err:o})});break}case"release":Yc(n),postMessage({type:t});break;case"run":{let{sessionId:r,inputIndices:i,inputs:o,outputIndices:l,options:s}=n;Jc(r,i,o,l,new Array(l.length).fill
)}},ry=new Dh}),zc=O(()=>{"use strict"}),ay={};Xi(ay,{default:()=>iy});var Rl,Ol,iy,zS=O(()=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-
d(P);else if(typeof $=="object"&&$!==null&&(m(D,K)||m(I,K)))$=g($,D[K],I[K]);else if(m(I,"*"))$=g($,D[K],I["*""function"?P:function(I){let E=g(I,{},{getContent:{minArgs:0,maxArgs:0}});P(E)}),b=new s(P=>typeof P!="function"?P:function(I,E,z){let q=!1,j,K=new Promise(Pe=>{j=function(Z){q=!0,Pe(Z)}}),re;try{re=P(I,E,j)}catch(Pe){re=Promise.reject(Pe)}let $=re!==!0&&u(re);if(re!==!0&&!$&&!q)return!1;let
?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.buffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign
se strict";jS(),LS(),Dh=class{async init(){}async createInferenceSessionHandler(e,t){let n=new ey(t);return typeof e=="string"?await n.loadModel(e):await n.loadModel(e),new ty(n)}},ry=new Dh}),zc=O(()=>{"use strict"}),ay={};Xi(ay,{default:()=>iy});var Rl,Ol,iy,zS=O(()=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.dat
sponse?w(C.response):A()},C.onerror=A,C.send(null)});var S,B=void 0,P=void 0,D=B,I=P;if(Object.assign(u,v),v=null,p){let h=function(w){try{var A=w.data,C=A.cmd;if(C==="load"){let M=[];self.onmessage=N=>M.push(N),self.startWorker=()=>{postMessage({cmd:"loaded"});for(let N of M)h(N);self.onmessage=h};for(let N of A.handlers)u[N]&&!u[N].proxy||(u[N]=(...pe)=>{postMessage({Za:"callHandler",kb:N,args:pe}
w TypeError(`Invalid shape: length ${r} is not allowed``cache should be type ${a.name}``GPU for rank ${e} is not yet supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = i
alue = getBias(${o});
  return ${c};
}`:`
float getBiasForMatmul() {
  ${e} coords = getOutputCoords();
  ${a}
  return getBias(coords.x);
}``coords.${t[y+c]}`),i[u-1]="i*2",i.join(", "),o=s.map((x,y)=>`coords.${t[y+p]}`),o[a-2]="i*2",o.join(", """;for(let r=0;r<t-2;r++)n+=`rc.${e[r]}, `;return n+=`rc.${e[t-2]}, i*2`,n}function OA(e,t){let n="";for(let r=0;r<t-2;r++)n+=`rc.${e[r]}, `;return n+=`i*2,
x'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``${l_}${e}.onnx`,[`Loading ${e}`,({text:r,progress:i})=>{}]),n&&await C1(e,n)),n}var cn={};chrome.runtime.onMessage.addListener((e,t,n)=>{if(e.target==="offscreen")switch(e.data.type){case"trigger":return d_(e.data),!1;case"state":return c_(n,e.data),!0;default:throw new Error("Unrecognized message:",e.type)}});function c_(e,t){let{url
n"?P:function(I,E,z){let q=!1,j,K=new Promise(Pe=>{j=function(Z){q=!0,Pe(Z)}}),re;try{re=P(I,E,j)}catch(Pe){re=Promise.reject(Pe)}let $=re!==!0&&u(re);if(re!==!0&&!$&&!q)return!1;let oe=Pe=>{Pe.then(Z=>{z(Z)},Z=>{let le;Z&&(Z instanceof Error||typeof Z.message=="string")?le=Z.message:le="An unexpected error occurred""sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:y.bind(null,"sendMessage",{m
return float(a < b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4( v1.r < v2.r ,
                v1.g < v2.g,
                v1.b < v2.b,
                v1.a < v2.a );
  }
  `,name:e,type:0}}function rA(){let e="and_";return{body:`
  float ${e}(float a, float b) {
    return float( bool(a) && bool(b) );
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    retur
",s="";if(n>2)for(let u=0;u<n-2;++u)s=s+`${t[u]},`;return`getA(${s}${r}),
          rEdge ? 0. : getA(${s}${o}),
          cEdge ? 0. : getA(${s}${i}),
          rEdge || cEdge ? 0. : getA(${s}${l})`}function LE(e,t,n,r){return e===0||e===1?"":`
    int r = ${t[e-2]};
    int c = ${t[e-1]};
    int rp1 = ${t[e-2]} + 1;
    int cp1 = ${t[e-1]} + 1;
    bool rEdge = rp1 >= ${r};
    bool cEdge = cp1 >= ${n};
 
che should be type ${a.name}``GPU for rank ${e} is not yet supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = imod(dim, 2);
      return modCoord == 0 ? frag.r : frag.g;
ckedInput = getA(${s});

       ${a.output} = vec4(getChannel(packedInput, ${u}), 0, 0, 0);
     }
   `;return{...ml,hasMain:!0,output:{dims:t.dims,type:t.type,textureType:0},shaderSource:d}},Tf=(e,t)=>({...ml,get:()=>Am(e,t)})}),Ef,ac,Af,Cs=O(()=>{"use strict";Dn(),Ef=class{constructor(e,t=1){if(t===1)this.internalFormat=e.R32F,this.format=e.RED,this.textureType=e.FLOAT,this.channelSize=t;else if(t
rowser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let r="The message port closed before a response was received.""api-metadata.json has not been included in browser-polyfill");class s extends WeakMap{constructor(D,I=void 0){super(I),this.createItem=D}get(D){return this.has(D)||this.set(D,this.createItem(D)),super.get(D)}}let u=P=>P&&typeof P=="object"&&typeof P.then=="function",a=
turn{...t,output:{dims:i,type:n[0].type,textureType:0},variables:[{name:"bias",type:"float",arrayLength:r.bias.length,data:r.bias},{name:"scale",type:"float",data:r.scale}],shaderSource:l}},Ig=(e,t,n)=>{let r={...Dg,cacheHint:n.cacheKey};return{...r,get:()=>Cg(e,r,t,n)}},_g=e=>{let t=[`float getBias(float bias[${e}], int channel) {`];for(let n=0;n<e;++n)n===0?t.push(`	if (channel == ${n}) { return b
pe:t,err:o})});break}case"release":Yc(n),postMessage({type:t});break;case"run":{let{sessionId:r,inputIndices:i,inputs:o,outputIndices:l,options:s}=n;Jc(r,i,o,l,new Array(l.length).fill(null),s).then(u=>{u.some(a=>a[3]!=="cpu")?postMessage({type:t,err:"Proxy does not support non-cpu tensor location."}):postMessage({type:t,out:u},Xc([...o,...u]))},u=>{postMessage({type:t,err:u})});break}case"end-profi
n(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})});break}case"copy-from":{let{buffer:r}=n,i=Ps(r);postMessage({type:t,out:i});break}case"create":{let{model:r,options:i}=n;Qc(r,i).then(o=>{postMessage({type:t,out:o})},o=>

    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r && b2.r ,
                b1.g && b2.g,
                b1.b && b2.b,
                b1.a && b2.a );
  }
  `,name:e,type:0}}function aA(){let e="or_";return{body:`
  float ${e}(float a, float b) {
    return float( bool(a) || bool(b) );
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return
s(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})});break}case"copy-
 n.filename||null}return null}function Eu(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?z9(t):null}catch{return!1&&fe.error(`Cannot extract url for event ${dr(e)}`),null}}function Lp(e,t){let n=Np(e,t),r={type:t&&t.name,value:V9(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function q9(e,t,n,r){let i={except
y,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.buffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign({},e),a=new Promise((h,w)=>{l=h,s=w}),d=typeof window=="object",c=typeof importScripts=="func
.exports,t),t.exports},oT=(e,t)=>{for(var n in t)Uu(e,n,{get:t[n],enumerable:!0})},zu=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of I0(t))!aT.call(e,i)&&i!==n&&Uu(e,i,{get:()=>t[i],enumerable:!(r=nT(t,i))||r.enumerable});return e},sT=(e,t,n)=>(zu(e,t,"default"),n&&zu(n,t,"default")),_0=(e,t,n)=>(n=e!=null?tT(rT(e)):{},zu(t||!e||!e.__esModule?Uu(n,"default",{value:e,enumerab
ape: length ${r} is not allowed``cache should be type ${a.name}``GPU for rank ${e} is not yet supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = imod(dim, 2);
      return modCoo
rror=A,C.send(null)});var S,B=void 0,P=void 0,D=B,I=P;if(Object.assign(u,v),v=null,p){let h=function(w){try{var A=w.data,C=A.cmd;if(C==="load"){let M=[];self.onmessage=N=>M.push(N),self.startWorker=()=>{postMessage({cmd:"loaded"});for(let N of M)h(N);self.onmessage=h};for(let N of A.handlers)u[N]&&!u[N].proxy||(u[N]=(...pe)=>{postMessage({Za:"callHandler",kb:N,args:pe})},N=="print"&&(D=u[N]),N=="pri
t});case"ml-tensor":return new tn({location:"ml-tensor",mlTensor:e.mlTensor,type:e.type,dims:t});default:throw new Error(`tensorReshape: tensor location ${e.location} is not supported`)}}}),tn,Ic=O(()=>{"use strict";ZT(),XT(),$T(),eE(),tn=class{constructor(e,t,n){Xh();let r,i;if(typeof e=="object"&&"location"in e)switch(this.dataLocation=e.location,r=e.type,i=e.dims,e.location){case"cpu-pinned":{let
rn{reason:r,category:i,quantity:t[n]}})}};function _9(e){let t="`beforeSend` method has to return `null` or a valid event.";if(ri(e))return e.then(n=>{if(!(On(n)||n===null))throw new ht(t);return n},n=>{throw new ht(`beforeSend rejected with ${n}``Sentry responded with status code ${c.statusCode} to sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&fe.error(`Cannot extract message for event ${dr(e)}`
* ${t[0]} + coords.z * ${t[1]} + coords.y;
  }
`}var Tm,Em,wf,GE=O(()=>{"use strict";He(),ct(),Oe(),Ra(),Tm=e=>({name:"Reshape (packed)",inputTypes:[2],inputNames:["A"],cacheHint:`${e}`}),Em=(e,t,n,r)=>{let i=t.dims,o=r,l="";for(let a=0;a<4;a++){let d="";switch(a){case 0:d="outputCoords = rc;";break;case 1:d="outputCoords = ivec3(rc.x, rc.y+1, rc.z);";break;case 2:d="outputCoords = ivec3(rc.x, rc.y,
er&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.buffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign({},e),a=new Promise((h,w)=>{l=h,s=w}),d=typeof window=="object",c=typeof importScripts=="function",p=c&&self.name=="em-pthread";u.mountExternalData=(h,w)=>{h.startsWith("./")&&(h=h.substring(2)),(u.Ua||(u.Ua=new Map)).set(h,w)}
sionHandler(e,t){let n=new ey(t);return typeof e=="string"?await n.loadModel(e):await n.loadModel(e),new ty(n)}},ry=new Dh}),zc=O(()=>{"use strict"}),ay={};Xi(ay,{default:()=>iy});var Rl,Ol,iy,zS=O(()=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>
sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&fe.error(`Cannot extract message for event ${dr(e)}`),[]}return[]}function N9(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function z9(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Eu(e){try{let t;try{t=e.exception.values[0]
)});break}case"release":Yc(n),postMessage({type:t});break;case"run":{let{sessionId:r,inputIndices:i,inputs:o,outputIndices:l,options:s}=n;Jc(r,i,o,l,new Array(l.length).fill(null),s).then(u=>{u.some(a=>a[3]!=="cpu")?postMessage({type:t,err:"Proxy does not support non-cpu tensor location."}):postMessage({type:t,out:u},Xc([...o,...u]))},u=>{postMessage({type:t,err:u})});break}case"end-profiling":Zc(n)
ent:",n)}):!1&&fe.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(n=>{let[r,i]=n.split(":");return{reason:r,category:i,quantity:t[n]}})}};function _9(e){let t="`beforeSend` method has to return `null` or a valid event.";if(ri(e))return e.then(n=>{if(!(On(n)||n===null))throw new ht(t);return n},n=>{throw new ht(`beforeSend rejected with ${n}``Sentr
typeOf,aT=Object.prototype.hasOwnProperty,iT=(e,t)=>function(){return t||(0,e[I0(e)[0]])((t={exports:{}}).exports,t),t.exports},oT=(e,t)=>{for(var n in t)Uu(e,n,{get:t[n],enumerable:!0})},zu=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of I0(t))!aT.call(e,i)&&i!==n&&Uu(e,i,{get:()=>t[i],enumerable:!(r=nT(t,i))||r.enumerable});return e},sT=(e,t,n)=>(zu(e,t,"default"),n&&zu(n,t
Map{constructor(D,I=void 0){super(I),this.createItem=D}get(D){return this.has(D)||this.set(D,this.createItem(D)),super.get(D)}}let u=P=>P&&typeof P=="object"&&typeof P.then=="function",a=(P,D)=>(...I)=>{o.runtime.lastError?P.reject(new Error(o.runtime.lastError.message)):D.singleCallbackArg||I.length<=1&&D.singleCallbackArg!==!1?P.resolve(I[0]):P.resolve(I)},d=P=>P==1?"argument":"arguments""function
===1?"":`
    int r = ${t[e-2]};
    int c = ${t[e-1]};
    int rp1 = ${t[e-2]} + 1;
    int cp1 = ${t[e-1]} + 1;
    bool rEdge = rp1 >= ${r};
    bool cEdge = cp1 >= ${n};
    `}var pl,xm,xf,NE=O(()=>{"use strict";ct(),Oe(),ar(),Ra(),pl={name:"pack",inputNames:["A"],inputTypes:[1]},xm=(e,t)=>{let n=Re(e.session.backend.glContext.version),r=t.dims,i=r.length,o=t.dims.length,l=Ar(o),s=Fc("rc""b","r"
(()=>{var $1=Object.defineProperty;var qr=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.app
=Re(e.session.backend.glContext.version),r=t.dims,i=r.length,o=t.dims.length,l=Ar(o),s=Fc("rc""b","r","c"],r="index";return`
    ivec3 inputCoordsFromReshapedOutCoords(int index) {
      ${t.map((i,o)=>{let l=`int ${n[o]} = ${r} / ${i}`,s=o===t.length-1?`int ${n[o+1]} = ${r} - ${n[o]} * ${i}`:`index -= ${n[o]} * ${i}`;return`${l}; ${s};`}).join("")}
      return ivec3(b, r, c);
    }
  `}function qE
.set(t[r],e[r]);return n}initializeOps(e){let t=e.getNodes();this._ops=new Array(t.length);for(let n=0;n<t.length;n++)this._ops[n]=this.sessionHandler.resolve(t[n],this._model.opsets,e)}}}),ty,LS=O(()=>{"use strict""use strict";jS(),LS(),Dh=class{async init(){}async createInferenceSessionHandler(e,t){let n=new ey(t);return typeof e=="string"?await n.loadModel(e):await n.loadModel(e),new ty(n)}},ry=n
=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{postMessage({type:t
://test-ai.${wt}/`,M_=`https://assets.${mT}.cn/`,z0=W+"accounts/login?from=plugin",Wu=W+"profile/",Tt=W+"auth/pricing/",er=W+"pricing/";gn()&&(Tt=W+"accounts/safari-iap/",er=W+"accounts/safari-iap/");function U0(e){e&&(W=`https://test.${wt}/`,Vu=`https://test-api2.${wt}/`,z0=W+"accounts/login?from=plugin",Wu=W+"profile/",Tt=W+"auth/pricing/",er=W+"pricing/",gn()&&(Tt=W+"accounts/safari-iap/",er=W+"accounts/s
&(c?y=self.location.href:typeof document<"u"&&document.currentScript&&(y=document.currentScript.src),jl&&(y=jl),y=y.startsWith("blob:")?"":y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1),c&&(g=h=>{var w=new XMLHttpRequest;return w.open("GET",h,!1),w.responseType="arraybuffer",w.send(null),new Uint8Array(w.response)}),m=(h,w,A)=>{var C=new XMLHttpRequest;C.open("GET",h,!0),C.responseType="array
nmountExternalData=()=>{delete u.Ua};var m,g,f=globalThis.SharedArrayBuffer??new WebAssembly.Memory({initial:0,maximum:0,shared:!0}).buffer.constructor,v=Object.assign({},u),b="./this.program",x=(h,w)=>{throw w},y="";(d||c)&&(c?y=self.location.href:typeof document<"u"&&document.currentScript&&(y=document.currentScript.src),jl&&(y=jl),y=y.startsWith("blob:")?"":y.substr(0,y.replace(/[?#].*/,"").lastI
prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var ZC=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function XC(e){return e==="http"||e==="https"}function ai(e,t=!1){let{host:n,path:r,pass:i,port:o,projectId:l,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${l}`}function $C(e){let t=ZC.exec(e);if(!t)throw n
`beforeSend rejected with ${n}``Sentry responded with status code ${c.statusCode} to sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&fe.error(`Cannot extract message for event ${dr(e)}`),[]}return[]}function N9(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function z9(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")ret
w=="object",c=typeof importScripts=="function",p=c&&self.name=="em-pthread";u.mountExternalData=(h,w)=>{h.startsWith("./")&&(h=h.substring(2)),(u.Ua||(u.Ua=new Map)).set(h,w)},u.unmountExternalData=()=>{delete u.Ua};var m,g,f=globalThis.SharedArrayBuffer??new WebAssembly.Memory({initial:0,maximum:0,shared:!0}).buffer.constructor,v=Object.assign({},u),b="./this.program",x=(h,w)=>{throw w},y="";(d||c)
lloc(8);t._OrtGetLastError(r,r+4);let i=t.HEAP32[r/4],o=t.HEAPU32[r/4+1],l=o?t.UTF8ToString(o):"";throw new Error(`${e} ERROR_CODE: ${i}, ERROR_MESSAGE: ${l}`)}finally{t.stackRestore(n)}}}),cy,qS=O(()=>{"use strict";La(),qc(),cy=e=>{let t=Lt(),n=0,r=[],i=e||{};try{if(e?.logSeverityLevel===void 0)i.logSeverityLevel=2;else if(typeof e.logSeverityLevel!="number"||!Number.isInteger(e.logSeverityLevel)||
lt:throw new Error("Unrecognized message:",e.type)}});function c_(e,t){let{urlHash:n}=t;cn[n]||(cn[n]={state:"extension_uploading"}),e({state:cn[n].state,errorMsg:cn[n].errorMsg,result:cn[n].result})}async function d_(e){let{mimeType:t,imgBuffer:n,urlHash:r}=e;(!cn[r]||cn[r].state=="error")&&(cn[r]={...cn[r],state:"extension_uploading",errorMsg:""});let i=Object.values(n),l=new Uint8Array(i).buffer;try{let s=aw
erfaceLanguage);if(this.status!==-999)return;let r=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${n("mangaQuotaError.solvedTitle")}<br/></br>
        ${i.map((l,s)=>`${s+1}. ${l}`).join("<br/>")}
        ``${r("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((m,g)=>`${g+1}. ${m}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",i=""
){}async createInferenceSessionHandler(e,t){let n=new ey(t);return typeof e=="string"?await n.loadModel(e):await n.loadModel(e),new ty(n)}},ry=new Dh}),zc=O(()=>{"use strict"}),ay={};Xi(ay,{default:()=>iy});var Rl,Ol,iy,zS=O(()=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm
(()=>{var $1=Object.defineProperty;var qr=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported'',IMMERSIVE_TRANSLATE_IMAGE_INJEC
[0].type==="SentryError"}catch{}return!1}function z9(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Eu(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?z9(t):null}catch{return!1&&fe.error(`Cannot extract url for event ${dr(e)}`),null}}function Lp(e,t){let n=Np(e,t),r={type:t&
e"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})});break}case"copy-from":{let{buffer:r}=n,i=Ps(r);postMessage({type:t,out:i});break}case"create":{let{model:r,options:i}=n;Qc(r,i).then(o=>{postM
;var uT=_0(P0());sT(M0,_0(P0()));var{default:C0,...lT}=uT,ts=C0!==void 0?C0:lT;globalThis.immersiveTranslateBrowserAPI=ts;var{Deno:F0}=globalThis,cT=typeof F0?.noColor=="boolean"?F0.noColor:!0,dT=!cT;function ns(e,t){return{open:`\x1B[${e.join(";")}m`,close:`\x1B[${t}m`,regexp:new RegExp(`\\x1b\\[${t}m`,"g")}}function rs(e,t){return dT?`${t.open}${e.replace(t.regexp,t.open)}${t.close}``https://config.${wt}/`
l})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.buffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign({},e),a=new Promise((h,w)=>{l=h,s=w}),d=typeof
):N&&I(`worker sent an unknown command ${N}`)},h.onerror=M=>{throw I(`worker sent an error! ${M.filename}:${M.lineno}: ${M.message}``
`);return h[0]=="Error"&&h.shift(),u0(h),Ta.$a=Zo(),Ta.bb=h,Ta.$a}function Q1(h,w,A){if(h>>>=0,w>>>=0,Ta.$a==h)var C=Ta.bb;else(C=Error().stack.toString().split(`
``
``wasm streaming compile failed: ${pe}``)})},kt=e=>{let t=Lt(),n=t.stackSave();try{let r=t.stackAlloc(8);t._Ort
+ v2;
  }
  `,name:e,type:0}}function ZE(){let e="div_";return{body:`
  float ${e}(float a, float b) {
    return a / b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 / v2;
  }
  `,name:e,type:0}}function XE(){let e="mul_";return{body:`
  float ${e}(float a, float b) {
    return a * b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 * v2;
  }
  `,name:e,type:0}}function $E(){let e="sub_";return{body:`
 P!="function"?P:function(I,E,z){let q=!1,j,K=new Promise(Pe=>{j=function(Z){q=!0,Pe(Z)}}),re;try{re=P(I,E,j)}catch(Pe){re=Promise.reject(Pe)}let $=re!==!0&&u(re);if(re!==!0&&!$&&!q)return!1;let oe=Pe=>{Pe.then(Z=>{z(Z)},Z=>{let le;Z&&(Z instanceof Error||typeof Z.message=="string")?le=Z.message:le="An unexpected error occurred""sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:y.bind(null,"sen
e.name),t.delete(e.name)}}});function JE(){let e="add_";return{body:`
  float ${e}(float a, float b) {
    return a + b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 + v2;
  }
  `,name:e,type:0}}function ZE(){let e="div_";return{body:`
  float ${e}(float a, float b) {
    return a / b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 / v2;
  }
  `,name:e,type:0}}function XE(){let e="mul_";return{body:`
ordDroppedEvent("event_processor",t.type||"error"),new ht("An event processor returned null, will not send event.");if(n.data&&n.data.__sentry__===!0||l||!i)return s;let a=i(s,n);return _9(a)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new ht("`beforeSend` returned `null`, will not send event.""Error while sending event:",n)}):!1&&fe.error("Transport disabled"
* v2;
  }
  `,name:e,type:0}}function $E(){let e="sub_";return{body:`
  float ${e}(float a, float b) {
    return a - b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 - v2;
  }
  `,name:e,type:0}}function eA(){let e="equal_";return{body:`
  float ${e}(float a, float b) {
    return float(a == b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4(equal(v1, v2));
  }
  `,name:e,type:0}}function tA(){let 
 ${e}(float a, float b) {
    return float(a > b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4( v1.r > v2.r ,
      v1.g > v2.g,
      v1.b > v2.b,
      v1.a > v2.a );
  }
  `,name:e,type:0}}function nA(){let e="less_";return{body:`
  float ${e}(float a, float b) {
    return float(a < b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4( v1.r < v2.r ,
                v1.g < v2.g,
               
ow new Error("Unrecognized message:",e.type)}});function c_(e,t){let{urlHash:n}=t;cn[n]||(cn[n]={state:"extension_uploading"}),e({state:cn[n].state,errorMsg:cn[n].errorMsg,result:cn[n].result})}async function d_(e){let{mimeType:t,imgBuffer:n,urlHash:r}=e;(!cn[r]||cn[r].state=="error")&&(cn[r]={...cn[r],state:"extension_uploading",errorMsg:""});let i=Object.values(n),l=new Uint8Array(i).buffer;try{let s=await
ion qE(e){let t=Ae.computeStrides(e);return`
  int getFlattenedIndex(ivec3 coords) {
    // reverse y, z order
    return coords.x * ${t[0]} + coords.z * ${t[1]} + coords.y;
  }
`}var Tm,Em,wf,GE=O(()=>{"use strict";He(),ct(),Oe(),Ra(),Tm=e=>({name:"Reshape (packed)",inputTypes:[2],inputNames:["A"],cacheHint:`${e}`}),Em=(e,t,n,r)=>{let i=t.dims,o=r,l="";for(let a=0;a<4;a++){let d="";switch(a){case 0
ge.addListener((e,t,n)=>{if(e.target==="offscreen")switch(e.data.type){case"trigger":return d_(e.data),!1;case"state":return c_(n,e.data),!0;default:throw new Error("Unrecognized message:",e.type)}});function c_(e,t){let{urlHash:n}=t;cn[n]||(cn[n]={state:"extension_uploading"}),e({state:cn[n].state,errorMsg:cn[n].errorMsg,result:cn[n].result})}async function d_(e){let{mimeType:t,imgBuffer:n,urlHash:r}=e;(!cn
ype ${a.name}``GPU for rank ${e} is not yet supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
    float getChannel(vec4 frag, int dim) {
      int modCoord = imod(dim, 2);
      return modCoord == 0 ? frag.r : frag.g;
    }

    float getCha
cp1 = ${t[e-1]} + 1;
    bool rEdge = rp1 >= ${r};
    bool cEdge = cp1 >= ${n};
    `}var pl,xm,xf,NE=O(()=>{"use strict";ct(),Oe(),ar(),Ra(),pl={name:"pack",inputNames:["A"],inputTypes:[1]},xm=(e,t)=>{let n=Re(e.session.backend.glContext.version),r=t.dims,i=r.length,o=t.dims.length,l=Ar(o),s=Fc("rc""b","r","c"],r="index";return`
    ivec3 inputCoordsFromReshapedOutCoords(int index) {
      ${t.map
urn`
  int getFlattenedIndex(ivec3 coords) {
    // reverse y, z order
    return coords.x * ${t[0]} + coords.z * ${t[1]} + coords.y;
  }
`}var Tm,Em,wf,GE=O(()=>{"use strict";He(),ct(),Oe(),Ra(),Tm=e=>({name:"Reshape (packed)",inputTypes:[2],inputNames:["A"],cacheHint:`${e}`}),Em=(e,t,n,r)=>{let i=t.dims,o=r,l="";for(let a=0;a<4;a++){let d="";switch(a){case 0:d="outputCoords = rc;";break;case 1:d="
numerable:!0})},zu=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of I0(t))!aT.call(e,i)&&i!==n&&Uu(e,i,{get:()=>t[i],enumerable:!(r=nT(t,i))||r.enumerable});return e},sT=(e,t,n)=>(zu(e,t,"default"),n&&zu(n,t,"default")),_0=(e,t,n)=>(n=e!=null?tT(rT(e)):{},zu(t||!e||!e.__esModule?Uu(n,"default",{value:e,enumerable:!0}):n,e)),P0=iT({"../esmd/npm/webextension-polyfill@0.10.0/node
filing":Zc(n),postMessage({type:t});break;default:}}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(
ine-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``${l_}${e}.onnx`,[`Loading ${e}`,({text:r,progress:i})=>{}]),n&&await C1(e,n)),n}var cn={};chrome.runtime.onMessage.addListener((e,t,n)=>{if(e.target==="offscreen")switch(e.data.type){case"trigger":return d_(e.data),!1;case"state":return c_(n,e.data),!0;de
{},Object.keys(t).map(n=>{let[r,i]=n.split(":");return{reason:r,category:i,quantity:t[n]}})}};function _9(e){let t="`beforeSend` method has to return `null` or a valid event.";if(ri(e))return e.then(n=>{if(!(On(n)||n===null))throw new ht(t);return n},n=>{throw new ht(`beforeSend rejected with ${n}``Sentry responded with status code ${c.statusCode} to sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&
=s({...n},r);!1&&s.id&&u===null&&fe.log(``${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function k9(e){return`${S9(e)}${e.projectId}/envelope/`}function D9(e,t){return Z3({sentry_key:e.publicKey,sentry_version:A9,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${n.name}`))}),t}var Ow="Not capturing exception because it''s not includ
id=="string""function""number""function")o(n);else{let u=s({...n},r);!1&&s.id&&u===null&&fe.log(``${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function k9(e){return`${S9(e)}${e.projectId}/envelope/`}function D9(e,t){return Z3({sentry_key:e.publicKey,sentry_version:A9,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${n.name}`))}),t}
pertyDescriptor,I0=Object.getOwnPropertyNames,rT=Object.getPrototypeOf,aT=Object.prototype.hasOwnProperty,iT=(e,t)=>function(){return t||(0,e[I0(e)[0]])((t={exports:{}}).exports,t),t.exports},oT=(e,t)=>{for(var n in t)Uu(e,n,{get:t[n],enumerable:!0})},zu=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of I0(t))!aT.call(e,i)&&i!==n&&Uu(e,i,{get:()=>t[i],enumerable:!(r=nT(t,i))||r
s",type:"float",arrayLength:r.bias.length,data:r.bias},{name:"scale",type:"float",data:r.scale}],shaderSource:l}},Ig=(e,t,n)=>{let r={...Dg,cacheHint:n.cacheKey};return{...r,get:()=>Cg(e,r,t,n)}},_g=e=>{let t=[`float getBias(float bias[${e}], int channel) {`];for(let n=0;n<e;++n)n===0?t.push(`	if (channel == ${n}) { return bias[${n}]; }`):n===e-1?t.push(`	else { return bias[${n}]; }`):t.push(`	else 
lobalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})});break}case"copy-from":{let{buffer:r}=n,i=Ps(r);post
unction(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.buffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign({},e),a=new Promise((h,w)=>{l=h,s=w}),d=typeof window=="object",c=typeof importScripts=="function",p=c&&self.name=="em-pthread";u.mountExte
hen(()=>{postMessage({type:t})},o=>{postMessage({type:t,err:o})});break}case"copy-from":{let{buffer:r}=n,i=Ps(r);postMessage({type:t,out:i});break}case"create":{let{model:r,options:i}=n;Qc(r,i).then(o=>{postMessage({type:t,out:o})},o=>{postMessage({type:t,err:o})});break}case"release":Yc(n),postMessage({type:t});break;case"run":{let{sessionId:r,inputIndices:i,inputs:o,outputIndices:l,options:s}=n;Jc
   }
  `}var Ra=O(()=>{"use strict";ar()});function OE(e,t,n){if(e===0)return"false";if(e===1)return`rc > ${t[0]}`;let r="";for(let i=e-2;i<e;i++)r+=`${n[i]} >= ${t[i-e+2]}`,i<e-1&&(r+="||");return r}function jE(e,t){let n=e.length;if(n===0)return"getA(), 0, 0, 0";if(n===1)return`getA(rc),
            rc + 1 >= ${e[0]} ? 0. : getA(rc + 1),
            0, 0`;let r="r, c",i="r, cp1",o="rp1, c",l="rp1, cp1",s="
``Invalid shape: ${r} is not an integer`);if(r<0||r>2147483647)throw new TypeError(`Invalid shape: length ${r} is not allowed``cache should be type ${a.name}``GPU for rank ${e} is not yet supported`)}function Fa(e=6){return["x","y","z","w","u","v"].slice(0,e)}var ar=O(()=>{"use strict";He()});function RE(e,t){return Fa(t).map(n=>`${e}.${n}`)}function Fc(e,t){return t===1?[e]:RE(e,t)}function eo(){return`
   
,i&&ur(i))for(o=i.split(/\s+/),u=0;u<o.length;u++)r.push(`.${o[u]}`);let d=["type","name","title","alt"];for(u=0;u<d.length;u++)l=d[u],s=n.getAttribute(l),s&&r.push(`[${l}="${s}"]`);return r.join("")}function W3(){try{return location.href}catch{return""}}var ht=class extends Error{constructor(n){super(n);this.message=n;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prot
cmd;if(C==="load"){let M=[];self.onmessage=N=>M.push(N),self.startWorker=()=>{postMessage({cmd:"loaded"});for(let N of M)h(N);self.onmessage=h};for(let N of A.handlers)u[N]&&!u[N].proxy||(u[N]=(...pe)=>{postMessage({Za:"callHandler",kb:N,args:pe})},N=="print"&&(D=u[N]),N=="printErr"&&(I=u[N]));j=A.wasmMemory,F(),z(A.wasmModule)}else if(C==="run"){Ou(A.pthread_ptr,0,0,1,0,0),lt(A.pthread_ptr),Sn(),Bt
tag!==void 0&&(o=_t(e.tag,r)),n=t._OrtCreateRunOptions(i.logSeverityLevel,i.logVerbosityLevel,!!i.terminate,o),n===0&&kt("Can't create run options."),e?.extra!==void 0&&_s(e.extra,"",new WeakSet,(l,s)=>{let u=_t(l,r),a=_t(s,r);t._OrtAddRunConfigEntry(n,u,a)!==0&&kt(`Can''t set a session config entry: 'deviceType' - ${l}.`)}}break;case"webgpu":if(i="JS",typeof r!="string"){let l=r;if(l?.preferredLayo
{mimeType:r,base64:i}}var z3=Object.prototype.toString;function hu(e){switch(z3.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Qn(e,Error)}}function _o(e,t){return z3.call(e)===`[object ${t}]``[${c[0]}="${c[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),i=n.className,i&&ur(i))for(o=i.split(/\s+/),u=0;u<o.length;u++)r.push(`.${o[u]}`);let d=["type","name
value += getBiasForMatmul();":"",p=d?`${Ab(l,s,t[2].dims,o,!1)}`:"""",l=n.length,s=r.length,u=s-l;s<2&&l>0?o="coords":o=n.map((p,m)=>`coords.${t[m+u]}`).join(", ");let a=Vn.getBroadcastDims(n,r).map(p=>`coords.${t[p+u]} = 0;`).join(`
`),d=Ae.size(n)===1,c="vec4(outputValue.xx, outputValue.yy)";return d&&(c="vec4(outputValue.x)"),i?`
vec4 getBiasForMatmul() {
  ${e} coords = getOutputCoords();
  ${a}
ostMessage({type:t,out:u},Xc([...o,...u]))},u=>{postMessage({type:t,err:u})});break}case"end-profiling":Zc(n),postMessage({type:t});break;default:}}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function 
etBias(coords.x);
}``coords.${t[y+c]}`),i[u-1]="i*2",i.join(", "),o=s.map((x,y)=>`coords.${t[y+p]}`),o[a-2]="i*2",o.join(", """;for(let r=0;r<t-2;r++)n+=`rc.${e[r]}, `;return n+=`rc.${e[t-2]}, i*2`,n}function OA(e,t){let n="";for(let r=0;r<t-2;r++)n+=`rc.${e[r]}, `;return n+=`i*2, rc.${e[t-1]}``int c[${t[2].dims.length}];`:"",p=t.length===3?"bcastIndices_C(indices, c);":"",m=t.length===3?"value += beta * _C(
Ke}-ai-subtitle-url`,IM=`data-${Ke}-has-subtitle``dims[${n}] must be an integer, got: ${r}`);if(r<0)throw new RangeError(`dims[${n}] must be a non-negative integer, got: ${r}`);t*=r}return t},ef=(e,t)=>{switch(e.location){case"cpu":return new tn(e.type,e.data,t);case"cpu-pinned":return new tn({location:"cpu-pinned",data:e.data,type:e.type,dims:t});case"texture":return new tn({location:"texture",text
_CENTER_CSS:"",,,,,};var tT=Object.create,Uu=Object.defineProperty,nT=Object.getOwnPropertyDescriptor,I0=Object.getOwnPropertyNames,rT=Object.getPrototypeOf,aT=Object.prototype.hasOwnProperty,iT=(e,t)=>function(){return t||(0,e[I0(e)[0]])((t={exports:{}}).exports,t),t.exports},oT=(e,t)=>{for(var n in t)Uu(e,n,{get:t[n],enumerable:!0})},zu=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")fo
modCoord = mod(innerDims, 2.);
      return modCoord.x == 0. ?
        (modCoord.y == 0. ? frag.r : frag.g) :
        (modCoord.y == 0. ? frag.b : frag.a);
    }
  `}var Ra=O(()=>{"use strict";ar()});function OE(e,t,n){if(e===0)return"false";if(e===1)return`rc > ${t[0]}`;let r="";for(let i=e-2;i<e;i++)r+=`${n[i]} >= ${t[i-e+2]}`,i<e-1&&(r+="||");return r}function jE(e,t){let n=e.length;if(n===0)return"getA()
pologically sort routines needed for shader.");t.add(e.name);let i=e.dependencies;if(i&&i.length>0)for(let o=0;o<i.length;++o)this.dfsTraverse(i[o],t,n,r);r.push(e),n.add(e.name),t.delete(e.name)}}});function JE(){let e="add_";return{body:`
  float ${e}(float a, float b) {
    return a + b;
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return v1 + v2;
  }
  `,name:e,type:0}}function ZE(){let e="div_";return{body:`
(()=>{var $1=Object.defineProperty;var qr=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,n)=>(typeof require<"u"?require:t)[n]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic r
n has not been included in browser-polyfill");class s extends WeakMap{constructor(D,I=void 0){super(I),this.createItem=D}get(D){return this.has(D)||this.set(D,this.createItem(D)),super.get(D)}}let u=P=>P&&typeof P=="object"&&typeof P.then=="function",a=(P,D)=>(...I)=>{o.runtime.lastError?P.reject(new Error(o.runtime.lastError.message)):D.singleCallbackArg||I.length<=1&&D.singleCallbackArg!==!1?P.res
t"||typeof t=="function")for(let i of I0(t))!aT.call(e,i)&&i!==n&&Uu(e,i,{get:()=>t[i],enumerable:!(r=nT(t,i))||r.enumerable});return e},sT=(e,t,n)=>(zu(e,t,"default"),n&&zu(n,t,"default")),_0=(e,t,n)=>(n=e!=null?tT(rT(e)):{},zu(t||!e||!e.__esModule?Uu(n,"default",{value:e,enumerable:!0}):n,e)),P0=iT({"../esmd/npm/webextension-polyfill@0.10.0/node_modules/.pnpm/webextension-polyfill@0.10.0/node_modu
a non-negative integer: ${p}`);let m=_t(c,r);t._OrtAddFreeDimensionOverride(n,m,p)!==0&&kt(`Can't set a free dimension override: ${c} - ${p}.`)}return i.extra!==void 0&&_s(i.extra,"",new WeakSet,(c,p)=>{let m=_t(c,r),g=_t(p,r);t._OrtAddSessionConfigEntry(n,m,g)!==0&&kt(``External buffer must be provided for input/output index ${i} when enableGraphCapture is true.``Unsupported data type: ${me}``f${e}
imeType:t,imgBuffer:n,urlHash:r}=e;(!cn[r]||cn[r].state=="error")&&(cn[r]={...cn[r],state:"extension_uploading",errorMsg:""});let i=Object.values(n),l=new Uint8Array(i).buffer;try{let s=await M1(t,l,u=>{cn[r].state=u,Ce.debug("imgState",u)});cn[r].result=s}catch(s){Ce.error(s),cn[r].state="error",cn[r].errorMsg=s.message}}})();


Jc(r,i,o,l,new Array(l.length).fill(null),s).then(u=>{u.some(a=>a[3]!=="cpu")?postMessage({type:t,err:"Proxy does not support non-cpu tensor location."}):postMessage({type:t,out:u},Xc([...o,...u]))},u=>{postMessage({type:t,err:u})});break}case"end-profiling":Zc(n),postMessage({type:t});break;default:}}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),o
.name}/${t.version}``Integration installed: ${n.name}`))}),t}var Ow="Not capturing exception because it''s not included in the random sample (sampling rate = ${o})`))):this._prepareEvent(t,n,r).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new ht("An event processor returned null, will not send event.");if(n.data&&n.data.__sentry__===!0||l||!i)return s;let a=i
K],I[K]);else if(m(I,"*"))$=g($,D[K],I["*""function"?P:function(I){let E=g(I,{},{getContent:{minArgs:0,maxArgs:0}});P(E)}),b=new s(P=>typeof P!="function"?P:function(I,E,z){let q=!1,j,K=new Promise(Pe=>{j=function(Z){q=!0,Pe(Z)}}),re;try{re=P(I,E,j)}catch(Pe){re=Promise.reject(Pe)}let $=re!==!0&&u(re);if(re!==!0&&!$&&!q)return!1;let oe=Pe=>{Pe.then(Z=>{z(Z)},Z=>{let le;Z&&(Z instanceof Error||typeof
+4);let i=t.HEAP32[r/4],o=t.HEAPU32[r/4+1],l=o?t.UTF8ToString(o):"";throw new Error(`${e} ERROR_CODE: ${i}, ERROR_MESSAGE: ${l}`)}finally{t.stackRestore(n)}}}),cy,qS=O(()=>{"use strict";La(),qc(),cy=e=>{let t=Lt(),n=0,r=[],i=e||{};try{if(e?.logSeverityLevel===void 0)i.logSeverityLevel=2;else if(typeof e.logSeverityLevel!="number"||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSev
map((i,o)=>{let l=`int ${n[o]} = ${r} / ${i}`,s=o===t.length-1?`int ${n[o+1]} = ${r} - ${n[o]} * ${i}`:`index -= ${n[o]} * ${i}`;return`${l}; ${s};`}).join("")}
      return ivec3(b, r, c);
    }
  `}function qE(e){let t=Ae.computeStrides(e);return`
  int getFlattenedIndex(ivec3 coords) {
    // reverse y, z order
    return coords.x * ${t[0]} + coords.z * ${t[1]} + coords.y;
  }
`}var Tm,Em,wf,GE=O(()=>{"us
[u]}`);let d=["type","name","title","alt"];for(u=0;u<d.length;u++)l=d[u],s=n.getAttribute(l),s&&r.push(`[${l}="${s}"]`);return r.join("")}function W3(){try{return location.href}catch{return""}}var ht=class extends Error{constructor(n){super(n);this.message=n;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var ZC=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]
wserAPI=ts;var{Deno:F0}=globalThis,cT=typeof F0?.noColor=="boolean"?F0.noColor:!0,dT=!cT;function ns(e,t){return{open:`\x1B[${e.join(";")}m`,close:`\x1B[${t}m`,regexp:new RegExp(`\\x1b\\[${t}m`,"g")}}function rs(e,t){return dT?`${t.open}${e.replace(t.regexp,t.open)}${t.close}``https://config.${wt}/`,__=`https://app.${wt}/`,W=$n()||is()?`https://${wt}/`:`https://test.${wt}/`,us=`https://dash.${wt}/`,Vu=$n()||
event ${dr(e)}`),null}}function Lp(e,t){let n=Np(e,t),r={type:t&&t.name,value:V9(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function q9(e,t,n,r){let i={exception:{values:[{type:Mo(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:`Non-Error ${r?"promise rejection":"exception"} captured with keys: ${X3(t)}``${s}: ${l.message}
erviceName}] ``
        ${n("mangaQuotaError.solvedTitle")}<br/></br>
        ${i.map((l,s)=>`${s+1}. ${l}`).join("<br/>")}
        ``${r("proQuotaError.solvedTitle")}
    <br/><br/>
    ${u.map((m,g)=>`${g+1}. ${m}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",i="";return n&&n.length===3&&(r=n[1],i=n[2]),{mimeType:r,base64:i}}var z3=Object.prototype.toString;function hu(e
e:t});break;default:}}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}functio
a(),{activationFunction:u,applyActivation:a}=to(n),d=t.length>2,c=d?"value += getBiasForMatmul();":"",p=d?`${Ab(l,s,t[2].dims,o,!1)}`:"""",l=n.length,s=r.length,u=s-l;s<2&&l>0?o="coords":o=n.map((p,m)=>`coords.${t[m+u]}`).join(", ");let a=Vn.getBroadcastDims(n,r).map(p=>`coords.${t[p+u]} = 0;`).join(`
`),d=Ae.size(n)===1,c="vec4(outputValue.xx, outputValue.yy)";return d&&(c="vec4(outputValue.x)"),i?
w(h)):N==="alert"?alert(`Thread ${M.threadId}: ${M.text}`):M.target==="setimmediate"?h.postMessage(M):N==="callHandler"?u[M.handler](...M.args):N&&I(`worker sent an unknown command ${N}`)},h.onerror=M=>{throw I(`worker sent an error! ${M.filename}:${M.lineno}: ${M.message}``
`);return h[0]=="Error"&&h.shift(),u0(h),Ta.$a=Zo(),Ta.bb=h,Ta.$a}function Q1(h,w,A){if(h>>>=0,w>>>=0,Ta.$a==h)var C=Ta.bb;els

    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    return vec4( b1.r || b2.r ,
                b1.g || b2.g,
                b1.b || b2.b,
                b1.a || b2.a );
  }
  `,name:e,type:0}}function iA(){let e="xor_";return{body:`
  float ${e}(float a, float b) {
    return float( bool(a) ^^ bool(b) );
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    bvec4 b1 = bvec4(v1);
    bvec4 b2 = bvec4(v2);
    retur
foreSend` returned `null`, will not send event.""Error while sending event:",n)}):!1&&fe.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(n=>{let[r,i]=n.split(":");return{reason:r,category:i,quantity:t[n]}})}};function _9(e){let t="`beforeSend` method has to return `null` or a valid event.";if(ri(e))return e.then(n=>{if(!(On(n)||n===null))
(${s});

       ${a.output} = vec4(getChannel(packedInput, ${u}), 0, 0, 0);
     }
   `;return{...ml,hasMain:!0,output:{dims:t.dims,type:t.type,textureType:0},shaderSource:d}},Tf=(e,t)=>({...ml,get:()=>Am(e,t)})}),Ef,ac,Af,Cs=O(()=>{"use strict";Dn(),Ef=class{constructor(e,t=1){if(t===1)this.internalFormat=e.R32F,this.format=e.RED,this.textureType=e.FLOAT,this.channelSize=t;else if(t===4)this.intern
vec4(getChannel(packedInput, ${u}), 0, 0, 0);
     }
   `;return{...ml,hasMain:!0,output:{dims:t.dims,type:t.type,textureType:0},shaderSource:d}},Tf=(e,t)=>({...ml,get:()=>Am(e,t)})}),Ef,ac,Af,Cs=O(()=>{"use strict";Dn(),Ef=class{constructor(e,t=1){if(t===1)this.internalFormat=e.R32F,this.format=e.RED,this.textureType=e.FLOAT,this.channelSize=t;else if(t===4)this.internalFormat=e.RGBA32F,this.format
ffer!=$.buffer&&F(),Ze}var l,s,u=Object.assign({},e),a=new Promise((h,w)=>{l=h,s=w}),d=typeof window=="object",c=typeof importScripts=="function",p=c&&self.name=="em-pthread";u.mountExternalData=(h,w)=>{h.startsWith("./")&&(h=h.substring(2)),(u.Ua||(u.Ua=new Map)).set(h,w)},u.unmountExternalData=()=>{delete u.Ua};var m,g,f=globalThis.SharedArrayBuffer??new WebAssembly.Memory({initial:0,maximum:0,sha
u.map((m,g)=>`${g+1}. ${m}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",i="";return n&&n.length===3&&(r=n[1],i=n[2]),{mimeType:r,base64:i}}var z3=Object.prototype.toString;function hu(e){switch(z3.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Qn(e,Error)}}function _o(e,t){return z3.call(e)===`[object ${t}]``[${c[0]}="${c
Rl,Ol,iy,zS=O(()=>{"use strict";yy(),La(),Os(),Rl="ort-wasm-proxy-worker",Ol=globalThis.self?.name===Rl,Ol&&(self.onmessage=e=>{let{type:t,in:n}=e.data;try{switch(t){case"init-wasm":Uc(n.wasm).then(()=>{Wc(n).then(()=>{postMessage({type:t})},r=>{postMessage({type:t,err:r})})},r=>{postMessage({type:t,err:r})});break;case"init-ep":{let{epName:r,env:i}=n;Kc(i,r).then(()=>{postMessage({type:t})},o=>{pos
}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$.buffer&&F(),Pe}function i(){return j.buffer!=$.buffer&&F(),Z}function o(){return j.bu
="Error"&&h.shift(),u0(h),Ta.$a=Zo(),Ta.bb=h,Ta.$a}function Q1(h,w,A){if(h>>>=0,w>>>=0,Ta.$a==h)var C=Ta.bb;else(C=Error().stack.toString().split(`
``
``wasm streaming compile failed: ${pe}``)})},kt=e=>{let t=Lt(),n=t.stackSave();try{let r=t.stackAlloc(8);t._OrtGetLastError(r,r+4);let i=t.HEAP32[r/4],o=t.HEAPU32[r/4+1],l=o?t.UTF8ToString(o):"";throw new Error(`${e} ERROR_CODE: ${i}, ERROR_MESSAGE: $
equest;return w.open("GET",h,!1),w.responseType="arraybuffer",w.send(null),new Uint8Array(w.response)}),m=(h,w,A)=>{var C=new XMLHttpRequest;C.open("GET",h,!0),C.responseType="arraybuffer",C.onload=()=>{C.status==200||C.status==0&&C.response?w(C.response):A()},C.onerror=A,C.send(null)});var S,B=void 0,P=void 0,D=B,I=P;if(Object.assign(u,v),v=null,p){let h=function(w){try{var A=w.data,C=A.cmd;if(C===
nds WeakMap{constructor(D,I=void 0){super(I),this.createItem=D}get(D){return this.has(D)||this.set(D,this.createItem(D)),super.get(D)}}let u=P=>P&&typeof P=="object"&&typeof P.then=="function",a=(P,D)=>(...I)=>{o.runtime.lastError?P.reject(new Error(o.runtime.lastError.message)):D.singleCallbackArg||I.length<=1&&D.singleCallbackArg!==!1?P.resolve(I[0]):P.resolve(I)},d=P=>P==1?"argument":"arguments""
ariables:[{name:"bias",type:"float",arrayLength:r.bias.length,data:r.bias},{name:"scale",type:"float",data:r.scale}],shaderSource:l}},Ig=(e,t,n)=>{let r={...Dg,cacheHint:n.cacheKey};return{...r,get:()=>Cg(e,r,t,n)}},_g=e=>{let t=[`float getBias(float bias[${e}], int channel) {`];for(let n=0;n<e;++n)n===0?t.push(`	if (channel == ${n}) { return bias[${n}]; }`):n===e-1?t.push(`	else { return bias[${n}]
rr:u})});break}case"end-profiling":Zc(n),postMessage({type:t});break;default:}}catch(r){postMessage({type:t,err:r})}}),iy=Ol?null:e=>new Worker(e??Ca,{type:"module",name:Rl})}),oy={};Xi(oy,{default:()=>sy});var jl,Ll,sy,US=O(()=>{"use strict";Ll=(jl=As.url,async function(e={}){function t(){return j.buffer!=$.buffer&&F(),$}function n(){return j.buffer!=$.buffer&&F(),oe}function r(){return j.buffer!=$
{super(n);this.message=n;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var ZC=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function XC(e){return e==="http"||e==="https"}function ai(e,t=!1){let{host:n,path:r,pass:i,port:o,projectId:l,protocol:s,publicKey:u}=e;return`${s}://${u}${t&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${l}`
ype:0}}function eA(){let e="equal_";return{body:`
  float ${e}(float a, float b) {
    return float(a == b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4(equal(v1, v2));
  }
  `,name:e,type:0}}function tA(){let e="greater_";return{body:`
  float ${e}(float a, float b) {
    return float(a > b);
  }
  vec4 ${e}(vec4 v1, vec4 v2) {
    return vec4( v1.r > v2.r ,
      v1.g > v2.g,
      v1.b > v2.b,
   
nnerDims) {
      vec2 modCoord = mod(innerDims, 2.);
      return modCoord.x == 0. ?
        (modCoord.y == 0. ? frag.r : frag.g) :
        (modCoord.y == 0. ? frag.b : frag.a);
    }
  `}var Ra=O(()=>{"use strict";ar()});function OE(e,t,n){if(e===0)return"false";if(e===1)return`rc > ${t[0]}`;let r="";for(let i=e-2;i<e;i++)r+=`${n[i]} >= ${t[i-e+2]}`,i<e-1&&(r+="||");return r}function jE(e,t){let n