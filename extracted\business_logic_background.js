getLanguage:Ju(e??a.targetLanguage,a.interfaceLanguage,!1,!0)}),o&&(r=`${Re(a.interfaceLanguage,"show-original")}`);let s=Vu(a.shortcuts.toggleTranslatePage);s&&(r+=`(${s})``updateUninstallUrl: ${t}``onInstalled reason: ${e.reason}``${e}/dark-32.png`,48:`${e}/dark-48.png`,64:`${e}/dark-64.png`,128:`${e}/dark-128.png`,256:`${e}/dark-256.png`}:{32:`${e}/32.png`,48:`${e}/48.png`,64:`${e}/64.png`,128:`${e}/128.p
targetLanguage,e.interfaceLanguage,!1,!0)});let r=Vu(e.shortcuts.toggleTranslatePage);r&&(n+=`(${r})`)}A.contextMenus.create({id:t.id,title:n,contexts:t.contexts,visible:a&&e.isShowContextMenu},()=>A.runtime.lastError)}catch(n){I.debug("create context menu error, it's ok!!",n,`menu id: ${t.id}`)}}}async function Ur({targetLanguage:e,text:t}){let a=await Ue(),n="toggleTranslatePage",r;if(t)zr=t,r=Re(a.inte
gleTranslatePage"){n=Re(e.interfaceLanguage,"browser.toggleTranslatePage",{targetLanguage:Ju(e.targetLanguage,e.interfaceLanguage,!1,!0)});let r=Vu(e.shortcuts.toggleTranslatePage);r&&(n+=`(${r})`)}A.contextMenus.create({id:t.id,title:n,contexts:t.contexts,visible:a&&e.isShowContextMenu},()=>A.runtime.lastError)}catch(n){I.debug("create context menu error, it's ok!!",n,`menu id: ${t.id}`)}}}async function Ur(
png`,64:`${e}/64.png`,128:`${e}/128.png`,256:`${e}/256.png``content_script:main:${a}`,e).catch(r=>{I.error("send content message request failed",e,r)})}function Ag(){typeof A.commands<"u"&&A.commands.onCommand.addListener(async(e,t)=>{if(I.debug(`received command: ${e}``content_script:main:${a}``received connect: ${e.name}``${To}api/plugins/${n}.json``    \u2022 ${a}`).join(`
`))}hg(),e.debug&&I.setLevel("