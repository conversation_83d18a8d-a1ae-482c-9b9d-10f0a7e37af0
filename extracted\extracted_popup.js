(()=>{var pR=Object.defineProperty;var c6=(e,t)=>{for(var n in t)pR(e,n,{get:t[n],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`${globalThis.location.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")||(e=`${globalThis.location.protocol}//${r}``\\x1B[${r.join(";")}m`,close:`\\x1B[${e}m`,regexp:new RegExp(`\\\\x1b\\\\[${e}m`,"g")}}function w(r,e){return ie?`${e.open}${r.replace(e.regexp,e.open)}${e.close}``https://config.${p}/`,rt=`https://app.${p}/`,u=S()||N()?`https://${p}/`:`https://test.${p}/`,G=`https://dash.${p}/`,ot=S()||N()?`https://api2.${p}/`:`https://test-api2.${p}/`,at=S()||N()?`https://ai.${p}/`:`https://test-ai.${p}/`,it=`https://assets.${le}.cn/`,ue=u+"accounts/login?from=plugin",X=u+"profile/",m=u+"auth/pricing/",x=u+"pricing/";Q()&&(m=u+"accounts/safari-iap/",x=u+"accounts/safari-iap/");var st=S()?`https://onboarding.${p}/`:`https://test-onboarding.${p}/`,Z=`https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${u}activities/components/image-pro`;var vr=50*1e4,Er=`[${Y}-ctx-divider]`,Cr=`${Y}_context_preview`;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``"+e+"` is not a valid argument for `n-gram``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``
`?(ro++,oa=0):e?oa+=e.length:oa++,e&&(bm+=e.length),e}var W5={default(){switch(rt){case"	":case"\v":case"\f":case" ":case"\xA0":case"\uFEFF":case`
``
``
`:case"\r":throw Rt(ne());case"\u2028":case"\u2029":break;case void 0:throw Rt(ne())}Ve+=ne()},start(){switch(rt){case"{":case"[":return It("punctuator",ne())}Ke="value"},beforePropertyName(){switch(rt){case"$":case"_":Ve=ne(),Ke="identifierName";return;case"\\":ne(),Ke="identifierNameStartEscape";return;case"}":return It("punctuator",ne());case'"':case"'":xc=ne()==='"''${DF(e)}' at ${ro}:${oa}`)}function os(){return y1(`JSON5: invalid end of input at ${ro}:${oa}`)}function V5(){return oa-=5,y1(`JSON5: invalid identifier character at ${ro}:${oa}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${n("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${n("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${n("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${n("error.gemini.429")}<br/><br/> ${o}`:o=`${n("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${n("error.claude.403")}<br/><br/>${o}`:o=`${n("error.403")}<br/><br/>${o}`:this.status===400?o=`${n("error.400")}<br/><br/> ${o}`:this.status===502?o=`${n("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${n("error.subscriptionExpired")}<br/><br/> ${o}`,r="setting",a="configError",i=n("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${n("error.azure.401")}<br/><br/> ${o}`),{type:a,title:i,errMsg:o,action:r}}handleFetchError(t){let n=ve.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let r=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${n("mangaQuotaError.solvedTitle")}<br/></br>
        ${a.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${r("proQuotaError.solvedTitle")}
    <br/><br/>
    ${l.map((p,g)=>`${g+1}. ${p}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",a="";return n&&n.length===3&&(r=n[1],a=n[2]),{mimeType:r,base64:a}}var qS=Object.prototype.toString;function Dp(e){switch(qS.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ga(e,Error)}}function $c(e,t){return qS.call(e)===`[object ${t}]``[${d[0]}="${d[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),a=n.className,a&&Li(a))for(i=a.split(/\s+/),l=0;l<i.length;l++)r.push(`.${i[l]}`);let c=["type","name","title","alt"];for(l=0;l<c.length;l++)o=c[l],s=n.getAttribute(o),s&&r.push(`[${o}="${s}"]`);return r.join("")}function YS(){try{return location.href}catch{return""}}var Yt=class extends Error{constructor(n){super(n);this.message=n;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var oz=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function sz(e){return e==="http"||e==="https"}function Yl(e,t=!1){let{host:n,path:r,pass:a,port:i,projectId:o,protocol:s,publicKey:l}=e;return`${s}://${l}${t&&a?`:${a}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${o}`}function lz(e){let t=oz.exec(e);if(!t)throw new Yt(`Invalid Sentry Dsn: ${e}``Invalid Sentry Dsn: ${String(i)} missing`)}),!n.match(/^\d+$/))throw new Yt(`Invalid Sentry Dsn: Invalid projectId ${n}`);if(!sz(r))throw new Yt(`Invalid Sentry Dsn: Invalid protocol ${r}`);if(t&&isNaN(parseInt(t,10)))throw new Yt(`Invalid Sentry Dsn: Invalid port ${t}``${e.substr(0,t)}...``${encodeURIComponent(t)}=${encodeURIComponent(e[t])}``
``Error while triggering instrumentation handler.
Type: ${e}
Name: ${va(n)}
Error:``${r.type}: ${r.value}``**non-serializable** (${r})``[Function: ${va(t)}]`:typeof t=="symbol"?`[${String(t)}]``
${JSON.stringify(s)}
``${e}`,10);if(!isNaN(n))return n*1e3;let r=Date.parse(`${e}``${t.did}``${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string""function""number""function")i(n);else{let l=s({...n},r);!1&&s.id&&l===null&&Ne.log(``${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function Bz(e){return`${Fz(e)}${e.projectId}/envelope/`}function _z(e,t){return eT({sentry_key:e.publicKey,sentry_version:Rz,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${n.name}`))}),t}var jT="Not capturing exception because it''s not included in the random sample (sampling rate = ${i})`))):this._prepareEvent(t,n,r).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new Yt("An event processor returned null, will not send event.");if(n.data&&n.data.__sentry__===!0||o||!a)return s;let u=a(s,n);return jz(u)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new Yt("`beforeSend` returned `null`, will not send event.""Error while sending event:",n)}):!1&&Ne.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(n=>{let[r,a]=n.split(":");return{reason:r,category:a,quantity:t[n]}})}};function jz(e){let t="`beforeSend` method has to return `null` or a valid event.";if(Ql(e))return e.then(n=>{if(!(ba(n)||n===null))throw new Yt(t);return n},n=>{throw new Yt(`beforeSend rejected with ${n}``Sentry responded with status code ${d.statusCode} to sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&Ne.error(`Cannot extract message for event ${Bi(e)}`),[]}return[]}function Qz(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function Yz(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Np(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?Yz(t):null}catch{return!1&&Ne.error(`Cannot extract url for event ${Bi(e)}`),null}}function l3(e,t){let n=u3(e,t),r={type:t&&t.name,value:eU(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Jz(e,t,n,r){let a={exception:{values:[{type:t0(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:`Non-Error ${r?"promise rejection":"exception"} captured with keys: ${tT(t)}``${s}: ${o.message}`:s;i=s3(e,l,n,r),o0(i,l)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return Dp(t)?o3(e,t):ba(t)||t0(t)?(i=Jz(e,t,n,a),Eo(i,{synthetic:!0}),i):(i=s3(e,t,n,r),o0(i,`${t}``ui.${n.name}`,message:r},{event:n.event,name:n.name,global:n.global})}return t}function nU(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:dT(e.level),message:O4(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${O4(e.args.slice(1)," ")||"console.assert"}``sentry.${t.type==="transaction"?"transaction":"event"}``safari-extension:${t}`:`safari-web-extension:${t}``Non-Error promise rejection captured with value: ${String(e)}``Global Handler attached: ${e}``${t}@${e}``Request timeout after ${s}ms``fail response: ${t} `,c);let d="";c&&(d=c.slice(0,500));let m=d,g=new URL(t).hostname.endsWith(`.${At}``${_.year}${_.week}``[domain]` tag:** Apply strictly with absolute priority, ** overriding any other translation logic for the term**. (Use original Source Term if Source==Translation, else use listed Translation).\n    *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2D\u6587\uFF0C{{html_only}}\u4EC5\u8F93\u51FA\u7FFB\u8BD1\u3002\u5982\u679C\u67D0\u4E9B\u5185\u5BB9\u65E0\u9700\u7FFB\u8BD1\uFF08\u5982\u4E13\u6709\u540D\u8BCD\u3001\u4EE3\u7801\u7B49\uFF09\uFF0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u4E1C\u5317\u4EBA\u7684\u53E3\u543B\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u751F\u6D3B,\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA",multipleSystemPrompt:""},{id:"wyw2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u7CBE\u901A\u53E4\u6587\u7684\u5B66\u8005\uFF0C\u53EA\u8FD4\u56DE\u767D\u8BDD\u6587",prompt:`\u8BF7\u5C06\u6587\u8A00\u6587\u7528\u767D\u8BDD\u6587\u89E3\u91CA:

{{text}}`,multiplePrompt:`\u5C06\u4E0B\u9762 YAML \u683C\u5F0F\u4E2D\u6240\u6709\u7684 {{imt_source_field}} \u5B57\u6BB5\u4E2D\u7684\u6587\u8A00\u6587\u7FFB\u8BD1\u4E3A\u767D\u8BDD\u6587\uFF0C\u5E76\u5C06\u7FFB\u8BD1\u7ED3\u679C\u5199\u5728 {{imt_trans_field}} \u5B57\u6BB5\u4E2D

{{normal_result_yaml_example}}

\u5F00\u59CB\u7FFB\u8BD1:

{{yaml}}`,multipleSystemPrompt:""},{id:"auto2wyw",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u6587\u8A00\u6587\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u6587\u8A00\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are you"}
    }
  ],
  "contextual_analysis": "Analysis of the word''t ","n''m sorry, but I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``/g,"\\`")}${c}': '${(u.v||u.k).replace(/`/g,"\\`")}''"')&&i?.endsWith('"''/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}async signedRequest({secretId:t,secretKey:n,action:r,payload:a,service:i,version:o}){let s=new Date().toISOString(),l=Math.random().toString(36).slice(2),u={Action:r,Version:o,Format:"JSON",AccessKeyId:t,SignatureNonce:l,Timestamp:s,SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0"},d=(h=>{let f=Object.keys(h).sort().map(y=>`${this.encode(y)}=${this.encode(h[y])}`).join("&");return`POST&%2F&${this.encode(f)}`})(Object.assign({},u,a)),m=this.SHA1.b64_hmac(`${n}&`,d),p=new URLSearchParams(Object.assign({},u,{Signature:m})).toString(),g=await super.request({retry:this.retry,url:`https://${i}.aliyuncs.com?${p}``%${t.charCodeAt(0).toString(16).toUpperCase()}`)}catch{return""}},d2=e=>Object.keys(e).map(t=>{let n=e[t];if(typeof n>"u"||n===null)return;let r=c2(t);if(r)return Array.isArray(n)?`${r}=${n.map(c2).sort().join(`&${r}=`)}`:`${r}=${c2(n)}``${an.algorithm} Credential=${t.accessKeyId}/${a}`),r.push(`SignedHeaders=${this.signedHeaders()}`),r.push(`Signature=${await this.signature(t,n)}`),r.join(", ")}async getSignUrl(t,n){let r=this.getDateTime(n),a={...this.request.params},i=this.request.params,o=this.request.headers;t.sessionToken&&(a[an.tokenHeader]=t.sessionToken),a[an.dateHeader]=r,a[an.notSignBody]="",a[an.credential]=`${t.accessKeyId}/${this.credentialString(r)}``${this.canonicalHeaders()}
`),t.push(this.signedHeaders()),t.push(await this.hexEncodedBodyHash()),t.join(`
`)}canonicalHeaders(){let t=[];Object.keys(this.request.headers).forEach(r=>{t.push([r,this.request.headers[r]])}),t.sort((r,a)=>r[0].toLowerCase()<a[0].toLowerCase()?-1:1);let n=[];return t.forEach(r=>{let a=r[0].toLowerCase();if(this.isSignableHeader(a)){let i=r[1];if(typeof i>"u"||i===null||typeof i.toString!="function")throw new Z(`Header ${a} contains invalid value`);n.push(`${a}:${this.canonicalHeaderValues(i.toString())}`)}}),n.join(`
``${an.kDatePrefix}${t.secretKey}``
``Unsupported language: ${a}`);a=this.langMap.get(a);let i=await this.checkLang(r,n);if(!i)return{text:n,from:r,to:a};r=i;let o=this.handleRequest(n,r,a),s=await super.request(o);return{text:this.handleResponseText(s),from:r,to:a}}async translateList(t){if(!Object.keys(this.apiServiceConfig).length)throw new Z("serivce id not found config");let{text:n,from:r,to:a}=t;if(!this.langMap.has(a))throw new Z(`Unsupported language: ${a}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${r==="auto"?"":D2.get(r)+"-"}${D2.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.toString()}``https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${n}``
`+n+`
`+o+`
`+i}}async getCanonicalRequest(t){let n=await In(t),r="POST",a="/",i="",o=`content-type:application/json; charset=utf-8
host:hunyuan.tencentcloudapi.com
x-tc-action:`+this.action.toLowerCase()+`
`,s="content-type;host;x-tc-action";return{signedHeaders:s,canonicalRequest:r+`
`+a+`
`+i+`
`+o+`
`+s+`
`+n}}getDate(t){let n=t.getUTCFullYear(),r=("0"+(t.getUTCMonth()+1)).slice(-2),a=("0"+t.getUTCDate()).slice(-2);return`${n}-${r}-${a}``${Oe}silicon/get-token?deviceId=${this.deviceId}``${Oe}big-model/get-token?deviceId=${this.deviceId}``Bearer ${this.apiKey}`,...this.headerConfigs}),body:JSON.stringify(s,null,2),timeout:this.requestTimeout,retry:this.retry},u;try{return u=await this.rawRequest(l),{text:this.parseResponse(u),from:r,to:a}}catch(c){throw c}}};var P2=!1;async function bu(e,t=!1){if(X(!1,!0)||!P2&&!t)return null;try{let n=await tA(e);return I.debug("server language detect:",n),n}catch(n){return I.debug("server language detect error",n),gg(!1),null}}async function tA(e,t=1500){let n=new Promise((i,o)=>{setTimeout(()=>o(new Error(`Timeout after ${t}ms`)),t)}),r=Me({url:`https://lang-detect.${At}/api/predict/batch``*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function xg(e){for(var t=9,n=e.length;n--;)t=Math.imul(t^e.charCodeAt(n),1597334677);return"#"+((t^t>>>9)>>>0).toString(36)}function Cg(e,t="@media "){return t+Lr(e).map(n=>(typeof n=="string"&&(n={min:n}),n.raw||Object.keys(n).map(r=>`(${r}-width:${n[r]})``var(${r})`:n;if(e.includes("<alpha-value>"))return e.replace("<alpha-value>",a);if(e[0]=="#"&&(e.length==4||e.length==7)){let i=(e.length-1)/3,o=[17,1,.062272][i-1];return`rgba(${[_2(e.substr(1,i),o),_2(e.substr(1+i,i),o),_2(e.substr(1+2*i,i),o),a]})`}return a=="1"?e:a=="0"?"#0000":e.replace(/^(rgb|hsl)(\([^)]+)\)$/,`$1a$2,${a})``])?(.+?)\1(?:\s*,\s*(["''ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"'.split(","),serif:'ui-serif,Georgia,Cambria,"Times New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(n){return n},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=UG(t.styleAliases||null),zG.indexOf(this.kind)===-1)throw new hr('Unknown kind "'+this.kind+'" is specified for "'+e+''"''there is a previously declared suffix for "'+r+'" tag handle'',''undeclared tag handle "'+a+'"''unidentified alias "'+n+'"''unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"''> tag; it should be "'+g.kind+'", not "'+e.kind+'"''unknown document directive "'+r+'"''\\"''"''""':"''";if(!e.noCompatMode&&(MW.indexOf(t)!==-1||DW.test(t)))return e.quotingType===L0?'"'+t+'"':"'"+t+"'";var i=e.indent*Math.max(1,n),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-i),s=r||e.flowLevel>-1&&n>=e.flowLevel;function l(u){return FW(e,u)}switch(NW(t,s,e.indent,o,l,e.quotingType,e.forceQuotes&&!r,a)){case Ek:return t;case ib:return"'"+t.replace(/'/g,"''")+"'";case Ak:return"|"+XA(t,e.indent)+$A(YA(t,i));case kk:return">"+XA(t,e.indent)+$A(YA(jW(t,o),i));case Cu:return'"'+zW(t)+'"''"'),l=i[o],u=n[l],e.replacer&&(u=e.replacer.call(n,l,u)),Gi(e,t,l,!1,!1)&&(e.dump.length>1024&&(c+="? "),c+=e.dump+(e.condenseFlow?'"''> tag resolver accepts not "'+l+'" style''"',"&quot;"),x=c.message.replaceAll(`
`,"").replaceAll('"''1'").replace(/data-immersive-translate-walked=\".+?\"/g,"dim_w='1''"')||t.includes(`
`)?``${j}-new-user-guide`;function Kb(){return C("img",{class:`${lQ}-img`,src:Ng("images/new_float_ball_intro.png")})}var Og=`${j}-new-user-guide`;function Qb(){let{t:e}=re();return C("div",{class:`${Og}-select-service-guide`,children:C("div",{class:`${Og}-select-service-guide-card`,children:[C("div",{class:`${Og}-max-model`,children:e("translationServicesGroup.pro")}),C("div",{class:`${Og}-model-example``${j}-new-user-guide`;function Jb(){let{t:e}=re();return C("div",{class:`${PM}-video-subtitle-guide`,children:C("div",{class:`${PM}-video-subtitle-guide-card`,children:C("div",{children:[C("img",{src:Zb,class:"service-icon"}),e("subtitle.quickButton.requestAiSubtitle")]})})})}var LM=`${j}-new-user-guide`;function Xb(){let{t:e}=re();return C("div",{class:`${LM}-video-subtitle-guide`,children:C("div",{class:`${LM}-video-subtitle-guide-card`,children:[C("div",{children:[C("img",{src:Yb,class:"service-icon"}),e("autoEnableSubtitle")]}),C(jg,{})]})})}var V0=`${j}-new-user-guide``${V0}-container`,style:d,children:[C("div",{class:`${V0}-close-icon`,onClick:e,children:C(AM,{})}),C("img",{class:`${V0}-bg ${n}`,src:vQ}),C("div",{class:`${V0}-content ${n}`,children:[C(c,{}),C("div",{class:`${V0}-message text-red-500`,children:[i[r],Re().any&&r==="float-ball"?`
${a("floatBall.longPress")}`:""]})]}),C(Wb,{position:n})]})}var vQ="";var br=`${j}-new-user-guide`,FM=``;var W0=`${j}-new-user-guide-root``#${W0}`);a&&r.target!==a&&$b(W0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function $b(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Io(e.config.shortcuts.toggleTranslatePage)})``meta[name='${jM}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(N){return l.test(N.trim())}let c=()=>p.value.trim()===""||!u(x.value)||x.value.trim()==="",d=document.createElement("div");d.innerText=s("reportInfo.title"),d.setAttribute("style",`text-align:left;margin-top:-20px;color:${Nn.text};`),r.append(d);let m=document.createElement("div");m.setAttribute("style","display:flex;flex-direction:column;");let p=document.createElement("textarea");p.placeholder=s("reportInfo.reasonDesc"),p.required=!0,p.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),p.setAttribute("id","reason");let g=document.createElement("div");g.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=p.id,h.innerHTML=`<span style="color: ${Nn.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${Nn.text};margin-right: 10px;`),g.append(h),g.append(p),p.addEventListener("input",function(){A.disabled=c()}),p.addEventListener("keydown",function(N){N.stopPropagation()}),m.append(g);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      font-size: 14px;
      width: 303px;
      height: 45px;
      box-sizing: border-box;
      margin: 0 1px;
      outline: none;`),x.placeholder=s("reportInfo.emailPlaceholder"),$e.get(dt,null).then(N=>{if(!N)return;let B=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(N.email);N.email&&!B&&(x.value=N.email)});let f=document.createElement("label");f.htmlFor=x.id,f.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,f.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let b=document.createElement("div");b.textContent=s("reportInfo.emailError"),b.setAttribute("style",`color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${Nn.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${Nn.text};`);let E=document.createElement("div");E.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${Nn.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function VQ(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${Oe}v1/img/img-upload-url?fileType=${a}&imgHash=${t}&comicHash=${n}&domain=${ID(location.hostname)}``/${t}/task-state?comicHash=${n}&domain=${ID(location.hostname)}``<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{Ct(Sy,"1"),i(c,!0)},a.appendChild(c);let d=document.createElement("button");d.className=`${j}-btn ${j}-cancel-btn`,d.innerText=s("close"),d.onclick=()=>{i(d,!0)},a.appendChild(d),ha(e,"freeImageError")}function LD(e,t,n,r,a,i){let o=e.rule.imageRule,s=ve.bind(null,e.config.interfaceLanguage),l=document.createElement("div");l.innerText=s("freeImage.title"),l.setAttribute("style","text-align:left;margin-top:-20px;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:Vf})}</div>`;r.innerHTML=yn?.createHTML(u)||u;let c=document.createElement("button");c.className=`${j}-btn ${j}-cancel-btn``${c}px Arial`,m=this.breakTextIntoLines(t,l-this.H_PADDING);c<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${m}">${u.message}</span>`),Qs[m]={ok:!1,sentence:d},RI(l,e,t,n,u)):c&&(g.innerHTML=vt.sanitize(c.text),Qs[m]={ok:!0,sentence:d}),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function LI(e){let t=0,n=0;kn("Translating"),document.dispatchEvent(new CustomEvent(Jt,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(Qs),a=[],i=[],o=Fc(e,"");for(let s of r){let l=Qs[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let d=c.parentElement;if(c.remove(),d){delete Qs[s];let m=o.cloneNode(!0);m.id=s,d.appendChild(m),i.push(l.sentence)}}}}try{await Vt({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,d=Ll(document.body,c);if(!d)return;let m=d.parentElement;m&&(d.remove(),s?(t+=1,m.innerHTML=vt.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${Gd}='${r}''"+Da(t.document.activeElement)+"''[contenteditable="true"], [contenteditable=""]''/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r?\n/g,"<BR>"),htmlDecode:(e,t)=>e.replace(/<BR\s*\/?>/gi,t||`\r
`).replace(/&nbsp;/g," ").replace(/&quot;/g,'"').replace(/&#39;/g,"'']?(\d+)[^\d>]*>([\s\S]*)/i.exec(c);if(d){let m={};m.type="caption",m.start=parseInt(d[1]),m.end=m.start+2e3,m.duration=m.end-m.start,m.content=d[2].replace(/^<\/SYNC[^>]*>/gi,"");let p=!0,g=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}N0.call(Ee,Ee.ctx)}async function _y(e,t){let n={};e&&e.detail?.trigger&&(n.trigger=e.detail.trigger),Ae({key:"share_to_draft""reportActive")||"")return;I.debug("\u672A\u6FC0\u6D3B\u8FC7");let t=await Gr();await Me({url:Oe+"v1/user/campaign-info-translated/"+t,method:"POST",headers:{"content-type":"application/json"}}),ys("reportActive","1")}catch(e){I.error(e)}}function BL(e,t){let n=e.rule?.subtitleRule,r=n?.autoEnableSubtitle,a=n?.enableTriggerTranslate||n?.liveSubtitleRule?.enableTriggerTranslate;!r&&a&&document.dispatchEvent(new CustomEvent(cl,{detail:{tempEnableSubtitle:t}}))}async function UX(e){let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;Rn;let r=n.config.rules?.find(a=>Po(e.currentUrl,a));if(n.rule.id!=r?.id){I.debug("new rule.id",r?.id,"old id",n.rule.id),I.debug("url change newRule",r);try{Vo(!1);let a=n.config.generalRule;r&&(n.rule=Rs(a,r),n.rule=await Wp(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await Hm("updateRuleByUrlChange",t.mutationChangeDelay||50),kn("Translated");let i=LL(n);return n.filterRule=Sn(n,!0),IL(i),!0}catch(a){return I.debug(a),!1}}return Rn,!1}var V9=[],HX=new AbortController,{signal:qX}=HX,hd=0,fd=0,$s=!1,Ko,_L=0,Zi=[];function A9(e){GX(),ku().forEach(t=>{Jh(e,t)})}function Jh(e,t=window){let n=e,r=n.config,a=r.generalRule.mouseHoverHoldKey==="Off",i=r.mouseModifierKeyPressTimeout||400;n.state.isTranslateDirectlyOnHover===!0&&(a=!1);let o=n.state.isTranslateDirectlyOnHover===!0||r.generalRule.mouseHoverHoldKey==="Auto";r.generalRule.mouseHoverHoldKey==="MouseHoldKeyPressHold"&&YX(n,t);function s(){o=!o,o?(I.debug("mouse hover translate on"),n.state.isTranslateDirectlyOnHover=!0,Jh(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,Jh(n,t))}t.document.addEventListener(sc,s),Zi.push(()=>{t.document.removeEventListener(sc,s)});let l=er(m=>{if(!(Math.abs(m.clientX-hd)+Math.abs(m.clientY-fd)<=3)&&(hd=m.clientX,fd=m.clientY,$s&&!Ko&&Q9(n,!1,t),o||$s&&!Ko)){let p=zL(n.rule,t);if(p){if(K9(p))return;Xh(n,!1,t,p)}}},o?700:300),u=m=>{let p=m.target;K9(p)||(Xh(n,!0,t),Q9(n,!0,t,!0))},c=m=>{let p=r?.generalRule?.mouseHoverHoldKey?.toLowerCase()||"alt";if(m.type==="keydown"&&(V9=$t.getPressedKeyCodes()),m.type==="keyup"){let g=V9,h=Yd[p]===g[0];if(g.length>1&&h&&(_L=Date.now(),$s=!1),g.length===1&&h){let x=Date.now();$s=!0,Ko&&clearTimeout(Ko),Ko=setTimeout(()=>{let f=_L-x;f>0&&f<=i?$s=!1:u(m),Ko=void 0},i)}V9=[]}};if(Zi.push(()=>{Ko&&clearTimeout(Ko)}),a)return;gd("mousemove",l,t),Zi.push(()=>{t.removeEventListener("mousemove",l)});function d(){o?l.cancel():$s=!1}if(gd("blur",d,t),Zi.push(()=>{t.removeEventListener("blur",d)}),!o){let m=r?.generalRule?.mouseHoverHoldKey?.toLowerCase()||"alt",p=Ov;gd("keyup",NL,t),Zi.push(()=>{t.removeEventListener("keyup",NL)}),p.includes(m)?$t("*",{scope:"mouseHover",element:t.document,keyup:!0},c):$t(r.generalRule.mouseHoverHoldKey,{scope:"mouseHover",element:t.document},g=>{if(m==="*"){g.key==="*"&&u(g);return}u(g)}),$t.setScope("mouseHover"),Zi.push(()=>{$t.deleteScope("mouseHover")})}}function GX(){try{Zi.forEach(e=>e())}catch{}Zi=[],$t.setScope("all")}function NL(e){$s=!1}function gd(e,t,n=window){return n.addEventListener(e,t,{signal:qX})}function Xh(e,t,n,r){if(r=r||zL(e.rule,n),!r){I.debug("can not find selection part!");return}if(WX(e,r,t))return;if(K9(r)){I.debug("exclude  dom");return}RL(e,r);let i=ld();i&&(i.setupMouseHoverListener=Jh);let o={...Sn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=q9(i,e);Vi({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Fl(n.document.documentElement)!==u.state.translationMode&&Na(n.document.documentElement,u.state.translationMode),qs(l,c,!0,"hover").then(m=>{if(i.autoIncreaseParagraphId=l.autoIncreaseParagraphId,i.paragraphEntities=l.paragraphEntities,i.paragraphQueue=l.paragraphQueue,m&&!s){s=!0;let p=Date.now();Ct(Sv,p);let g={translation_service:l?.translationService||""};Ae({key:"mouse_hover_translate",ctx:{...u,sourceLanguage:"mouseHover"},params:g})}})},onFrame:()=>{},onIgnoreElement:()=>{}})}function zL(e,t){return VX(hd,fd,e,t)}function VX(e,t,n,r){let a=oC(e,t,n,r);if(a==null)return;if(a instanceof HTMLElement)return a;let i=()=>{let l=r.document.elementFromPoint(e,t);if(!l)return;let u=W1(l,e,t);return u===l?l.nodeName==="BUTTON"?l:void 0:jL(u,n)},o=()=>{try{a.setStartBefore(a.startContainer),a.setEndAfter(a.startContainer)}catch(u){I.debug("get mouse over word fail",u)}let l=a.getBoundingClientRect();if(!(l.left>e||l.right<e||l.top>t||l.bottom<t))return jL(a.startContainer,n)},s;return a.startContainer.nodeType!==Node.TEXT_NODE?s=i():s=o(),s}function K9(e){return!!(!(e.nodeType===Node.ELEMENT_NODE||e.nodeType===Node.TEXT_NODE)||e.nodeType===Node.ELEMENT_NODE&&io(e,Av))}function WX(e,t,n){let r=KX(t);if(n&&r.length)return r.forEach(a=>{QX(e,a)}),!0}function KX(e){let t=[e];if(e.nodeName=="FONT"&&e.className.includes(j)){let n=e.closest(`.${j}-target-wrapper`);n&&(t=[n.parentElement])}else{let r=[...e.querySelectorAll(`.${j}-target-wrapper``fingers.${t.generalRule[r[n]]}``${r}_btn_document`)}},{title:s("widget.text"),icon:C(f7,{}),tooltipText:s("widget.textTooltip",{shortcut:Re().any?"":`(${i.shortcuts.toggleSidePanel})`}),tooltipStyle:{fontSize:13},onClick:()=>{e(Xv,`${r}_btn_text`)}},{title:"PDF Pro",icon:C(S7,{}),tooltipText:s("browser.PdfProFile"),tooltipStyle:{left:"unset",right:-10,transform:"unset",fontSize:13},onClick:()=>{e(Jv,`${r}_btn_pdfpro``${e.type}_trial_pro_service_logo`)}}),C("div",{class:"flex-1"}),C("input",{type:"checkbox",role:"switch",class:"shrink-0",checked:!1,onChange:n=>{e.onOpenUrl(fn,`${e.type}_trial_pro_service``min-select ${t?"":"min-select-no-arrow"} ${r||""}`,value:ef,style:{maxWidth:`${i}px``${e.type}_more``\u{1F4AA} ${l(p?"disableNavTranslate":"enableNavTranslate")}``${e.type}_more_babeldoc`)}},{label:"\u{1F4C1} "+l("browser.translateLocalPdfFile"),value:"translateLocalPdfFile",onClick:()=>{e.onOpenUrl(d.PDF_VIEWER_URL,`${e.type}_more_pdf`)}},{label:"\u2747\uFE0F "+l("browser.PdfProFile"),value:"pdfProFile",onClick:()=>{e.onOpenUrl(d.PDF_PRO_URL,`${e.type}_more_pdfpro``${j}-no-select text-sm text-gray-c2`,onClick:a=>{a.preventDefault(),YC(`${le}docs/CHANGELOG/#${r.replace(/\./ig,"")}``popup-container ${e.className||""}``text-sm text-gray-9 mt-4 ml-1 ${u?"":"display-none"}`,children:u}),C("div",{class:`text-sm text-gray-9 mt-4 ml-1 ${i?"":"display-none"}`` (${l(r.rule.touchShortcutsToggleTranslatePage)})`):m+=` (${Io(u.shortcuts.toggleTranslatePage)})`),u.shortcuts.toggleTranslateToThePageEndImmediately&&(p+=` (${u.shortcuts.toggleTranslateToThePageEndImmediately})``${U}_${W}``content_script:main:${i}``content_script:main_sync:${e}`;return a.includes("Async")&&(o=`content_script:main:${e}`),t.sendMessage(o,{method:a,data:i})}let r={...x$};return Object.keys(r).forEach(a=>{r[a]=n.bind(null,a)}),r}var C$=()=>{On("shareToDraft")()};function S$(e,t){let n=e;t&&(n=`${e}?utm_source=extension&utm_medium=extension&utm_campaign=${t}`),$.tabs.create({url:n}),setTimeout(()=>{globalThis.close()},50)}function rR(){globalThis.close()}var el={},X9={};function aR(e,t){if(X9[e])try{t()}catch(n){I.error("run callback failed",n)}else el[e]||(el[e]=[]),el[e].push(t)}function iR(e){if(el[e]&&el[e].length){let t=[...el[e]];el[e]=[],t.forEach(n=>n())}}async function oR(){try{if(X())return;let e=$.runtime.getURL("locales.json"),n=await(await fetch(e)).json();Object.assign(To,n)}catch{}}var sR=document.getElementById("mount");Uw();sR&&(async()=>{let e=await Ot();await oR(),e.debug&&I.setLevel("debug"),_r(C(Qa,{lang:e.interfaceLanguage,children:C($9,{})}),sR)})();})();

