(()=>{var lO=Object.defineProperty;var $5=(e,t)=>{for(var n in t)lO(e,n,{get:t[n],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}},C=new Proxy(T,V),xe=new Proxy(re,V);function _(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`${globalThis.location.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")||(e=`${globalThis.location.protocol}//${r}``\\x1B[${r.join(";")}m`,close:`\\x1B[${e}m`,regexp:new RegExp(`\\\\x1b\\\\[${e}m`,"g")}}function w(r,e){return ie?`${e.open}${r.replace(e.regexp,e.open)}${e.close}``https://config.${p}/`,rt=`https://app.${p}/`,u=S()||N()?`https://${p}/`:`https://test.${p}/`,G=`https://dash.${p}/`,ot=S()||N()?`https://api2.${p}/`:`https://test-api2.${p}/`,at=S()||N()?`https://ai.${p}/`:`https://test-ai.${p}/`,it=`https://assets.${le}.cn/`,ue=u+"accounts/login?from=plugin",X=u+"profile/",m=u+"auth/pricing/",x=u+"pricing/";Q()&&(m=u+"accounts/safari-iap/",x=u+"accounts/safari-iap/");var st=S()?`https://onboarding.${p}/`:`https://test-onboarding.${p}/`,Z=`https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${u}activities/components/image-pro`;var vr=50*1e4,Er=`[${Y}-ctx-divider]`,Cr=`${Y}_context_preview`;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function wu(e){for(var t=9,n=e.length;n--;)t=Math.imul(t^e.charCodeAt(n),1597334677);return"#"+((t^t>>>9)>>>0).toString(36)}function Dp(e,t="@media "){return t+Ut(e).map(n=>(typeof n=="string"&&(n={min:n}),n.raw||Object.keys(n).map(r=>`(${r}-width:${n[r]})``var(${r})`:n;if(e.includes("<alpha-value>"))return e.replace("<alpha-value>",a);if(e[0]=="#"&&(e.length==4||e.length==7)){let i=(e.length-1)/3,o=[17,1,.062272][i-1];return`rgba(${[X4(e.substr(1,i),o),X4(e.substr(1+i,i),o),X4(e.substr(1+2*i,i),o),a]})`}return a=="1"?e:a=="0"?"#0000":e.replace(/^(rgb|hsl)(\([^)]+)\)$/,`$1a$2,${a})``])?(.+?)\1(?:\s*,\s*(["''style[data-twind=""]''ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"'.split(","),serif:'ui-serif,Georgia,Cambria,"Times New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(n){return n},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.representName=t.representName||null,this.defaultStyle=t.defaultStyle||null,this.multi=t.multi||!1,this.styleAliases=Hj(t.styleAliases||null),zj.indexOf(this.kind)===-1)throw new wr('Unknown kind "'+this.kind+'" is specified for "'+e+''"''there is a previously declared suffix for "'+r+'" tag handle'',''undeclared tag handle "'+a+'"''unidentified alias "'+n+'"''unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"''> tag; it should be "'+f.kind+'", not "'+e.kind+'"''unknown document directive "'+r+'"''\\"''"''""':"''";if(!e.noCompatMode&&(mz.indexOf(t)!==-1||pz.test(t)))return e.quotingType===Gd?'"'+t+'"':"'"+t+"'";var i=e.indent*Math.max(1,n),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-i),s=r||e.flowLevel>-1&&n>=e.flowLevel;function l(u){return yz(e,u)}switch(Cz(t,s,e.indent,o,l,e.quotingType,e.forceQuotes&&!r,a)){case pT:return t;case k2:return"'"+t.replace(/'/g,"''")+"'";case gT:return"|"+wS(t,e.indent)+ES(CS(t,i));case fT:return">"+wS(t,e.indent)+ES(CS(Tz(t,o),i));case ku:return'"'+wz(t)+'"''"'),l=i[o],u=n[l],e.replacer&&(u=e.replacer.call(n,l,u)),oo(e,t,l,!1,!1)&&(e.dump.length>1024&&(c+="? "),c+=e.dump+(e.condenseFlow?'"''> tag resolver accepts not "'+l+'" style''\xE0an",yue:"\u7CB5\u8A9E","zh-CN":"\u7B80\u4F53\u4E2D\u6587","zh-TW":"\u7E41\u9AD4\u4E2D\u6587-\u53F0\u6E7E","zh-HK":"\u7E41\u9AD4\u4E2D\u6587-\u9999\u6E2F","zh-CN-NE":"\u6771\u5317\u5B98\u8A71",zu:"isiZulu","<all>":"All Languages","ur-roman":"Roman Urdu"},hm={placeholder:"\u8BF7\u9009\u62E9",af:"\u5357\u975E\u8377\u5170\u8BED",am:"\u963F\u59C6\u54C8\u62C9\u8BED",ar:"\u963F\u62C9\u4F2F\u8BED",auto:"\u81EA\u52A8\u68C0\u6D4B",az:"\u963F\u585E\u62DC\u7586\u8BED",be:"\u767D\u4FC4\u7F57\u65AF\u8BED",bg:"\u4FDD\u52A0\u5229\u4E9A\u8BED",tn:"\u8D5E\u7EB3\u8BED",bn:"\u5B5F\u52A0\u62C9\u8BED",bs:"\u6CE2\u65AF\u5C3C\u4E9A\u8BED",bo:"\u85CF\u8BED",ca:"\u52A0\u6CF0\u7F57\u5C3C\u4E9A\u8BED",ceb:"\u5BBF\u52A1\u8BED",co:"\u79D1\u897F\u5609\u8BED",cs:"\u6377\u514B\u8BED",cy:"\u5A01\u5C14\u58EB\u8BED",da:"\u4E39\u9EA6\u8BED",de:"\u5FB7\u8BED",el:"\u5E0C\u814A\u8BED",en:"\u82F1\u8BED",eo:"\u4E16\u754C\u8BED",es:"\u897F\u73ED\u7259\u8BED",et:"\u7231\u6C99\u5C3C\u4E9A\u8BED",eu:"\u5DF4\u65AF\u514B\u8BED",sa:"\u68B5\u8BED",fa:"\u6CE2\u65AF\u8BED",fi:"\u82AC\u5170\u8BED",fil:"\u83F2\u5F8B\u5BBE\u8BED",fj:"\u6590\u6D4E\u8BED",fr:"\u6CD5\u8BED",fy:"\u5F17\u91CC\u65AF\u5170\u8BED",ga:"\u7231\u5C14\u5170\u8BED",gd:"\u82CF\u683C\u5170\u76D6\u5C14\u8BED",gl:"\u52A0\u5229\u897F\u4E9A\u8BED",gu:"\u53E4\u5409\u62C9\u7279\u8BED",ha:"\u8C6A\u8428\u8BED",haw:"\u590F\u5A01\u5937\u8BED",he:"\u5E0C\u4F2F\u6765\u8BED",hi:"\u5370\u5730\u8BED",hmn:"\u82D7\u8BED",hr:"\u514B\u7F57\u5730\u4E9A\u8BED",ht:"\u6D77\u5730\u514B\u91CC\u5965\u5C14\u8BED",hu:"\u5308\u7259\u5229\u8BED",hy:"\u4E9A\u7F8E\u5C3C\u4E9A\u8BED",id:"\u5370\u5EA6\u5C3C\u897F\u4E9A\u8BED",ig:"\u4F0A\u535A\u8BED",is:"\u51B0\u5C9B\u8BED",it:"\u610F\u5927\u5229\u8BED",ja:"\u65E5\u8BED",jw:"\u722A\u54C7\u8BED",ka:"\u683C\u9C81\u5409\u4E9A\u8BED",kk:"\u54C8\u8428\u514B\u8BED",km:"\u9AD8\u68C9\u8BED",kn:"\u5361\u7EB3\u8FBE\u8BED",ko:"\u97E9\u8BED",ku:"\u5E93\u5C14\u5FB7\u8BED",ky:"\u5409\u5C14\u5409\u65AF\u8BED",la:"\u62C9\u4E01\u8BED",lb:"\u5362\u68EE\u5821\u8BED",lo:"\u8001\u631D\u8BED",lt:"\u7ACB\u9676\u5B9B\u8BED",lv:"\u62C9\u8131\u7EF4\u4E9A\u8BED",mg:"\u9A6C\u62C9\u52A0\u65AF\u8BED",mi:"\u6BDB\u5229\u8BED",mk:"\u9A6C\u5176\u987F\u8BED",ml:"\u9A6C\u62C9\u96C5\u62C9\u59C6\u8BED",mn:"\u8499\u53E4\u8BED",mr:"\u9A6C\u62C9\u5730\u8BED",ms:"\u9A6C\u6765\u8BED",mt:"\u9A6C\u8033\u4ED6\u8BED",mww:"\u767D\u82D7\u8BED",my:"\u7F05\u7538\u8BED",ne:"\u5C3C\u6CCA\u5C14\u8BED",nl:"\u8377\u5170\u8BED",no:"\u632A\u5A01\u8BED",ny:"\u9F50\u5207\u74E6\u8BED\uFF08\u5C3C\u626C\u8D3E\u8BED\uFF09",otq:"\u514B\u96F7\u5854\u7F57\u5965\u6258\u7C73\u8BED",pa:"\u65C1\u906E\u666E\u8BED",pl:"\u6CE2\u5170\u8BED",ps:"\u666E\u4EC0\u56FE\u8BED",pt:"\u8461\u8404\u7259\u8BED","pt-br":"\u8461\u8404\u7259\u8BED\uFF08\u5DF4\u897F\uFF09","pt-BR":"\u8461\u8404\u7259\u8BED\uFF08\u5DF4\u897F\uFF09","pt-PT":"\u8461\u8404\u7259\u8BED\uFF08\u8461\u8404\u7259\uFF09",ro:"\u7F57\u9A6C\u5C3C\u4E9A\u8BED",ru:"\u4FC4\u8BED",sd:"\u4FE1\u5FB7\u8BED",si:"\u50E7\u4F3D\u7F57\u8BED",sk:"\u65AF\u6D1B\u4F10\u514B\u8BED",sl:"\u65AF\u6D1B\u6587\u5C3C\u4E9A\u8BED",sm:"\u8428\u6469\u4E9A\u8BED",sn:"\u4FEE\u7EB3\u8BED",so:"\u7D22\u9A6C\u91CC\u8BED",sq:"\u963F\u5C14\u5DF4\u5C3C\u4E9A\u8BED",sr:"\u585E\u5C14\u7EF4\u4E9A\u8BED","sr-Cyrl":"\u585E\u5C14\u7EF4\u4E9A\u8BED\uFF08\u897F\u91CC\u5C14\u6587\uFF09","sr-Latn":"\u585E\u5C14\u7EF4\u4E9A\u8BED\uFF08\u62C9\u4E01\u6587\uFF09",st:"\u585E\u7D22\u6258\u8BED",su:"\u5DFD\u4ED6\u8BED",sv:"\u745E\u5178\u8BED",sw:"\u65AF\u74E6\u5E0C\u91CC\u8BED",ta:"\u6CF0\u7C73\u5C14\u8BED",te:"\u6CF0\u5362\u56FA\u8BED",tg:"\u5854\u5409\u514B\u8BED",th:"\u6CF0\u8BED",tlh:"\u514B\u6797\u8D21\u8BED","tlh-Qaak":"\u514B\u6797\u8D21\u8BED\uFF08piqaD\uFF09",to:"\u6C64\u52A0\u8BED",tr:"\u571F\u8033\u5176\u8BED",ty:"\u5854\u5E0C\u63D0\u8BED",ug:"\u7EF4\u543E\u5C14\u8BED",uk:"\u4E4C\u514B\u5170\u8BED",ur:"\u4E4C\u5C14\u90FD\u8BED",uz:"\u4E4C\u5179\u522B\u514B\u8BED",vi:"\u8D8A\u5357\u8BED",wyw:"\u6587\u8A00\u6587",xh:"\u73ED\u56FE\u8BED",yi:"\u610F\u7B2C\u7EEA\u8BED",yo:"\u7EA6\u9C81\u5DF4\u8BED",yua:"\u5C24\u5361\u5766\u739B\u96C5\u8BED",yue:"\u7CA4\u8BED","zh-CN":"\u7B80\u4F53\u4E2D\u6587","zh-TW":"\u7E41\u4F53\u4E2D\u6587-\u53F0\u6E7E","zh-HK":"\u7E41\u4F53\u4E2D\u6587-\u9999\u6E2F","zh-CN-NE":"\u4E1C\u5317\u8BDD",zu:"\u7956\u9C81\u8BED","<all>":"\u6240\u6709\u8BED\u8A00","ur-roman":"\u7F57\u9A6C\u4E4C\u5C14\u90FD\u8BED"},bm={placeholder:"\u8ACB\u9078\u64C7",af:"\u963F\u975E\u5229\u5361\u8A9E",am:"\u963F\u59C6\u54C8\u62C9\u8A9E",ar:"\u963F\u62C9\u4F2F\u8A9E",auto:"\u81EA\u52D5\u6AA2\u6E2C",az:"\u963F\u585E\u62DC\u7586\u8A9E",be:"\u767D\u4FC4\u7F85\u65AF\u8A9E",bg:"\u4FDD\u52A0\u5229\u4E9E\u8A9E",tn:"\u8D0A\u7D0D\u8A9E",bn:"\u5B5F\u52A0\u62C9\u8A9E",bs:"\u6CE2\u65AF\u5C3C\u4E9E\u8A9E",bo:"\u85CF\u8A9E",ca:"\u52A0\u6CF0\u862D\u8A9E",ceb:"\u5BBF\u9727\u8A9E",co:"\u79D1\u897F\u5609\u8A9E",cs:"\u6377\u514B\u8A9E",cy:"\u5A01\u723E\u65AF\u8A9E",da:"\u4E39\u9EA5\u8A9E",de:"\u5FB7\u8A9E",el:"\u5E0C\u81D8\u8A9E",en:"\u82F1\u8A9E",eo:"\u4E16\u754C\u8A9E",es:"\u897F\u73ED\u7259\u8A9E",et:"\u611B\u6C99\u5C3C\u4E9E\u8A9E",eu:"\u5DF4\u65AF\u514B\u8A9E",sa:"\u68B5\u8A9E",fa:"\u6CE2\u65AF\u8A9E",fi:"\u82AC\u862D\u8A9E",fil:"\u83F2\u5F8B\u8CD3\u8A9E",fj:"\u6590\u6FDF\u8A9E",fr:"\u6CD5\u8A9E",fy:"\u5F17\u91CC\u897F\u8A9E",ga:"\u611B\u723E\u862D\u8A9E",gd:"\u8607\u683C\u862D\u84CB\u723E\u8A9E",gl:"\u52A0\u5229\u897F\u4E9E\u8A9E",gu:"\u53E4\u5409\u62C9\u7279\u8A9E",ha:"\u8C6A\u6492\u8A9E",haw:"\u590F\u5A01\u5937\u8A9E",he:"\u5E0C\u4F2F\u4F86\u8A9E",hi:"\u5370\u5730\u8A9E",hmn:"\u82D7\u8A9E",hr:"\u514B\u7F85\u5730\u4E9E\u8A9E",ht:"\u6D77\u5730\u514B\u91CC\u5967\u723E\u8A9E",hu:"\u5308\u7259\u5229\u8A9E",hy:"\u4E9E\u7F8E\u5C3C\u4E9E\u8A9E",id:"\u5370\u5C3C\u8A9E",ig:"\u4F0A\u535A\u8A9E",is:"\u51B0\u5CF6\u8A9E",it:"\u610F\u5927\u5229\u8A9E",ja:"\u65E5\u8A9E",jw:"\u722A\u54C7\u8A9E",ka:"\u55AC\u6CBB\u4E9E\u8A9E",kk:"\u54C8\u85A9\u514B\u8A9E",km:"\u9AD8\u68C9\u8A9E",kn:"\u574E\u7D0D\u9054\u8A9E",ko:"\u97D3\u8A9E",ku:"\u5EAB\u723E\u5FB7\u8A9E",ky:"\u5409\u723E\u5409\u65AF\u8A9E",la:"\u62C9\u4E01\u8A9E",lb:"\u76E7\u68EE\u5821\u8A9E",lo:"\u8001\u64BE\u8A9E",lt:"\u7ACB\u9676\u5B9B\u8A9E",lv:"\u62C9\u812B\u7DAD\u4E9E\u8A9E",mg:"\u99AC\u62C9\u52A0\u65AF\u8A9E",mi:"\u6BDB\u5229\u8A9E",mk:"\u99AC\u5176\u9813\u8A9E",ml:"\u99AC\u62C9\u96C5\u62C9\u59C6\u8A9E",mn:"\u8499\u53E4\u8A9E",mr:"\u99AC\u62C9\u5730\u8A9E",ms:"\u99AC\u4F86\u8A9E",mt:"\u99AC\u723E\u4ED6\u8A9E",mww:"\u767D\u82D7\u8A9E",my:"\u7DEC\u7538\u8A9E",ne:"\u5C3C\u6CCA\u723E\u8A9E",nl:"\u8377\u862D\u8A9E",no:"\u632A\u5A01\u8A9E",ny:"\u9F4A\u5207\u74E6\u8A9E",otq:"\u594E\u96F7\u5854\u7F85\u5967\u6258\u7C73\u8A9E",pa:"\u65C1\u906E\u666E\u8A9E",pl:"\u6CE2\u862D\u8A9E",ps:"\u666E\u4EC0\u5716\u8A9E",pt:"\u8461\u8404\u7259\u8A9E","pt-br":"\u8461\u8404\u7259\u8A9E\uFF08\u5DF4\u897F\uFF09","pt-BR":"\u8461\u8404\u7259\u8A9E\uFF08\u5DF4\u897F\uFF09","pt-PT":"\u8461\u8404\u7259\u8A9E\uFF08\u8461\u8404\u7259\uFF09",ro:"\u7F85\u99AC\u5C3C\u4E9E\u8A9E",ru:"\u4FC4\u8A9E",sd:"\u4FE1\u5FB7\u8A9E",si:"\u50E7\u4F3D\u7F85\u8A9E",sk:"\u65AF\u6D1B\u4F10\u514B\u8A9E",sl:"\u65AF\u6D1B\u7DAD\u5C3C\u4E9E\u8A9E",sm:"\u85A9\u6469\u4E9E\u8A9E",sn:"\u7D39\u7D0D\u8A9E",so:"\u7D22\u99AC\u91CC\u8A9E",sq:"\u963F\u723E\u5DF4\u5C3C\u4E9E\u8A9E",sr:"\u585E\u723E\u7DAD\u4E9E\u8A9E","sr-Cyrl":"\u585E\u723E\u7DAD\u4E9E\u8A9E (\u897F\u91CC\u723E\u6587)","sr-Latn":"\u585E\u723E\u7DAD\u4E9E\u8A9E (\u62C9\u4E01\u6587)",st:"\u585E\u7D22\u6258\u8A9E",su:"\u5DFD\u4ED6\u8A9E",sv:"\u745E\u5178\u8A9E",sw:"\u65AF\u74E6\u5E0C\u91CC\u8A9E",ta:"\u6CF0\u7C73\u723E\u8A9E",te:"\u6CF0\u76E7\u56FA\u8A9E",tg:"\u5854\u5409\u514B\u8A9E",th:"\u6CF0\u8A9E",tlh:"\u514B\u6797\u8CA2\u8A9E","tlh-Qaak":"\u514B\u6797\u8CA2\u8A9E (piqaD)",to:"\u6771\u52A0\u8A9E",tr:"\u571F\u8033\u5176\u8A9E",ty:"\u5854\u5E0C\u63D0\u8A9E",ug:"\u7DAD\u543E\u723E\u8A9E",uk:"\u70CF\u514B\u862D\u8A9E",ur:"\u70CF\u723E\u90FD\u8A9E",uz:"\u70CF\u8332\u5225\u514B\u8A9E",vi:"\u8D8A\u5357\u8A9E",wyw:"\u6587\u8A00\u6587",xh:"\u79D1\u85A9\u8A9E",yi:"\u610F\u7B2C\u7DD2\u8A9E",yo:"\u7D04\u9B6F\u5DF4\u8A9E",yua:"\u5C24\u52A0\u6566\u99AC\u96C5\u8A9E",yue:"\u5EE3\u6771\u8A71 (\u50B3\u7D71)","zh-CN":"\u7C21\u9AD4\u4E2D\u6587","zh-TW":"\u7E41\u9AD4\u4E2D\u6587-\u53F0\u7063","zh-HK":"\u7E41\u9AD4\u4E2D\u6587-\u9999\u6E2F","zh-CN-NE":"\u6771\u5317\u8A71",zu:"\u7956\u9B6F\u8A9E","<all>":"\u6240\u6709\u8A9E\u8A00","ur-roman":"\u7F57\u9A6C\u4E4C\u723E\u90FD\u8A9E"},ns=["zh-CN","zh-TW","ja","th","lo","km","my","bo","vi","sa","su","jw","ug","haw","ko"],rs=[["auto","auto"],["zh-CN","Simplified Chinese"],["zh-TW","Traditional Chinese (Taiwan)"],["zh-HK","Traditional Chinese (Hong Kong)"],["zh-CN-NE","Northeastern Chinese"],["en","English"],["yue","Cantonese"],["bo","\u85CF\u8BED"],["wyw","Classical Chinese"],["ja","Japanese"],["ko","Korean"],["fr","French"],["de","German"],["es","Spanish"],["it","Italian"],["ru","Russian"],["pt","Portuguese"],["pt-br","Portuguese (Brazil)"],["nl","Dutch"],["pl","Polish"],["ar","Arabic"],["af","Afrikaans"],["am","Amharic"],["az","Azerbaijani"],["be","Belarusian"],["bg","Bulgarian"],["bn","Bengali"],["bs","Bosnian"],["ca","Catalan"],["ceb","Cebuano"],["co","Corsican"],["no","Norwegian"],["cs","Czech"],["cy","Welsh"],["da","Danish"],["el","Greek"],["eo","Esperanto"],["et","Estonian"],["eu","Basque"],["fa","Persian"],["fi","Finnish"],["fj","Fijian"],["fil","Filipino"],["fy","Western Frisian"],["ga","Irish"],["gd","Scottish Gaelic"],["gl","Galician"],["gu","Gujarati"],["ha","Hausa"],["haw","Hawaiian"],["he","Hebrew"],["hi","Hindi"],["hmn","Hmong"],["hr","Croatian"],["ht","Haitian Creole"],["hu","Hungarian"],["hy","Armenian"],["id","Indonesian"],["ig","Igbo"],["is","Icelandic"],["jw","Javanese"],["ka","Georgian"],["kk","Kazakh"],["km","Khmer"],["kn","Kannada"],["ku","Kurdish"],["ky","Kyrgyz"],["la","Lao"],["lb","Luxembourgish"],["lo","Lao"],["lt","Lithuanian"],["lv","Latvian"],["mg","Malagasy"],["mi","Maori"],["mk","Macedonian"],["ml","Malayalam"],["mn","Mongolian"],["mr","Marathi"],["ms","Malay"],["mt","Maltese"],["my","Burmese"],["pa","Punjabi"],["ps","Pashto"],["ro","Romanian"],["si","Sinhala"],["sk","Slovak"],["sl","Slovene"],["si","Sinhala"],["sa","Sanskrit"],["sm","Samoan"],["sn","Shona"],["so","Somali"],["sq","Albanian"],["sr","Serbian"],["sr-Cyrl","Serbian (Cyrillic)"],["sr-Latn","Serbian (Latin)"],["st","Southern Sotho"],["su","Sundanese"],["sv","Swedish"],["sw","Swahili"],["ta","Tamil"],["te","Telugu"],["tg","Tajik"],["th","Thai"],["tr","Turkish"],["ug","Uyghur"],["uk","Ukrainian"],["ur","Urdu"],["ur-roman","Roman Urdu"],["uz","Uzbek"],["vi","Vietnamese"],["xh","Xhosa"],["yi","Yiddish"],["yo","Yoruba"],["zu","Zulu"]];function de(e){if(typeof e!="string")return"auto";let t=e.toLowerCase();if(t==="und")return"auto";if(t==="zh"||t.startsWith("zh-hans"))return"zh-CN";if(t.startsWith("zh-hant")||t.startsWith("zh-hk")||t.startsWith("zh-tw")||t.startsWith("yue"))return"zh-TW";if(t.startsWith("zh-"))return"zh-CN";if(t==="iw")return"he";if(t==="jv")return"jw";let n=Er.map(a=>a.toLowerCase()),r=n.indexOf(t);if(r===-1)if(t.indexOf("-")>=0){t=t.split("-")[0];let a=n.indexOf(t);return a===-1?"auto":Er[a]}else return"auto";else return Er[r]}function Aw(){return de(document?.documentElement?.lang||"auto")}var f3=/iPhone/i,kw=/iPod/i,Mw=/iPad/i,Dw=/\biOS-universal(?:.+)Mac\b/i,h3=/\bAndroid(?:.+)Mobile\b/i,Iw=/Android/i,zu=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,Eg=/Silk/i,uo=/Windows Phone/i,Pw=/\bWindows(?:.+)ARM\b/i,Lw=/BlackBerry/i,Rw=/BB10/i,Fw=/Opera Mini/i,_w=/\b(CriOS|Chrome)(?:.+)Mobile/i,Bw=/Mobile(?:.+)Firefox\b/i,Nw=e=>typeof e<"u"&&e.platform==="MacIntel"&&typeof e.maxTouchPoints=="number"&&e.maxTouchPoints>1&&typeof globalThis.MSStream>"u";function nH(e){return t=>t.test(e)}function xe(e){let t={userAgent:"",platform:"",maxTouchPoints:0};!e&&typeof navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let n=t.userAgent,r=n.split("[FBAN");typeof r[1]<"u"&&(n=r[0]),r=n.split("Twitter"),typeof r[1]<"u""DENO",Ag="CHROME",kg="FIREFOX";function Ow(e){let t;try{let n=navigator?.userAgent||"";/firefox/i.test(n)||typeof InstallTrigger<"u"?t=kg:/deno/i.test(n)?t=b3:/chrome/i.test(n)&&(t=Ag)}catch{}return e===Ag&&t===Ag||e===kg&&t===kg||e===b3&&t===b3}function v3(){return navigator.userAgent.indexOf("Edg")!==-1}function Yn(){return Ow(Ag)}function jw(){return typeof Deno<"u"}function In(){return Me().IMMERSIVE_TRANSLATE_FIREFOX==="1"?!0:Ow(kg)}function Ar(e){return!mr(e)}var y3;function mr(e){return e?.confirmSupportMouse!=null?e?.confirmSupportMouse:y3?!0:!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement?!1:!!globalThis.matchMedia("(pointer:fine)").matches}function Uw(){let e=!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement;return!!globalThis.matchMedia("(pointer:fine)").matches&&e}function x3(e){return y3!=null?!1:(y3=e,!0)}function C3(){return X(!1,!0)?"monkey":Yn()?"chrome":In()?"firefox":rt()?"safari":null}function zw(){let e=globalThis.innerWidth,t=!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement;return e<768&&t}var Hw={addListener:()=>{},removeListener:()=>{},hasListener:()=>{}},qw={permissions:{contains:()=>{},request:()=>{}},runtime:{onMessage:Hw,openOptionsPage:()=>{},lastError:{message:""}},storage:{sync:{get:()=>{},set:()=>{}},local:{map:new Map,async get(e){return new Promise(t=>{setTimeout(()=>{let n=this.map.get(e);t({[e]:n})},100)})},async set(e,t){return new Promise((n,r)=>{setTimeout(()=>{this.map.set(e,t),n("")},100)})}}},tabs:{onUpdated:Hw,query:()=>{},sendMessage:()=>{}}};var Y;jw()?Y=qw:Y=globalThis.immersiveTranslateBrowserAPI;var ml=me+"CacheKey_""active")}function Vw(e){return e?.subscription?.memberShip==="max"}function Gw(e){if(e){let t="unknown";if(e.subscription&&e.subscription.subscriptionId&&(e.subscription.subscriptionId.startsWith("sub_")?t="stripe":t="admin"),t==="stripe")return!0}return!1}function Mg(e){if(e){let t=new Date(e.createTime),n=aH(t),r="free",a="unknown";return e.subscription&&e.subscription.subscriptionStatus==="active"&&(r=e.subscription.subscriptionType),e.subscription&&e.subscription.subscriptionId&&(e.subscription.subscriptionId.startsWith("sub_")?a="stripe":a="admin"),e.subscription?.isTrial&&(r="trial"),{user_type:r,user_register_day:n,subscription_from:a}}else return null}function aH(e){try{let n=e.toLocaleString("en-US",{timeZone:"Asia/Shanghai"}).split(" ")[0];n.endsWith(",")&&(n=n.slice(0,-1));let[r,a,i]=n.split("/");return n=`${i}-${r}-${a}``"+e+"` is not a valid argument for `n-gram``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``Translate the text to {{to}}, please do not explain any sentences, just translate or leave them as they are.:

{{text}}``subscription.${n.memberShip}`);return n.memberType&&n.memberType!=="individual"&&(r+=`-${t(`subscription.${n.memberType}`)}`),n.datePeriod&&(r+=`(${t(`subscription.${n.datePeriod}`)})``${o}-custom``${e}-pro`:BH(e,t)?`${e}-free`:`${e}-custom``${o}-pro`:i.find(s=>s.type===o)?`${o}-free`:r.find(s=>s.type===o)?`${o}-max`:o)}function Ng(e,t){return po(e.config,t).filter(r=>!(e.config.translationServices?.[r]?.visible==!1))}function va(e,t){let n=e.translationServices?.[t],r=Se.bind(null,e.interfaceLanguage),a=n?.type||t,i=`translationServices.${a}`,o=r(i);return o==i&&(o=""),n?.name||o||a}function yl(e,t){let n=e.translationServices?.[t];return n?.group==="pro"||n?.group==="max"}function S8(e,t){return e.translationServices?.[t]?.group==="pro"}function Gr(e){return e.prompt?.trim().length>0}function Og(e){return Gr(e)||e.withTerms}var R3=async()=>await xa.get("v1/novice-tasks"),NH=async e=>{await xa.post(`v1/novice-tasks/${e}/complete`,{})},w8=async e=>{await xa.post(`v1/novice-tasks/${e}/take-reward``Task ${e} is already completed`),!1):(await NH(e),I.debug(`Task ${e} completed successfully`),xl({method:"refreshRewardCenter",data:{}}),!0):(I.debug(`Task ${e} is not enabled`),!1):(I.debug(`Task with key ${e} not found`),!1)}catch(a){return I.error(`Failed to complete task ${e}:``${s.from}->${s.to}`;i[l]||(i[l]=[]),i[l].push(s)}let o=[];for(let s in i){let l=i[s];if(l.length===0)continue;let u=l[0].from,c=l[0].to,m=l[0].fromByService,p=l[0].url,g=e[0].inArticleContext;if(s!=="all"){let x=s.split("->""";for(let x=0;x<g.length;x++){let b=g[x];if(b.trim()===""){f.length>0?x<g.length-1&&(f[f.length-1].suffix+=`
`):h+=`
`;continue}else if(b.length>t){let v=[];qH(b,t,v);for(let C=0;C<v.length;C++){let S=v[C],{text:T,prefix:w,suffix:A}=S;p++,f.push({from:o,fromByService:s,to:l,text:T,prefix:w,suffix:A,index:a,url:c,sentenceTotalParts:p,partIndex:p-1,xpath:m,force:i.force||!1}),r[a]=p}}else p++,f.push({text:b,prefix:h,suffix:"",from:o,fromByService:s,to:l,index:a,url:c,sentenceTotalParts:p,partIndex:p-1,xpath:m,force:i.force||!1}),r[a]=p;f.length>0&&x<g.length-1&&(f[f.length-1].suffix+=`
``${a} ${n("needAction")}`)),kr()||(a=n("translationServices."+t.id),t.id.startsWith("custom-ai")&&(a=t.name)),a}function GH(e){let t=[],n=[],r=[],a=[];for(let i of e)i.value==="auto"?n.push(i):i.value==="common"?t.push(i):i.value?.startsWith("custom")?r.push(i):a.push(i);return[...t,...n,...r,...a]}function WH(e,t){let n=d8(e,t),r="",a="";for(let o of n)o.wasAdded?a+=o.character:o.wasRemoved||(i(),r+=o.character);return i(),r;function i(){!a.trim()||r.trim().endsWith("{{")||r.trim().endsWith("<")?(r+=a,a=""):(r+=`<div style='color:red;display:inline;''"':case"'":km=ae()==='"''"':if(km)return ae(),Kt("string",tt);tt+=ae();return;case"'":if(!km)return ae(),Kt("string",tt);tt+=ae();return;case`
`:case"\r":throw Xt(ae());case"\u2028":case"\u2029":break;case void 0:throw Xt(ae())}tt+=ae()},start(){switch(ht){case"{":case"[":return Kt("punctuator",ae())}at="value"},beforePropertyName(){switch(ht){case"$":case"_":tt=ae(),at="identifierName";return;case"\\":ae(),at="identifierNameStartEscape";return;case"}":return Kt("punctuator",ae());case'"':case"'":km=ae()==='"''${hq(e)}' at ${ms}:${za}`)}function Tl(){return Z3(`JSON5: invalid end of input at ${ms}:${za}`)}function G8(){return za-=5,Z3(`JSON5: invalid identifier character at ${ms}:${za}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${n("error.googleLimitIp")}<br/><br/>${o}`:this.data?.translationService=="openai"&&(this.message.indexOf("Limit: 3 / min")>=0||this.message.includes("rate_limit_exceeded")&&this.message.includes("Limit 3"))?o=`${n("error.openAIFreeLimit")}<br/><br/>
          ${o}`:this.data?.translationService=="openai"&&this.message.includes("You exceeded your current quota")?o=`${n("error.openAIExceededQuota")}<br/><br/>
          ${o}`:this.data?.translationService=="gemini"&&this.message.includes("RESOURCE_EXHAUSTED")?o=`${n("error.gemini.429")}<br/><br/> ${o}`:o=`${n("error.429")}<br/><br/> ${o}`:this.status===403?this.data?.translationService=="claude"?o=`${n("error.claude.403")}<br/><br/>${o}`:o=`${n("error.403")}<br/><br/>${o}`:this.status===400?o=`${n("error.400")}<br/><br/> ${o}`:this.status===502?o=`${n("error.502")}<br/><br/> ${o}`:this.status===404?o.includes("User subscription not found")&&(o=`${n("error.subscriptionExpired")}<br/><br/> ${o}`,r="setting",a="configError",i=n("error.subscriptionExpiredTitle")):this.status===401&&this.data?.translationService==="azure"&&(o=`${n("error.azure.401")}<br/><br/> ${o}`),{type:a,title:i,errMsg:o,action:r}}handleFetchError(t){let n=Se.bind(null,t.config.interfaceLanguage);if(this.status!==-999)return;let r=this.getErrorMsg(t);return{type:"network",title:`[${this.serviceName}] ``
        ${n("mangaQuotaError.solvedTitle")}<br/></br>
        ${a.map((o,s)=>`${s+1}. ${o}`).join("<br/>")}
        ``${r("proQuotaError.solvedTitle")}
    <br/><br/>
    ${l.map((g,f)=>`${f+1}. ${g}`).join("<br/>")}``Exceeded max retry count (${n})``${n.split("base64_""",a="";return n&&n.length===3&&(r=n[1],a=n[2]),{mimeType:r,base64:a}}var HA=Object.prototype.toString;function zf(e){switch(HA.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Di(e,Error)}}function Qm(e,t){return HA.call(e)===`[object ${t}]``[${m[0]}="${m[1]}"]`)});else if(n.id&&r.push(`#${n.id}`),a=n.className,a&&Ao(a))for(i=a.split(/\s+/),l=0;l<i.length;l++)r.push(`.${i[l]}`);let c=["type","name","title","alt"];for(l=0;l<c.length;l++)o=c[l],s=n.getAttribute(o),s&&r.push(`[${o}="${s}"]`);return r.join("")}function YA(){try{return location.href}catch{return""}}var hn=class extends Error{constructor(n){super(n);this.message=n;this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}name};var lY=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function uY(e){return e==="http"||e==="https"}function yc(e,t=!1){let{host:n,path:r,pass:a,port:i,projectId:o,protocol:s,publicKey:l}=e;return`${s}://${l}${t&&a?`:${a}`:""}@${n}${i?`:${i}`:""}/${r&&`${r}/`}${o}`}function cY(e){let t=lY.exec(e);if(!t)throw new hn(`Invalid Sentry Dsn: ${e}``Invalid Sentry Dsn: ${String(i)} missing`)}),!n.match(/^\d+$/))throw new hn(`Invalid Sentry Dsn: Invalid projectId ${n}`);if(!uY(r))throw new hn(`Invalid Sentry Dsn: Invalid protocol ${r}`);if(t&&isNaN(parseInt(t,10)))throw new hn(`Invalid Sentry Dsn: Invalid port ${t}``${e.substr(0,t)}...``${encodeURIComponent(t)}=${encodeURIComponent(e[t])}``
``Error while triggering instrumentation handler.
Type: ${e}
Name: ${ei(n)}
Error:``${r.type}: ${r.value}``**non-serializable** (${r})``[Function: ${ei(t)}]`:typeof t=="symbol"?`[${String(t)}]``
${JSON.stringify(s)}
``${e}`,10);if(!isNaN(n))return n*1e3;let r=Date.parse(`${e}``${t.did}``${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string""function""number""function")i(n);else{let l=s({...n},r);!1&&s.id&&l===null&&qe.log(``${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function NY(e){return`${BY(e)}${e.projectId}/envelope/`}function OY(e,t){return $A({sentry_key:e.publicKey,sentry_version:_Y,...t&&{sentry_client:`${t.name}/${t.version}``Integration installed: ${n.name}`))}),t}var Ok="Not capturing exception because it''s not included in the random sample (sampling rate = ${i})`))):this._prepareEvent(t,n,r).then(s=>{if(s===null)throw this.recordDroppedEvent("event_processor",t.type||"error"),new hn("An event processor returned null, will not send event.");if(n.data&&n.data.__sentry__===!0||o||!a)return s;let u=a(s,n);return zY(u)}).then(s=>{if(s===null)throw this.recordDroppedEvent("before_send",t.type||"error"),new hn("`beforeSend` returned `null`, will not send event.""Error while sending event:",n)}):!1&&qe.error("Transport disabled")}_clearOutcomes(){let t=this._outcomes;return this._outcomes={},Object.keys(t).map(n=>{let[r,a]=n.split(":");return{reason:r,category:a,quantity:t[n]}})}};function zY(e){let t="`beforeSend` method has to return `null` or a valid event.";if(bc(e))return e.then(n=>{if(!(Xa(n)||n===null))throw new hn(t);return n},n=>{throw new hn(`beforeSend rejected with ${n}``Sentry responded with status code ${m.statusCode} to sent event.``${n}`,`${t}: ${n}`]}catch{return!1&&qe.error(`Cannot extract message for event ${Do(e)}`),[]}return[]}function ZY(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function JY(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function Qf(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?JY(t):null}catch{return!1&&qe.error(`Cannot extract url for event ${Do(e)}`),null}}function Ey(e,t){let n=Ay(e,t),r={type:t&&t.name,value:nQ(t)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function $Y(e,t,n,r){let a={exception:{values:[{type:Jm(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:`Non-Error ${r?"promise rejection":"exception"} captured with keys: ${ek(t)}``${s}: ${o.message}`:s;i=wy(e,l,n,r),n0(i,l)}return"code"in o&&(i.tags={...i.tags,"DOMException.code":`${o.code}`}),i}return zf(t)?Ty(e,t):Xa(t)||Jm(t)?(i=$Y(e,t,n,a),Es(i,{synthetic:!0}),i):(i=wy(e,t,n,r),n0(i,`${t}``ui.${n.name}`,message:r},{event:n.event,name:n.name,global:n.global})}return t}function aQ(e){let t={category:"console",data:{arguments:e.args,logger:"console"},level:ck(e.level),message:ty(e.args," ")};if(e.level==="assert")if(e.args[0]===!1)t.message=`Assertion failed: ${ty(e.args.slice(1)," ")||"console.assert"}``sentry.${t.type==="transaction"?"transaction":"event"}``safari-extension:${t}`:`safari-web-extension:${t}``Non-Error promise rejection captured with value: ${String(e)}``Global Handler attached: ${e}``${t}@${e}``Request timeout after ${s}ms``fail response: ${t} `,c);let m="";c&&(m=c.slice(0,500));let p=m,f=new URL(t).hostname.endsWith(`.${et}``${_.year}${_.week}``[domain]` tag:** Apply strictly with absolute priority, ** overriding any other translation logic for the term**. (Use original Source Term if Source==Translation, else use listed Translation).\n    *   *Example:* If rule is `'kick': 'kick''s the translation:" or "Translation as follows:""multipleSystemPrompt.add_v.[1.17.2]":`You are a professional {{to}} native translator who needs to fluently translate text into {{to}}.

## Translation Rules
1. Output only the translated content, without explanations or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u5176\u7FFB\u8BD1\u4E3A\u7B80\u4F53\u4E2D\u6587\uFF0C{{html_only}}\u4EC5\u8F93\u51FA\u7FFB\u8BD1\u3002\u5982\u679C\u67D0\u4E9B\u5185\u5BB9\u65E0\u9700\u7FFB\u8BD1\uFF08\u5982\u4E13\u6709\u540D\u8BCD\u3001\u4EE3\u7801\u7B49\uFF09\uFF0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u4E1C\u5317\u4EBA\u7684\u53E3\u543B\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u751F\u6D3B,\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA",multipleSystemPrompt:""},{id:"wyw2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u7CBE\u901A\u53E4\u6587\u7684\u5B66\u8005\uFF0C\u53EA\u8FD4\u56DE\u767D\u8BDD\u6587",prompt:`\u8BF7\u5C06\u6587\u8A00\u6587\u7528\u767D\u8BDD\u6587\u89E3\u91CA:

{{text}}`,multiplePrompt:`\u5C06\u4E0B\u9762 YAML \u683C\u5F0F\u4E2D\u6240\u6709\u7684 {{imt_source_field}} \u5B57\u6BB5\u4E2D\u7684\u6587\u8A00\u6587\u7FFB\u8BD1\u4E3A\u767D\u8BDD\u6587\uFF0C\u5E76\u5C06\u7FFB\u8BD1\u7ED3\u679C\u5199\u5728 {{imt_trans_field}} \u5B57\u6BB5\u4E2D

{{normal_result_yaml_example}}

\u5F00\u59CB\u7FFB\u8BD1:

{{yaml}}`,multipleSystemPrompt:""},{id:"auto2wyw",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u6587\u8A00\u6587\u7FFB\u8BD1\uFF0C\u8BF7\u7528\u6587\u8A00\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are you"}
    }
  ],
  "contextual_analysis": "Analysis of the word''t ","n''m sorry, but I cannot","^I'm sorry, but I cannot provide","^I'm sorry, I can''il vous pla\xEEt ne pas expliquer mon texte d''al:ios:url'][content^='medium://''rgh-seen-''rtl'] {text-align: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awgt0.r-1lzbym2  > [dir="ltr"] > span``
``
``${vZ}?token=${t}``width=500,height=650,left=${screen.width/2-250},top=${screen.height/2-325}``${xZ}?access_token=${e}`,n=new Request(t,{method:"GET"});function r(a){return new Promise((i,o)=>{a.status!=200&&o("Token validation error"),a.json().then(s=>{s.aud&&s.aud===CZ?i(e):o("Token validation error")})})}return fetch(n).then(r)}function rv(e,t=!1){let n=SZ;if(typeof window<"u"&&window.location.protocol.startsWith("http")){let a=window.location.hostname,i=window.location.port;n=`${window.location.protocol}//${a}${i?`:${i}`:""}/auth-done/`}return new _s(e,n).auth(t)}function av(e,t=!1){return rv(e,t).then(n=>n.access_token||null)}async function dD(){}var TZ="https://www.google-analytics.com/mp/collect",wZ=30,EZ=100;async function AZ(){let{sessionData:e}=await Y.storage.session.get("sessionData""firefox_store";if(n?.name?.startsWith("ImtFx")&&(r=!1),r)return;let{fakeUserId:a}=await To(),i=`${TZ}?measurement_id=${e.measurement_id}&api_secret=${e.api_secret}``updateUninstallUrl: ${t}``${e}/dark-32.png`,48:`${e}/dark-48.png`,64:`${e}/dark-64.png`,128:`${e}/dark-128.png`,256:`${e}/dark-256.png`}:{32:`${e}/32.png`,48:`${e}/48.png`,64:`${e}/64.png`,128:`${e}/128.png`,256:`${e}/256.png``content_script:main:${n}``browser.${t.id}`,{targetLanguage:kt(e.targetLanguage,e.interfaceLanguage,!1,!0)});Y.contextMenus.update(t.id,{title:n,visible:e.isShowContextMenu})}}async function _c({targetLanguage:e,text:t}){let n=await Ct(),r="toggleTranslatePage",a;if(t)xD=t,a=Se(n.interfaceLanguage,"browser.translateText",{text:t});else{let i=await ov(),o=i&&i!=="Original";a=Se(n.interfaceLanguage,"browser.toggleTranslatePage",{targetLanguage:kt(e??n.targetLanguage,n.interfaceLanguage,!1,!0)}),o&&(a=`${Se(n.interfaceLanguage,"show-original")}`);let s=Rr(n.shortcuts.toggleTranslatePage);s&&(a+=`(${s})`),xD=""}Y.contextMenus.update(r,{title:a,visible:n.isShowContextMenu})}async function Jl(){try{let e=await Ct(),t=await vD();Y.contextMenus.update(tm,{visible:t&&e.isShowContextMenu})}catch{}}function CD(e,t){let n=t;n||(n=Me().IMMERSIVE_TRANSLATE_INJECTED_CSS||"");let a=`${`--${j}-${e}`}: (.+?);``
`).filter(h=>!!h.trim()).length!=u.split(`
`).filter(h=>!!h.trim()).length&&!/<[^>]+>/g.test(t.innerHTML))return t.innerText||"";return o;function p(h){return h.replace(/\s+/g,"")}function g(h){return h instanceof HTMLElement?h.innerText:h?.textContent||""}function f(){let h=p(g(s)),x=p(l);if(h==x){let b=g(s).replace(/ */g,"");if(!u.startsWith(`
`)&&b?.startsWith(`
`)||!u.endsWith(`
`)&&b?.endsWith(`
`)){o+=l;return}o+=LD(s,a)}else o+=l}}function AD(e,t,n,r,a){if(!e||!t)return"";let i=LD(n,a)||"";return t.replace(i,r)}var sv=globalThis.document?.createElement("div");function VZ(e){return sv?(sv.innerHTML=qn?.createHTML(e)||e,sv?.childNodes?.[0]?.nodeType==Node.TEXT_NODE):!1}function kD(e,t,n){if(!t)return t;let r=t,a=/<([a-zA-Z0-9-]+)\s*([^<]*?)>/ig;return r.replace(a,(o,s,l,u)=>{if(n&&s.startsWith(n)&&s!=n||VZ(`<${s} ${l}/>`))return o;let c=Object.keys(e).length;return e[c]={tag:s,attributes:l,index:u,id:c},`<${c}>`}).replace(/&&/ig,"%26%26").replace(/&lt;/ig,"%3C").replace(/&gt;/ig,"%3E")}function MD(e,t,n,r,a){let i=/<\/?([a-zA-Z0-9-]+)\s*([^<]*?)>/ig,o=[],s=[],l=[],u=t.replace(i,(m,p,g,f)=>{if(t[f+1]=="/"){let x=o.findIndex(y=>y.tag==p);if(x==-1)return m;let b=o[x];return o=o.slice(x+1),n?`</${n}${b.id}>`:`</${b.tag}>`}else{let x=e[p];if(!x)return m;if(o.unshift(x),n)return`<${n}${p}>`;if(!x.attributes)return`<${x.tag}>`;let b=a||"";return`<${x.tag} ${r||"id"}=${b}${x.id}${b}>``<${n}${p}></${n}${p}>`:g=`<${m.tag} ${r||"id"}=${p}></${m.tag}>`,e[p].content=c.slice(m.begin,m.end),u=u.replace(e[p].content,g)}),u}function DD(e,t,n,r){let a="";return n?a=ID(e,t,n):a=PD(e,t,r),a.replace(/%26%26/ig,"&&").replace(/%3C/ig,"&lt;").replace(/%3E/ig,"&gt;")}function ID(e,t,n){let r=/<\s*\/?([a-zA-Z0-9-]+)\s*([^<]*?)>/ig;return t.replace(r,(i,o,s,l)=>{let u=t[l+1];if(o[0]!=n)return i;let c=o.slice(1),m=e[c];return m?u=="/"?m.content?"":`</${m.tag}>`:m.content?ID(e,m.content,n):m.attributes?`<${m.tag} ${m.attributes}>`:`<${m.tag}>`:i})}function PD(e,t,n){let r=new RegExp(`<\\s*([a-zA-Z0-9-]+)\\s*${n||"id"}=["']?(\\d+)["'']?([a-zA-Z0-9]+)[''"').replace(/&#39;/ig,"'").replace(/&#34;/ig,'"''"method":"',()=>{let t=e;return(t.id+3)%13===0||(t.id+5)%29===0?'"method" : "':'"method": "''"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"''"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"''${u.k.replace(/`/g,"\\`")}${c}': '${(u.v||u.k).replace(/`/g,"\\`")}''"')&&i?.endsWith('"''/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}async signedRequest({secretId:t,secretKey:n,action:r,payload:a,service:i,version:o}){let s=new Date().toISOString(),l=Math.random().toString(36).slice(2),u={Action:r,Version:o,Format:"JSON",AccessKeyId:t,SignatureNonce:l,Timestamp:s,SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0"},m=(h=>{let b=Object.keys(h).sort().map(y=>`${this.encode(y)}=${this.encode(h[y])}`).join("&");return`POST&%2F&${this.encode(b)}`})(Object.assign({},u,a)),p=this.SHA1.b64_hmac(`${n}&`,m),g=new URLSearchParams(Object.assign({},u,{Signature:p})).toString(),f=await super.request({retry:this.retry,url:`https://${i}.aliyuncs.com?${g}``%${t.charCodeAt(0).toString(16).toUpperCase()}`)}catch{return""}},Kv=e=>Object.keys(e).map(t=>{let n=e[t];if(typeof n>"u"||n===null)return;let r=Wv(t);if(r)return Array.isArray(n)?`${r}=${n.map(Wv).sort().join(`&${r}=`)}`:`${r}=${Wv(n)}``${wn.algorithm} Credential=${t.accessKeyId}/${a}`),r.push(`SignedHeaders=${this.signedHeaders()}`),r.push(`Signature=${await this.signature(t,n)}`),r.join(", ")}async getSignUrl(t,n){let r=this.getDateTime(n),a={...this.request.params},i=this.request.params,o=this.request.headers;t.sessionToken&&(a[wn.tokenHeader]=t.sessionToken),a[wn.dateHeader]=r,a[wn.notSignBody]="",a[wn.credential]=`${t.accessKeyId}/${this.credentialString(r)}``${this.canonicalHeaders()}
`),t.push(this.signedHeaders()),t.push(await this.hexEncodedBodyHash()),t.join(`
`)}canonicalHeaders(){let t=[];Object.keys(this.request.headers).forEach(r=>{t.push([r,this.request.headers[r]])}),t.sort((r,a)=>r[0].toLowerCase()<a[0].toLowerCase()?-1:1);let n=[];return t.forEach(r=>{let a=r[0].toLowerCase();if(this.isSignableHeader(a)){let i=r[1];if(typeof i>"u"||i===null||typeof i.toString!="function")throw new ee(`Header ${a} contains invalid value`);n.push(`${a}:${this.canonicalHeaderValues(i.toString())}`)}}),n.join(`
``${wn.kDatePrefix}${t.secretKey}``
``Unsupported language: ${a}`);a=this.langMap.get(a);let i=await this.checkLang(r,n);if(!i)return{text:n,from:r,to:a};r=i;let o=this.handleRequest(n,r,a),s=await super.request(o);return{text:this.handleResponseText(s),from:r,to:a}}async translateList(t){if(!Object.keys(this.apiServiceConfig).length)throw new ee("serivce id not found config");let{text:n,from:r,to:a}=t;if(!this.langMap.has(a))throw new ee(`Unsupported language: ${a}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${r==="auto"?"":d9.get(r)+"-"}${d9.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.toString()}``https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${t}&client_secret=${n}``
`+n+`
`+o+`
`+i}}async getCanonicalRequest(t){let n=await er(t),r="POST",a="/",i="",o=`content-type:application/json; charset=utf-8
host:hunyuan.tencentcloudapi.com
x-tc-action:`+this.action.toLowerCase()+`
`,s="content-type;host;x-tc-action";return{signedHeaders:s,canonicalRequest:r+`
`+a+`
`+i+`
`+o+`
`+s+`
`+n}}getDate(t){let n=t.getUTCFullYear(),r=("0"+(t.getUTCMonth()+1)).slice(-2),a=("0"+t.getUTCDate()).slice(-2);return`${n}-${r}-${a}``${Oe}silicon/get-token?deviceId=${this.deviceId}``${Oe}big-model/get-token?deviceId=${this.deviceId}``Bearer ${this.apiKey}`,...this.headerConfigs}),body:JSON.stringify(s,null,2),timeout:this.requestTimeout,retry:this.retry},u;try{return u=await this.rawRequest(l),{text:this.parseResponse(u),from:r,to:a}}catch(c){throw c}}};var p9=!1;async function Yc(e,t=!1){if(X(!1,!0)||!p9&&!t)return null;try{let n=await nP(e);return I.debug("server language detect:",n),n}catch(n){return I.debug("server language detect error",n),Nh(!1),null}}async function nP(e,t=1500){let n=new Promise((i,o)=>{setTimeout(()=>o(new Error(`Timeout after ${t}ms`)),t)}),r=De({url:`https://lang-detect.${et}/api/predict/batch``
`)?`"${t.replace(/"/g,'""''"''"',"&quot;"),x=c.message.replaceAll(`
``${j}-new-user-guide`;function B6(){return d("img",{class:`${Zte}-img`,src:An("images/new_float_ball_intro.png")})}var k1=`${j}-new-user-guide`;function N6(){let{t:e}=H();return d("div",{class:`${k1}-select-service-guide`,children:d("div",{class:`${k1}-select-service-guide-card`,children:[d("div",{class:`${k1}-max-model`,children:e("translationServicesGroup.pro")}),d("div",{class:`${k1}-model-example``${j}-new-user-guide`;function U6(){let{t:e}=H();return d("div",{class:`${VR}-video-subtitle-guide`,children:d("div",{class:`${VR}-video-subtitle-guide-card`,children:d("div",{children:[d("img",{src:j6,class:"service-icon"}),e("subtitle.quickButton.requestAiSubtitle")]})})})}var GR=`${j}-new-user-guide`;function z6(){let{t:e}=H();return d("div",{class:`${GR}-video-subtitle-guide`,children:d("div",{class:`${GR}-video-subtitle-guide-card`,children:[d("div",{children:[d("img",{src:O6,class:"service-icon"}),e("autoEnableSubtitle")]}),d(M1,{})]})})}var W0=`${j}-new-user-guide``${W0}-container`,style:m,children:[d("div",{class:`${W0}-close-icon`,onClick:e,children:d(jR,{})}),d("img",{class:`${W0}-bg ${n}`,src:sne}),d("div",{class:`${W0}-content ${n}`,children:[d(c,{}),d("div",{class:`${W0}-message text-red-500`,children:[i[r],xe().any&&r==="float-ball"?`
${a("floatBall.longPress")}`:""]})]}),d(_6,{position:n})]})}var sne="";var Yr=`${j}-new-user-guide`,KR=``;var K0=`${j}-new-user-guide-root``#${K0}`);a&&r.target!==a&&H6(K0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function H6(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Rr(e.config.shortcuts.toggleTranslatePage)})``meta[name='${XR}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(O){return l.test(O.trim())}let c=()=>g.value.trim()===""||!u(x.value)||x.value.trim()==="",m=document.createElement("div");m.innerText=s("reportInfo.title"),m.setAttribute("style",`text-align:left;margin-top:-20px;color:${lr.text};`),r.append(m);let p=document.createElement("div");p.setAttribute("style","display:flex;flex-direction:column;");let g=document.createElement("textarea");g.placeholder=s("reportInfo.reasonDesc"),g.required=!0,g.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${lr.border};
      background: ${lr.inputBackground};
      color: ${lr.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),g.setAttribute("id","reason");let f=document.createElement("div");f.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=g.id,h.innerHTML=`<span style="color: ${lr.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${lr.text};margin-right: 10px;`),f.append(h),f.append(g),g.addEventListener("input",function(){A.disabled=c()}),g.addEventListener("keydown",function(O){O.stopPropagation()}),p.append(f);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${lr.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${lr.inputBackground};
      color: ${lr.text};
      font-size: 14px;
      width: 303px;
      height: 45px;
      box-sizing: border-box;
      margin: 0 1px;
      outline: none;`),x.placeholder=s("reportInfo.emailPlaceholder"),ze.get(Xe,null).then(O=>{if(!O)return;let B=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(O.email);O.email&&!B&&(x.value=O.email)});let b=document.createElement("label");b.htmlFor=x.id,b.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,b.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${lr.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let v=document.createElement("div");v.textContent=s("reportInfo.emailError"),v.setAttribute("style",`color: ${lr.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${lr.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${lr.text};`);let w=document.createElement("div");w.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${lr.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function Pne(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${Oe}v1/img/img-upload-url?fileType=${a}&imgHash=${t}&comicHash=${n}&domain=${HF(location.hostname)}``/${t}/task-state?comicHash=${n}&domain=${HF(location.hostname)}``<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-cancel-btn`,m.innerText=s("close"),m.onclick=()=>{i(m,!0)},a.appendChild(m),Qa(e,"freeImageError")}function VF(e,t,n,r,a,i){let o=e.rule.imageRule,s=Se.bind(null,e.config.interfaceLanguage),l=document.createElement("div");l.innerText=s("freeImage.title"),l.setAttribute("style","text-align:left;margin-top:-20px;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=`${j}-btn ${j}-cancel-btn``${c}px Arial`,p=this.breakTextIntoLines(t,l-this.H_PADDING);c<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${p}">${u.message}</span>`),pu[p]={ok:!1,sentence:m},H_(l,e,t,n,u)):c&&(f.innerHTML=St.sanitize(c.text),pu[p]={ok:!0,sentence:m}),document.dispatchEvent(new CustomEvent(cn,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function z_(e){let t=0,n=0;Jn("Translating"),document.dispatchEvent(new CustomEvent(cn,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(pu),a=[],i=[],o=m0(e,"");for(let s of r){let l=pu[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let m=c.parentElement;if(c.remove(),m){delete pu[s];let p=o.cloneNode(!0);p.id=s,m.appendChild(p),i.push(l.sentence)}}}}try{await mn({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,m=Mc(document.body,c);if(!m)return;let p=m.parentElement;p&&(m.remove(),s?(t+=1,p.innerHTML=St.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${og}='${r}''"+hi(t.document.activeElement)+"''[contenteditable="true"], [contenteditable=""]''/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r?\n/g,"<BR>"),htmlDecode:(e,t)=>e.replace(/<BR\s*\/?>/gi,t||`\r
`).replace(/&nbsp;/g," ").replace(/&quot;/g,'"').replace(/&#39;/g,"'']?(\d+)[^\d>]*>([\s\S]*)/i.exec(c);if(m){let p={};p.type="caption",p.start=parseInt(m[1]),p.end=p.start+2e3,p.duration=p.end-p.start,p.content=m[2].replace(/^<\/SYNC[^>]*>/gi,"");let g=!0,f=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}U0.call(Fe,Fe.ctx)}async function Ex(e,t){let n={};e&&e.detail?.trigger&&(n.trigger=e.detail.trigger),Te({key:"share_to_draft""reportActive")||"")return;I.debug("\u672A\u6FC0\u6D3B\u8FC7");let t=await Da();await De({url:Oe+"v1/user/campaign-info-translated/"+t,method:"POST",headers:{"content-type":"application/json"}}),kl("reportActive","1")}catch(e){I.error(e)}}function NN(e,t){let n=e.rule?.subtitleRule,r=n?.autoEnableSubtitle,a=n?.enableTriggerTranslate||n?.liveSubtitleRule?.enableTriggerTranslate;!r&&a&&document.dispatchEvent(new CustomEvent(Ru,{detail:{tempEnableSubtitle:t}}))}async function woe(e){let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;ar;let r=n.config.rules?.find(a=>Us(e.currentUrl,a));if(n.rule.id!=r?.id){I.debug("new rule.id",r?.id,"old id",n.rule.id),I.debug("url change newRule",r);try{Ys(!1);let a=n.config.generalRule;r&&(n.rule=Ul(a,r),n.rule=await oh(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await dh("updateRuleByUrlChange",t.mutationChangeDelay||50),Jn("Translated");let i=FN(n);return n.filterRule=Gn(n,!0),LN(i),!0}catch(a){return I.debug(a),!1}}return ar,!1}var D5=[],Eoe=new AbortController,{signal:Aoe}=Eoe,fp=0,hp=0,yu=!1,Zs,ON=0,Wo=[];function f5(e){koe(),od().forEach(t=>{B4(e,t)})}function B4(e,t=window){let n=e,r=n.config,a=r.generalRule.mouseHoverHoldKey==="Off",i=r.mouseModifierKeyPressTimeout||400;n.state.isTranslateDirectlyOnHover===!0&&(a=!1);let o=n.state.isTranslateDirectlyOnHover===!0||r.generalRule.mouseHoverHoldKey==="Auto";r.generalRule.mouseHoverHoldKey==="MouseHoldKeyPressHold"&&Loe(n,t);function s(){o=!o,o?(I.debug("mouse hover translate on"),n.state.isTranslateDirectlyOnHover=!0,B4(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,B4(n,t))}t.document.addEventListener($d,s),Wo.push(()=>{t.document.removeEventListener($d,s)});let l=Dr(p=>{if(!(Math.abs(p.clientX-fp)+Math.abs(p.clientY-hp)<=3)&&(fp=p.clientX,hp=p.clientY,yu&&!Zs&&L5(n,!1,t),o||yu&&!Zs)){let g=HN(n.rule,t);if(g){if(P5(g))return;N4(n,!1,t,g)}}},o?700:300),u=p=>{let g=p.target;P5(g)||(N4(n,!0,t),L5(n,!0,t,!0))},c=p=>{let g=r?.generalRule?.mouseHoverHoldKey?.toLowerCase()||"alt";if(p.type==="keydown"&&(D5=Ot.getPressedKeyCodes()),p.type==="keyup"){let f=D5,h=mg[g]===f[0];if(f.length>1&&h&&(ON=Date.now(),yu=!1),f.length===1&&h){let x=Date.now();yu=!0,Zs&&clearTimeout(Zs),Zs=setTimeout(()=>{let b=ON-x;b>0&&b<=i?yu=!1:u(p),Zs=void 0},i)}D5=[]}};if(Wo.push(()=>{Zs&&clearTimeout(Zs)}),a)return;gp("mousemove",l,t),Wo.push(()=>{t.removeEventListener("mousemove",l)});function m(){o?l.cancel():yu=!1}if(gp("blur",m,t),Wo.push(()=>{t.removeEventListener("blur",m)}),!o){let p=r?.generalRule?.mouseHoverHoldKey?.toLowerCase()||"alt",g=KT;gp("keyup",jN,t),Wo.push(()=>{t.removeEventListener("keyup",jN)}),g.includes(p)?Ot("*",{scope:"mouseHover",element:t.document,keyup:!0},c):Ot(r.generalRule.mouseHoverHoldKey,{scope:"mouseHover",element:t.document},f=>{if(p==="*"){f.key==="*"&&u(f);return}u(f)}),Ot.setScope("mouseHover"),Wo.push(()=>{Ot.deleteScope("mouseHover")})}}function koe(){try{Wo.forEach(e=>e())}catch{}Wo=[],Ot.setScope("all")}function jN(e){yu=!1}function gp(e,t,n=window){return n.addEventListener(e,t,{signal:Aoe})}function N4(e,t,n,r){if(r=r||HN(e.rule,n),!r){I.debug("can not find selection part!");return}if(Doe(e,r,t))return;if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.setupMouseHoverListener=B4);let o={...Gn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.documentElement)!==u.state.translationMode&&Fi(n.document.documentElement,u.state.translationMode),cu(l,c,!0,"hover").then(p=>{if(i.autoIncreaseParagraphId=l.autoIncreaseParagraphId,i.paragraphEntities=l.paragraphEntities,i.paragraphQueue=l.paragraphQueue,p&&!s){s=!0;let g=Date.now();pt(PT,g);let f={translation_service:l?.translationService||""};Te({key:"mouse_hover_translate",ctx:{...u,sourceLanguage:"mouseHover"},params:f})}})},onFrame:()=>{},onIgnoreElement:()=>{}})}function HN(e,t){return Moe(fp,hp,e,t)}function Moe(e,t,n,r){let a=aD(e,t,n,r);if(a==null)return;if(a instanceof HTMLElement)return a;let i=()=>{let l=r.document.elementFromPoint(e,t);if(!l)return;let u=ev(l,e,t);return u===l?l.nodeName==="BUTTON"?l:void 0:zN(u,n)},o=()=>{try{a.setStartBefore(a.startContainer),a.setEndAfter(a.startContainer)}catch(u){I.debug("get mouse over word fail",u)}let l=a.getBoundingClientRect();if(!(l.left>e||l.right<e||l.top>t||l.bottom<t))return zN(a.startContainer,n)},s;return a.startContainer.nodeType!==Node.TEXT_NODE?s=i():s=o(),s}function P5(e){return!!(!(e.nodeType===Node.ELEMENT_NODE||e.nodeType===Node.TEXT_NODE)||e.nodeType===Node.ELEMENT_NODE&&Rs(e,_T))}function Doe(e,t,n){let r=Ioe(t);if(n&&r.length)return r.forEach(a=>{Poe(e,a)}),!0}function Ioe(e){let t=[e];if(e.nodeName=="FONT"&&e.className.includes(j)){let n=e.closest(`.${j}-target-wrapper`);n&&(t=[n.parentElement])}else{let r=[...e.querySelectorAll(`.${j}-target-wrapper``fingers.${t.generalRule[r[n]]}``${p(g)}``translationServices.${g}``desc.${x}`)!==`desc.${x}`&&(y=u(`desc.${x}`));let C="";b==="finger"?C=F5(n,r,x):b==="mouseHoverHoldKey"?r?.generalRule&&r?.generalRule[x]!==void 0?C=r.generalRule[x]:C=t.generalRule[x]:r?.shortcuts&&r?.shortcuts[x]!==void 0?C=r.shortcuts[x]:C=t.shortcuts[x];let S=u(`browser.${x}`,{targetLanguage:kt(t.targetLanguage,t.interfaceLanguage,!1,!0)});(b==="finger"||b==="mouseHoverHoldKey")&&(S=u(`${x}``shortcutGroup.${f.name}`)}),f.shortcuts.map((x,b)=>el(Js,{...x,key:`shortcut-${b}``shortcut-group-${h}``${j}-${g}-${cc()}.json`)}}}function j5({config:e,ctx:t}){let n=xn(),{t:r,lang:a}=H(),i=Bt(e,t.isPro);return d(ce,{children:[d("header",{className:"header-navbar",children:[d("a",{class:"header-navbar-brand",href:i?"javascript:void(0)":te,target:"_blank",children:[d(UP,{}),d("h1",{children:r("browser.shortBrandName")})]}),d("span",{style:{cursor:"pointer"},class:"version",onClick:o=>{o.preventDefault(),!i&&qr(`${te}docs/CHANGELOG/#${n.replace(/\./gi,"")}``<div>
<br>
    <a target="_blank" href="${GT}">${l("feedbackOrMore")}</a>
  </div>`,m=[...u.map((g,f)=>`<span>
        <a target="_blank" href="${g.url}">${g.name}</a>
        ${f!==u.length-1?", ":""}
      </span>``nav ${n}``nav ${t}`,children:[d(Ce,{title:l("image.enableTools")}),d("label",{for:"enable",children:d("input",{type:"checkbox",id:"enable",name:"switch",role:"switch",onChange:u=>{let c=u.target.checked;r(m=>({...m,generalRule:{...m.generalRule,"imageRule.add":{...m.generalRule?.["imageRule.add"],enableTools:c}}}))},checked:s.enableTools})})]})})}function QN({ctx:e,className:t,styles:n}){let[r,a,i,o,s]=Ae(pe),{t:l}=H();return d(ce,{children:d("div",{class:`nav ${t}``nav ${t||""}`,style:{justifyContent:"unset",...n},children:[d("legend",{class:"mb-4",children:o("chooseProviderLabel")}),d("div",{class:"flex flex-col",children:["client","pro"].map(f=>{let h=o(`chooseProvider.${f}`),x=o(`chooseProvider.${f}Desc``nav ${t}``#terms/${B.id}`}catch{alert(s("terms.saveError"))}finally{a(!1),T()}})()},[v,r,s,T,C]),K(()=>{y&&(alert(y),T())},[y,T]);let w=async()=>{o(!0)},A=async()=>{try{b(!0);let R={id:`custom-${ki(8)}`,name:s("terms.unname"),description:"",matches:["*"],author:"self",glossaries:[],active:!0,lastUserOpTime:Date.now()+""};await Kr(R),await fc(Date.now()+""),window.location.href=`#terms/${R.id}``https://github.com/${j}/terms``#terms/${e.id}`},children:[d("div",{className:"flex items-center justify-between mb-4",children:[d("div",{className:"mb-0 pr-2 flex justify-start flex-col text-left assistant-content flex-1 cursor-pointer",children:[d("div",{className:"text-base font-bold assistant-text",children:i}),d("div",{className:"font-normal assistant-text",style:{color:"#999999",fontSize:"14px"},children:e.author?`@${e.author}`:""})]}),d("div",{className:"flex items-center",onClick:l=>l.stopPropagation(),children:d("label",{className:"relative inline-flex items-center cursor-pointer",onClick:s,children:d("input",{type:"checkbox",role:"switch",checked:e.active})})})]}),d("p",{class:"cursor-pointer",dangerouslySetInnerHTML:{__html:o}})]})}function eO({total:e,currentPage:t,totalPages:n,itemsPerPage:r,goToPrevPage:a,goToNextPage:i,onItemsPerPageChange:o}){let{t:s}=H(),l=`items-per-page-${t}``${a}_glossary.csv``lang-${q}-${U}`))]})}),d("td",{children:d("a",{href:"javascript:void(0)",onClick:()=>F(q),title:i("delete"),style:{display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"0.25rem"},children:d(si,{style:{width:32,height:32}})})})]},`${L.k}-${q}``\u6210\u529F\u5C06\u672F\u8BED\u5E93 ${_.id} \u7684\u66F4\u6539\u540C\u6B65\u5230\u670D\u52A1\u5668`)}catch(N){I.error(`\u540C\u6B65\u672F\u8BED\u5E93 ${_.id} \u5230\u670D\u52A1\u5668\u5931\u8D25:``min-[${Ko}px]:mt-6`,children:d("nav",{class:`menu-nav flex flex-col h-full justify-between min-[${Ko}px]:pr-2`,children:[d("div",{class:`max-[${Ko}px]:px-2`,children:d("ul",{class:`flex flex-wrap flex-start min-[${Ko}px]:block min-[${Ko}px]:text-base`,children:s.map((m,p)=>d("li",{class:"li",children:d("a",{...m.props,children:m.name})},`nav-${p}`))})}),d("div",{class:`flex flex-wrap min-[${Ko}px]:flex-col max-[${Ko}px]:px-4``https://weblate.${et}/browse/${j}/extension/${r}/`,children:n("helpToTranslate")}),d("a",{class:"py-3 no-focus secondary  mr-2",href:"#developer",hidden:i,children:n("developer")})]})]})}),d("div",{role:"main",class:`relative max-[${Ko}px]:px-4 min-[${Ko}px]:pt-6`,children:d(tv,{value:t,defaultCase:a1,cases:u})})]})})]})}async function nO(){await JN();let e=document.getElementById("mount");e&&(e.classList.add("min-h-screen","flex","flex-col"),(async()=>{h2(KL);let t=await Ct();if(location.href.includes(Lu)&&t.joinJobs){let r=xw.replace("{jobs}",t.joinJobs.map(a=>`    \u2022 ${a}`).join(`
`))}t.debug&&I.setLevel("debug"),globalThis.location.hash||(globalThis.location.hash="#general");let n=await vr({url:"http://localhost",config:t});Te({key:"options_page_view""visibilitychange",i),()=>{globalThis.removeEventListener("visibilitychange",i)}},[a]),K(()=>{let i=async()=>(await a(),!0);return globalThis.addEventListener("beforeunload",i),()=>{globalThis.removeEventListener("beforeunload",i)}},[a])}function nse(){let e=En(),[t,n]=e;K(()=>{if(!t||!n)return;(async()=>{try{let a=new URL(globalThis.location.href),i=a.searchParams.get("token");if(!i||(a.searchParams.delete("token"),globalThis.history.replaceState(null,"",a.toString()),(await ze.get(Xe,null))?.token===i))return;let l=(await rse(i)).data;l.token=i,await ze.set(Xe,l),await iu(l.token,t,n),globalThis.location.reload()}catch{}})()},[t])}function rse(e){return De({responseType:"json",url:Oe+"v1/user",method:"get",headers:{token:e}})}function ase(e,t,n){let r=xn(),a=vn(r,"1.16.1"),{t:i,lang:o}=H(),{beta:s}=t,l=Bt(t,e.isPro),u=t.generalRule?.imageRule?.enable,c=[{name:i("general"),props:{href:"#general",className:"secondary"}},{name:i("translationServiceNav"),props:{href:"#services",className:"secondary"}},...s||t.enableAiAssistant?[{name:i("field.assistant"),props:{href:"#ai",className:"secondary"}}]:[],...a?[{name:i("field.terms"),props:{href:"#terms",className:"secondary"}}]:[],{name:i("subtitle"),props:{href:"#subtitle",className:"secondary"}},...u&&!l?[{name:Ni(e,!0)?i("mangaAndImage"):i("manga"),props:{href:"#manga",className:"secondary"}}]:[],{name:i("inputOptions"),props:{href:"#input",className:"secondary"}},...xe().any||X()?[]:[{name:i("selectionTranslation"),props:{href:"#selection_translation",className:"secondary"}}],{name:i("mouseHoverOptions"),props:{href:"#mouse_hover",className:"secondary"}},{name:i("floatBallOptions"),props:{href:"#floating",className:"secondary"}},{name:i("shortcutSettings"),props:{href:"#shortcuts",className:"secondary"}},{name:i("advanced"),props:{href:"#advanced",className:"secondary"}},{name:i("import_export"),props:{href:"#import_export",className:"secondary"}},{name:i("about"),props:{href:"#about",className:"secondary"}}];return o.startsWith("zh")||c.splice(c.length-1,0,{name:i("contact"),props:{href:"#contact",className:"secondary"}}),c.forEach(m=>{if(m.props.href==="#services"){if(!n.startsWith("#services"))return;m.props.className="primary",m.props["aria-current"]="page";return}if(m.props.href==="#ai"){if(!n.startsWith("#ai"))return;m.props.className="primary",m.props["aria-current"]="page";return}n===m.props.href&&(m.props.className="primary",m.props["aria-current"]="page")}),c}nO();})();

