#!/usr/bin/env python3
"""
简单但高效的跨文件去重工具
专门用于移除在所有文件中都出现的大块相同内容
"""

import os
import re
from typing import List, Dict, Set

class SimpleDeduplicator:
    def __init__(self):
        self.files_content = {}
        
    def load_files(self, file_paths: List[str]) -> None:
        """加载所有文件"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.files_content[file_path] = content
                    print(f"加载: {os.path.basename(file_path)} ({len(content):,} 字符)")
    
    def find_common_large_blocks(self, min_size: int = 1000) -> List[str]:
        """找出所有文件中都存在的大块内容"""
        if len(self.files_content) < 2:
            return []
        
        print(f"\n查找大小 >= {min_size} 字符的公共块...")
        
        # 获取第一个文件作为基准
        first_file = list(self.files_content.values())[0]
        other_files = list(self.files_content.values())[1:]
        
        common_blocks = []
        
        # 使用滑动窗口查找公共块
        step = 500  # 步长，提高效率
        
        for start in range(0, len(first_file) - min_size + 1, step):
            if start % 50000 == 0:
                print(f"  进度: {start/len(first_file)*100:.1f}%")
            
            # 尝试不同的块大小
            for size in [5000, 3000, 2000, 1500, min_size]:
                if start + size > len(first_file):
                    continue
                    
                block = first_file[start:start + size]
                
                # 跳过主要是空白的块
                if len(block.strip()) < size * 0.3:
                    continue
                
                # 检查是否在所有其他文件中都存在
                if all(block in other_file for other_file in other_files):
                    # 检查是否已经被更大的块包含
                    if not any(block in existing_block for existing_block in common_blocks):
                        # 移除被当前块包含的较小块
                        common_blocks = [b for b in common_blocks if b not in block]
                        common_blocks.append(block)
                        print(f"  发现公共块 (大小: {size}): {block[:80].replace(chr(10), ' ')[:80]}...")
                        break  # 找到最大的块后跳出
        
        # 按大小排序
        common_blocks.sort(key=len, reverse=True)
        return common_blocks
    
    def find_css_and_constants(self) -> List[str]:
        """专门查找CSS和常量定义"""
        print("\n查找CSS和常量定义...")
        
        # 定义要查找的模式
        patterns = [
            # CSS样式块
            r'\.immersive-translate[^{]*\{[^}]*\}',
            r'\[imt-[^{]*\{[^}]*\}',
            
            # 大型字符串常量
            r'IMMERSIVE_TRANSLATE_[A-Z_]+\s*:\s*["`\'][^`"\']{500,}["`\']',
            
            # 配置对象
            r'\{[^{}]*"immersivetranslate\.com"[^{}]*\}',
            r'\{[^{}]*"https://[^"]*immersivetranslate[^{}]*\}',
            
            # 语言代码数组
            r'\["auto","zh-CN"[^\]]*\]',
            
            # 大型对象字面量
            r'\{[^{}]{2000,}\}',
        ]
        
        common_patterns = []
        
        for pattern in patterns:
            print(f"  检查模式: {pattern[:50]}...")
            
            # 在第一个文件中查找匹配
            first_file = list(self.files_content.values())[0]
            matches = re.findall(pattern, first_file, re.DOTALL)
            
            if not matches:
                continue
            
            # 检查哪些匹配在所有文件中都存在
            other_files = list(self.files_content.values())[1:]
            
            for match in matches:
                if len(match) > 200 and all(match in other_file for other_file in other_files):
                    if match not in common_patterns:
                        common_patterns.append(match)
                        print(f"    找到公共模式 (大小: {len(match)})")
        
        return common_patterns
    
    def remove_common_content(self, content: str, common_blocks: List[str], common_patterns: List[str]) -> tuple:
        """从内容中移除公共块和模式"""
        cleaned_content = content
        total_removed = 0
        
        # 移除大块内容
        for i, block in enumerate(common_blocks):
            if block in cleaned_content:
                old_size = len(cleaned_content)
                cleaned_content = cleaned_content.replace(block, f'/* REMOVED_COMMON_BLOCK_{i} */')
                removed = old_size - len(cleaned_content)
                total_removed += removed
                if removed > 0:
                    print(f"    移除公共块 {i}: {removed:,} 字符")
        
        # 移除模式匹配的内容
        for i, pattern in enumerate(common_patterns):
            if pattern in cleaned_content:
                old_size = len(cleaned_content)
                cleaned_content = cleaned_content.replace(pattern, f'/* REMOVED_PATTERN_{i} */')
                removed = old_size - len(cleaned_content)
                total_removed += removed
                if removed > 0:
                    print(f"    移除公共模式 {i}: {removed:,} 字符")
        
        return cleaned_content, total_removed
    
    def cleanup_content(self, content: str) -> str:
        """清理内容"""
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        
        # 合并连续的移除标记
        content = re.sub(r'(/\*\s*REMOVED_[^*]*\*/\s*){2,}', '/* MULTIPLE_REMOVED_BLOCKS */', content)
        
        # 清理语法错误
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r'{\s*,', '{', content)
        content = re.sub(r',\s*}', '}', content)
        
        return content
    
    def process_all_files(self, output_dir: str = "simple_dedup") -> Dict:
        """处理所有文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 查找公共内容
        common_blocks = self.find_common_large_blocks(1000)
        common_patterns = self.find_css_and_constants()
        
        print(f"\n找到 {len(common_blocks)} 个公共块")
        print(f"找到 {len(common_patterns)} 个公共模式")
        
        results = {}
        
        print("\n" + "=" * 60)
        print("开始处理文件...")
        
        for file_path, original_content in self.files_content.items():
            filename = os.path.basename(file_path)
            print(f"\n处理: {filename}")
            
            # 移除公共内容
            cleaned_content, total_removed = self.remove_common_content(
                original_content, common_blocks, common_patterns
            )
            
            # 清理内容
            cleaned_content = self.cleanup_content(cleaned_content)
            
            # 保存文件
            output_path = os.path.join(output_dir, f"clean_{filename}")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            # 统计结果
            original_size = len(original_content)
            cleaned_size = len(cleaned_content)
            compression_ratio = (1 - cleaned_size / original_size) * 100
            
            results[filename] = {
                'original_size': original_size,
                'cleaned_size': cleaned_size,
                'compression_ratio': compression_ratio,
                'removed_size': total_removed,
                'output_path': output_path
            }
            
            print(f"  原始: {original_size:,} 字符 ({original_size/1024:.1f} KB)")
            print(f"  清理后: {cleaned_size:,} 字符 ({cleaned_size/1024:.1f} KB)")
            print(f"  压缩比例: {compression_ratio:.1f}%")
            print(f"  移除: {total_removed:,} 字符")
        
        return results

def main():
    deduplicator = SimpleDeduplicator()
    
    # 文件列表
    js_files = [
        'background.js',
        'content_script.js', 
        'content_start.js',
        'offscreen.js',
        'options.js',
        'popup.js',
        'side-panel.js'
    ]
    
    print("简单跨文件去重工具")
    print("=" * 60)
    
    # 加载文件
    deduplicator.load_files(js_files)
    
    if len(deduplicator.files_content) < 2:
        print("需要至少2个文件进行比较")
        return
    
    # 处理文件
    results = deduplicator.process_all_files()
    
    # 打印总结
    print("\n" + "=" * 60)
    print("处理完成！总结:")
    
    total_original = sum(r['original_size'] for r in results.values())
    total_cleaned = sum(r['cleaned_size'] for r in results.values())
    overall_compression = (1 - total_cleaned / total_original) * 100
    
    print(f"总原始大小: {total_original:,} 字符 ({total_original/1024:.1f} KB)")
    print(f"总清理后大小: {total_cleaned:,} 字符 ({total_cleaned/1024:.1f} KB)")
    print(f"总体压缩比例: {overall_compression:.1f}%")
    print(f"\n清理后的文件保存在: simple_dedup/ 目录")

if __name__ == "__main__":
    main()
