emailError"),v.setAttribute("style",`color: ${lr.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${lr.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${lr.text};`);let w=document.createElement("div");w.setAttribute("style",`marg
ing":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.cons
e}=H();return d("div",{class:`${VR}-video-subtitle-guide`,children:d("div",{class:`${VR}-video-subtitle-guide-card`,children:d("div",{children:[d("img",{src:j6,class:"service-icon"}),e("subtitle.quickButton.requestAiSubtitle")]})})})}var GR=`${j}-new-user-guide`;function z6(){let{t:e}=H();return d("div",{class:`${GR}-video-subtitle-guide`,children:d("div",{class:`${GR}-video-subtitle-guide-card`,childre
obalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globa
nts>1&&typeof globalThis.MSStream>"u";function nH(e){return t=>t.test(e)}function xe(e){let t={userAgent:"",platform:"",maxTouchPoints:0};!e&&typeof navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let 
(()=>{var lO=Object.defineProperty;var $5=(e,t)=>{for(var n in t)lO(e,n,{get:t[n],enumerable:!0})};var REMOVED_CONSTANTS={};var h="imt-subtitle-inject""message",s))};globalThis.addEventListener("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.ad
return;case"\\":ae(),at="identifierNameStartEscape";return;case"}":return Kt("punctuator",ae());case'"':case"'":km=ae()==='"''${hq(e)}' at ${ms}:${za}`)}function Tl(){return Z3(`JSON5: invalid end of input at ${ms}:${za}`)}function G8(){return za-=5,Z3(`JSON5: invalid identifier character at ${ms}:${za}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on 
;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=`${j}-btn ${j}-cancel-btn``${c}px Arial`,p=this.breakTextIntoLines(t,l-this.H_PADDING);c<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${p}">${u.message}</span>`)
lass:`${Zte}-img`,src:An("images/new_float_ball_intro.png")})}var k1=`${j}-new-user-guide`;function N6(){let{t:e}=H();return d("div",{class:`${k1}-select-service-guide`,children:d("div",{class:`${k1}-select-service-guide-card`,children:[d("div",{class:`${k1}-max-model`,children:e("translationServicesGroup.pro")}),d("div",{class:`${k1}-model-example``${j}-new-user-guide`;function U6(){let{t:e}=H();return
F(location.hostname)}``<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-cancel-btn`,m.innerText=s
Loe(n,t);function s(){o=!o,o?(I.debug("mouse hover translate on"),n.state.isTranslateDirectlyOnHover=!0,B4(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,B4(n,t))}t.document.addEventListener($d,s),Wo.push(()=>{t.document.removeEventListener($d,s)});let l=Dr(p=>{if(!(Math.abs(p.clientX-fp)+Math.abs(p.clientY-hp)<=3)&&(fp=p.clientX,hp=p.clientY,yu&&!Zs&&L5(n,!1,t),o||yu&&!Z
ge/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${u}activities/components/image-pro`;var vr=50*1e4,Er=`[${Y}-ctx-divider]`,Cr=`${Y}_context_preview`;var Pr=`${o}_selection_update_params`,Ar=`data-${l}-subtitle-type`,Ir=`data-${l}-ai-subtitle-url`,Lr=`data-${l}-has-subtitle``*+.,;:\\/<=>?@#$%&^|~()[\]{}]/g,"\\$&").replace(/^\d/,"\\3$& "));function wu(e){for(var t=9,n
==""||!u(x.value)||x.value.trim()==="",m=document.createElement("div");m.innerText=s("reportInfo.title"),m.setAttribute("style",`text-align:left;margin-top:-20px;color:${lr.text};`),r.append(m);let p=document.createElement("div");p.setAttribute("style","display:flex;flex-direction:column;");let g=document.createElement("textarea");g.placeholder=s("reportInfo.reasonDesc"),g.required=!0,g.setAttribute("style
{j}-new-user-guide`,KR=``;var K0=`${j}-new-user-guide-root``#${K0}`);a&&r.target!==a&&H6(K0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function H6(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Rr(e.config.shortcuts.toggleTranslatePage)})``meta[name='${XR}'][content='true''
Event(new CustomEvent(cn,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(pu),a=[],i=[],o=m0(e,"");for(let s of r){let l=pu[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let m=c.parentElement;if(c.remove(),m){delete pu[s];let p=o.cloneNode(!0);p.id=s,m.appendChild(p),i.push(l.sentence)}}}}try{await mn({sentences:i},e,(s,l,u)=>{n+=1;let c=u.i
<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${p}">${u.message}</span>`),pu[p]={ok:!1,sentence:m},H_(l,e,t,n,u)):c&&(f.innerHTML=St.sanitize(c.text),pu[p]={ok:!0,sentence:m}),document.dispatchEvent(new CustomEvent(cn,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function z_(e){let t=0,n=0;Jn("Translating"),document.dispatchEvent(new CustomEvent(c
can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=
e(/^<\/SYNC[^>]*>/gi,"");let g=!0,f=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}U0.call(Fe,Fe.ctx)}async functi
=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}text/`;var yr=u+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`htt
}`)}function G8(){return za-=5,Z3(`JSON5: invalid identifier character at ${ms}:${za}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick
uide`,KR=``;var K0=`${j}-new-user-guide-root``#${K0}`);a&&r.target!==a&&H6(K0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function H6(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Rr(e.config.shortcuts.toggleTranslatePage)})``meta[name='${XR}'][content='true''*+/=?^_`{|}
0C\u5219\u4FDD\u6301\u539F\u6587\u4E0D\u53D8\u3002\u4E0D\u8981\u89E3\u91CA\uFF0C\u8F93\u5165\u6587\u672C:
 {{text}}`,multipleSystemPrompt:""},{id:"auto2zh-TW",prompt:`;; Treat next line as plain text input and translate it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanatio
n y3!=null?!1:(y3=e,!0)}function C3(){return X(!1,!0)?"monkey":Yn()?"chrome":In()?"firefox":rt()?"safari":null}function zw(){let e=globalThis.innerWidth,t=!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement;return e<768&&t}var Hw={addListener:()=>{},removeListener:()=>{},hasListener:()=>{}},qw={permissions:{contains:()=>{},request:()=>{}},runtime:{onMessage:Hw,openOptionsPage:()=>{},lastE
he same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let o=this.getErrorMsg(t);return this.status===429?this.data?.translationService=="google"?o=`${n("er
\u6587\u7684\u65B9\u5F0F\u8FDB\u884C\u7FFB\u8BD1\uFF0C\u5C3D\u53EF\u80FD\u8D34\u8FD1\u53E4\u98CE\uFF0C\u53EA\u8FD4\u56DE\u8BD1\u6587\uFF0C\u4E0D\u542B\u4EFB\u4F55\u89E3\u91CA\u3002"""},{id:"ja2zh-CN",selectionSystemPrompt:``},{id:"en2en""phonetic": "/h\u0259\u02C8l\u0259\u028A/",
  "definitions": [
    {
      "pos": "adj.",
      "meaning": "hello",
      "example": {
        "source": "Hello, how are 
 assistant-text",children:i}),d("div",{className:"font-normal assistant-text",style:{color:"#999999",fontSize:"14px"},children:e.author?`@${e.author}`:""})]}),d("div",{className:"flex items-center",onClick:l=>l.stopPropagation(),children:d("label",{className:"relative inline-flex items-center cursor-pointer",onClick:s,children:d("input",{type:"checkbox",role:"switch",checked:e.active})})})]}),d("p",{cl
4px"},children:e.author?`@${e.author}`:""})]}),d("div",{className:"flex items-center",onClick:l=>l.stopPropagation(),children:d("label",{className:"relative inline-flex items-center cursor-pointer",onClick:s,children:d("input",{type:"checkbox",role:"switch",checked:e.active})})})]}),d("p",{class:"cursor-pointer",dangerouslySetInnerHTML:{__html:o}})]})}function eO({total:e,currentPage:t,totalPages:n,ite
udeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.documentElement)!==u.state.translationMode&&Fi(n.document.documentElement,u.state.translationMode),cu(l,c,!0,"hover").then(p=>{if(i.autoIncreaseParagraphId=l.autoIncreaseParagraphId,i.paragraphEntitie
 ${lr.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${lr.text};margin-right: 10px;`),f.append(h),f.append(g),g.addEventListener("input",function(){A.disabled=c()}),g.addEventListener("keydown",function(O){O.stopPropagation()}),p.append(f);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${lr.border};
      border-ra
t g=document.createElement("textarea");g.placeholder=s("reportInfo.reasonDesc"),g.required=!0,g.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${lr.border};
      background: ${lr.inputBackground};
      color: ${lr.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),g.setAttribute("id","reason");let f=document.createElement("div");f.setAtt
rule,n),!r){I.debug("can not find selection part!");return}if(Doe(e,r,t))return;if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.setupMouseHoverListener=B4);let o={...Gn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.
hor?`@${e.author}`:""})]}),d("div",{className:"flex items-center",onClick:l=>l.stopPropagation(),children:d("label",{className:"relative inline-flex items-center cursor-pointer",onClick:s,children:d("input",{type:"checkbox",role:"switch",checked:e.active})})})]}),d("p",{class:"cursor-pointer",dangerouslySetInnerHTML:{__html:o}})]})}function eO({total:e,currentPage:t,totalPages:n,itemsPerPage:r,goToPrev
udeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.documentElement)!==u.state.translationMode&&Fi(n.document.documentElement,u.state.translationMode),cu(l,c,!0,"hover").then(p=>{if(i.autoIncreaseParagraphId=l.autoIncreaseParagraphId,i.paragraphEntities=l.paragraphEntities,i.paragraphQueue=l.paragraphQueue,p&
a target="_blank" href="${g.url}">${g.name}</a>
        ${f!==u.length-1?", ":""}
      </span>``nav ${n}``nav ${t}`,children:[d(Ce,{title:l("image.enableTools")}),d("label",{for:"enable",children:d("input",{type:"checkbox",id:"enable",name:"switch",role:"switch",onChange:u=>{let c=u.target.checked;r(m=>({...m,generalRule:{...m.generalRule,"imageRule.add":{...m.generalRule?.["imageRule.add"],enableTool
[d("div",{children:[d("img",{src:O6,class:"service-icon"}),e("autoEnableSubtitle")]}),d(M1,{})]})})}var W0=`${j}-new-user-guide``${W0}-container`,style:m,children:[d("div",{class:`${W0}-close-icon`,onClick:e,children:d(jR,{})}),d("img",{class:`${W0}-bg ${n}`,src:sne}),d("div",{class:`${W0}-content ${n}`,children:[d(c,{}),d("div",{class:`${W0}-message text-red-500`,children:[i[r],xe().any&&r==="float-ba
.border};
      background: ${lr.inputBackground};
      color: ${lr.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),g.setAttribute("id","reason");let f=document.createElement("div");f.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=g.id,h.innerHTML=`<span style="color: ${lr.error};">*</span>${s("reportI
g("url change newRule",r);try{Ys(!1);let a=n.config.generalRule;r&&(n.rule=Ul(a,r),n.rule=await oh(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await dh("updateRuleByUrlChange",t.mutationChangeDelay||50),Jn("Translated");let i=FN(n);return n.filterRule=Gn(n,!0),LN(i),!0}catch(a){return I.debug(a),!1}}return ar,!1}var D5=[],Eoe=new AbortController,{signal:Aoe}=Eoe,fp=0,hp=0
-right: 10px;`),f.append(h),f.append(g),g.addEventListener("input",function(){A.disabled=c()}),g.addEventListener("keydown",function(O){O.stopPropagation()}),p.append(f);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${lr.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${lr.inputBackground};
      color: ${
TML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-cancel-btn`,m.innerText=s("close"),m.onclick=()=>{i(m,!0)},a.appendChild(m),Qa(e,"freeImageError")}function VF(e,t,n,r,a,i){let o=e.rule.imageRule,s=Se.bind(null,e.config
587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation 
ction part!");return}if(Doe(e,r,t))return;if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.setupMouseHoverListener=B4);let o={...Gn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.documentElement)!==u.state.translation
t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}U0.call(Fe,Fe.ctx)}async function Ex(e,t){let n={};e&&e.detail?.trigger&&(n.trigger=e.detail.trigger),Te({key:"share
temsPerPage:r,goToPrevPage:a,goToNextPage:i,onItemsPerPageChange:o}){let{t:s}=H(),l=`items-per-page-${t}``${a}_glossary.csv``lang-${q}-${U}`))]})}),d("td",{children:d("a",{href:"javascript:void(0)",onClick:()=>F(q),title:i("delete"),style:{display:"inline-flex",alignItems:"center",justifyContent:"center",padding:"0.25rem"},children:d(si,{style:{width:32,height:32}})})})]},`${L.k}-${q}``\u6210\u529F\u5C
le\b)/i,Eg=/Silk/i,uo=/Windows Phone/i,Pw=/\bWindows(?:.+)ARM\b/i,Lw=/BlackBerry/i,Rw=/BB10/i,Fw=/Opera Mini/i,_w=/\b(CriOS|Chrome)(?:.+)Mobile/i,Bw=/Mobile(?:.+)Firefox\b/i,Nw=e=>typeof e<"u"&&e.platform==="MacIntel"&&typeof e.maxTouchPoints=="number"&&e.maxTouchPoints>1&&typeof globalThis.MSStream>"u";function nH(e){return t=>t.test(e)}function xe(e){let t={userAgent:"",platform:"",maxTouchPoints:0}
gth)return r.forEach(a=>{Poe(e,a)}),!0}function Ioe(e){let t=[e];if(e.nodeName=="FONT"&&e.className.includes(j)){let n=e.closest(`.${j}-target-wrapper`);n&&(t=[n.parentElement])}else{let r=[...e.querySelectorAll(`.${j}-target-wrapper``fingers.${t.generalRule[r[n]]}``${p(g)}``translationServices.${g}``desc.${x}`)!==`desc.${x}`&&(y=u(`desc.${x}`));let C="";b==="finger"?C=F5(n,r,x):b==="mouseHoverHoldKey"?
{let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;ar;let r=n.config.rules?.find(a=>Us(e.currentUrl,a));if(n.rule.id!=r?.id){I.debug("new rule.id",r?.id,"old id",n.rule.id),I.debug("url change newRule",r);try{Ys(!1);let a=n.config.generalRule;r&&(n.rule=Ul(a,r),n.rule=await oh(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await dh("updateRuleByUrlChange",t.mut
"method":"',()=>{let t=e;return(t.id+3)%13===0||(t.id+5)%29===0?'"method" : "':'"method": "''"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"''"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"''${u.k.replace(/`/g,"\\`")}${c}': '${(u.v||u.k).replace(/`/g,"
ren:n("developer")})]})]})}),d("div",{role:"main",class:`relative max-[${Ko}px]:px-4 min-[${Ko}px]:pt-6`,children:d(tv,{value:t,defaultCase:a1,cases:u})})]})})]})}async function nO(){await JN();let e=document.getElementById("mount");e&&(e.classList.add("min-h-screen","flex","flex-col"),(async()=>{h2(KL);let t=await Ct();if(location.href.includes(Lu)&&t.joinJobs){let r=xw.replace("{jobs}",t.joinJobs.map(a=>
 it into Traditional Chinese (Taiwan style),{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multiplePrompt:``,subtitlePrompt:``},{id:"zh-CN2ru",prompt:`;; Treat next line as \u7B80\u4F53\u4E2D\u6587 and translate it into \u4FC4\u8BED\uFF0C{{html_only}} output translation ONLY. I
cked:e.active})})})]}),d("p",{class:"cursor-pointer",dangerouslySetInnerHTML:{__html:o}})]})}function eO({total:e,currentPage:t,totalPages:n,itemsPerPage:r,goToPrevPage:a,goToNextPage:i,onItemsPerPageChange:o}){let{t:s}=H(),l=`items-per-page-${t}``${a}_glossary.csv``lang-${q}-${U}`))]})}),d("td",{children:d("a",{href:"javascript:void(0)",onClick:()=>F(q),title:i("delete"),style:{display:"inline-flex",al
rance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.t
Color Emoji"'.split(","),serif:'ui-serif,Georgia,Cambria,"Times New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{v
`:""]})]}),d(_6,{position:n})]})}var sne="";var Yr=`${j}-new-user-guide`,KR=``;var K0=`${j}-new-user-guide-root``#${K0}`);a&&r.target!==a&&H6(K0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function H6(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Rr(e.config.shortcuts.toggleTransl
ute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function Pne(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/
o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatus`,$t=`${o}PageUrlChanged`,Kt=`${o}ReceiveCommand`,jt=o+"LastUseMouseHoverTime",Vt=o+"LastUseInputTime",zt=o+"LastUseManualTranslatePageTime",Jt=`${o}PopupReceiveMessage``${v}download-subtitle/`,hr=`${v}pdf-pro/`,Sr=`${v}tex
tart=parseInt(m[1]),p.end=p.start+2e3,p.duration=p.end-p.start,p.content=m[2].replace(/^<\/SYNC[^>]*>/gi,"");let g=!0,f=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typ
`background-color:${lr.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${lr.text};`);let w=document.createElement("div");w.setAttribute("style",`margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${lr.checkColor};``<svg width="72" height="72" viewBox="0 0 72 72" fill="non
}if(Doe(e,r,t))return;if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.setupMouseHoverListener=B4);let o={...Gn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.documentElement)!==u.state.translationMode&&Fi(n.document.
g.id,h.innerHTML=`<span style="color: ${lr.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${lr.text};margin-right: 10px;`),f.append(h),f.append(g),g.addEventListener("input",function(){A.disabled=c()}),g.addEventListener("keydown",function(O){O.stopPropagation()}),p.append(f);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",
unction Moe(e,t,n,r){let a=aD(e,t,n,r);if(a==null)return;if(a instanceof HTMLElement)return a;let i=()=>{let l=r.document.elementFromPoint(e,t);if(!l)return;let u=ev(l,e,t);return u===l?l.nodeName==="BUTTON"?l:void 0:zN(u,n)},o=()=>{try{a.setStartBefore(a.startContainer),a.setEndAfter(a.startContainer)}catch(u){I.debug("get mouse over word fail",u)}let l=a.getBoundingClientRect();if(!(l.left>e||l.right<
 translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"zh-CN2auto",subtitlePrompt:``,multipleSystemPrompt:""},{id:"auto2zh-CN-NE",extends:"auto2zh-CN",systemPrompt:"\u4F60\u662F\u4E00\u4E2A\u4E1C\u5317\u4EBA\u7FF
n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("message",t)}}handleMessages(e){let n=({data:t})=>{h===t.eventType&&t.to===this.from&&e(t)};return globalThis.addEventListener("message",n),()=>{globalThis.removeEventListener("message",n)}}},re=new E("content-script","inject"),T=new E("inject","content-script"),V={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a
e.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-cancel-btn`,m.innerText=s("close"),m.onclick=()=>{i(m,!0)},a.appendChild(m),Qa(e,"freeImage
ror-id-${s}`);if(c){let m=c.parentElement;if(c.remove(),m){delete pu[s];let p=o.cloneNode(!0);p.id=s,m.appendChild(p),i.push(l.sentence)}}}}try{await mn({sentences:i},e,(s,l,u)=>{n+=1;let c=u.id,m=Mc(document.body,c);if(!m)return;let p=m.parentElement;p&&(m.remove(),s?(t+=1,p.innerHTML=St.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${og}='${r}''"+hi(t.document.activeElement)+"''[contenteditabl
is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO notes. Input:
 {{text}}`,multipleSystemPrompt:""},{id:"ja2auto",prompt:`;; Treat next line as plain text input and translate it from \u65E5\u8BED into {{to}},{{html_only}} output translation ONLY. If translation is unnecessary (e.g. proper nouns, codes, etc.), return the original text. NO explanations. NO not
RSIVE_TRANSLATE_FIREFOX==="1"?!0:Ow(kg)}function Ar(e){return!mr(e)}var y3;function mr(e){return e?.confirmSupportMouse!=null?e?.confirmSupportMouse:y3?!0:!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement?!1:!!globalThis.matchMedia("(pointer:fine)").matches}function Uw(){let e=!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement;return!!globalThis.matchMedia("(pointer:f
.enableTriggerTranslate||n?.liveSubtitleRule?.enableTriggerTranslate;!r&&a&&document.dispatchEvent(new CustomEvent(Ru,{detail:{tempEnableSubtitle:t}}))}async function woe(e){let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;ar;let r=n.config.rules?.find(a=>Us(e.currentUrl,a));if(n.rule.id!=r?.id){I.debug("new rule.id",r?.id,"old id",n.rule.id),I.debug("url change newRule",r);try{Ys(!1);let
path="url(#clip0_12094_60954)">
  <path d="" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,n.append(i);let o=document.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=docu
{p}/`:`https://test-onboarding.${p}/`,Z=`https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTrans
e,(s,l,u)=>{n+=1;let c=u.id,m=Mc(document.body,c);if(!m)return;let p=m.parentElement;p&&(m.remove(),s?(t+=1,p.innerHTML=St.sanitize(`<span id="error-id-${c}">${s.message}</span>``[${og}='${r}''"+hi(t.document.activeElement)+"''[contenteditable="true"], [contenteditable=""]''/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r?\n/g,"<BR>"),htmlDecode:(e,t)=>e.replace(/<BR\s*\/?>/gi,t||`\r
`).re
label");h.htmlFor=g.id,h.innerHTML=`<span style="color: ${lr.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${lr.text};margin-right: 10px;`),f.append(h),f.append(g),g.addEventListener("input",function(){A.disabled=c()}),g.addEventListener("keydown",function(O){O.stopPropagation()}),p.append(f);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("
function s(){o=!o,o?(I.debug("mouse hover translate on"),n.state.isTranslateDirectlyOnHover=!0,B4(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,B4(n,t))}t.document.addEventListener($d,s),Wo.push(()=>{t.document.removeEventListener($d,s)});let l=Dr(p=>{if(!(Math.abs(p.clientX-fp)+Math.abs(p.clientY-hp)<=3)&&(fp=p.clientX,hp=p.clientY,yu&&!Zs&&L5(n,!1,t),o||yu&&!Zs)){let g=HN(n.r
if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.setupMouseHoverListener=B4);let o={...Gn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSelectors],o.excludeSelectorsRegexes={},o.selectors=[];let s=!1,{engineCtx:l,ctx:u}=k5(i,e);Uo({id:i.id,container:r,filterRule:o,force:!0,onParagraph:c=>{Pc(n.document.documentElement)!==u.state.translationMode&&Fi(n.document.documentElement,u.stat
b.htmlFor=x.id,b.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,b.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;color:${lr.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let v=document.createElement("div");v.textContent=s("reportInfo.emailError"),v.setAttribute("style"
:"secondary"}}]:[],{name:i("subtitle"),props:{href:"#subtitle",className:"secondary"}},...u&&!l?[{name:Ni(e,!0)?i("mangaAndImage"):i("manga"),props:{href:"#manga",className:"secondary"}}]:[],{name:i("inputOptions"),props:{href:"#input",className:"secondary"}},...xe().any||X()?[]:[{name:i("selectionTranslation"),props:{href:"#selection_translation",className:"secondary"}}],{name:i("mouseHoverOptions"),p
son"}}),kl("reportActive","1")}catch(e){I.error(e)}}function NN(e,t){let n=e.rule?.subtitleRule,r=n?.autoEnableSubtitle,a=n?.enableTriggerTranslate||n?.liveSubtitleRule?.enableTriggerTranslate;!r&&a&&document.dispatchEvent(new CustomEvent(Ru,{detail:{tempEnableSubtitle:t}}))}async function woe(e){let t=e.ctx.rule;if(!t.urlChangeUpdateRule)return!1;let n=e.ctx;ar;let r=n.config.rules?.find(a=>Us(e.currentUr
ule",r);try{Ys(!1);let a=n.config.generalRule;r&&(n.rule=Ul(a,r),n.rule=await oh(n.config,n.rule,[...a.advanceMergeConfig||[],...t.advanceMergeConfig||[]])),await dh("updateRuleByUrlChange",t.mutationChangeDelay||50),Jn("Translated");let i=FN(n);return n.filterRule=Gn(n,!0),LN(i),!0}catch(a){return I.debug(a),!1}}return ar,!1}var D5=[],Eoe=new AbortController,{signal:Aoe}=Eoe,fp=0,hp=0,yu=!1,Zs,ON=0,Wo=
itle"),m.setAttribute("style",`text-align:left;margin-top:-20px;color:${lr.text};`),r.append(m);let p=document.createElement("div");p.setAttribute("style","display:flex;flex-direction:column;");let g=document.createElement("textarea");g.placeholder=s("reportInfo.reasonDesc"),g.required=!0,g.setAttribute("style",`border-radius: 10px;
      border: 1px solid ${lr.border};
      background: ${lr.inputBackgrou
e",`display: flex; margin-right: 10px;word-break:keep-all;color:${lr.text};`);let y=document.createElement("div");y.setAttribute("style","display: flex; align-items: center; text-align: left;");let v=document.createElement("div");v.textContent=s("reportInfo.emailError"),v.setAttribute("style",`color: ${lr.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;`
er("message",s)})}handleMessageOnce(e){return new Promise(n=>{let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&(n(s.data),globalThis.removeEventListener("message",t))};globalThis.addEventListener("message",t)})}handleMessage(e,n){let t=a=>{let s=a.data;h===s.eventType&&s.type===e&&s.to===this.from&&n(s)};return globalThis.addEventListener("message",t),()=>{globalThis.removeEventListener("mess
https://github.com/${l}/${l}/`,lt=`https://s.${p}/``${o}Share`,Ot=`${o}ShowFloatBallGuide`,Ut=`${o}ShowPopupModalGuide`,wt=o+"DocumentMessageTempEnableSubtitleChanged",Dt=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Bt=`${o}ToggleMouseHoverTranslateDirectly`,Ft=`${o}ReqDraft`,Nt=`${o}ResDraft`,Gt=`${o}Container`,qt=`${o}SpecifiedContainer`;var Wt=`${o}PageTranslatedStatus`,Ht=`${o}MangaTranslatedStatu
=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-cancel-btn`,m.innerText=s("close"),m.onclick=()=>{i(m,!0)},a.appendChild(m),Qa(e,"freeImageError")}function VF(e,t,n,r,a,i){let o=e.rule.imageRule,s=Se.bind(null,e.config.interfaceLanguage),l=document.createElement("div");l.innerText=s("freeIma
(this.apiServiceConfig).length)throw new ee("serivce id not found config");let{text:n,from:r,to:a}=t;if(!this.langMap.has(a))throw new ee(`Unsupported language: ${a}``${i.accessToken}-0-0`),o.append("format","html"),o.append("lang",`${r==="auto"?"":d9.get(r)+"-"}${d9.get(a)}`),n.forEach(u=>{o.append("text",u)});let s=await super.request({url:`https://translate.yandex.net/api/v1/tr.json/translate?${o.t
pt:void(0)":te,target:"_blank",children:[d(UP,{}),d("h1",{children:r("browser.shortBrandName")})]}),d("span",{style:{cursor:"pointer"},class:"version",onClick:o=>{o.preventDefault(),!i&&qr(`${te}docs/CHANGELOG/#${n.replace(/\./gi,"")}``<div>
<br>
    <a target="_blank" href="${GT}">${l("feedbackOrMore")}</a>
  </div>`,m=[...u.map((g,f)=>`<span>
        <a target="_blank" href="${g.url}">${g.name}</a>
  
mage'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can also enable the hover quick translation button below. Once enabled, when you hover over any image, you'll see a 'Quick Translate' button - click it to translate.``[${this.serviceName}] `+n("networkError");if(!this.status||this.status<0)return;let
").replace(/\*/g,"%2A")}async signedRequest({secretId:t,secretKey:n,action:r,payload:a,service:i,version:o}){let s=new Date().toISOString(),l=Math.random().toString(36).slice(2),u={Action:r,Version:o,Format:"JSON",AccessKeyId:t,SignatureNonce:l,Timestamp:s,SignatureMethod:"HMAC-SHA1",SignatureVersion:"1.0"},m=(h=>{let b=Object.keys(h).sort().map(y=>`${this.encode(y)}=${this.encode(h[y])}`).join("&");r
t x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${lr.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${lr.inputBackground};
      color: ${lr.text};
      font-size: 14px;
      width: 303px;
      height: 45px;
      box-sizing: border-box;
      margin: 0 1px;
      outline: none;`),x.placeholder=s("reportIn
f navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let n=t.userAgent,r=n.split("[FBAN");typeof r[1]<"u"&&(n=r[0]),r=n.split("Twitter"),typeof r[1]<"u""DENO",Ag="CHROME",kg="FIREFOX";function Ow(e){let t
gn:left;margin-top:-20px;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=`${j}-btn ${j}-cancel-btn``${c}px Arial`,p=this.breakTextIntoLines(t,l-this.H_PADDING);c<this.MAX_FONT_SIZE;){this.ctx.font=`${c}px Arial``<span id="error-id-${p}">
:"http://localhost",config:t});Te({key:"options_page_view""visibilitychange",i),()=>{globalThis.removeEventListener("visibilitychange",i)}},[a]),K(()=>{let i=async()=>(await a(),!0);return globalThis.addEventListener("beforeunload",i),()=>{globalThis.removeEventListener("beforeunload",i)}},[a])}function nse(){let e=En(),[t,n]=e;K(()=>{if(!t||!n)return;(async()=>{try{let a=new URL(globalThis.location.href),i=a.sea
 of input at ${ms}:${za}`)}function G8(){return za-=5,Z3(`JSON5: invalid identifier character at ${ms}:${za}``f${e}``Image translation is only available for Pro members.

To translate an image, right-click on any image and select 'Translate Image'. Additionally, if you have set up a hotkey for hover translation of paragraphs, you can use the same hotkey to translate images. For convenience, you can als
 ${a}`).join(`
`))}t.debug&&I.setLevel("debug"),globalThis.location.hash||(globalThis.location.hash="#general");let n=await vr({url:"http://localhost",config:t});Te({key:"options_page_view""visibilitychange",i),()=>{globalThis.removeEventListener("visibilitychange",i)}},[a]),K(()=>{let i=async()=>(await a(),!0);return globalThis.addEventListener("beforeunload",i),()=>{globalThis.removeEventListener("bef
gPress")}`:""]})]}),d(_6,{position:n})]})}var sne="";var Yr=`${j}-new-user-guide`,KR=``;var K0=`${j}-new-user-guide-root``#${K0}`);a&&r.target!==a&&H6(K0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function H6(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.current.style.top=`${L}px``(${Rr(e.config.shortc
rtInfo.emailPlaceholder"),ze.get(Xe,null).then(O=>{if(!O)return;let B=t.config.ignoreReportEmailRegex&&new RegExp(t.config.ignoreReportEmailRegex).test(O.email);O.email&&!B&&(x.value=O.email)});let b=document.createElement("label");b.htmlFor=x.id,b.innerHTML=`<span style="color: transparent;">*</span>${s("reportInfo.email")}`,b.setAttribute("style",`display: flex; margin-right: 10px;word-break:keep-all;col
ign: right;}",".immersive-translate-target-wrapper[dir='rtl'] [data-immersive-translate-class-bak*='block-wrapper'] {display:block;}",".immersive-translate-target-wrapper {word-break:break-word; user-select:text;}",`[imt-state="translation"] .immersive-translate-target-wrapper[dir='rtl'] {display:inline-block!important;}``.css-175oi2r.r-13awgt0.r-1pi2tsx.r-13qz1uu > [role='listbox'] .css-175oi2r.r-13awg
>typeof e<"u"&&e.platform==="MacIntel"&&typeof e.maxTouchPoints=="number"&&e.maxTouchPoints>1&&typeof globalThis.MSStream>"u";function nH(e){return t=>t.test(e)}function xe(e){let t={userAgent:"",platform:"",maxTouchPoints:0};!e&&typeof navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&
tn ${j}-cancel-btn`,m.innerText=s("close"),m.onclick=()=>{i(m,!0)},a.appendChild(m),Qa(e,"freeImageError")}function VF(e,t,n,r,a,i){let o=e.rule.imageRule,s=Se.bind(null,e.config.interfaceLanguage),l=document.createElement("div");l.innerText=s("freeImage.title"),l.setAttribute("style","text-align:left;margin-top:-20px;"),n.append(l);let u=`<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang
,":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){retur
ument.dispatchEvent(new CustomEvent(cn,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(pu),a=[],i=[],o=m0(e,"");for(let s of r){let l=pu[s];if(!l.ok){let c=document.querySelector(`#error-id-${s}`);if(c){let m=c.parentElement;if(c.remove(),m){delete pu[s];let p=o.cloneNode(!0);p.id=s,m.appendChild(p),i.push(l.sentence)}}}}try{await mn({sentences:i},e,(s,l,u)=>{n+=
).any&&r==="float-ball"?`
${a("floatBall.longPress")}`:""]})]}),d(_6,{position:n})]})}var sne="";var Yr=`${j}-new-user-guide`,KR=``;var K0=`${j}-new-user-guide-root``#${K0}`);a&&r.target!==a&&H6(K0)};document.removeEventListener("mousedown",n),document.addEventListener("mousedown",n)}function H6(e){let t=document.querySelector(`#${e}``${L}px`,s.current.style.right="0px",s.current.style.left="auto"):(s.curr
ons or additional content (such as "Here''s meaning within the provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \
"");let g=!0,f=/^<P.+Class\s*=\s*["''parse' op: ${r}`);return i(t,n)};build=(t,n={})=>{let r=n.format||"srt";if(!r||r.trim().length===0)throw new Error("Cannot determine subtitle format!");let a=this.format[r];if(typeof a>"u")throw new Error(`Unsupported subtitle format: ${r}`);let i=a.build;if(typeof i!="function")throw new Error(``);i&&i.remove()}}U0.call(Fe,Fe.ctx)}async function Ex(e,t){let n={};e
(e=>e())}catch{}Wo=[],Ot.setScope("all")}function jN(e){yu=!1}function gp(e,t,n=window){return n.addEventListener(e,t,{signal:Aoe})}function N4(e,t,n,r){if(r=r||HN(e.rule,n),!r){I.debug("can not find selection part!");return}if(Doe(e,r,t))return;if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.setupMouseHoverListener=B4);let o={...Gn(e)};o.excludeSelectors=[...e.rule.mouseHoverExcludeSe
lThis.MSStream>"u";function nH(e){return t=>t.test(e)}function xe(e){let t={userAgent:"",platform:"",maxTouchPoints:0};!e&&typeof navigator<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let n=t.userAgent,r=n.s
ePage)})``meta[name='${XR}'][content='true''*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;function u(O){return l.test(O.trim())}let c=()=>g.value.trim()===""||!u(x.value)||x.value.trim()==="",m=document.createElement("div");m.innerText=s("reportInfo.title"),m.setAttribute("style",`text-align:left;margin-top:-20px;color:${lr.text};`),r.append(m);let p=document.createElement("div");p.setAttribute("styl
Mouse:y3?!0:!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement?!1:!!globalThis.matchMedia("(pointer:fine)").matches}function Uw(){let e=!!navigator.maxTouchPoints||"ontouchstart"in document.documentElement;return!!globalThis.matchMedia("(pointer:fine)").matches&&e}function x3(e){return y3!=null?!1:(y3=e,!0)}function C3(){return X(!1,!0)?"monkey":Yn()?"chrome":In()?"firefox":rt()?"safari"
omicHash=${n}&domain=${HF(location.hostname)}``<div style="text-align:left;"> ${s("freeImage.notSupport",{freeLang:o.clientSupportLangs.join(","),1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-canc
t-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function Pne(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.innerHTML="";let l=document.createElement("div");l.innerHTML=`<svg width="72" height="72" viewBox="0 0 72 72" f
.location.hash||(globalThis.location.hash="#general");let n=await vr({url:"http://localhost",config:t});Te({key:"options_page_view""visibilitychange",i),()=>{globalThis.removeEventListener("visibilitychange",i)}},[a]),K(()=>{let i=async()=>(await a(),!0);return globalThis.addEventListener("beforeunload",i),()=>{globalThis.removeEventListener("beforeunload",i)}},[a])}function nse(){let e=En(),[t,n]=e;K((
ium";v="113", "Not-A.Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"''"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',"sec-ch-ua-mobile":"?0","sec-ch-ua-platform":'"Windows"''${u.k.replace(/`/g,"\\`")}${c}': '${(u.v||u.k).replace(/`/g,"\\`")}''"')&&i?.endsWith('"''/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}async signedRequest({se
mes New Roman",Times,serif'.split(","),mono:'ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace''''button'],[type='reset'],[type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-web
or:"transparent",backgroundImage:"none"},":-moz-focusring":{outline:"auto"},":-moz-ui-invalid":{boxShadow:"none"},progress:{verticalAlign:"baseline"},"::-webkit-inner-spin-button,::-webkit-outer-spin-button":{height:"auto"},"[type='search''button,[role="button"]''in "'+e.mark.name+'" ''Unknown option "'+n+'" is met in definition of "'+e+'" YAML type.')}),this.options=t,this.tag=e,this.kind=t.kind||null,
ext),pu[p]={ok:!0,sentence:m}),document.dispatchEvent(new CustomEvent(cn,{detail:JSON.stringify({type:"paragraphTranslated",payload:{ok:!u}})})))})}}async function z_(e){let t=0,n=0;Jn("Translating"),document.dispatchEvent(new CustomEvent(cn,{detail:JSON.stringify({type:"retryFailedParagraphsStart",payload:{}})}));let r=Object.keys(pu),a=[],i=[],o=m0(e,"");for(let s of r){let l=pu[s];if(!l.ok){let c=docume
 Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u672C\u8F93\u5165\uFF0C\u5E76\u5C06\u51
veloper")})]})]})}),d("div",{role:"main",class:`relative max-[${Ko}px]:px-4 min-[${Ko}px]:pt-6`,children:d(tv,{value:t,defaultCase:a1,cases:u})})]})})]})}async function nO(){await JN();let e=document.getElementById("mount");e&&(e.classList.add("min-h-screen","flex","flex-col"),(async()=>{h2(KL);let t=await Ct();if(location.href.includes(Lu)&&t.joinJobs){let r=xw.replace("{jobs}",t.joinJobs.map(a=>`    \u2022 ${
",{class:"header-navbar-brand",href:i?"javascript:void(0)":te,target:"_blank",children:[d(UP,{}),d("h1",{children:r("browser.shortBrandName")})]}),d("span",{style:{cursor:"pointer"},class:"version",onClick:o=>{o.preventDefault(),!i&&qr(`${te}docs/CHANGELOG/#${n.replace(/\./gi,"")}``<div>
<br>
    <a target="_blank" href="${GT}">${l("feedbackOrMore")}</a>
  </div>`,m=[...u.map((g,f)=>`<span>
        <a 
;o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function Pne(e,t,n,r,a,i,o,s){r.innerHTML="",a.innerHTML="",i.inner
color: ${lr.text};margin-right: 10px;`),f.append(h),f.append(g),g.addEventListener("input",function(){A.disabled=c()}),g.addEventListener("keydown",function(O){O.stopPropagation()}),p.append(f);let x=document.createElement("input");x.type="email",x.id="emailReport",x.setAttribute("style",`border: 1px solid ${lr.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${lr.inputBackgrou
})}})},onFrame:()=>{},onIgnoreElement:()=>{}})}function HN(e,t){return Moe(fp,hp,e,t)}function Moe(e,t,n,r){let a=aD(e,t,n,r);if(a==null)return;if(a instanceof HTMLElement)return a;let i=()=>{let l=r.document.elementFromPoint(e,t);if(!l)return;let u=ev(l,e,t);return u===l?l.nodeName==="BUTTON"?l:void 0:zN(u,n)},o=()=>{try{a.setStartBefore(a.startContainer),a.setEndAfter(a.startContainer)}catch(u){I.debug("
ubtitle"),props:{href:"#subtitle",className:"secondary"}},...u&&!l?[{name:Ni(e,!0)?i("mangaAndImage"):i("manga"),props:{href:"#manga",className:"secondary"}}]:[],{name:i("inputOptions"),props:{href:"#input",className:"secondary"}},...xe().any||X()?[]:[{name:i("selectionTranslation"),props:{href:"#selection_translation",className:"secondary"}}],{name:i("mouseHoverOptions"),props:{href:"#mouse_hover",cla
e"):i("manga"),props:{href:"#manga",className:"secondary"}}]:[],{name:i("inputOptions"),props:{href:"#input",className:"secondary"}},...xe().any||X()?[]:[{name:i("selectionTranslation"),props:{href:"#selection_translation",className:"secondary"}}],{name:i("mouseHoverOptions"),props:{href:"#mouse_hover",className:"secondary"}},{name:i("floatBallOptions"),props:{href:"#floating",className:"secondary"}},{n
path="url(#clip0_12096_60967)">
  <path d="" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `,a.append(l);let u=document.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=documen
ver translate on"),n.state.isTranslateDirectlyOnHover=!0,B4(n,t)):(I.debug("mouse hover translate off"),n.state.isTranslateDirectlyOnHover=!1,B4(n,t))}t.document.addEventListener($d,s),Wo.push(()=>{t.document.removeEventListener($d,s)});let l=Dr(p=>{if(!(Math.abs(p.clientX-fp)+Math.abs(p.clientY-hp)<=3)&&(fp=p.clientX,hp=p.clientY,yu&&!Zs&&L5(n,!1,t),o||yu&&!Zs)){let g=HN(n.rule,t);if(g){if(P5(g))return;N4
ngth-1?", ":""}
      </span>``nav ${n}``nav ${t}`,children:[d(Ce,{title:l("image.enableTools")}),d("label",{for:"enable",children:d("input",{type:"checkbox",id:"enable",name:"switch",role:"switch",onChange:u=>{let c=u.target.checked;r(m=>({...m,generalRule:{...m.generalRule,"imageRule.add":{...m.generalRule?.["imageRule.add"],enableTools:c}}}))},checked:s.enableTools})})]})})}function QN({ctx:e,classNa
textContent=s("reportInfo.emailError"),v.setAttribute("style",`color: ${lr.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;``background-color:${lr.inputBackground};`);let T=document.createElement("label");T.htmlFor=S.id,T.innerText=s("reportInfo.attachLog"),T.setAttribute("style",`margin-left:8px;color:${lr.text};`);let w=document.createElement("div"
}),Ot.setScope("mouseHover"),Wo.push(()=>{Ot.deleteScope("mouseHover")})}}function koe(){try{Wo.forEach(e=>e())}catch{}Wo=[],Ot.setScope("all")}function jN(e){yu=!1}function gp(e,t,n=window){return n.addEventListener(e,t,{signal:Aoe})}function N4(e,t,n,r){if(r=r||HN(e.rule,n),!r){I.debug("can not find selection part!");return}if(Doe(e,r,t))return;if(P5(r)){I.debug("exclude  dom");return}_N(e,r);let i=lp();i&&(i.s
<"u"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof e=="string"?t.userAgent=e:e&&e.userAgent&&(t={userAgent:e.userAgent,platform:e.platform,maxTouchPoints:e.maxTouchPoints||0});let n=t.userAgent,r=n.split("[FBAN");typeof r[1]<"u"&&(n=r[0]),r=n.split("Twitter"),typeof r[1]<"u""DENO",Ag="CHROME",kg="FIREFOX";function Ow(e){let t;try{let n=
u&&!l?[{name:Ni(e,!0)?i("mangaAndImage"):i("manga"),props:{href:"#manga",className:"secondary"}}]:[],{name:i("inputOptions"),props:{href:"#input",className:"secondary"}},...xe().any||X()?[]:[{name:i("selectionTranslation"),props:{href:"#selection_translation",className:"secondary"}}],{name:i("mouseHoverOptions"),props:{href:"#mouse_hover",className:"secondary"}},{name:i("floatBallOptions"),props:{href:"
,1:d3})}</div>`;r.innerHTML=qn?.createHTML(u)||u;let c=document.createElement("button");c.className=j+"-btn",c.innerText=s("freeImage.close"),c.onclick=()=>{pt(dx,"1"),i(c,!0)},a.appendChild(c);let m=document.createElement("button");m.className=`${j}-btn ${j}-cancel-btn`,m.innerText=s("close"),m.onclick=()=>{i(m,!0)},a.appendChild(m),Qa(e,"freeImageError")}function VF(e,t,n,r,a,i){let o=e.rule.imageRule,s=
ent.createElement("div");o.innerText=e("reportInfo.submitSuccess"),o.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),n.append(o);let s=document.createElement("button");s.setAttribute("style","margin-top:36px"),s.className=j+"-btn",s.innerText=e("reportInfo.ok"),s.onclick=()=>{a(s,!0)},r.append(s)}function Pne(e,t,n,r,a,i,o,s){r.innerHTML="",a
00px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`),g.setAttribute("id","reason");let f=document.createElement("div");f.setAttribute("style","display: flex; text-align: left;");let h=document.createElement("label");h.htmlFor=g.id,h.innerHTML=`<span style="color: ${lr.error};">*</span>${s("reportInfo.reasonLabel")}`,h.setAttribute("style",`color: ${lr.text};margin-right: 10px;`),f.append(h)
`${j}-new-user-guide`;function B6(){return d("img",{class:`${Zte}-img`,src:An("images/new_float_ball_intro.png")})}var k1=`${j}-new-user-guide`;function N6(){let{t:e}=H();return d("div",{class:`${k1}-select-service-guide`,children:d("div",{class:`${k1}-select-service-guide-card`,children:[d("div",{class:`${k1}-max-model`,children:e("translationServicesGroup.pro")}),d("div",{class:`${k1}-model-example``$
e provided context"
}
\u3010Sentence Example\u3011: 
{
  "translation": "This is a test sentence."
}
# Strict Prohibitions 
- Mixed output formats
- Missing required fields
- Unrequested additional information
- Language system mixing`,selectionPrompt:`\u3010Content to Translate\u3011:
"{{text}}"`,langOverrides:[{id:"auto2zh-CN",prompt:`;; \u628A\u4E0B\u4E00\u884C\u6587\u672C\u4F5C\u4E3A\u7EAF\u6587\u
cument.createElement("div");u.innerText=n("reportInfo.submitFail"),u.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`),a.append(u);let c=document.createElement("div");c.innerText=n("reportInfo.submitFailDes"),c.setAttribute("style",`color: ${lr.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;``${